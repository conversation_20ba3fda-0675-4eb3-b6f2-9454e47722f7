<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Position">

    <resultMap id="resultMapPosition"           type="Position">
        <result property="id"                   column="id"/>
        <result property="code"                 column="code"/>
        <result property="desc"                 column="desc_it"/>
        <result property="descEn"               column="desc_en"/>
        <result property="descFr"               column="desc_fr"/>
        <result property="descEs"               column="desc_es"/>
        <result property="positionId"           column="position_id"/>
    </resultMap>
	
    <select id="getPositions" resultMap="resultMapPosition" parameterType="java.util.TreeMap">
        SELECT id, code, desc_it, IFNULL(desc_en, desc_it) desc_en, IFNULL(desc_fr, desc_it) desc_fr, IFNULL(desc_es, desc_it) desc_es
        FROM position
        WHERE sport_id = 0
    </select>
    
    <select id="getPositionDetails" resultMap="resultMapPosition" parameterType="java.util.TreeMap">
        SELECT id, code, desc_it, IFNULL(desc_en, desc_it) desc_en, IFNULL(desc_fr, desc_it) desc_fr, IFNULL(desc_es, desc_it) desc_es, position_id
        FROM position_detail
        WHERE sport_id = 0 AND id > 0
    </select>

</mapper>
