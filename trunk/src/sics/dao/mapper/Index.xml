<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Index">

    <resultMap id="resultMapIndexEvent"         type="IndexEvent">
        <result property="id"                   column="id"/>
        <result property="fixtureId"            column="fixture_id"/>
        <result property="teamId"               column="team_id"/>
        <result property="indexEventTypeId"     column="index_event_type_id"/>
        <result property="value"                column="value"/>
    </resultMap>

    <resultMap id="resultMapIndexEventType"     type="IndexEventType">
        <result property="id"                   column="id"/>
        <result property="indexTypeId"          column="index_type_id"/>
        <result property="desc"                 column="desc_it"/>
        <result property="descEn"               column="desc_en"/>
        <result property="descFr"               column="desc_fr"/>
        <result property="descEs"               column="desc_es"/>
        <result property="descRu"               column="desc_ru"/>
    </resultMap>

    <resultMap id="resultMapIndexTrend"         type="IndexTrend">
        <result property="id"                   column="id"/>
        <result property="fixtureId"            column="fixture_id"/>
        <result property="teamId"               column="team_id"/>
        <result property="indexEventTypeId"     column="index_event_type_id"/>
        <result property="periodId"             column="period_id"/>
        <result property="periodMinute"         column="period_minute"/>
        <result property="periodSecond"         column="period_second"/>
        <result property="score"                column="score"/>
        <result property="oi"                   column="oi"/>
        <result property="delta"                column="delta"/>
    </resultMap>
	
    <select id="getFixtureIndexEvent" resultMap="resultMapIndexEvent" parameterType="java.util.TreeMap">
        SELECT id, fixture_id, team_id, oi_event_type_id index_event_type_id, value
        FROM oi_event
        WHERE fixture_id = ${fixtureId} AND groupset_id = -1
    </select>

    <select id="getIndexEventType" resultMap="resultMapIndexEventType" parameterType="java.util.TreeMap">
        SELECT id, oi_type_id index_type_id, desc_it, desc_en, desc_fr, desc_es, desc_ru
        FROM oi_event_type
    </select>

    <select id="getFixtureIndexTrend" resultMap="resultMapIndexTrend" parameterType="java.util.TreeMap">
        SELECT id, fixture_id, team_id, oi_event_type_id index_event_type_id,
               period_id, period_minute, period_second, score, oi, delta
        FROM oi_trend
        WHERE fixture_id = ${fixtureId} AND groupset_id = -1
    </select>

</mapper>
