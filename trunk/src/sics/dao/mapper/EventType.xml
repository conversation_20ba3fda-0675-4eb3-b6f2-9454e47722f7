<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="EventType">

    <resultMap id="resultMapEventType"              type="EventType">
        <id     property="id"                       column="id"/>
        <result property="desc"                     column="desc_it"/>
        <result property="descEn"                   column="desc_en"/>
        <result property="descFr"                   column="desc_fr"/>
        <result property="descEs"                   column="desc_es"/>
        <result property="descRu"                   column="desc_ru"/>
    </resultMap>

    <select id="getEventTypes" resultMap="resultMapEventType" parameterType="java.util.TreeMap">
        SELECT id, code, desc_it, desc_en, IFNULL(desc_fr, desc_en) desc_fr, IFNULL(desc_es, desc_en) desc_es, IFNULL(desc_ru, desc_en) desc_ru
        FROM event_type
        WHERE panel_id = 1 AND groupset_id = -1 AND id NOT IN(740, 284, 29, 30, 739, 737, 33) AND visible = 1
    </select>
    
    <select id="getOppositeEventTypes" resultMap="resultMapEventType" parameterType="java.util.TreeMap">
        SELECT id, code, desc_it, desc_en, IFNULL(desc_fr, desc_en) desc_fr, IFNULL(desc_es, desc_en) desc_es, IFNULL(desc_ru, desc_en) desc_ru
        FROM event_type
        WHERE panel_id = 1 AND groupset_id = -1 AND IFNULl(opposite, 0) = 1 AND id NOT IN(740, 284, 29, 30, 739, 737, 33) AND visible = 1
    </select>
        
</mapper>