<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Country">

    <resultMap id="resultMapCountry"            type="Country">
        <result property="id"                   column="id"/>
        <result property="internationalCompetitionId" column="international_competition_id"/>
        <result property="name"                 column="name_it"/>
        <result property="nameEn"               column="name_en"/>
        <result property="logo"                 column="logo"/>
    </resultMap>
	
    <select id="getCountries" resultMap="resultMapCountry" parameterType="java.util.TreeMap">
        SELECT id, international_competition_id, name_it, IFNULL(name_en, name_it) name_en, logo
        FROM country
    </select>

</mapper>
