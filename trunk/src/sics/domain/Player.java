package sics.domain;

import com.google.gson.Gson;
import java.time.Period;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import sics.controller.BaseController;
import sics.helper.DateHelper;
import sics.helper.GlobalHelper;
import sics.helper.SpringApplicationContextHelper;

/**
 *
 * <AUTHOR>
 */
public class Player implements Cloneable {

    private Long id;
    private String firstName, lastName, knownName;
    private String photo;
    private Date bornDate;

    private Long footId, countryId, playerAgencyId, marketValue;

    @Override
    public Player clone() throws CloneNotSupportedException {
        Player clone = (Player) super.clone();
        return clone;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getKnownName() {
        return knownName;
    }

    public void setKnownName(String knownName) {
        this.knownName = knownName;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public Date getBornDate() {
        return bornDate;
    }

    public String getBornDateString() {
        if (bornDate != null) {
            return DateHelper.toString(bornDate);
        } else {
            return "";
        }
    }

    public String getBornYearString() {
        if (bornDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.bornDate);
            Integer year = calendar.get(Calendar.YEAR);
            return "'" + year.toString().substring(2);
        } else {
            return "";
        }
    }

    public Integer getAge() {
        if (this.bornDate != null) {
            return Period.between(
                    this.bornDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                    new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
            ).getYears();
        }

        return 0;
    }

    public void setBornDate(Date bornDate) {
        this.bornDate = bornDate;
    }

    public Long getFootId() {
        return footId;
    }

    public void setFootId(Long footId) {
        this.footId = footId;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public Long getPlayerAgencyId() {
        return playerAgencyId;
    }

    public void setPlayerAgencyId(Long playerAgencyId) {
        this.playerAgencyId = playerAgencyId;
    }

    public Long getMarketValue() {
        return marketValue;
    }

    public String getMarketValueString(String language) {
        if (marketValue != null) {
            return GlobalHelper.formatNumber(marketValue, SpringApplicationContextHelper.getMessage("number.format.thousands", BaseController.getLocaleFromLanguange(language)), SpringApplicationContextHelper.getMessage("number.format.millions", BaseController.getLocaleFromLanguange(language)));
        } else {
            return "";
        }
    }

    public void setMarketValue(Long marketValue) {
        this.marketValue = marketValue;
    }

    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
