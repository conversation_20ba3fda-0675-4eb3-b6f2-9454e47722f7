package sics.domain;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import sics.helper.DateHelper;

/**
 *
 * <AUTHOR>
 */
public class UserLogWrapper {

    private User user;
    private String groupsetName;
    private Date expirationDate, lastLoginDate;
    private Integer totalLogins, totalCharts;

    private List<Log> sessions;

    public UserLogWrapper() {
        this.sessions = new ArrayList<>();
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getGroupsetName() {
        return groupsetName;
    }

    public void setGroupsetName(String groupsetName) {
        this.groupsetName = groupsetName;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public String getExpirationDateString() {
        return DateHelper.toString(expirationDate);
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Date getLastLoginDate() {
        return lastLoginDate;
    }

    public String getLastLoginDateString() {
        return DateHelper.toStringExt(lastLoginDate);
    }

    public void setLastLoginDate(Date lastLoginDate) {
        this.lastLoginDate = lastLoginDate;
    }

    public Integer getTotalLogins() {
        return totalLogins;
    }

    public void addTotalLogins(int amount) {
        if (totalLogins == null) {
            this.totalLogins = 0;
        }

        this.totalLogins += amount;
    }

    public void setTotalLogins(Integer totalLogins) {
        this.totalLogins = totalLogins;
    }

    public Integer getTotalCharts() {
        return totalCharts;
    }

    public void addTotalCharts(int amount) {
        if (totalCharts == null) {
            this.totalCharts = 0;
        }

        this.totalCharts += amount;
    }

    public void setTotalCharts(Integer totalCharts) {
        this.totalCharts = totalCharts;
    }

    public List<Log> getSessions() {
        return sessions;
    }

    public void setSessions(List<Log> sessions) {
        this.sessions = sessions;
    }

    public String getJson() {
        Gson gson = new GsonBuilder()
                .setDateFormat("dd/MM/yyyy HH:mm").create();
        return gson.toJson(this);
    }
}
