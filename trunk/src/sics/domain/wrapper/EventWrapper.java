package sics.domain.wrapper;

import java.util.ArrayList;
import java.util.List;
import sics.domain.Event;

/**
 *
 * <AUTHOR>
 */
public class EventWrapper {

    private List<Event> events;

    private Integer totalEvents, maxEventsAmount;
    private Integer percentage;

    private String color;                       // 3 colori: danger, warning, success

    public EventWrapper() {
        this.events = new ArrayList<>();
        this.totalEvents = -1;                  // per evitare problemi di divisione per 0
        this.maxEventsAmount = -1;              // per evitare problemi di divisione per 0
        this.percentage = 0;
        this.color = "danger";
    }

    public List<Event> getEvents() {
        return events;
    }

    public void setEvents(List<Event> events) {
        this.events = events;
    }

    public Integer getTotalEvents() {
        return totalEvents;
    }

    public void setTotalEvents(Integer totalEvents) {
        this.totalEvents = totalEvents;
    }

    public Integer getMaxEventsAmount() {
        return maxEventsAmount;
    }

    public void setMaxEventsAmount(Integer maxEventsAmount) {
        this.maxEventsAmount = maxEventsAmount;
    }

    public Integer getPercentage() {
        return percentage;
    }

    public void setPercentage(Integer percentage) {
        this.percentage = percentage;
        calculateColor(null);
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public void calculateColor(Integer percentage) {
        if (percentage == null) {
            percentage = this.percentage;
        }
        if (percentage != null) {
            if (percentage > 75) {
                this.color = "dark-success";
            } else if (percentage > 50) {
                this.color = "dark-success bg-opacity-75";
            } else if (percentage > 25) {
                this.color = "warning";
            }
        }
    }
}
