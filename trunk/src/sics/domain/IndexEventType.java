package sics.domain;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.WordUtils;

public class IndexEventType {

    private Long id, indexTypeId;
    private String desc, descEn, descFr, descEs, descRu;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getIndexTypeId() {
        return indexTypeId;
    }

    public void setIndexTypeId(Long indexTypeId) {
        this.indexTypeId = indexTypeId;
    }

    public String getDesc() {
        return desc;
    }

    public String getDesc(String language) {
        language = StringUtils.defaultIfEmpty(language, "en");
        switch (language) {
            case "it":
                return desc;
            case "fr":
                return descFr;
            case "es":
                return descEs;
            case "ru":
                return descRu;
            default:
                return descEn;
        }
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDescEn() {
        return descEn;
    }

    public void setDescEn(String descEn) {
        this.descEn = descEn;
    }

    public String getDescFr() {
        return descFr;
    }

    public void setDescFr(String descFr) {
        this.descFr = descFr;
    }

    public String getDescEs() {
        return descEs;
    }

    public void setDescEs(String descEs) {
        this.descEs = descEs;
    }

    public String getDescRu() {
        return descRu;
    }

    public void setDescRu(String descRu) {
        this.descRu = descRu;
    }
}
