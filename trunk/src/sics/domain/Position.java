package sics.domain;

import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class Position {

    private Long id;
    private String code, desc, descEn, descFr, descEs;
    private Integer positionId;     // usato per sapere a quale ruolo fa riferimento quello specifico

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public String getDesc(String language) {
        language = StringUtils.defaultIfEmpty(language, "en");
        switch (language) {
            case "it":
                return desc;
            case "fr":
                return descFr;
            case "es":
                return descEs;
            default:
                return descEn;
        }
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDescEn() {
        return descEn;
    }

    public void setDescEn(String descEn) {
        this.descEn = descEn;
    }

    public String getDescFr() {
        return descFr;
    }

    public void setDescFr(String descFr) {
        this.descFr = descFr;
    }

    public String getDescEs() {
        return descEs;
    }

    public void setDescEs(String descEs) {
        this.descEs = descEs;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }
}
