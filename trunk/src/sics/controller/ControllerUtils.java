package sics.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.BooleanUtils;
import sics.domain.DocumentRow;
import sics.enums.GroupedField;
import sics.helper.GlobalHelper;

/**
 *
 * <AUTHOR>
 */
public class ControllerUtils {

    public static Map<Long, DocumentRow> getTotals(Boolean isTeam, List<DocumentRow> result, Map<String, Object> params, GroupedField field) {
        Map<Long, DocumentRow> groupedData = new LinkedHashMap<>();
        for (DocumentRow row : result) {
            boolean found = false;
            switch (field) {
                case TEAM:
                    found = groupedData.containsKey(row.getTeamId());
                    break;
                case PLAYER:
                    found = groupedData.containsKey(row.getPlayerId());
                    break;
                case FIXTURE:
                    found = groupedData.containsKey(row.getFixtureId());
                    break;
                case EVENT_TYPE_ID:
                    found = groupedData.containsKey(row.getEventTypeId());
                    break;
            }

            if (found) {
                if (params.containsKey("half")) {
                    if (params.containsKey("zone")) {
                        if (params.containsKey("channel")) {
                            String half = params.get("half").toString();
                            String zone = params.get("zone").toString();
                            String channel = params.get("channel").toString();
                            Double add;
                            switch (half) {
                                case "1":
                                    switch (zone) {
                                        case "1":
                                            switch (channel) {
                                                case "1":
                                                    add = row.getOffZoneOneChannelOne();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "2":
                                                    add = row.getOffZoneOneChannelTwo();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "3":
                                                    add = row.getOffZoneOneChannelThree();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "4":
                                                    add = row.getOffZoneOneChannelFour();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "5":
                                                    add = row.getOffZoneOneChannelFive();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                            }
                                            break;
                                        case "2":
                                            switch (channel) {
                                                case "1":
                                                    add = row.getOffZoneTwoChannelOne();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "2":
                                                    add = row.getOffZoneTwoChannelTwo();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "3":
                                                    add = row.getOffZoneTwoChannelThree();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "4":
                                                    add = row.getOffZoneTwoChannelFour();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "5":
                                                    add = row.getOffZoneTwoChannelFive();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                            }
                                            break;
                                        case "3":
                                            switch (channel) {
                                                case "1":
                                                    add = row.getOffZoneThreeChannelOne();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "2":
                                                    add = row.getOffZoneThreeChannelTwo();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "3":
                                                    add = row.getOffZoneThreeChannelThree();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "4":
                                                    add = row.getOffZoneThreeChannelFour();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "5":
                                                    add = row.getOffZoneThreeChannelFive();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                            }
                                            break;
                                    }
                                    break;
                                case "2":
                                    switch (zone) {
                                        case "1":
                                            switch (channel) {
                                                case "1":
                                                    add = row.getDifZoneOneChannelOne();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "2":
                                                    add = row.getDifZoneOneChannelTwo();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "3":
                                                    add = row.getDifZoneOneChannelThree();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "4":
                                                    add = row.getDifZoneOneChannelFour();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "5":
                                                    add = row.getDifZoneOneChannelFive();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                            }
                                            break;
                                        case "2":
                                            switch (channel) {
                                                case "1":
                                                    add = row.getDifZoneTwoChannelOne();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "2":
                                                    add = row.getDifZoneTwoChannelTwo();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "3":
                                                    add = row.getDifZoneTwoChannelThree();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "4":
                                                    add = row.getDifZoneTwoChannelFour();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "5":
                                                    add = row.getDifZoneTwoChannelFive();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                            }
                                            break;
                                        case "3":
                                            switch (channel) {
                                                case "1":
                                                    add = row.getDifZoneThreeChannelOne();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "2":
                                                    add = row.getDifZoneThreeChannelTwo();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "3":
                                                    add = row.getDifZoneThreeChannelThree();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "4":
                                                    add = row.getDifZoneThreeChannelFour();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                                case "5":
                                                    add = row.getDifZoneThreeChannelFive();
                                                    sum(groupedData, row, add, field);
                                                    break;
                                            }
                                            break;
                                    }
                            }
                        } else {
                            String half = params.get("half").toString();
                            String zone = params.get("zone").toString();
                            Double add;
                            switch (half) {
                                case "1":
                                    switch (zone) {
                                        case "1":
                                            add = row.getOffZoneOneChannelOne() + row.getOffZoneOneChannelTwo() + row.getOffZoneOneChannelThree() + row.getOffZoneOneChannelFour() + row.getOffZoneOneChannelFive();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "2":
                                            add = row.getOffZoneTwoChannelOne() + row.getOffZoneTwoChannelTwo() + row.getOffZoneTwoChannelThree() + row.getOffZoneTwoChannelFour() + row.getOffZoneTwoChannelFive();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "3":
                                            add = row.getOffZoneThreeChannelOne() + row.getOffZoneThreeChannelTwo() + row.getOffZoneThreeChannelThree() + row.getOffZoneThreeChannelFour() + row.getOffZoneThreeChannelFive();
                                            sum(groupedData, row, add, field);
                                            break;
                                    }
                                    break;
                                case "2":
                                    switch (zone) {
                                        case "1":
                                            add = row.getDifZoneOneChannelOne() + row.getDifZoneOneChannelTwo() + row.getDifZoneOneChannelThree() + row.getDifZoneOneChannelFour() + row.getDifZoneOneChannelFive();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "2":
                                            add = row.getDifZoneTwoChannelOne() + row.getDifZoneTwoChannelTwo() + row.getDifZoneTwoChannelThree() + row.getDifZoneTwoChannelFour() + row.getDifZoneTwoChannelFive();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "3":
                                            add = row.getDifZoneThreeChannelOne() + row.getDifZoneThreeChannelTwo() + row.getDifZoneThreeChannelThree() + row.getDifZoneThreeChannelFour() + row.getDifZoneThreeChannelFive();
                                            sum(groupedData, row, add, field);
                                            break;
                                    }
                                    break;
                            }
                        }
                    } else {
                        if (params.containsKey("channel")) {
                            String half = params.get("half").toString();
                            String channel = params.get("channel").toString();
                            Double add;
                            switch (half) {
                                case "1":
                                    switch (channel) {
                                        case "1":
                                            add = row.getOffZoneTwoChannelOne() + row.getOffZoneThreeChannelOne();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "2":
                                            add = row.getOffZoneTwoChannelTwo() + row.getOffZoneThreeChannelTwo();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "3":
                                            add = row.getOffZoneTwoChannelThree() + row.getOffZoneThreeChannelThree();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "4":
                                            add = row.getOffZoneTwoChannelFour() + row.getOffZoneThreeChannelFour();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "5":
                                            add = row.getOffZoneTwoChannelFive() + row.getOffZoneThreeChannelFive();
                                            sum(groupedData, row, add, field);
                                            break;
                                    }
                                    break;
                                case "2":
                                    switch (channel) {
                                        case "1":
                                            add = row.getDifZoneOneChannelOne() + row.getDifZoneTwoChannelOne();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "2":
                                            add = row.getDifZoneOneChannelTwo() + row.getDifZoneTwoChannelTwo();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "3":
                                            add = row.getDifZoneOneChannelThree() + row.getDifZoneTwoChannelThree();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "4":
                                            add = row.getDifZoneOneChannelFour() + row.getDifZoneTwoChannelFour();
                                            sum(groupedData, row, add, field);
                                            break;
                                        case "5":
                                            add = row.getDifZoneOneChannelFive() + row.getDifZoneTwoChannelFive();
                                            sum(groupedData, row, add, field);
                                            break;
                                    }
                                    break;
                            }
                        } else {
                            String half = params.get("half").toString();
                            Double add;
                            switch (half) {
                                case "1":
                                    add = row.getOff();
                                    sum(groupedData, row, add, field);
                                    break;
                                case "2":
                                    add = row.getDif();
                                    sum(groupedData, row, add, field);
                                    break;
                            }
                        }
                    }
                } else if (params.containsKey("zone")) {
                    if (params.containsKey("channel")) {
                        String zone = params.get("zone").toString();
                        String channel = params.get("channel").toString();
                        Double add;
                        switch (zone) {
                            case "1":
                                switch (channel) {
                                    case "1":
                                        add = row.getDifZoneOneChannelOne();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "2":
                                        add = row.getDifZoneOneChannelTwo();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "3":
                                        add = row.getDifZoneOneChannelThree();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "4":
                                        add = row.getDifZoneOneChannelFour();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "5":
                                        add = row.getDifZoneOneChannelFive();
                                        sum(groupedData, row, add, field);
                                        break;
                                }
                                break;
                            case "2":
                                switch (channel) {
                                    case "1":
                                        add = row.getOffZoneTwoChannelOne() + row.getDifZoneTwoChannelOne();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "2":
                                        add = row.getOffZoneTwoChannelTwo() + row.getDifZoneTwoChannelTwo();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "3":
                                        add = row.getOffZoneTwoChannelThree() + row.getDifZoneTwoChannelThree();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "4":
                                        add = row.getOffZoneTwoChannelFour() + row.getDifZoneTwoChannelFour();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "5":
                                        add = row.getOffZoneTwoChannelFive() + row.getDifZoneTwoChannelFive();
                                        sum(groupedData, row, add, field);
                                        break;
                                }
                                break;
                            case "3":
                                switch (channel) {
                                    case "1":
                                        add = row.getOffZoneThreeChannelOne();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "2":
                                        add = row.getOffZoneThreeChannelTwo();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "3":
                                        add = row.getOffZoneThreeChannelThree();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "4":
                                        add = row.getOffZoneThreeChannelFour();
                                        sum(groupedData, row, add, field);
                                        break;
                                    case "5":
                                        add = row.getOffZoneThreeChannelFive();
                                        sum(groupedData, row, add, field);
                                        break;
                                }
                                break;
                        }
                    } else {
                        String zone = params.get("zone").toString();
                        Double add;
                        switch (zone) {
                            case "1":
                                add = row.getZoneOne();
                                sum(groupedData, row, add, field);
                                break;
                            case "2":
                                add = row.getZoneTwo();
                                sum(groupedData, row, add, field);
                                break;
                            case "3":
                                add = row.getZoneThree();
                                sum(groupedData, row, add, field);
                                break;
                        }
                    }
                } else if (params.containsKey("channel")) {
                    String channel = params.get("channel").toString();
                    Double add;
                    switch (channel) {
                        case "1":
                            add = row.getChannelOne();
                            sum(groupedData, row, add, field);
                            break;
                        case "2":
                            add = row.getChannelTwo();
                            sum(groupedData, row, add, field);
                            break;
                        case "3":
                            add = row.getChannelThree();
                            sum(groupedData, row, add, field);
                            break;
                        case "4":
                            add = row.getChannelFour();
                            sum(groupedData, row, add, field);
                            break;
                        case "5":
                            add = row.getChannelFive();
                            sum(groupedData, row, add, field);
                            break;
                    }
                } else if (params.containsKey("area")) {
                    String area = params.get("area").toString();
                    Double add;
                    switch (area) {
                        case "1":
                            add = row.getDifArea();
                            sum(groupedData, row, add, field);
                            break;
                        case "2":
                            add = row.getDifSmallArea();
                            sum(groupedData, row, add, field);
                            break;
                        case "3":
                            add = row.getOffArea();
                            sum(groupedData, row, add, field);
                            break;
                        case "4":
                            add = row.getOffSmallArea();
                            sum(groupedData, row, add, field);
                            break;
                    }
                } else if (params.containsKey("eventTypeId") && Long.compare(Long.parseLong(params.get("eventTypeId").toString()), 999L) == 0) {
                    // gestione metrica "Punti"
                    if (BooleanUtils.isTrue(row.getIsHomeTeam())) {
                        sum(groupedData, row, row.getHomePoints().doubleValue(), field);
                    } else {
                        sum(groupedData, row, row.getAwayPoints().doubleValue(), field);
                    }
                } else {
                    sum(groupedData, row, row.getTotal(), field);
                }
            } else {
                // INSERISCO VALORE DI DEFAULT
                if (params.containsKey("half")) {
                    if (params.containsKey("zone")) {
                        if (params.containsKey("channel")) {
                            String half = params.get("half").toString();
                            String zone = params.get("zone").toString();
                            String channel = params.get("channel").toString();
                            Double total;
                            switch (half) {
                                case "1":
                                    switch (zone) {
                                        case "1":
                                            switch (channel) {
                                                case "1":
                                                    total = row.getOffZoneOneChannelOne();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "2":
                                                    total = row.getOffZoneOneChannelTwo();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "3":
                                                    total = row.getOffZoneOneChannelThree();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "4":
                                                    total = row.getOffZoneOneChannelFour();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "5":
                                                    total = row.getOffZoneOneChannelFive();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                            }
                                            break;
                                        case "2":
                                            switch (channel) {
                                                case "1":
                                                    total = row.getOffZoneTwoChannelOne();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "2":
                                                    total = row.getOffZoneTwoChannelTwo();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "3":
                                                    total = row.getOffZoneTwoChannelThree();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "4":
                                                    total = row.getOffZoneTwoChannelFour();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "5":
                                                    total = row.getOffZoneTwoChannelFive();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                            }
                                            break;
                                        case "3":
                                            switch (channel) {
                                                case "1":
                                                    total = row.getOffZoneThreeChannelOne();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "2":
                                                    total = row.getOffZoneThreeChannelTwo();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "3":
                                                    total = row.getOffZoneThreeChannelThree();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "4":
                                                    total = row.getOffZoneThreeChannelFour();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "5":
                                                    total = row.getOffZoneThreeChannelFive();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                            }
                                            break;
                                    }
                                    break;
                                case "2":
                                    switch (zone) {
                                        case "1":
                                            switch (channel) {
                                                case "1":
                                                    total = row.getDifZoneOneChannelOne();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "2":
                                                    total = row.getDifZoneOneChannelTwo();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "3":
                                                    total = row.getDifZoneOneChannelThree();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "4":
                                                    total = row.getDifZoneOneChannelFour();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "5":
                                                    total = row.getDifZoneOneChannelFive();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                            }
                                            break;
                                        case "2":
                                            switch (channel) {
                                                case "1":
                                                    total = row.getDifZoneTwoChannelOne();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "2":
                                                    total = row.getDifZoneTwoChannelTwo();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "3":
                                                    total = row.getDifZoneTwoChannelThree();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "4":
                                                    total = row.getDifZoneTwoChannelFour();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "5":
                                                    total = row.getDifZoneTwoChannelFive();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                            }
                                            break;
                                        case "3":
                                            switch (channel) {
                                                case "1":
                                                    total = row.getDifZoneThreeChannelOne();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "2":
                                                    total = row.getDifZoneThreeChannelTwo();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "3":
                                                    total = row.getDifZoneThreeChannelThree();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "4":
                                                    total = row.getDifZoneThreeChannelFour();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                                case "5":
                                                    total = row.getDifZoneThreeChannelFive();
                                                    initialize(groupedData, row, field);
                                                    row.setTotal(total);
                                                    break;
                                            }
                                            break;
                                    }
                            }
                        } else {
                            String half = params.get("half").toString();
                            String zone = params.get("zone").toString();
                            Double total;
                            switch (half) {
                                case "1":
                                    switch (zone) {
                                        case "1":
                                            total = row.getOffZoneOneChannelOne() + row.getOffZoneOneChannelTwo() + row.getOffZoneOneChannelThree() + row.getOffZoneOneChannelFour() + row.getOffZoneOneChannelFive();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "2":
                                            total = row.getOffZoneTwoChannelOne() + row.getOffZoneTwoChannelTwo() + row.getOffZoneTwoChannelThree() + row.getOffZoneTwoChannelFour() + row.getOffZoneTwoChannelFive();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "3":
                                            total = row.getOffZoneThreeChannelOne() + row.getOffZoneThreeChannelTwo() + row.getOffZoneThreeChannelThree() + row.getOffZoneThreeChannelFour() + row.getOffZoneThreeChannelFive();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                    }
                                    break;
                                case "2":
                                    switch (zone) {
                                        case "1":
                                            total = row.getDifZoneOneChannelOne() + row.getDifZoneOneChannelTwo() + row.getDifZoneOneChannelThree() + row.getDifZoneOneChannelFour() + row.getDifZoneOneChannelFive();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "2":
                                            total = row.getDifZoneTwoChannelOne() + row.getDifZoneTwoChannelTwo() + row.getDifZoneTwoChannelThree() + row.getDifZoneTwoChannelFour() + row.getDifZoneTwoChannelFive();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "3":
                                            total = row.getDifZoneThreeChannelOne() + row.getDifZoneThreeChannelTwo() + row.getDifZoneThreeChannelThree() + row.getDifZoneThreeChannelFour() + row.getDifZoneThreeChannelFive();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                    }
                                    break;
                            }
                        }
                    } else {
                        if (params.containsKey("channel")) {
                            String half = params.get("half").toString();
                            String channel = params.get("channel").toString();
                            Double total;
                            switch (half) {
                                case "1":
                                    switch (channel) {
                                        case "1":
                                            total = row.getOffZoneTwoChannelOne() + row.getOffZoneThreeChannelOne();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "2":
                                            total = row.getOffZoneTwoChannelTwo() + row.getOffZoneThreeChannelTwo();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "3":
                                            total = row.getOffZoneTwoChannelThree() + row.getOffZoneThreeChannelThree();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "4":
                                            total = row.getOffZoneTwoChannelFour() + row.getOffZoneThreeChannelFour();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "5":
                                            total = row.getOffZoneTwoChannelFive() + row.getOffZoneThreeChannelFive();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                    }
                                    break;
                                case "2":
                                    switch (channel) {
                                        case "1":
                                            total = row.getDifZoneOneChannelOne() + row.getDifZoneTwoChannelOne();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "2":
                                            total = row.getDifZoneOneChannelTwo() + row.getDifZoneTwoChannelTwo();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "3":
                                            total = row.getDifZoneOneChannelThree() + row.getDifZoneTwoChannelThree();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "4":
                                            total = row.getDifZoneOneChannelFour() + row.getDifZoneTwoChannelFour();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                        case "5":
                                            total = row.getDifZoneOneChannelFive() + row.getDifZoneTwoChannelFive();
                                            initialize(groupedData, row, field);
                                            row.setTotal(total);
                                            break;
                                    }
                                    break;
                            }
                        } else {
                            String half = params.get("half").toString();
                            Double total;
                            switch (half) {
                                case "1":
                                    total = row.getOff();
                                    initialize(groupedData, row, field);
                                    row.setTotal(total);
                                    break;
                                case "2":
                                    total = row.getDif();
                                    initialize(groupedData, row, field);
                                    row.setTotal(total);
                                    break;
                            }
                        }
                    }
                } else if (params.containsKey("zone")) {
                    if (params.containsKey("channel")) {
                        String zone = params.get("zone").toString();
                        String channel = params.get("channel").toString();
                        Double total;
                        switch (zone) {
                            case "1":
                                switch (channel) {
                                    case "1":
                                        total = row.getDifZoneOneChannelOne();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "2":
                                        total = row.getDifZoneOneChannelTwo();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "3":
                                        total = row.getDifZoneOneChannelThree();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "4":
                                        total = row.getDifZoneOneChannelFour();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "5":
                                        total = row.getDifZoneOneChannelFive();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                }
                                break;
                            case "2":
                                switch (channel) {
                                    case "1":
                                        total = row.getOffZoneTwoChannelOne() + row.getDifZoneTwoChannelOne();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "2":
                                        total = row.getOffZoneTwoChannelTwo() + row.getDifZoneTwoChannelTwo();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "3":
                                        total = row.getOffZoneTwoChannelThree() + row.getDifZoneTwoChannelThree();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "4":
                                        total = row.getOffZoneTwoChannelFour() + row.getDifZoneTwoChannelFour();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "5":
                                        total = row.getOffZoneTwoChannelFive() + row.getDifZoneTwoChannelFive();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                }
                                break;
                            case "3":
                                switch (channel) {
                                    case "1":
                                        total = row.getOffZoneThreeChannelOne();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "2":
                                        total = row.getOffZoneThreeChannelTwo();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "3":
                                        total = row.getOffZoneThreeChannelThree();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "4":
                                        total = row.getOffZoneThreeChannelFour();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                    case "5":
                                        total = row.getOffZoneThreeChannelFive();
                                        initialize(groupedData, row, field);
                                        row.setTotal(total);
                                        break;
                                }
                                break;
                        }
                    } else {
                        String zone = params.get("zone").toString();
                        Double total;
                        switch (zone) {
                            case "1":
                                total = row.getZoneOne();
                                initialize(groupedData, row, field);
                                row.setTotal(total);
                                break;
                            case "2":
                                total = row.getZoneTwo();
                                initialize(groupedData, row, field);
                                row.setTotal(total);
                                break;
                            case "3":
                                total = row.getZoneThree();
                                initialize(groupedData, row, field);
                                row.setTotal(total);
                                break;
                        }
                    }
                } else if (params.containsKey("channel")) {
                    String channel = params.get("channel").toString();
                    Double total;
                    switch (channel) {
                        case "1":
                            total = row.getChannelOne();
                            initialize(groupedData, row, field);
                            row.setTotal(total);
                            break;
                        case "2":
                            total = row.getChannelTwo();
                            initialize(groupedData, row, field);
                            row.setTotal(total);
                            break;
                        case "3":
                            total = row.getChannelThree();
                            initialize(groupedData, row, field);
                            row.setTotal(total);
                            break;
                        case "4":
                            total = row.getChannelFour();
                            initialize(groupedData, row, field);
                            row.setTotal(total);
                            break;
                        case "5":
                            total = row.getChannelFive();
                            initialize(groupedData, row, field);
                            row.setTotal(total);
                            break;
                    }
                } else if (params.containsKey("area")) {
                    String area = params.get("area").toString();
                    Double total;
                    switch (area) {
                        case "1":
                            total = row.getDifArea();
                            initialize(groupedData, row, field);
                            row.setTotal(total);
                            break;
                        case "2":
                            total = row.getDifSmallArea();
                            initialize(groupedData, row, field);
                            row.setTotal(total);
                            break;
                        case "3":
                            total = row.getOffArea();
                            initialize(groupedData, row, field);
                            row.setTotal(total);
                            break;
                        case "4":
                            total = row.getOffSmallArea();
                            initialize(groupedData, row, field);
                            row.setTotal(total);
                            break;
                    }
                } else if (params.containsKey("eventTypeId") && Long.compare(Long.parseLong(params.get("eventTypeId").toString()), 999L) == 0) {
                    // gestione metrica "Punti"
                    initialize(groupedData, row, field);
                    if (BooleanUtils.isTrue(row.getIsHomeTeam())) {
                        row.setTotal(row.getHomePoints().doubleValue());
                    } else {
                        row.setTotal(row.getAwayPoints().doubleValue());
                    }
                } else {
                    initialize(groupedData, row, field);
                }
            }
        }

        // arrotondo valori
        for (DocumentRow row : groupedData.values()) {
            if (row.getTotal() != null) {
                row.setTotal(Math.round(row.getTotal() * 100) / 100D);
            }
            if (row.getTotalP90() != null) {
                row.setTotalP90(Math.round(row.getTotalP90() * 100) / 100D);
            }
            if (row.getTotalAverage() != null) {
                row.setTotalAverage(Math.round(row.getTotalAverage() * 100) / 100D);
            }
        }

        return groupedData;
    }

    public static List<String> getFieldsToSum(Map<String, Object> params) {
        List<String> fields = new ArrayList<>();

        // INSERISCO VALORE DI DEFAULT
        if (params.containsKey("half")) {
            if (params.containsKey("zone")) {
                if (params.containsKey("channel")) {
                    String half = params.get("half").toString();
                    String zone = params.get("zone").toString();
                    String channel = params.get("channel").toString();
                    switch (half) {
                        case "1":
                            switch (zone) {
                                case "1":
                                    switch (channel) {
                                        case "1":
                                            fields.add("offZoneOneChannelOne");
                                            break;
                                        case "2":
                                            fields.add("offZoneOneChannelTwo");
                                            break;
                                        case "3":
                                            fields.add("offZoneOneChannelThree");
                                            break;
                                        case "4":
                                            fields.add("offZoneOneChannelFour");
                                            break;
                                        case "5":
                                            fields.add("offZoneOneChannelFive");
                                            break;
                                    }
                                    break;
                                case "2":
                                    switch (channel) {
                                        case "1":
                                            fields.add("offZoneTwoChannelOne");
                                            break;
                                        case "2":
                                            fields.add("offZoneTwoChannelTwo");
                                            break;
                                        case "3":
                                            fields.add("offZoneTwoChannelThree");
                                            break;
                                        case "4":
                                            fields.add("offZoneTwoChannelFour");
                                            break;
                                        case "5":
                                            fields.add("offZoneTwoChannelFive");
                                            break;
                                    }
                                    break;
                                case "3":
                                    switch (channel) {
                                        case "1":
                                            fields.add("offZoneThreeChannelOne");
                                            break;
                                        case "2":
                                            fields.add("offZoneThreeChannelTwo");
                                            break;
                                        case "3":
                                            fields.add("offZoneThreeChannelThree");
                                            break;
                                        case "4":
                                            fields.add("offZoneThreeChannelFour");
                                            break;
                                        case "5":
                                            fields.add("offZoneThreeChannelFive");
                                            break;
                                    }
                                    break;
                            }
                            break;
                        case "2":
                            switch (zone) {
                                case "1":
                                    switch (channel) {
                                        case "1":
                                            fields.add("difZoneOneChannelOne");
                                            break;
                                        case "2":
                                            fields.add("difZoneOneChannelTwo");
                                            break;
                                        case "3":
                                            fields.add("difZoneOneChannelThree");
                                            break;
                                        case "4":
                                            fields.add("difZoneOneChannelFour");
                                            break;
                                        case "5":
                                            fields.add("difZoneOneChannelFive");
                                            break;
                                    }
                                    break;
                                case "2":
                                    switch (channel) {
                                        case "1":
                                            fields.add("difZoneTwoChannelOne");
                                            break;
                                        case "2":
                                            fields.add("difZoneTwoChannelTwo");
                                            break;
                                        case "3":
                                            fields.add("difZoneTwoChannelThree");
                                            break;
                                        case "4":
                                            fields.add("difZoneTwoChannelFour");
                                            break;
                                        case "5":
                                            fields.add("difZoneTwoChannelFive");
                                            break;
                                    }
                                    break;
                                case "3":
                                    switch (channel) {
                                        case "1":
                                            fields.add("difZoneThreeChannelOne");
                                            break;
                                        case "2":
                                            fields.add("difZoneThreeChannelTwo");
                                            break;
                                        case "3":
                                            fields.add("difZoneThreeChannelThree");
                                            break;
                                        case "4":
                                            fields.add("difZoneThreeChannelFour");
                                            break;
                                        case "5":
                                            fields.add("difZoneThreeChannelFive");
                                            break;
                                    }
                                    break;
                            }
                    }
                } else {
                    String half = params.get("half").toString();
                    String zone = params.get("zone").toString();
                    switch (half) {
                        case "1":
                            switch (zone) {
                                case "1":
                                    fields.addAll(Arrays.asList("offZoneOneChannelOne", "offZoneOneChannelTwo", "offZoneOneChannelThree", "offZoneOneChannelFour", "offZoneOneChannelFive"));
                                    break;
                                case "2":
                                    fields.addAll(Arrays.asList("offZoneTwoChannelOne", "offZoneTwoChannelTwo", "offZoneTwoChannelThree", "offZoneTwoChannelFour", "offZoneTwoChannelFive"));
                                    break;
                                case "3":
                                    fields.addAll(Arrays.asList("offZoneThreeChannelOne", "offZoneThreeChannelTwo", "offZoneThreeChannelThree", "offZoneThreeChannelFour", "offZoneThreeChannelFive"));
                                    break;
                            }
                            break;
                        case "2":
                            switch (zone) {
                                case "1":
                                    fields.addAll(Arrays.asList("difZoneOneChannelOne", "difZoneOneChannelTwo", "difZoneOneChannelThree", "difZoneOneChannelFour", "difZoneOneChannelFive"));
                                    break;
                                case "2":
                                    fields.addAll(Arrays.asList("difZoneTwoChannelOne", "difZoneTwoChannelTwo", "difZoneTwoChannelThree", "difZoneTwoChannelFour", "difZoneTwoChannelFive"));
                                    break;
                                case "3":
                                    fields.addAll(Arrays.asList("difZoneThreeChannelOne", "difZoneThreeChannelTwo", "difZoneThreeChannelThree", "difZoneThreeChannelFour", "difZoneThreeChannelFive"));
                                    break;
                            }
                            break;
                    }
                }
            } else {
                if (params.containsKey("channel")) {
                    String half = params.get("half").toString();
                    String channel = params.get("channel").toString();
                    switch (half) {
                        case "1":
                            switch (channel) {
                                case "1":
                                    fields.addAll(Arrays.asList("offZoneTwoChannelOne", "offZoneThreeChannelOne"));
                                    break;
                                case "2":
                                    fields.addAll(Arrays.asList("offZoneTwoChannelTwo", "offZoneThreeChannelTwo"));
                                    break;
                                case "3":
                                    fields.addAll(Arrays.asList("offZoneTwoChannelThree", "offZoneThreeChannelThree"));
                                    break;
                                case "4":
                                    fields.addAll(Arrays.asList("offZoneTwoChannelFour", "offZoneTwoChannelFive"));
                                    break;
                                case "5":
                                    fields.addAll(Arrays.asList("offZoneThreeChannelFour", "offZoneThreeChannelFive"));
                                    break;
                            }
                            break;
                        case "2":
                            switch (channel) {
                                case "1":
                                    fields.addAll(Arrays.asList("difZoneTwoChannelOne", "difZoneThreeChannelOne"));
                                    break;
                                case "2":
                                    fields.addAll(Arrays.asList("difZoneTwoChannelTwo", "difZoneThreeChannelTwo"));
                                    break;
                                case "3":
                                    fields.addAll(Arrays.asList("difZoneTwoChannelThree", "difZoneThreeChannelThree"));
                                    break;
                                case "4":
                                    fields.addAll(Arrays.asList("difZoneTwoChannelFour", "difZoneTwoChannelFive"));
                                    break;
                                case "5":
                                    fields.addAll(Arrays.asList("difZoneThreeChannelFour", "difZoneThreeChannelFive"));
                                    break;
                            }
                            break;
                    }
                } else {
                    String half = params.get("half").toString();
                    switch (half) {
                        case "1":
                            fields.add("off");
                            break;
                        case "2":
                            fields.add("dif");
                            break;
                    }
                }
            }
        } else if (params.containsKey("zone")) {
            if (params.containsKey("channel")) {
                String zone = params.get("zone").toString();
                String channel = params.get("channel").toString();
                switch (zone) {
                    case "1":
                        switch (channel) {
                            case "1":
                                fields.add("difZoneOneChannelOne");
                                break;
                            case "2":
                                fields.add("difZoneOneChannelTwo");
                                break;
                            case "3":
                                fields.add("difZoneOneChannelThree");
                                break;
                            case "4":
                                fields.add("difZoneOneChannelFour");
                                break;
                            case "5":
                                fields.add("difZoneOneChannelFive");
                                break;
                        }
                        break;
                    case "2":
                        switch (channel) {
                            case "1":
                                fields.addAll(Arrays.asList("offZoneTwoChannelOne", "difZoneTwoChannelOne"));
                                break;
                            case "2":
                                fields.addAll(Arrays.asList("offZoneTwoChannelTwo", "difZoneTwoChannelTwo"));
                                break;
                            case "3":
                                fields.addAll(Arrays.asList("offZoneTwoChannelThree", "difZoneTwoChannelThree"));
                                break;
                            case "4":
                                fields.addAll(Arrays.asList("offZoneTwoChannelFour", "difZoneTwoChannelFour"));
                                break;
                            case "5":
                                fields.addAll(Arrays.asList("offZoneTwoChannelFive", "difZoneTwoChannelFive"));
                                break;
                        }
                        break;
                    case "3":
                        switch (channel) {
                            case "1":
                                fields.add("offZoneThreeChannelOne");
                                break;
                            case "2":
                                fields.add("offZoneThreeChannelTwo");
                                break;
                            case "3":
                                fields.add("offZoneThreeChannelThree");
                                break;
                            case "4":
                                fields.add("offZoneThreeChannelFour");
                                break;
                            case "5":
                                fields.add("offZoneThreeChannelFive");
                                break;
                        }
                        break;
                }
            } else {
                String zone = params.get("zone").toString();
                switch (zone) {
                    case "1":
                        fields.add("zoneOne");
                        break;
                    case "2":
                        fields.add("zoneTwo");
                        break;
                    case "3":
                        fields.add("zoneThree");
                        break;
                }
            }
        } else if (params.containsKey("channel")) {
            String channel = params.get("channel").toString();
            switch (channel) {
                case "1":
                    fields.add("channelOne");
                    break;
                case "2":
                    fields.add("channelTwo");
                    break;
                case "3":
                    fields.add("channelThree");
                    break;
                case "4":
                    fields.add("channelFour");
                    break;
                case "5":
                    fields.add("channelFive");
                    break;
            }
        } else if (params.containsKey("area")) {
            String area = params.get("area").toString();
            switch (area) {
                case "1":
                    fields.add("difArea");
                    break;
                case "2":
                    fields.add("difSmallArea");
                    break;
                case "3":
                    fields.add("offArea");
                    break;
                case "4":
                    fields.add("offSmallArea");
                    break;
            }
        } else {
            fields.add("total");
        }

        return fields;
    }

    private static void sum(Map<Long, DocumentRow> groupedData, DocumentRow row, Double value, GroupedField field) {
        switch (field) {
            case TEAM:
                groupedData.get(row.getTeamId()).setTotal(groupedData.get(row.getTeamId()).getTotal() + value);
                break;
            case PLAYER:
                groupedData.get(row.getPlayerId()).setTotal(groupedData.get(row.getPlayerId()).getTotal() + value);
                break;
            case FIXTURE:
                groupedData.get(row.getFixtureId()).setTotal(groupedData.get(row.getFixtureId()).getTotal() + value);
                break;
            case EVENT_TYPE_ID:
                groupedData.get(row.getEventTypeId()).setTotal(groupedData.get(row.getEventTypeId()).getTotal() + value);
                break;
        }
    }

    private static void initialize(Map<Long, DocumentRow> groupedData, DocumentRow row, GroupedField field) {
        try {
            switch (field) {
                case TEAM:
                    groupedData.putIfAbsent(row.getTeamId(), row.clone());
                    break;
                case PLAYER:
                    groupedData.putIfAbsent(row.getPlayerId(), row.clone());
                    break;
                case FIXTURE:
                    groupedData.putIfAbsent(row.getFixtureId(), row.clone());
                    break;
                case EVENT_TYPE_ID:
                    groupedData.putIfAbsent(row.getEventTypeId(), row.clone());
                    break;
            }
        } catch (CloneNotSupportedException ex) {
            GlobalHelper.reportError(ex);
        }
    }
}
