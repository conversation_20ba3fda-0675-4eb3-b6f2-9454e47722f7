package sics.helper;

import java.util.*;
import javax.mail.*;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.xmlrpc.XmlRpcException;

public class MailHelper {

    private String from;
    private String to;
    private String subject;
    private String text;
    private String cc;
    private String ccn;
    private Session session = null;
    private ArrayList mAttach;

    protected transient final Log log = LogFactory.getLog(getClass());

    public MailHelper(String smtp, String port, String authuser, String authpwd) {

        try {
            Properties props = System.getProperties();
            props.setProperty("mail.transport.protocol", "smtp");
            props.put("mail.smtp.host", smtp);
            props.put("mail.smtp.localhost", smtp);
            props.put("mail.smtp.port", port);

            if (authuser != null && authuser.length() > 0) {
                final String username = authuser;
                final String password = authpwd;
                props.put("mail.smtp.auth", "true");
                session = Session.getInstance(props, new javax.mail.Authenticator() {
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(username, password);
                    }
                });
            }
            System.setProperties(props);
        } catch (Exception mex) {
            log.error("Exception in MailSender.java", mex);
        }
    }

    public void sendMail(String from, String to, String cc, String ccn, String header, String title, String subject, String text, ArrayList attach, String psw) {

        try {
            this.from = from;
            this.to = to + "|" + ccn;
            this.cc = cc;
            this.ccn = ccn;
            this.subject = subject;
            this.text = SpringApplicationContextHelper.getMessage("email.header", (header != null) ? header : "")
                    + SpringApplicationContextHelper.getMessage("email.title", (title != null) ? title : "")
                    + SpringApplicationContextHelper.getMessage("email.content", (text != null) ? text : "")
                    + SpringApplicationContextHelper.getMessage("email.footer");

            this.mAttach = attach;

            if (to != null && to.length() > 0) {
//                send();
                sendMailRPC(false, this.from, this.to, this.subject, this.text, psw);
            }

        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static Boolean sendMailRPC(Boolean copiaNascosta, String da, String to_or_ccn, String subject, String body, String psw) {
        boolean ok = false;
        try {
            Object result;

            Vector<String> params = new Vector<>();
            params.addElement(da);
            params.addElement(to_or_ccn);
            params.addElement(subject);
            params.addElement(body);
            params.addElement(psw);
            if (copiaNascosta) {
                result = GlobalHelper.serverMail.execute("mail.sendMail", params);
            } else {
                result = GlobalHelper.serverMail.execute("mail.sendMailPSWTo", params);
            }
            String res = ((String) result);
            if (res.equalsIgnoreCase("OK")) {
                ok = true;
            }
        } catch (XmlRpcException e) {
            ok = true;
        } catch (Exception e) {
            GlobalHelper.reportError(e);
            System.err.println("Repository.sendMail");
        }
        return ok;
    }

}
