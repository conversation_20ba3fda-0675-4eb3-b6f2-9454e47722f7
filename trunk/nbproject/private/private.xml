<?xml version="1.0" encoding="UTF-8"?>
<project-private xmlns="http://www.netbeans.org/ns/project-private/1">
    <editor-bookmarks xmlns="http://www.netbeans.org/ns/editor-bookmarks/2" lastBookmarkId="0"/>
    <open-files xmlns="http://www.netbeans.org/ns/projectui-open-files/2">
        <group>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/helper/GlobalHelper.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/controller/HomeController.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/js/filters.js</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/service/UserService.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/helper/MongoHelper.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/domain/Group.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/domain/Country.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/dao/mapper/Fixture.xml</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/META-INF/context.xml</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/css/custom-matchstudio.css</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/js/amcharts.js</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/pages/player/top.jsp</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/mapper-config.xml</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/js/utils.js</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/web.xml</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/controller/AdminController.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/pages/team/scatterplot.jsp</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/controller/BaseController.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/dao/mapper/Group.xml</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/pages/player/ranking.jsp</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/controller/MatchStudioController.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/filter/CustomAuthenticationFailureHandler.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/controller/team/TeamRankingController.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/dao/mapper/User.xml</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/dao/AppDAO.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/dao/AppDaoImpl.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/domain/Foot.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/controller/UserController.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/pages/auth/matchStudio.jsp</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/pages/admin/home.jsp</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/pages/global/main.jsp</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/domain/Player.java</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/css/custom.css</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/dao/mapper/Player.xml</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/pages/player/distribution.jsp</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/src/sics/dao/mapper/PlayerAgency.xml</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/pages/player/scatterplot.jsp</file>
            <file>file:/C:/lavori/git/sicsdataanalytics/trunk/web/WEB-INF/pages/team/ranking.jsp</file>
        </group>
    </open-files>
</project-private>
