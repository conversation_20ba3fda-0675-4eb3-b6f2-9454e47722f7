<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div id="content-container">
    <script type="text/javascript">
        $(document).ready(function () {
        <c:if test="${mEvents != null}">
            // Load the background image
            const canvas = document.getElementById('heatmapCanvas');
            const ctx = canvas.getContext('2d');
            const canvasEnd = document.getElementById('heatmapEndCanvas');
            const ctxEnd = canvasEnd.getContext('2d');

            const fieldImage = new Image();
            fieldImage.src = '/sicsdataanalytics/images/campo_2023_linee-h.png';

            fieldImage.onload = function () {
                ctx.drawImage(fieldImage, 0, 0, canvas.width, canvas.height);
                ctxEnd.drawImage(fieldImage, 0, 0, canvas.width, canvas.height);
                drawHeatmap();
            };

            // Initialize heatmap.js
            const heatmapInstance = h337.create({
                container: document.querySelector('.heatmap'),
                radius: 50,
                maxOpacity: .7,
                scaleRadius: true,
                useLocalExtrema: true,
                gradient: {
                    // enter n keys between 0 and 1 here
                    // for gradient color customization
                    '.2': 'blue',
                    '.55': 'green',
                    '.8': '#d7de0b',
                    '.9': 'orange',
                    '1': 'red'
                }
            });
            // Initialize heatmap.js
            const heatmapEndInstance = h337.create({
                container: document.querySelector('.heatmapEnd'),
                radius: 50,
                maxOpacity: .7,
                scaleRadius: true,
                useLocalExtrema: true,
                gradient: {
                    // enter n keys between 0 and 1 here
                    // for gradient color customization
                    '.2': 'blue',
                    '.55': 'green',
                    '.8': '#d7de0b',
                    '.9': 'orange',
                    '1': 'red'
                }
            });

            // Sample data points for the heatmap
            const points = ${mHeatmapPoints};
            const pointsEnd = ${mHeatmapEndPoints};

            // Function to draw the heatmap
            function drawHeatmap() {
                heatmapInstance.setData({
                    max: 1.5,
                    data: points
                });
                heatmapEndInstance.setData({
                    max: ${mHeatmapEndMax},
                    data: pointsEnd
                });
            }

            // posizionale direzioni
            drawField("direction-container");
            drawField("touch-container", pitchTouch);
            currentContainerId = "diagram-container";
            drawDiagram();
            <c:forEach var="event" items="${mEvents}">
            eventMap.set('${event.id}', eval(${event.getJson()}));
            drawEvent(eventMap.get('${event.id}'));
            drawEvent(eventMap.get('${event.id}'), true, pitchTouch);
            </c:forEach>

            zoneEventAmount = new Map();
            <c:forEach var="angleZone" items="${mGroupedAngleZone.keySet()}">
            zoneEventAmount.set('${angleZone}', ${mGroupedAngleZone.get(angleZone).events.size()});
            </c:forEach>
            updateZoneCounters();
            let angleZone = $("#filter-anglezone").val();
            if (notEmpty(angleZone)) {
                var element = d3.select("[zone='" + angleZone + "']");
                var baseColor = element.attr("base-color");
                if (element.attr("fill") === baseColor) {
                    element.attr("fill", element.attr("click-color"));
                } else {
                    element.attr("fill", baseColor);
                }
            }

            // bind click
            $(".is-positional-filter").on("click", function () {
                let third = $(this).attr("third");
                let channel = $(this).attr("channel");
                let verticalChannel = $(this).attr("vertical-channel");
                let time = $(this).attr("time");
                let playerId = $(this).attr("playerid");

                stopChartLoading = true;
                if (notEmpty(third)) {
                    if ($("#filter-zone").val() !== third) {
                        $("#filter-zone").val(third).change();
                    } else {
                        $("#filter-zone").val(null).change();
                    }
                }
                if (notEmpty(channel)) {
                    if ($("#filter-channel").val() !== channel) {
                        $("#filter-channel").val(channel).change();
                    } else {
                        $("#filter-channel").val(null).change();
                    }
                }
                if (notEmpty(verticalChannel)) {
                    if ($("#filter-vertical-channel").val() !== verticalChannel) {
                        $("#filter-vertical-channel").val(verticalChannel).change();
                    } else {
                        $("#filter-vertical-channel").val(null).change();
                    }
                }
                if (notEmpty(time)) {
                    if ($("#filter-time-interval").val() !== time) {
                        $("#filter-time-interval").val(time).change();
                    } else {
                        $("#filter-time-interval").val(null).change();
                    }
                }
                if (notEmpty(playerId)) {
                    if ($("#filter-playerid option[value='" + playerId + "']").is(":selected")) {
                        $("#filter-playerid").multiselect('deselect', playerId).change();
                    } else {
                        $("#filter-playerid").multiselect('select', playerId).change();
                    }
                }
                stopChartLoading = false;
                updateChart();
            });

            <c:if test="${!mPlayerFiltered}">
            $("#filter-playerid option").remove();
                <c:forEach var="playerId" items="${mGroupedPlayer.keySet()}">
            $("#filter-playerid").append($("<option>", {
                value: ${playerId},
                text: "${mPlayers.get(playerId).knownName}"
            }));
                </c:forEach>
            $("#filter-playerid").multiselect('rebuild');
            animate("filter-playerid");
            </c:if>
        </c:if>
        <c:if test="${mEvents == null}">
            $("#extra-content").addClass("d-none");
            $("#chartdiv-two-content").addClass("d-none");
            showChartMessage(2);
        </c:if>
        });

        function checkDataVisualization() {
            if ($("#heatmap-start-point").is(":checked")) {
                $("#heatmapStart").removeClass("d-none");
                $("#heatmapEnd").addClass("d-none");
                $("#touch-map").addClass("d-none");
                $("#direction-map").addClass("d-none");
                $("#start-point-amounts").removeClass("d-none");
                $("#end-point-amounts").addClass("d-none");
            } else if ($("#heatmap-end-point").is(":checked")) {
                $("#heatmapStart").addClass("d-none");
                $("#heatmapEnd").removeClass("d-none");
                $("#touch-map").addClass("d-none");
                $("#direction-map").addClass("d-none");
                $("#start-point-amounts").addClass("d-none");
                $("#end-point-amounts").removeClass("d-none");
            } else if ($("#touch-point").is(":checked")) {
                $("#heatmapStart").addClass("d-none");
                $("#heatmapEnd").addClass("d-none");
                $("#touch-map").removeClass("d-none");
                $("#direction-map").addClass("d-none");
                $("#start-point-amounts").removeClass("d-none");
                $("#end-point-amounts").addClass("d-none");
            } else if ($("#direction-point").is(":checked")) {
                $("#heatmapStart").addClass("d-none");
                $("#heatmapEnd").addClass("d-none");
                $("#touch-map").addClass("d-none");
                $("#direction-map").removeClass("d-none");
                $("#start-point-amounts").removeClass("d-none");
                $("#end-point-amounts").addClass("d-none");
            }
        }
    </script>

    <div class="row justify-content-around">
        <div class="p-0" style="width: 700px;">
            <div id="start-point-amounts">
                <h4 class="text-center mb-1">(${mEvents.size()}) <spring:message code="team.positional.third.vertical.channel"/></h4>
                <div class="card">
                    <div class="card-body p-0">
                        <img src="/sicsdataanalytics/images/campo_2023_linee-h-with-lines.png"/>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('1-1')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 0%; left: 0%" third="1" channel="1">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('1-2')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 0%; left: 33.33%" third="2" channel="1">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('1-3')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 0%; left: 66.66%" third="3" channel="1">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('2-1')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 20%; left: 0%" third="1" channel="2">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('2-2')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 20%; left: 33.33%" third="2" channel="2">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('2-3')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 20%; left: 66.66%" third="3" channel="2">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('3-1')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 40%; left: 0%" third="1" channel="3">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('3-2')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 40%; left: 33.33%" third="2" channel="3">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('3-3')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 40%; left: 66.66%" third="3" channel="3">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('4-1')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 60%; left: 0%" third="1" channel="4">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('4-2')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 60%; left: 33.33%" third="2" channel="4">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('4-3')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 60%; left: 66.66%" third="3" channel="4">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('5-1')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 80%; left: 0%" third="1" channel="5">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('5-2')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 80%; left: 33.33%" third="2" channel="5">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThird.get('5-3')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 80%; left: 66.66%" third="3" channel="5">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                    </div>
                </div>
            </div>
            <div id="end-point-amounts" class="d-none">
                <h4 class="text-center mb-1">(${mEvents.size()}) <spring:message code="team.positional.third.vertical.channel"/></h4>
                <div class="card">
                    <div class="card-body p-0">
                        <img src="/sicsdataanalytics/images/campo_2023_linee-h-with-lines.png"/>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('1-1')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 0%; left: 0%" third="1" channel="1">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('1-2')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 0%; left: 33.33%" third="2" channel="1">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('1-3')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 0%; left: 66.66%" third="3" channel="1">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('2-1')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 20%; left: 0%" third="1" channel="2">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('2-2')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 20%; left: 33.33%" third="2" channel="2">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('2-3')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 20%; left: 66.66%" third="3" channel="2">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('3-1')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 40%; left: 0%" third="1" channel="3">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('3-2')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 40%; left: 33.33%" third="2" channel="3">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('3-3')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 40%; left: 66.66%" third="3" channel="3">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('4-1')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 60%; left: 0%" third="1" channel="4">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('4-2')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 60%; left: 33.33%" third="2" channel="4">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('4-3')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 60%; left: 66.66%" third="3" channel="4">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('5-1')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 80%; left: 0%" third="1" channel="5">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('5-2')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 80%; left: 33.33%" third="2" channel="5">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                        <c:set var="wrapper" value="${mGroupedChannelThirdEnd.get('5-3')}"/>
                        <c:if test="${!wrapper.events.isEmpty()}">
                            <div class="position-absolute positional-block d-flex align-items-center justify-content-center is-positional-filter" style="top: 80%; left: 66.66%" third="3" channel="5">
                                <span class="badge bg-${wrapper.color} p-2 px-4"><div class="mb-1 fs-lg">${wrapper.events.isEmpty() ? '-' : wrapper.events.size()}</div><div class="fs-xs"><c:if test="${wrapper.percentage > 0}">(${wrapper.percentage}%)</c:if></div></span>
                                </div>
                        </c:if>
                    </div>
                </div>
            </div>
        </div>
        <div class="p-0" style="width: 700px;">
            <div>
                <div>
                    <span class="mb-1 h4">HEATMAP</span>

                    <div class="float-end">
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input" name="heatmap-type" id="heatmap-start-point" ${mHeatmapEndMax == 0 ? 'checked' : ''} onchange="checkDataVisualization();">
                            <label class="form-check-label" for="heatmap-start-point"><spring:message code="team.positional.start.point"/></label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input" name="heatmap-type" id="heatmap-end-point" ${mHeatmapEndMax == 0 ? 'disabled' : ''} onchange="checkDataVisualization();">
                            <label class="form-check-label" for="heatmap-end-point"><spring:message code="team.positional.end.point"/></label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input" name="heatmap-type" id="touch-point" onchange="checkDataVisualization();">
                            <label class="form-check-label" for="touch-point"><spring:message code="team.positional.touch"/></label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input" name="heatmap-type" id="direction-point" ${mHeatmapEndMax == 0 ? 'disabled' : 'checked'} onchange="checkDataVisualization();">
                            <label class="form-check-label" for="direction-point"><spring:message code="team.positional.direction"/></label>
                        </div>
                    </div>
                </div>
                <div class="card ${mHeatmapEndMax == 0 ? '' : 'd-none'}" id="heatmapStart">
                    <div class="card-body p-0">
                        <div class="heatmap" style="width: 700px; height: 450px">
                            <canvas id="heatmapCanvas" width="700" height="450"></canvas>
                        </div>
                    </div>
                </div>
                <div class="card d-none" id="heatmapEnd">
                    <div class="card-body p-0">
                        <div class="heatmapEnd" style="width: 700px; height: 450px">
                            <canvas id="heatmapEndCanvas" width="700" height="450"></canvas>
                        </div>
                    </div>
                </div>
                <div class="card d-none" id="touch-map">
                    <div class="card-body p-0">
                        <div class="touch-map" id="touch-container">
                        </div>
                    </div>
                </div>
                <div class="card ${mHeatmapEndMax == 0 ? 'd-none' : ''}" id="direction-map">
                    <div class="card-body p-0">
                        <div class="direction-map" id="direction-container">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-3" id="diagram-container">
            <h4 class="text-center mb-0 d-flex justify-content-center align-items-center">
                <div class="me-1">
                    <i class="icon-reset icon-small cursor-pointer" onclick="resetFilter('filter-anglezone');"></i>
                </div>
                <spring:message code="team.positional.angle.zone"/>
            </h4>
        </div>
        <div class="col-lg-3">
            <h4 class="text-center mb-0 d-flex justify-content-center align-items-center">
                <div class="me-1">
                    <i class="icon-reset icon-small cursor-pointer" onclick="resetFilter('filter-zone');"></i>
                </div>
                <spring:message code="team.positional.third"/>
            </h4>
            <div class="card">
                <div class="card-body p-1 overflow-auto" style="max-height: 40vh;">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 25%"></th>
                                <th class="text-center"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:set var="wrapper" value="${mGroupedThird.get('1')}"/>
                            <tr class="is-positional-filter" third="1">
                                <td><spring:message code="team.positional.zone"/> 1</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedThird.get('2')}"/>
                            <tr class="is-positional-filter" third="2">
                                <td><spring:message code="team.positional.zone"/> 2</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedThird.get('3')}"/>
                            <tr class="is-positional-filter" third="3">
                                <td><spring:message code="team.positional.zone"/> 3</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <h4 class="text-center mb-0 d-flex justify-content-center align-items-center">
                <div class="me-1">
                    <i class="icon-reset icon-small cursor-pointer" onclick="resetFilter('filter-channel');"></i>
                </div>
                <spring:message code="team.positional.channel"/>
            </h4>
            <div class="card">
                <div class="card-body p-1 overflow-auto" style="max-height: 40vh;">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 30%"></th>
                                <th class="text-center"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:set var="wrapper" value="${mGroupedChannel.get('1')}"/>
                            <tr class="is-positional-filter" channel="1">
                                <td><spring:message code="filters.vertical.channel.option.1"/></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedChannel.get('2')}"/>
                            <tr class="is-positional-filter" channel="2">
                                <td><spring:message code="filters.vertical.channel.option.2"/></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedChannel.get('3')}"/>
                            <tr class="is-positional-filter" channel="3">
                                <td><spring:message code="filters.vertical.channel.option.3"/></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedChannel.get('4')}"/>
                            <tr class="is-positional-filter" channel="4">
                                <td><spring:message code="filters.vertical.channel.option.4"/></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedChannel.get('5')}"/>
                            <tr class="is-positional-filter" channel="5">
                                <td><spring:message code="filters.vertical.channel.option.5"/></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <h4 class="text-center mb-0 d-flex justify-content-center align-items-center">
                <div class="me-1">
                    <i class="icon-reset icon-small cursor-pointer" onclick="resetFilter('filter-time-interval');"></i>
                </div>
                <spring:message code="team.positional.time.interval"/>
            </h4>
            <div class="card">
                <div class="card-body p-1 overflow-auto" style="max-height: 40vh;">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 30%"></th>
                                <th class="text-center"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:set var="wrapper" value="${mGroupedTime.get('1')}"/>
                            <tr class="is-positional-filter" time="1">
                                <td>1'-15'</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedTime.get('2')}"/>
                            <tr class="is-positional-filter" time="2">
                                <td>16'-30'</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedTime.get('3')}"/>
                            <tr class="is-positional-filter" time="3">
                                <td>31'-45'</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedTime.get('4')}"/>
                            <tr class="is-positional-filter" time="4">
                                <td>46'-60'</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedTime.get('5')}"/>
                            <tr class="is-positional-filter" time="5">
                                <td>61'-75'</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:set var="wrapper" value="${mGroupedTime.get('6')}"/>
                            <tr class="is-positional-filter" time="6">
                                <td>76'-90'</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                    </div>
                                </td>
                            </tr>
                            <c:if test="${!mGroupedTime.get('7').events.isEmpty()}">
                                <c:set var="wrapper" value="${mGroupedTime.get('7')}"/>
                                <tr class="is-positional-filter" time="7">
                                    <td>91'-105'</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                        </div>
                                    </td>
                                </tr>
                            </c:if>
                            <c:if test="${!mGroupedTime.get('8').events.isEmpty()}">
                                <c:set var="wrapper" value="${mGroupedTime.get('8')}"/>
                                <tr class="is-positional-filter" time="8">
                                    <td>106'-120'</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${v}">${wrapper.events.size()}</div>
                                        </div>
                                    </td>
                                </tr>
                            </c:if>
                            <c:if test="${!mGroupedTime.get('9').events.isEmpty()}">
                                <c:set var="wrapper" value="${mGroupedTime.get('9')}"/>
                                <tr class="is-positional-filter" time="9">
                                    <td><spring:message code="filters.time.option.9"/></td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}">${wrapper.events.size()}</div>
                                        </div>
                                    </td>
                                </tr>
                            </c:if>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <h4 class="text-center mb-0 d-flex justify-content-center align-items-center">
                <div class="me-1">
                    <i class="icon-reset icon-small cursor-pointer" onclick="resetFilter('filter-playerid');"></i>
                </div>
                <spring:message code="team.positional.players"/>
            </h4>
            <div class="card">
                <div class="card-body p-1 overflow-auto" style="max-height: 55vh;">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 50%"></th>
                                <th style="width: 10%"></th>
                                <th style="width: 40%"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="playerId" items="${mGroupedPlayer.keySet()}">
                                <c:set var="wrapper" value="${mGroupedPlayer.get(playerId)}"/>
                                <tr class="is-positional-filter" playerid="${mPlayers.get(playerId).id}">
                                    <td class="py-1">${mPlayers.get(playerId).knownName}</td>
                                    <td class="py-1">${wrapper.events.size()}</td>
                                    <td class="py-1">
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped bg-${wrapper.color.replace('dark-', '')} rounded-end fw-bold" style="width: ${wrapper.percentage}%" aria-valuenow="${wrapper.percentage}" aria-valuemin="0" aria-valuemax="${wrapper.maxEventsAmount}"></div>
                                        </div>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>