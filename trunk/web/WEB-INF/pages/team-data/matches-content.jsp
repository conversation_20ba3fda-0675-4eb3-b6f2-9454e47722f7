<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<script type="text/javascript">
    var colorScale = chroma.scale(['#e63235', '#fc9228', '#86d44b', '#039158']).domain([0, ${mTeamMaxValue}]);
    $(document).ready(function () {
        if (typeof table !== "undefined") {
            table.destroy();
        }
        table = $('#matches-table').DataTable({
            dom: 't',
            paging: false,
            stateSave: true,
            order: [0, "desc"],
            columnDefs: [
                {
                    targets: [0],
                    orderable: false
                },
                {
                    type: "date-euro",
                    targets: [1],
                    render: function (data, type, row) {
                        // Se il dato � vuoto, restituisci una data molto grande per i valori "pi� alti"
                        if (data === '') {
                            return type === 'sort' ? '99/99/9999' : data;
                        }
                        return data;
                    }
                }
            ]
        });

        updateProgressColor();
    });

    function updateProgressColor() {
        $(".progress-bar.progress-bar-striped:not(.bg-info)").each(function (index, element) {
            let value = parseFloat($(element).text());
            if (value) {
                $(element).css("background-color", colorScale(value).hex());
            }
        });
    }

    function redirectToOverview(teamId) {
        let index = filtersIndex.indexOf("teamid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);

            location.href = "/sicsdataanalytics/team/overview.htm";
        }
    }

    function downloadMatchReport(fixtureId) {
        if (fixtureId) {
            var data = encodeURI("fixtureId=" + fixtureId);
            $.ajax({
                type: "GET",
                url: "/sicsdataanalytics/user/getMatchReport.htm",
                cache: false,
                data: data,
                success: function (msg) {
                    if (msg === "ko") {
                        new Noty({
                            text: "<spring:message code="error.unexpected"/>",
                            type: "error",
                            layout: "topRight"
                        }).show();
                    } else if (msg === "limitExceeded") {
                        new Noty({
                            text: "<spring:message code="messages.report.download.limit"/>",
                            type: "warning",
                            layout: "topRight"
                        }).show();
                    } else {
                        let parts = msg.split("|");
                        window.open(parts[0], '_blank');
                        new Noty({
                            text: "<spring:message code="messages.report.download.left"/>".replace("X", parts[1]),
                            type: "info",
                            layout: "topRight"
                        }).show();
                    }
                }
            });
        } else {
            new Noty({
                text: "<spring:message code="error.unexpected"/>",
                type: "error",
                layout: "topRight"
            }).show();
        }
    }
</script>
<div class="row">
    <span class="d-none" id="matches-content-empty" isempty="${mRows.isEmpty()}"></span>
    <span class="d-none" id="matches-content-valid" isvalid="${mValid}"></span>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="matches-table">
                    <thead>
                        <tr>
                            <th class="text-center" style="width: 3%"></th>
                            <th class="text-center" style="width: 15%"><spring:message code="team.matches.date"/></th>
                            <th class="text-center" style="width: 30%"><spring:message code="team.matches.match"/></th>
                            <th class="text-center"><spring:message code="team.matches.home"/></th>
                            <th class="text-center"><spring:message code="team.matches.away"/></th>
                            <th class="text-center"><spring:message code="team.matches.total"/></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="fixtureId" items="${mRows.keySet()}">
                            <c:if test="${!mRows.get(fixtureId).isEmpty()}">
                                <c:choose>
                                    <c:when test="${mRows.get(fixtureId).get(0).teamId != null && mFixtures.get(fixtureId).homeTeamId == mRows.get(fixtureId).get(0).teamId}">
                                        <c:set var="homeValue" value="${mRows.get(fixtureId).get(0).total}"/>
                                        <c:set var="awayValue" value="${mRows.get(fixtureId).size() > 1 ? mRows.get(fixtureId).get(1).total : 0}"/>
                                    </c:when>
                                    <c:when test="${mRows.get(fixtureId).get(0).teamId != null && mFixtures.get(fixtureId).awayTeamId == mRows.get(fixtureId).get(0).teamId}">
                                        <c:set var="homeValue" value="${mRows.get(fixtureId).size() > 1 ? mRows.get(fixtureId).get(1).total : 0}"/>
                                        <c:set var="awayValue" value="${mRows.get(fixtureId).get(0).total}"/>
                                    </c:when>
                                    <c:when test="${mRows.get(fixtureId).get(0).teamId == null}">
                                        <c:choose>
                                            <c:when test="${mRows.get(fixtureId).size() > 1 && mRows.get(fixtureId).get(1).teamId != null && mFixtures.get(fixtureId).homeTeamId == mRows.get(fixtureId).get(1).teamId}">
                                                <c:set var="homeValue" value="${mRows.get(fixtureId).size() > 1 ? mRows.get(fixtureId).get(1).total : 0}"/>
                                                <c:set var="awayValue" value="${mRows.get(fixtureId).get(0).total}"/>
                                            </c:when>
                                            <c:when test="${mRows.get(fixtureId).size() > 1 && mRows.get(fixtureId).get(1).teamId != null && mFixtures.get(fixtureId).awayTeamId == mRows.get(fixtureId).get(1).teamId}">
                                                <c:set var="homeValue" value="${mRows.get(fixtureId).get(0).total}"/>
                                                <c:set var="awayValue" value="${mRows.get(fixtureId).size() > 1 ? mRows.get(fixtureId).get(1).total : 0}"/>
                                            </c:when>
                                            <c:otherwise>
                                                <c:set var="homeValue" value="${mRows.get(fixtureId).get(0).total}"/>
                                                <c:set var="awayValue" value="${mRows.get(fixtureId).size() > 1 ? mRows.get(fixtureId).get(1).total : 0}"/>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:when>
                                    <c:otherwise>
                                        <c:set var="homeValue" value="${mRows.get(fixtureId).get(0).total}"/>
                                        <c:set var="awayValue" value="${mRows.get(fixtureId).size() > 1 ? mRows.get(fixtureId).get(1).total : 0}"/>
                                    </c:otherwise>
                                </c:choose>
                                <c:if test="${homeValue > 0 || awayValue > 0}">
                                    <c:set var="total" value="${Math.round((homeValue + awayValue) * 100) / 100 * 1.0}"/>

                                    <c:set var="homePercentage" value="${Math.round(homeValue / mMaxValue * 100)}"/>
                                    <c:set var="awayPercentage" value="${Math.round(awayValue / mMaxValue * 100)}"/>
                                    <c:set var="totalPercentage" value="${Math.round(total / mMaxValue * 100)}"/>
                                    <c:set var="rowColor" value="${mUser.preferredTeamId != null && (mUser.preferredTeamId == mRows.get(fixtureId).get(0).teamId || mUser.preferredTeamId == mRows.get(fixtureId).get(1).teamId) ? 'bg-yellow bg-opacity-20' : ''}"/>
                                    <tr class="${rowColor}">
                                        <td class="text-center p-1">
                                            <a type="button" onclick="downloadMatchReport(${fixtureId});" class="text-body" title="<spring:message code="messages.report.download.title"/>"><i class="ph-download-simple cursor-pointer"></i></a>
                                        </td>
                                        <td class="text-center">
                                            ${mFixtures.get(fixtureId).getGameDateString()}
                                        </td>
                                        <td class="text-center">
                                            <table class="w-100">
                                                <tbody>
                                                    <tr>
                                                        <td class="text-end fs-base border-bottom-0" style="width: 35%">
                                                            ${mTeams.get(mFixtures.get(fixtureId).homeTeamId).getName(mUser.tvLanguage)}
                                                        </td>
                                                        <td class="text-center border-bottom-0" style="width: 10%">
                                                            <img width="24" height="24" class="rounded-pill cursor-pointer" onclick="redirectToOverview(${mFixtures.get(fixtureId).homeTeamId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(mFixtures.get(fixtureId).homeTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'"/>
                                                        </td>
                                                        <td class="text-center border-bottom-0" style="width: 10%">
                                                            <span class="text-muted fs-sm">${mFixtures.get(fixtureId).homeScore}-${mFixtures.get(fixtureId).awayScore}</span>
                                                        </td>
                                                        <td class="text-center border-bottom-0" style="width: 10%">
                                                            <img width="24" height="24" class="rounded-pill cursor-pointer" onclick="redirectToOverview(${mFixtures.get(fixtureId).awayTeamId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(mFixtures.get(fixtureId).awayTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'"/>
                                                        </td>
                                                        <td class="fs-base border-bottom-0" style="width: 35%">
                                                            ${mTeams.get(mFixtures.get(fixtureId).awayTeamId).getName(mUser.tvLanguage)}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                        <td class="text-center">
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-striped rounded-end text-white" style="width: ${homePercentage}%" aria-valuenow="${homePercentage}" aria-valuemin="0" aria-valuemax="100">${homeValue}</div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-striped rounded-end text-white" style="width: ${awayPercentage}%" aria-valuenow="${awayPercentage}" aria-valuemin="0" aria-valuemax="100">${awayValue}</div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-striped bg-info rounded-end text-white" style="width: ${totalPercentage}%" aria-valuenow="${totalPercentage}" aria-valuemin="0" aria-valuemax="100">${total}</div>
                                            </div>
                                        </td>
                                    </tr>
                                </c:if>
                            </c:if>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>