<%@ include file="../global/main.jsp" %>
<script type="text/javascript">
    $(document).ready(function () {
        if (${not empty usr} && ${not empty psw}) {
            $("#sumbitButton").click();
        }
    });
</script>

<body class="bg-dark">
    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content d-flex justify-content-center align-items-center">

                    <!-- Login form -->
                    <form class="login-form" action="/sicsdataanalytics/j_spring_security_check" method="POST">
                        <div class="card mb-0">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="d-inline-flex align-items-center justify-content-center mb-2 mt-2">
                                        <img src="/sicsdataanalytics/images/logo_new.svg" class="h-80px" alt="">
                                    </div>
                                    <h5 class="mb-0"><spring:message code='auth.login'/></h5>
                                    <span class="d-block text-muted"><spring:message code='auth.login.description'/></span>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label"><spring:message code='auth.username'/></label>
                                    <div class="form-control-feedback form-control-feedback-start">
                                        <input id="j_username" name="j_username" type="text" class="form-control" placeholder="<EMAIL>" value="${usr}">
                                        <div class="form-control-feedback-icon">
                                            <i class="ph-user-circle text-muted"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label"><spring:message code='auth.password'/></label>
                                    <div class="form-control-feedback form-control-feedback-start">
                                        <input id="j_password" name="j_password" type="password" class="form-control" placeholder="********" value="${psw}">
                                        <div class="form-control-feedback-icon">
                                            <i class="ph-lock text-muted"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-2">
                                    <button id="sumbitButton" type="submit" class="btn btn-primary w-100"><spring:message code='auth.sign.in'/></button>
                                </div>

                                <div class="mb-3 text-center">
                                    <a href="/sicsdataanalytics/auth/recoverPassword.htm"><spring:message code="auth.forgot.password"/></a>
                                </div>

                                <c:if test="${mUpdated != null && mUpdated}">
                                    <div class="alert alert-info alert-icon-start alert-dismissible fade show mb-0">
                                        <span class="alert-icon bg-info text-white">
                                            <i class="ph-info"></i>
                                        </span>
                                        <spring:message code="auth.site.updated"/>
                                    </div>
                                </c:if>
                                <c:if test="${not empty param.expired
                                              or sessionScope.expiredAccess != null
                                              or sessionScope.permissionError != null
                                              or not empty param.login_error
                                              or (not empty param.session and (mUpdated == null or mUpdated == false))}">
                                      <div class="alert alert-danger alert-icon-start alert-dismissible fade show mb-0">
                                          <span class="alert-icon bg-danger text-white">
                                              <i class="ph-bell-ringing"></i>
                                          </span>
                                          <c:choose>
                                              <c:when test="${not empty param.expired}">
                                                  <spring:message code="auth.session.expired"/>
                                              </c:when>
                                              <c:when test="${sessionScope.expiredAccess != null}">
                                                  <spring:message code="auth.login.expired"/> ${sessionScope.expiredAccess}
                                              </c:when>
                                              <c:when test="${sessionScope.permissionError != null}">
                                                  <spring:message code="auth.login.permission.error"/>
                                              </c:when>
                                              <c:when test="${not empty param.login_error}">
                                                  <spring:message code="auth.login.failed"/>
                                              </c:when>
                                              <c:when test="${not empty param.session}">
                                                  <spring:message code="auth.login.other.device"/>
                                              </c:when>
                                          </c:choose>
                                      </div>
                                </c:if>
                            </div>
                        </div>
                    </form>
                    <!-- /login form -->

                </div>
                <!-- /content area -->

            </div>
            <!-- /inner content -->

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
</body>
