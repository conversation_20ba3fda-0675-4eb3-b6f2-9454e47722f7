<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/demo.js"></script>

	<title>Responsive examples - Initialisation</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Responsive example <span>Initialisation</span></h1>

			<div class="info">
				<p>Responsive can be run on a DataTable in a number of different ways:</p>

				<ul class="markdown">
					<li>By adding the class <code>responsive</code> or <code>dt-responsive</code> to the <code class="tag" title="HTML tag">table</code></li>
					<li>Using the <a href="//datatables.net/extensions/responsive/reference/option/responsive"><code class="option" title=
					"Responsive initialisation option">responsive<span>R</span></code></a> option in the DataTables initialisation</li>
					<li>Use the <code>$.fn.dataTable.Responsive</code> constructor.</li>
				</ul>

				<p>This set of examples demonstrates these initialisation options.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Basic initialisation</a></h3>
						<ul class="toc">
							<li><a href="./className.html">Class name</a></li>
							<li><a href="./option.html">Configuration option</a></li>
							<li><a href="./new.html">`new` constructor</a></li>
							<li><a href="./ajax.html">Ajax data</a></li>
							<li><a href="./default.html">Default initialisation</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a>
					which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>