{"version": 3, "file": "locales/lt_LT.js", "mappings": "wJACO,MAAMA,ECOb,CAEC,kBAAqB,IACrB,mBAAsB,IAGtB,eAAkB,KAClB,eAAkB,IAGlB,kBAAqB,YACrB,uBAA0B,eAC1B,aAAgB,WAChB,kBAAqB,WACrB,aAAgB,QAChB,kBAAqB,qBACrB,WAAc,QACd,gBAAmB,qBACnB,UAAa,SACb,eAAkB,aAClB,WAAc,KACd,gBAAmB,aACnB,YAAe,MACf,iBAAoB,YACpB,WAAc,OAGd,sBAAyB,MACzB,iBAAoB,KACpB,iBAAoB,KACpB,eAAkB,KAClB,cAAiB,KACjB,eAAkB,KAClB,gBAAmB,KACnB,eAAkB,OAGlB,QAAW,OACX,QAAW,SAGX,EAAK,IACL,EAAK,IACL,GAAM,OACN,GAAM,SACN,OAAQ,OACR,OAAQ,SAGR,QAAW,SACX,SAAY,UACZ,MAAS,OACT,MAAS,YACT,IAAO,UACP,KAAQ,WACR,KAAQ,SACR,OAAU,YACV,UAAa,UACb,QAAW,SACX,SAAY,YACZ,SAAY,WACZ,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,aAAc,MACd,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,OAAU,cACV,OAAU,cACV,QAAW,cACX,UAAa,eACb,SAAY,iBACZ,OAAU,eACV,SAAY,cACZ,IAAO,QACP,IAAO,QACP,IAAO,QACP,IAAO,QACP,IAAO,QACP,IAAO,QACP,IAAO,QAGP,SAAY,SAASC,GACpB,MAAO,IACR,EAGA,WAAY,eACZ,KAAQ,WACR,KAAQ,aACR,OAAU,UACV,wBAAyB,mEACzB,QAAW,WACX,KAAQ,UAGR,MAAS,WACT,eAAgB,qBAChB,YAAa,eACb,YAAa,uBACb,cAAe,yBACf,cAAe,uBACf,iBAAkB,kBAClB,gBAAiB,iBACjB,eAAgB,gBAChB,gBAAiB,mBAGjB,OAAU,SACV,qBAAsB,oCACtB,gBAAiB,6BACjB,cAAe,2BACf,mBAAoB,qBACpB,aAAc,aAGd,IAAO,YACP,yBAA0B,6CAC1B,0BAA2B,6CAC3B,oCAAqC,2DACrC,8DAA+D,sGAG/D,OAAU,cACV,MAAS,UACT,KAAQ,WACR,MAAS,aACT,sBAAuB,uDACvB,wBAAyB,yDACzB,+BAAgC,kEAChC,wBAAyB,iCACzB,+EAAgF,mEAChF,aAAc,eACd,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,KAAQ,GACR,IAAO,GACP,KAAQ,GACR,KAAQ,GAGR,8EAA+E,gHAC/E,8CAA+C,mEAC/C,mDAAoD,qEACpD,oDAAqD,sEACrD,wEAAyE,iHACzE,2CAA4C,oEAC5C,iDAAkD,wEAClD,iDAAkD,0EAClD,gBAAiB,gBACjB,UAAW,SACX,QAAS,SAGT,mCAAoC,0CACpC,yBAA0B,iCAC1B,0BAA2B,6BAC3B,eAAgB,iB", "sources": ["webpack://@amcharts/amcharts5/./tmp/webpack/locales/lt_LT.js", "webpack://@amcharts/amcharts5/./src/locales/lt_LT.ts"], "sourcesContent": ["import m from \"./../../../dist/es2015/locales/lt_LT.js\";\nexport const am5locales_lt_LT = m;", "/**\n * amCharts 5 locale\n * \n * Locale: lt_LT\n * Language: Lithuanian\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v5/concepts/locales/creating-translations/) to make corrections or add new translations.\n */\nexport default {\n\t// number formatter related\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \" \",\n\n\t// Position of the percent sign in numbers\n\t\"_percentPrefix\": null,\n\t\"_percentSuffix\": \"%\",\n\n\t// Default date formats for various periods\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_millisecond_full\": \"HH:mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_second_full\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_minute_full\": \"HH:mm - yyyy-MM-dd\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_hour_full\": \"HH:mm - yyyy-MM-dd\",\n\t\"_date_day\": \"MMM dd\",\n\t\"_date_day_full\": \"yyyy-MM-dd\",\n\t\"_date_week\": \"ww\",\n\t\"_date_week_full\": \"yyyy-MM-dd\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_month_full\": \"MMM, yyyy\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era\n\t\"_era_ad\": \"m.e.\",\n\t\"_era_bc\": \"p.m.e.\",\n\n\t// Period\n\t\"A\": \"R\",\n\t\"P\": \"V\",\n\t\"AM\": \"ryto\",\n\t\"PM\": \"vakaro\",\n\t\"A.M.\": \"ryto\",\n\t\"P.M.\": \"vakaro\",\n\n\t// Dates\n\t\"January\": \"Sausio\",\n\t\"February\": \"Vasario\",\n\t\"March\": \"Kovo\",\n\t\"April\": \"Balandžio\",\n\t\"May\": \"Gegužės\",\n\t\"June\": \"Birželio\",\n\t\"July\": \"Liepos\",\n\t\"August\": \"Rugpjūčio\",\n\t\"September\": \"Rugsėjo\",\n\t\"October\": \"Spalio\",\n\t\"November\": \"Lapkričio\",\n\t\"December\": \"Gruodžio\",\n\t\"Jan\": \"Sau\",\n\t\"Feb\": \"Vas\",\n\t\"Mar\": \"Kov\",\n\t\"Apr\": \"Bal\",\n\t\"May(short)\": \"Geg\",\n\t\"Jun\": \"Bir\",\n\t\"Jul\": \"Lie\",\n\t\"Aug\": \"Rgp\",\n\t\"Sep\": \"Rgs\",\n\t\"Oct\": \"Spa\",\n\t\"Nov\": \"Lap\",\n\t\"Dec\": \"Gru\",\n\t\"Sunday\": \"sekmadienis\",\n\t\"Monday\": \"pirmadienis\",\n\t\"Tuesday\": \"antradienis\",\n\t\"Wednesday\": \"trečiadienis\",\n\t\"Thursday\": \"ketvirtadienis\",\n\t\"Friday\": \"penktadienis\",\n\t\"Saturday\": \"šeštadienis\",\n\t\"Sun\": \"sekm.\",\n\t\"Mon\": \"pirm.\",\n\t\"Tue\": \"antr.\",\n\t\"Wed\": \"treč.\",\n\t\"Thu\": \"ketv.\",\n\t\"Fri\": \"penk.\",\n\t\"Sat\": \"šešt.\",\n\n\t// ordinal function\n\t\"_dateOrd\": function(_day: number): string {\n\t\treturn \"-a\";\n\t},\n\n\t// Chart elements\n\t\"Zoom Out\": \"Rodyti viską\",\n\t\"Play\": \"Paleisti\",\n\t\"Stop\": \"Sustabdyti\",\n\t\"Legend\": \"Legenda\",\n\t\"Press ENTER to toggle\": \"Spragtelkite, palieskite arba spauskite ENTER, kad perjungtumėte\",\n\t\"Loading\": \"Kraunama\",\n\t\"Home\": \"Pradžia\",\n\n\t// Chart types\n\t\"Chart\": \"Grafikas\",\n\t\"Serial chart\": \"Serijinis grafikas\",\n\t\"X/Y chart\": \"X/Y grafikas\",\n\t\"Pie chart\": \"Pyrago tipo grafikas\",\n\t\"Gauge chart\": \"Daviklio tipo grafikas\",\n\t\"Radar chart\": \"Radaro tipo grafikas\",\n\t\"Sankey diagram\": \"Sankey diagrama\",\n\t\"Chord diagram\": \"Chord diagrama\",\n\t\"Flow diagram\": \"Flow diagrama\",\n\t\"TreeMap chart\": \"TreeMap grafikas\",\n\n\t// Series types\n\t\"Series\": \"Serija\",\n\t\"Candlestick Series\": \"\\\"Candlestick\\\" tipo grafiko serija\",\n\t\"Column Series\": \"Stulpelinio grafiko serija\",\n\t\"Line Series\": \"Linijinio grafiko serija\",\n\t\"Pie Slice Series\": \"Pyrago tipo serija\",\n\t\"X/Y Series\": \"X/Y serija\",\n\n\t// Map-related\n\t\"Map\": \"Žemėlapis\",\n\t\"Press ENTER to zoom in\": \"Spauskite ENTER, kad pritrauktumėte vaizdą\",\n\t\"Press ENTER to zoom out\": \"Spauskite ENTER, kad atitolintumėte vaizdą\",\n\t\"Use arrow keys to zoom in and out\": \"Naudokitės royklėmis vaizdo pritraukimui ar atitolinimui\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Spauskite pliuso arba minuso klavišus ant klaviatūros, kad pritrautumėte arba atitolintumėte vaizdą\",\n\n\t// Export-related\n\t\"Export\": \"Eksportuoti\",\n\t\"Image\": \"Vaizdas\",\n\t\"Data\": \"Duomenys\",\n\t\"Print\": \"Spausdinti\",\n\t\"Press ENTER to open\": \"Spragtelkite arba spauskite ENTER, kad atidarytumėte\",\n\t\"Press ENTER to print.\": \"Spragtelkite arba spauskite ENTER, kad spausdintumėte.\",\n\t\"Press ENTER to export as %1.\": \"Spragtelkite arba spauskite ENTER, kad eksportuotumėte kaip %1.\",\n\t\"Image Export Complete\": \"Paveiksliuko eksportas baigtas\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"Eksportas užtruko ilgiau negu turėtų. Greičiausiai įvyko klaida.\",\n\t\"Saved from\": \"Išsaugota iš\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\t\"HTML\": \"\",\n\n\t// Scrollbar-related\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"Spauskite TAB klavišą, kad pasirinktumėte žymeklius, arba kairė/dešinė klavišus, kad pakeistumėte pasirinkimą\",\n\t\"Use left and right arrows to move selection\": \"Naudokitės klavišais kairė/dešinė, kad pajudintumėte pasirinkimą\",\n\t\"Use left and right arrows to move left selection\": \"Naudokitės klavišais kairė/dešinė, kad pajudintumėte kairį žymeklį\",\n\t\"Use left and right arrows to move right selection\": \"Naudokitės klavišais kairė/dešinė, kad pajudintumėte dešinį žymeklį\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"Spauskite TAB klavišą, kad pasirinktumėte žymeklius, arba aukštyn/žemyn klavišus, kad pakeistumėte pasirinkimą\",\n\t\"Use up and down arrows to move selection\": \"Naudokitės klavišais aukštyn/žemyn, kad pajudintumėte pasirinkimą\",\n\t\"Use up and down arrows to move lower selection\": \"Naudokitės klavišais aukštyn/žemyn, kad pajudintumėte apatinį žymeklį\",\n\t\"Use up and down arrows to move upper selection\": \"Naudokitės klavišais aukštyn/žemyn, kad pajudintumėte viršutinį žymeklį\",\n\t\"From %1 to %2\": \"Nuo %1 iki %2\",\n\t\"From %1\": \"Nuo %1\",\n\t\"To %1\": \"Iki %1\",\n\n\t// Data loader-related\n\t\"No parser available for file: %1\": \"Failui %1 neturime tinkamo dešifruotojo\",\n\t\"Error parsing file: %1\": \"Skaitant failą %1 įvyko klaida\",\n\t\"Unable to load file: %1\": \"Nepavyko užkrauti failo %1\",\n\t\"Invalid date\": \"Klaidinga data\",\n\n};"], "names": ["am5locales_lt_LT", "_day"], "sourceRoot": ""}