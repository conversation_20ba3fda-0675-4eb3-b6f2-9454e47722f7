{"version": 3, "file": "locales/sv_SE.js", "mappings": "sJACO,MAAMA,ECmBb,CAKC,kBAAqB,IACrB,mBAAsB,IAGtB,eAAkB,KAClB,eAAkB,IAWlB,kBAAqB,YACrB,uBAA0B,eAC1B,aAAgB,WAChB,kBAAqB,WACrB,aAAgB,QAChB,kBAAqB,qBACrB,WAAc,QACd,gBAAmB,qBACnB,UAAa,aACb,eAAkB,aAClB,WAAc,KACd,gBAAmB,aACnB,YAAe,MACf,iBAAoB,YACpB,WAAc,OASd,sBAAyB,MACzB,iBAAoB,KACpB,iBAAoB,KACpB,eAAkB,KAClB,cAAiB,KACjB,eAAkB,KAClB,gBAAmB,KACnB,eAAkB,OAGlB,QAAW,QACX,QAAW,QAUX,EAAK,KACL,EAAK,KACL,GAAM,KACN,GAAM,KACN,OAAQ,OACR,OAAQ,OAWR,QAAW,UACX,SAAY,WACZ,MAAS,OACT,MAAS,QACT,IAAO,MACP,KAAQ,OACR,KAAQ,OACR,OAAU,UACV,UAAa,YACb,QAAW,UACX,SAAY,WACZ,SAAY,WACZ,IAAO,OACP,IAAO,OACP,IAAO,OACP,IAAO,OACP,aAAc,MACd,IAAO,OACP,IAAO,OACP,IAAO,OACP,IAAO,OACP,IAAO,OACP,IAAO,OACP,IAAO,OAGP,OAAU,SACV,OAAU,SACV,QAAW,SACX,UAAa,SACb,SAAY,UACZ,OAAU,SACV,SAAY,SACZ,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MAWP,SAAY,SAASC,GAEpB,MAAO,EACR,EAIA,WAAY,WAGZ,KAAQ,QACR,KAAQ,SAGR,OAAU,mBAGV,wBAAyB,yCAGzB,QAAW,WAIX,KAAQ,MAKR,MAAS,UACT,eAAgB,eAChB,YAAa,aACb,YAAa,cACb,cAAe,oBACf,cAAe,eACf,iBAAkB,gBAClB,gBAAiB,gBACjB,eAAgB,eAChB,gBAAiB,eAKjB,OAAU,SACV,qBAAsB,qBACtB,gBAAiB,eACjB,cAAe,cACf,mBAAoB,aACpB,aAAc,aAGd,IAAO,QACP,yBAA0B,+BAC1B,0BAA2B,+BAC3B,oCAAqC,+CACrC,8DAA+D,2DAY/D,OAAU,YACV,MAAS,OACT,KAAQ,OACR,MAAS,WACT,sBAAuB,yCACvB,wBAAyB,8CACzB,+BAAgC,sDAChC,oCAAqC,6BACrC,wBAAyB,kBACzB,+EAAgF,GAChF,aAAc,cACd,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,KAAQ,GACR,IAAO,GACP,KAAQ,GACR,KAAQ,GAYR,8EAA+E,GAC/E,8CAA+C,6DAC/C,mDAAoD,kEACpD,oDAAqD,gEACrD,wEAAyE,GACzE,2CAA4C,uDAC5C,iDAAkD,6DAClD,iDAAkD,4DAClD,gBAAiB,kBACjB,UAAW,UACX,QAAS,UAGT,mCAAoC,GACpC,yBAA0B,GAC1B,0BAA2B,GAC3B,eAAgB,iB", "sources": ["webpack://@amcharts/amcharts5/./tmp/webpack/locales/sv_SE.js", "webpack://@amcharts/amcharts5/./src/locales/sv_SE.ts"], "sourcesContent": ["import m from \"./../../../dist/es2015/locales/sv_SE.js\";\nexport const am5locales_sv_SE = m;", "/**\n * amCharts 5 locale\n *\n * Locale: sv\n * Language: Swedish\n * Author: <PERSON><PERSON><PERSON>\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v5/concepts/locales/creating-translations/) to make corrections or add new translations.\n *\n * ---\n * Empty string means no translation, so default \"International English\"\n * will be used.\n *\n * If you need the translation to literally be an empty string, use `null`\n * instead.\n *\n * IMPORTANT:\n * When translating make good effort to keep the translation length\n * at least the same chartcount as the English, especially for short prompts.\n */\nexport default {\n\t// Number formatting options.\n\t//\n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \" \",\n\n\t// Position of the percent sign in numbers\n\t\"_percentPrefix\": null,\n\t\"_percentSuffix\": \"%\",\n\n\t// Default date formats for various periods.\n\t//\n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v5/concepts/formatters/formatting-dates/#Format_codes\n\t//\n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_millisecond_full\": \"HH:mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_second_full\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_minute_full\": \"HH:mm - yyyy-MM-dd\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_hour_full\": \"HH:mm - yyyy-MM-dd\",\n\t\"_date_day\": \"yyyy-MM-dd\",\n\t\"_date_day_full\": \"yyyy-MM-dd\",\n\t\"_date_week\": \"ww\",\n\t\"_date_week_full\": \"yyyy-MM-dd\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_month_full\": \"MMM, yyyy\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t//\n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t//\n\t// Available codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era translations\n\t\"_era_ad\": \"e.Kr.\",\n\t\"_era_bc\": \"f.Kr.\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t//\n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"fm\",\n\t\"P\": \"em\",\n\t\"AM\": \"fm\",\n\t\"PM\": \"em\",\n\t\"A.M.\": \"f.m.\",\n\t\"P.M.\": \"e.m.\",\n\n\t// Date-related stuff.\n\t//\n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t//\n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t\"January\": \"januari\",\n\t\"February\": \"februari\",\n\t\"March\": \"mars\",\n\t\"April\": \"april\",\n\t\"May\": \"maj\",\n\t\"June\": \"juni\",\n\t\"July\": \"juli\",\n\t\"August\": \"augusti\",\n\t\"September\": \"september\",\n\t\"October\": \"oktober\",\n\t\"November\": \"november\",\n\t\"December\": \"december\",\n\t\"Jan\": \"jan.\",\n\t\"Feb\": \"feb.\",\n\t\"Mar\": \"mars\",\n\t\"Apr\": \"apr.\",\n\t\"May(short)\": \"maj\",\n\t\"Jun\": \"juni\",\n\t\"Jul\": \"juli\",\n\t\"Aug\": \"aug.\",\n\t\"Sep\": \"sep.\",\n\t\"Oct\": \"okt.\",\n\t\"Nov\": \"nov.\",\n\t\"Dec\": \"dec.\",\n\n\t// Weekdays.\n\t\"Sunday\": \"söndag\",\n\t\"Monday\": \"måndag\",\n\t\"Tuesday\": \"tisdag\",\n\t\"Wednesday\": \"onsdag\",\n\t\"Thursday\": \"torsdag\",\n\t\"Friday\": \"fredag\",\n\t\"Saturday\": \"lördag\",\n\t\"Sun\": \"sön\",\n\t\"Mon\": \"mån\",\n\t\"Tue\": \"tis\",\n\t\"Wed\": \"ons\",\n\t\"Thu\": \"tor\",\n\t\"Fri\": \"fre\",\n\t\"Sat\": \"lör\",\n\n\t// Date ordinal function.\n\t//\n\t// This is used when adding number ordinal when formatting days in dates.\n\t//\n\t// E.g. \"January 1st\", \"February 2nd\".\n\t//\n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function(_day: number): string {\n\t\t// When indicating dates, suffixes are never used in Swedish.\n\t\treturn \"\";\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"Zooma ut\",\n\n\t// Timeline buttons\n\t\"Play\": \"Spela\",\n\t\"Stop\": \"Stoppa\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"Teckenförklaring\",\n\n\t// Legend's item screen reader indicator.\n\t\"Press ENTER to toggle\": \"Klicka eller tryck ENTER för att ändra\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"Läser in\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"Hem\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"Diagram\",\n\t\"Serial chart\": \"Seriediagram\", // ???\n\t\"X/Y chart\": \"XY-diagram\",\n\t\"Pie chart\": \"Tårtdiagram\", // aka cirkeldiagram\n\t\"Gauge chart\": \"Instrumentdiagram\", // ???\n\t\"Radar chart\": \"Radardiagram\", // aka Spindelnätsdiagram\n\t\"Sankey diagram\": \"Sankeydiagram\",\n\t\"Chord diagram\": \"Strängdiagram\",\n\t\"Flow diagram\": \"Flödesschema\",\n\t\"TreeMap chart\": \"Träddiagram \",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"Serier\",\n\t\"Candlestick Series\": \"Candlestick-serier\",\n\t\"Column Series\": \"Kolumnserier\",\n\t\"Line Series\": \"Linjeserier\",\n\t\"Pie Slice Series\": \"Tårtserier\",\n\t\"X/Y Series\": \"X/Y-serier\",\n\n\t// Map-related stuff.\n\t\"Map\": \"Karta\",\n\t\"Press ENTER to zoom in\": \"Tryck RETUR för att zooma in\",\n\t\"Press ENTER to zoom out\": \"Tryck RETUR för att zooma ut\",\n\t\"Use arrow keys to zoom in and out\": \"Använd pil-knapparna för att zooma in och ut\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Använd plus- och minus-knapparna för att zooma in och ut\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t//\n\t// \"Export\" is the top-level menu item.\n\t//\n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t//\n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"Exportera\",\n\t\"Image\": \"Bild\",\n\t\"Data\": \"Data\",\n\t\"Print\": \"Skriv ut\",\n\t\"Press ENTER to open\": \"Klicka eller tryck ENTER för att öppna\",\n\t\"Press ENTER to print.\": \"Klicka eller tryck ENTER för att skriva ut.\",\n\t\"Press ENTER to export as %1.\": \"Klicka eller tryck ENTER för att exportera till %1.\",\n\t\"(Press ESC to close this message)\": \"(Tryck ESC för att stänga)\",\n\t\"Image Export Complete\": \"Bildexport klar\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"\",\n\t\"Saved from\": \"Sparad från\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\t\"HTML\": \"\",\n\n\t// Scrollbar-related stuff.\n\t//\n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t//\n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t//\n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t//\n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"\",\n\t\"Use left and right arrows to move selection\": \"Använd vänster och höger pilknappar för att flytta urvalet\",\n\t\"Use left and right arrows to move left selection\": \"Använd vänster och höger pilknappar för att flytta vänsterurval\",\n\t\"Use left and right arrows to move right selection\": \"Använd vänster och höger pilknappar för att flytta högerurval\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"\",\n\t\"Use up and down arrows to move selection\": \"Använd upp och ner pilknappar för att flytta urvalet\",\n\t\"Use up and down arrows to move lower selection\": \"Använd upp och ner pilknappar för att flytta nedre urvalet\",\n\t\"Use up and down arrows to move upper selection\": \"Använd upp och ner pilknappar för att flytta övre urvalet\",\n\t\"From %1 to %2\": \"Från %1 till %2\",\n\t\"From %1\": \"Från %1\",\n\t\"To %1\": \"Till %1\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"\",\n\t\"Error parsing file: %1\": \"\",\n\t\"Unable to load file: %1\": \"\",\n\t\"Invalid date\": \"Ogiltigt datum\",\n};\n"], "names": ["am5locales_sv_SE", "_day"], "sourceRoot": ""}