{"version": 3, "file": "flow.js", "mappings": "8uBAeO,MAAMA,UAAyBC,EAAA,EAC3B,iBAAAC,GACTC,MAAMD,oBAEN,MAAME,EAAKC,KAAKC,MAAMC,gBAChBC,EAAIH,KAAKI,KAAKC,KAAKL,MAOzBG,EAAE,QAAQG,OAAO,CAChBC,MAAO,KACPC,OAAQ,KACRC,YAAa,GACbC,aAAc,GACdC,WAAY,GACZC,cAAe,GACfC,WAAY,IACZC,QAAS,EACTC,eAAgB,IAGjBZ,EAAE,aAAaG,OAAO,CACrBU,OAAQC,EAAA,EAASC,IAAIlB,KAAKC,MAAO,CAAC,GAClCkB,gBAAiB,SACjBC,gBAAiB,sCAIlBjB,EAAE,YAAYG,OAAO,CACpBe,oBAAoB,EACpBC,gBAAiB,UACjBC,UAAW,aAIZpB,EAAE,YAAYqB,OAAOC,OAAO,WAAY,CAAC,GAEzCtB,EAAE,WAAY,CAAC,YAAYG,OAAO,CACjCoB,WAAW,EACXC,QAAS,IAGVxB,EAAE,QAAS,CAAC,SAASqB,OAAOC,OAAO,WAAY,CAC9CG,KAAM7B,EAAG8B,IAAI,cAGd1B,EAAE,cAAe,CAAC,OAAQ,SAASG,OAAO,CACzCwB,KAAM,SACNC,cAAc,IAGf5B,EAAE,YAAYG,OAAO,CACpB0B,UAAW,WACXC,YAAa,aAGd9B,EAAE,WAAY,CAAC,SAAU,YAAYG,OAAO,CAAC,GAG7CH,EAAE,WAAY,CAAC,SAAU,YAAYG,OAAO,CAAC,GAI7CH,EAAE,YAAY+B,OAAOC,GAAG,eAAgBC,IACvC,MAAMC,EAAWD,EAAEE,OAAOD,SAC1B,GAAIA,EAAU,CACb,MAAME,EAAWF,EAASR,IAAI,iBAC1BU,GACH,OAAYA,GAAWC,IACtB,MAAMC,EAAQD,EAAqBX,IAAI,QACvCY,EAAKC,QACLD,EAAKE,aAAa,IAGpB,MAAMC,EAAWP,EAASR,IAAI,iBAC1Be,GACH,OAAYA,GAAWJ,IACtB,MAAMC,EAAQD,EAAqBX,IAAI,QACvCY,EAAKC,QACLD,EAAKE,aAAa,G,CAKrB,IAAIE,EAAkBR,EAAUR,IAAI,UAAkBQ,EAAUR,IAAI,aAChEgB,GAAaA,EAAUhB,IAAI,gBAC9BgB,EAAUC,a,IAIZ3C,EAAE,YAAY+B,OAAOC,GAAG,cAAeC,IACtC,MAAMC,EAAWD,EAAEE,OAAOD,SAC1B,GAAIA,EAAU,CACb,MAAME,EAAWF,EAASR,IAAI,iBAC1BU,GACH,OAAYA,GAAWC,IACrBA,EAAqBX,IAAI,QAAQkB,SAAS,IAG7C,MAAMH,EAAWP,EAASR,IAAI,iBAC1Be,GACH,OAAYA,GAAWJ,IACrBA,EAAqBX,IAAI,QAAQkB,SAAS,G,KAa/C5C,EAAE,UAAUG,OAAO,CAClB0C,YAAa,aACbC,UAAW,UACXC,YAAa,GACbC,YAAa,GACbC,UAAW,KAIZjD,EAAE,mBAAoB,CAAC,SAAU,OAAQ,UAAUG,OAAO,CACzD+C,eAAgB,EAChBC,eAAgB,EAChBC,eAAgB,EAChBC,eAAgB,IAGjBrD,EAAE,mBAAoB,CAAC,UAAUqB,OAAOC,OAAO,WAAY,CAC1DG,KAAM7B,EAAG8B,IAAI,cAGd1B,EAAE,cAAcG,OAAO,CACtBmD,qBAAsB,KAGvBtD,EAAE,WAAY,CAAC,WAAWG,OAAO,CAChCoB,WAAW,IAGZ,CACC,MAAMtB,EAAOD,EAAE,WAAY,CAAC,SAAU,SAEtCC,EAAKE,OAAO,CACXoD,YAAa,GACbC,cAAe,EACfC,aAAa,EACbC,YAAa,sCAGd,OAASzD,EAAM,OAAQL,EAAI,O,CAG5BI,EAAE,WAAY,CAAC,SAAU,SAASqB,OAAOC,OAAO,QAAS,CAAEiC,YAAa,KAExEvD,EAAE,QAAS,CAAC,SAAU,SAASG,OAAO,CACrCwB,KAAM,SACNC,cAAc,IAGf5B,EAAE,QAAS,CAAC,SAAU,eAAeG,OAAO,CAC3CwD,EAAG,KACHC,QAAS,KACTtD,YAAa,KAGdN,EAAE,QAAS,CAAC,SAAU,aAAaG,OAAO,CACzC0D,EAAG,KACHC,QAAS,KACTtD,WAAY,KAUbR,EAAE,SAASG,OAAO,CACjB4D,QAAQ,QAAQ,IAChBd,UAAW,GACXe,SAAU,EACVC,WAAY,EACZC,KAAK,SAGNlE,EAAE,iBAAiBG,OAAO,CACzBgE,eAAgB,KAGjBnE,EAAE,cAAcG,OAAO,CACtB0D,EAAG,KACHF,EAAG,OAGJ3D,EAAE,WAAY,CAAC,UAAUG,OAAO,CAC/BoB,WAAW,IAGZvB,EAAE,aAAaG,OAAO,CACrBiE,aAAc,KACdC,aAAc,KACdxC,UAAW,QACXC,YAAa,QACb4B,YAAa,qCAGd1D,EAAE,QAAS,CAAC,QAAS,OAAQ,UAAUG,OAAO,CAC7CmE,aAAc,IAGftE,EAAE,QAAS,CAAC,UAAUqB,OAAOC,OAAO,WAAY,CAC/CG,KAAM7B,EAAG8B,IAAI,cAGd1B,EAAE,cAAe,CAAC,QAAS,SAASG,OAAO,CAC1C4D,OAAQ,EACRQ,SAAU,aAGXvE,EAAE,qBAAqBG,OAAO,CAC7BqE,WAAY,KAIb,CACC,MAAMvE,EAAOD,EAAE,WAAY,CAAC,QAAS,OAAQ,UAE7CC,EAAKE,OAAO,CACXoD,YAAa,GACbC,cAAe,EACfC,aAAa,KAGd,OAASxD,EAAM,OAAQL,EAAI,SAC3B,OAASK,EAAM,SAAUL,EAAI,O,CAG9BI,EAAE,WAAY,CAAC,QAAS,OAAQ,UAAUqB,OAAOC,OAAO,QAAS,CAAEiC,YAAa,KAEhFvD,EAAE,kBAAkBG,OAAO,CAC1BsE,SAAU,UAGXzE,EAAE,YAAa,CAAC,UAAUG,OAAO,CAChC0B,UAAW,OACXC,YAAa,WAGd9B,EAAE,WAAY,CAAC,QAAS,OAAQ,QAAS,UAAUG,OAAO,CACzDqD,cAAe,KAGhBxD,EAAE,WAAY,CAAC,QAAS,OAAQ,QAAS,UAAUqB,OAAOC,OAAO,QAAS,CAAEoD,YAAa,EAAGlB,cAAe,IAS3GxD,EAAE,cAAcG,OAAO,CACtB0C,YAAa,aACbG,YAAa,EACb2B,UAAW,EACXC,UAAW,MACXC,gBAAiB,MAAU,WAG5B7E,EAAE,kBAAmB,CAAC,eAAeG,OAAO,CAC3CwD,EAAG,KACHC,QAAS,OAGV5D,EAAE,kBAAmB,CAAC,aAAaG,OAAO,CACzC2D,QAAS,IAGV9D,EAAE,SAAU,CAAC,aAAc,OAAQ,UAAUG,OAAO,CACnDuD,YAAa,kBAGd1D,EAAE,SAAU,CAAC,aAAc,OAAQ,UAAUqB,OAAOC,OAAO,WAAY,CACtEG,KAAM7B,EAAG8B,IAAI,cAGd,CACC,MAAMzB,EAAOD,EAAE,iBAAkB,CAAC,OAAQ,UAE1CC,EAAKE,OAAO,CACXqD,cAAe,IACf1B,YAAa,QACbD,UAAW,OACXiD,YAAY,KAGb,OAAS7E,EAAM,SAAUL,EAAI,O,CAG9BI,EAAE,iBAAkB,CAAC,OAAQ,UAAUqB,OAAOC,OAAO,QAAS,CAC7DkC,cAAe,IAGhBxD,EAAE,QAAS,CAAC,aAAc,SAASG,OAAO,CACzCwB,KAAM,SACNC,cAAc,IAGf5B,EAAE,QAAS,CAAC,aAAc,eAAeG,OAAO,CAC/CwD,EAAG,EACHC,QAAS,KACTE,QAAS,KACTiB,UAAW,KAGZ/E,EAAE,QAAS,CAAC,aAAc,aAAaG,OAAO,CAC7CyD,QAAS,KACTE,QAAS,KACTvD,aAAc,IAEhB,E,4CCrMM,MAAeyE,UAAaC,EAAA,EAAnC,c,oBAmBC,6C,gDAAiCpF,KAAKqF,SAASC,KAAKC,EAAA,EAAUrE,IAAIlB,KAAKC,MAAO,CAAC,MAO/E,yC,gDAA6D,KAC7D,yC,gDAAoI,KACpI,qC,gDAAmB,IACnB,4C,iDAAmC,IAEnC,4C,gDAAoD,CAAC,GAyXtD,CAxXW,SAAAuF,GACTxF,KAAKyF,eAAeH,KAAK3F,EAAiBuB,IAAIlB,KAAKC,QAEnDD,KAAK0F,OAAOJ,KAAK,WAAY,WAAY,YAErCtF,KAAK2F,QACR3F,KAAK2F,MAAMC,KAAO5F,MAGnBF,MAAM0F,YAENxF,KAAKqF,SAASC,KAAKtF,KAAK6F,iBACzB,CAOU,eAAAC,CAAgBzD,GACzBvC,MAAMgG,gBAAgBzD,GAEtB,MAAMsD,EAAQ3F,KAAK2F,MACnB,GAAIA,EAAO,CACV,IAAII,GAAU,EACVC,EAAW3D,EAASR,IAAI,YACxBoE,EAAiBN,EAAMO,gBAAgBF,GAEtCC,IACY,MAAZD,IACHA,EAAW,YAAchG,KAAKmG,OAC9BnG,KAAKmG,SACLJ,GAAU,GAEXJ,EAAMS,KAAKd,KAAK,CAAEe,GAAIL,EAAUD,QAASA,IACzCE,EAAiBN,EAAMO,gBAAgBF,GAClCD,GACJE,EAAeK,IAAI,OAAQN,IAI7BD,GAAU,EACV,IAAIQ,EAAWlE,EAASR,IAAI,YAExB2E,EAAiBb,EAAMO,gBAAgBK,GACtCC,IACY,MAAZD,IACHA,EAAW,YAAcvG,KAAKmG,OAC9BnG,KAAKmG,SACLJ,GAAU,GAGXJ,EAAMS,KAAKd,KAAK,CAAEe,GAAIE,EAAUR,QAASA,IACzCS,EAAiBb,EAAMO,gBAAgBK,GAClCR,GACJS,EAAeF,IAAI,OAAQC,IAIzBN,IACH5D,EAASiE,IAAI,SAAUL,GACvBN,EAAMc,gBAAgBR,EAAgB5D,IAGnCmE,IACHnE,EAASiE,IAAI,SAAUE,GACvBb,EAAMe,gBAAgBF,EAAgBnE,IAGvCA,EAASiE,IAAI,OAAQtG,KAAK2G,SAAStE,IAEnC,MAAMuE,EAAc5G,KAAK2F,MAAMkB,UAAUC,QAAQb,GAC3Cc,EAAc/G,KAAK2F,MAAMkB,UAAUC,QAAQN,GAEjDxG,KAAKgH,cAAcJ,EAAc,IAAMG,GAAe1E,EAElD4D,EAAepE,IAAI,aAClB2E,GACHP,EAAeK,IAAI,OAAQE,EAAe3E,IAAI,SAG/CQ,EAASR,IAAI,QAAQyE,IAAI,YAAa,aAInCE,EAAe3E,IAAI,aAClBoE,GACHO,EAAeF,IAAI,OAAQL,EAAepE,IAAI,SAG/CQ,EAASR,IAAI,QAAQyE,IAAI,YAAa,aAGvCtG,KAAKiH,iBAAiB5E,E,CAExB,CAEU,YAAA6E,GACJlH,KAAK2F,MAAMwB,eACfnH,KAAK2F,MAAMS,KAAK9F,OAAO,IACvBN,KAAK2F,MAAMwB,cAAe,EAG5B,CAEO,gBAAAC,GACNtH,MAAMsH,mBAEN,IAAIC,EAAWC,IACXC,GAAY,IACZC,EAAW,EAEf,GAAIxH,KAAKyH,aAAc,CACtBzH,KAAK0H,WAAa,GAClB,MAAM/B,EAAQ3F,KAAK2F,MACfA,GACH,OAAYA,EAAMkB,WAAYxE,IAC7B,MAAMsF,EAAetF,EAASR,IAAI,gBAClC7B,KAAK0H,WAAWpC,KAAKqC,GAErB,MAAM/E,EAAWP,EAASR,IAAI,iBAE9B,IAAI+F,EAAc,EACdC,EAAqB,EACrBjF,GACH,OAAYA,GAAWH,IACtB,MAAMqF,EAAQrF,EAAKZ,IAAI,SACjBkG,EAAetF,EAAKZ,IAAI,gBAC9B+F,GAAeE,EACfD,GAAsBE,CAAY,IAIpC1F,EAASiE,IAAI,cAAesB,GAC5BvF,EAASiE,IAAI,qBAAsBuB,GAEnC,MAAMtF,EAAWF,EAASR,IAAI,iBAC9B,IAAImG,EAAc,EACdC,EAAqB,EACrB1F,GACH,OAAYA,GAAWE,IACtB,MAAMqF,EAAQrF,EAAKZ,IAAI,SACjBkG,EAAetF,EAAKZ,IAAI,gBAC9BmG,GAAeF,EACfG,GAAsBF,CAAY,IAIpC1F,EAASiE,IAAI,cAAe0B,GAC5B3F,EAASiE,IAAI,qBAAsB2B,GAEnC5F,EAASiE,IAAI,MAAOsB,EAAcI,GAClC3F,EAASiE,IAAI,aAAcuB,EAAqBI,GAEhDtC,EAAMuC,kBAAkB7F,EAAS,IAGnCrC,KAAKmI,WAAa,GAElB,OAAYnI,KAAK6G,WAAYxE,IAC5B,IAAIyF,EAAQzF,EAASR,IAAI,SACrB,WAAeiG,KACdA,EAAQT,IACXA,EAAWS,GAGRA,EAAQP,IACXA,EAAYO,GAEbN,GAAYM,E,IAId,OAAY9H,KAAK6G,WAAYxE,IAC5B,IAAIyF,EAAQzF,EAASR,IAAI,SACzB,GAAI,WAAeiG,GAAQ,CAC1B,IAAIM,EAAe/F,EAASR,IAAI,gBAC5Bf,EAAUd,KAAK6B,IAAI,UAAW,GAC9Bf,EAAU,GACTsH,EAAetH,EAAU0G,IAC5BY,EAAetH,EAAU0G,GAI3B,IAAIa,EAAe,CAAEC,OAAQjG,EAASR,IAAI,UAAUA,IAAI,gBAAiBS,OAAQD,EAASR,IAAI,UAAUA,IAAI,gBAAiBiG,MAAOM,GACpI/F,EAASkG,OAAO,eAAgBF,GAChCrI,KAAKmI,WAAW7C,KAAK+C,GACrBrI,KAAKkI,kBAAkB7F,E,KAIzBrC,KAAKwI,cAAc,YAAajB,GAChCvH,KAAKwI,cAAc,WAAYnB,GAC/BrH,KAAKwI,cAAc,WAAYhB,E,CAEjC,CAEO,gBAAAP,CAAiB5E,GACvB,MAAMI,EAAOJ,EAASR,IAAI,QAEpBG,EAAYS,EAAKZ,IAAI,aACrBI,EAAcQ,EAAKZ,IAAI,eACvByG,EAASjG,EAASR,IAAI,UACtBS,EAASD,EAASR,IAAI,UACtB4G,EAAaH,EAAOzG,IAAI,QACxB6G,EAAapG,EAAOT,IAAI,QAI9B,OAHAY,EAAKkG,OAAO,gBACZlG,EAAKkG,OAAO,kBAEJ3G,GAEP,IAAK,QACJS,EAAKmG,kBACL,MACD,IAAK,SACJnG,EAAK6D,IAAI,OAAQmC,GACjB,MAED,IAAK,SACJhG,EAAK6D,IAAI,OAAQoC,GACjB,MAED,IAAK,WACJ,IAAIG,EAAWpG,EAAKqG,cACfD,IACJA,EAAWE,EAAA,EAAe7H,IAAIlB,KAAKC,MAAO,CAAC,IAE5C,MAAM+I,EAAkB,CAAEC,MAAOR,GAC7BH,EAAOzG,IAAI,aACdmH,EAAWrH,QAAU,GAEtB,MAAMuH,EAAkB,CAAED,MAAOP,GAC7BpG,EAAOT,IAAI,aACdqH,EAAWvH,QAAU,GAGtBkH,EAASvC,IAAI,QAAS,CAAC0C,EAAYE,IACnCzG,EAAKqG,cAAgBD,EAErBpG,EAAK6D,IAAI,eAAgBuC,GACzB,MACD,IAAK,OACJpG,EAAK6D,IAAI,YAAQ6C,GAInB,OAAQlH,GACP,IAAK,QACJQ,EAAKmG,kBACL,MAED,IAAK,SACJnG,EAAK6D,IAAI,SAAUmC,GACnB,MAED,IAAK,SACJhG,EAAK6D,IAAI,SAAUoC,GACnB,MACD,IAAK,WACJ,IAAIG,EAAWpG,EAAK2G,gBACpB,IAAKP,EAAU,CACdA,EAAWE,EAAA,EAAe7H,IAAIlB,KAAKC,MAAO,CAAC,GAC3C,MAAM+I,EAAkB,CAAEC,MAAOR,GAC7BH,EAAOzG,IAAI,aACdmH,EAAWrH,QAAU,GAEtB,MAAMuH,EAAkB,CAAED,MAAOP,GAC7BpG,EAAOT,IAAI,aACdqH,EAAWvH,QAAU,GAGtBkH,EAASvC,IAAI,QAAS,CAAC0C,EAAYE,IACnCzG,EAAK2G,gBAAkBP,C,CAExBpG,EAAK6D,IAAI,iBAAkBuC,GAC3B,MAED,IAAK,OACJpG,EAAKkG,OAAO,UAGf,CAKO,eAAAU,CAAgBhH,GACtBvC,MAAMuJ,gBAAgBhH,GACtB,IAAII,EAAOJ,EAASR,IAAI,QACpBY,IACHzC,KAAKsJ,MAAMC,YAAY9G,GACvBA,EAAK+G,UAEP,CASa,YAAAC,CAAapH,EAA+CqH,G,uHACxE,MAAMC,EAAW,CAAC,EAAMF,aAAY,UAACpH,EAAUqH,IACzCE,EAAc5J,KAAKwB,OAAOC,OAAO,SAAU,CAAC,GAE5CoI,EAAyB,yBACzBC,EAAuB,uBAExB,WAAeJ,KACnBA,EAAWE,EAAY/H,IAAIgI,EAAwB7J,KAAK6B,IAAIgI,EAAwB,KAGrF,MAAME,EAASH,EAAY/H,IAAIiI,EAAsB9J,KAAK6B,IAAIiI,IAE9DH,EAASrE,KAAKjD,EAAS2H,QAAQ,CAC9BC,IAAK,eACLC,GAAIC,KAAKC,IAAIpK,KAAK6B,IAAI,iBAAkB,GAAI7B,KAAK6B,IAAI,aAAc,GAAKQ,EAASR,IAAI,UACrF6H,SAAUA,EACVK,OAAQA,IACNM,eAEkBhI,EAASR,IAAI,QACrByI,aAEPC,QAAQC,IAAIb,EACnB,G,CASa,YAAAc,CAAapI,EAA+CqH,G,uHACxE,MAAMC,EAAW,CAAC,EAAMc,aAAY,UAACpI,EAAUqH,IAE1C,WAAeA,KACnBA,EAAW1J,KAAK6B,IAAI,yBAA0B,IAG/C,MAAMkI,EAAS/J,KAAK6B,IAAI,wBAExB8H,EAASrE,KAAKjD,EAAS2H,QAAQ,CAAEC,IAAK,eAAuBC,GAAI7H,EAASR,IAAI,SAAU6H,SAAUA,EAAUK,OAAQA,IAAUM,eAEzGhI,EAASR,IAAI,QACrB6I,aAEPH,QAAQC,IAAIb,EACnB,G,CAEO,eAAAgB,CAAgBC,GACtB,MAAMC,EAASD,EAAO/I,IAAI,UAE1B,GAAIgJ,EAAQ,CACX,MAAMxI,EAAWwI,EAAOxI,SACxB,GAAIA,EAAU,CACb,MAAMI,EAAOJ,EAASR,IAAI,QACpBgJ,EAASD,EAAO/I,IAAI,UAE1B,GAAIgJ,EAAQ,CACX,MAAMC,EAAQrI,EAAKsI,SAAS/K,KAAKgL,mBAAmBJ,IACpDC,EAAOvK,OAAO,CAAE0D,EAAG8G,EAAM9G,EAAGF,EAAGgH,EAAMhH,IAEjC8G,EAAO/I,IAAI,eACdgJ,EAAOvE,IAAI,WAAYwE,EAAMG,MAAQL,EAAO/I,IAAI,kBAAmB,G,GAKxE,CAEU,kBAAAmJ,CAAmBJ,GAC5B,OAAOA,EAAO/I,IAAI,YAAa,EAChC,EAtZA,qC,gDAAkC,SAClC,sC,gDAA0CuD,EAAA,EAAO8F,WAAWC,OAAO,CAAChG,EAAKiG,cCpJnE,IAAIC,EAAMlB,KAAKkB,IACXC,EAAMnB,KAAKmB,IACXC,EAAMpB,KAAKoB,IACXC,EAAKrB,KAAKsB,GACVC,EAASF,EAAK,EACdG,EAAW,EAALH,EACNpB,EAAMD,KAAKC,IACXwB,EAAU,MCLrB,SAASC,EAAMC,EAAGC,GAChB,OAAOC,MAAMC,KAAK,CAACC,OAAQH,EAAID,IAAI,CAACK,EAAGC,IAAMN,EAAIM,GACnD,CAuBA,SAAS,EAAMC,EAAUC,GACvB,IAAInI,EAAW,EACXoI,EAAa,KACbC,EAAgB,KAChBC,EAAa,KAEjB,SAASC,EAAMC,GACb,IAKWC,EALPC,EAAIF,EAAOT,OACXY,EAAY,IAAId,MAAMa,GACtBE,EAAalB,EAAM,EAAGgB,GACtBG,EAAS,IAAIhB,MAAMa,EAAIA,GACvBI,EAAS,IAAIjB,MAAMa,GACnBT,EAAI,EAERO,EAASO,aAAajB,KAAK,CAACC,OAAQW,EAAIA,GAAIP,EACtC,CAACH,EAAGL,IAAMa,EAAOb,EAAIe,GAAGf,EAAIe,EAAI,GAChC,CAACV,EAAGL,IAAMa,EAAOb,EAAIe,EAAI,GAAGf,EAAIe,IAGtC,IAAK,IAAIf,EAAI,EAAGA,EAAIe,IAAKf,EAAG,CAC1B,IAAI9H,EAAI,EACR,IAAK,IAAI+H,EAAI,EAAGA,EAAIc,IAAKd,EAAG/H,GAAK2I,EAAOb,EAAIe,EAAId,GAAKM,EAAWM,EAAOZ,EAAIc,EAAIf,GAC/EM,GAAKU,EAAUhB,GAAK9H,CACtB,CAEA4I,GADAR,EAAIhC,EAAI,EAAGuB,EAAMxH,EAAW0I,GAAKT,GACxBjI,EAAWwH,EAAMkB,EAG1B,CACE,IAAI7I,EAAI,EACJuI,GAAYQ,EAAW1I,MAAK,CAAC8I,EAAGC,IAAMb,EAAWO,EAAUK,GAAIL,EAAUM,MAC7E,IAAK,MAAMtB,KAAKiB,EAAY,CAC1B,MAAMM,EAAKrJ,EACX,GAAIqI,EAAU,CACZ,MAAMiB,EAAgBzB,EAAW,GAAJgB,EAAOA,GAAGU,QAAOxB,GAAKA,EAAI,EAAIY,GAAQZ,EAAIc,EAAIf,GAAKa,EAAOb,EAAIe,EAAId,KAC3FS,GAAec,EAAcjJ,MAAK,CAAC8I,EAAGC,IAAMZ,EAAcW,EAAI,GAAKR,GAAQQ,EAAIN,EAAIf,GAAKa,EAAOb,EAAIe,EAAIM,GAAIC,EAAI,GAAKT,GAAQS,EAAIP,EAAIf,GAAKa,EAAOb,EAAIe,EAAIO,MAC5J,IAAK,MAAMrB,KAAKuB,EACVvB,EAAI,GACQiB,GAAQjB,EAAIc,EAAIf,KAAOkB,GAAQjB,EAAIc,EAAIf,GAAK,CAACxD,OAAQ,KAAMhG,OAAQ,QAC3EA,OAAS,CAACkL,MAAO1B,EAAG1H,WAAYJ,EAAGyJ,SAAUzJ,GAAK2I,GAAQZ,EAAIc,EAAIf,GAAKM,EAAGtE,MAAO6E,GAAQZ,EAAIc,EAAIf,KAEzFkB,EAAOlB,EAAIe,EAAId,KAAOiB,EAAOlB,EAAIe,EAAId,GAAK,CAACzD,OAAQ,KAAMhG,OAAQ,QACzEgG,OAAS,CAACkF,MAAO1B,EAAG1H,WAAYJ,EAAGyJ,SAAUzJ,GAAK2I,EAAOb,EAAIe,EAAId,GAAKK,EAAGtE,MAAO6E,EAAOb,EAAIe,EAAId,IAGzGkB,EAAOnB,GAAK,CAAC0B,MAAO1B,EAAG1H,WAAYiJ,EAAII,SAAUzJ,EAAG8D,MAAOgF,EAAUhB,GACvE,KAAO,CACL,MAAMwB,EAAgBzB,EAAM,EAAGgB,GAAGU,QAAOxB,GAAKY,EAAOb,EAAIe,EAAId,IAAMY,EAAOZ,EAAIc,EAAIf,KAC9EU,GAAec,EAAcjJ,MAAK,CAAC8I,EAAGC,IAAMZ,EAAcG,EAAOb,EAAIe,EAAIM,GAAIR,EAAOb,EAAIe,EAAIO,MAChG,IAAK,MAAMrB,KAAKuB,EAAe,CAC7B,IAAIZ,EASJ,GARIZ,EAAIC,GACNW,EAAQM,EAAOlB,EAAIe,EAAId,KAAOiB,EAAOlB,EAAIe,EAAId,GAAK,CAACzD,OAAQ,KAAMhG,OAAQ,OACzEoK,EAAMpE,OAAS,CAACkF,MAAO1B,EAAG1H,WAAYJ,EAAGyJ,SAAUzJ,GAAK2I,EAAOb,EAAIe,EAAId,GAAKK,EAAGtE,MAAO6E,EAAOb,EAAIe,EAAId,MAErGW,EAAQM,EAAOjB,EAAIc,EAAIf,KAAOkB,EAAOjB,EAAIc,EAAIf,GAAK,CAACxD,OAAQ,KAAMhG,OAAQ,OACzEoK,EAAMpK,OAAS,CAACkL,MAAO1B,EAAG1H,WAAYJ,EAAGyJ,SAAUzJ,GAAK2I,EAAOb,EAAIe,EAAId,GAAKK,EAAGtE,MAAO6E,EAAOb,EAAIe,EAAId,IACjGD,IAAMC,IAAGW,EAAMpE,OAASoE,EAAMpK,SAEhCoK,EAAMpE,QAAUoE,EAAMpK,QAAUoK,EAAMpE,OAAOR,MAAQ4E,EAAMpK,OAAOwF,MAAO,CAC3E,MAAMQ,EAASoE,EAAMpE,OACrBoE,EAAMpE,OAASoE,EAAMpK,OACrBoK,EAAMpK,OAASgG,CACjB,CACF,CACA2E,EAAOnB,GAAK,CAAC0B,MAAO1B,EAAG1H,WAAYiJ,EAAII,SAAUzJ,EAAG8D,MAAOgF,EAAUhB,GACvE,CACA9H,GAAK4I,CACP,CACF,CAKA,OAFAI,EAASU,OAAOC,OAAOX,IAChBC,OAASA,EACTR,EAAaO,EAAO3I,KAAKoI,GAAcO,CAChD,CAkBA,OAhBAN,EAAMvI,SAAW,SAASgI,GACxB,OAAOyB,UAAU1B,QAAU/H,EAAWiG,EAAI,EAAG+B,GAAIO,GAASvI,CAC5D,EAEAuI,EAAMH,WAAa,SAASJ,GAC1B,OAAOyB,UAAU1B,QAAUK,EAAaJ,EAAGO,GAASH,CACtD,EAEAG,EAAMF,cAAgB,SAASL,GAC7B,OAAOyB,UAAU1B,QAAUM,EAAgBL,EAAGO,GAASF,CACzD,EAEAE,EAAMD,WAAa,SAASN,GAC1B,OAAOyB,UAAU1B,QAAe,MAALC,EAAYM,EAAa,MA/GlCoB,EA+GoE1B,EAA1BM,EA9GvD,SAASU,EAAGC,GACjB,OAAOS,EACLV,EAAE7E,OAAOR,MAAQqF,EAAE7K,OAAOwF,MAC1BsF,EAAE9E,OAAOR,MAAQsF,EAAE9K,OAAOwF,MAE9B,GAyG4FqE,EAAIA,EAAGO,GAASD,GAAcA,EAAWN,EA/GvI,IAAsB0B,CAgHpB,EAEOnB,CACT,C,cCzHWoB,EAAQ9B,MAAM+B,UAAUD,MCApB,WAAS9J,GACtB,OAAO,WACL,OAAOA,CACT,CACF,CCCA,SAASgK,EAAcC,GACrB,OAAOA,EAAE3F,MACX,CAEA,SAAS4F,EAAcD,GACrB,OAAOA,EAAE3L,MACX,CAEA,SAAS6L,EAAcF,GACrB,OAAOA,EAAE/J,MACX,CAEA,SAASkK,EAAkBH,GACzB,OAAOA,EAAE7J,UACX,CAEA,SAASiK,EAAgBJ,GACvB,OAAOA,EAAER,QACX,CAEA,SAASa,IACP,OAAO,CACT,CAEA,SAASC,IACP,OAAO,EACT,CAEA,SAASC,EAAO7J,GACd,IAAI2D,EAAS0F,EACT1L,EAAS4L,EACT3J,EAAe4J,EACf3J,EAAe2J,EACf/J,EAAagK,EACbX,EAAWY,EACXlK,EAAWmK,EACXG,EAAU,KAEd,SAASD,IACP,IAAIE,EACAC,EAAIrG,EAAOsG,MAAM5O,KAAM4N,WACvBiB,EAAIvM,EAAOsM,MAAM5O,KAAM4N,WACvBkB,EAAK3K,EAASyK,MAAM5O,KAAM4N,WAAa,EACvCmB,EAAOjB,EAAMkB,KAAKpB,WAClBqB,GAAM1K,EAAaqK,MAAM5O,MAAO+O,EAAK,GAAKJ,EAAGI,IAC7CG,EAAM9K,EAAWwK,MAAM5O,KAAM+O,GAAQrD,EACrCyD,EAAM1B,EAASmB,MAAM5O,KAAM+O,GAAQrD,EACnC0D,GAAM5K,EAAaoK,MAAM5O,MAAO+O,EAAK,GAAKF,EAAGE,IAC7CM,EAAMjL,EAAWwK,MAAM5O,KAAM+O,GAAQrD,EACrC4D,EAAM7B,EAASmB,MAAM5O,KAAM+O,GAAQrD,EAavC,GAXK+C,IAASA,EAAUC,GAAS,EAAAa,EAAA,OAE7BT,EAAKlD,IACHP,EAAI8D,EAAMD,GAAY,EAALJ,EAASlD,EAASuD,EAAMD,GAAOA,GAAOJ,EAAIK,GAAOL,IAAOI,GAAOJ,EAAIK,GAAOL,GAC1FI,EAAMC,GAAOD,EAAMC,GAAO,EAC3B9D,EAAIiE,EAAMD,GAAY,EAALP,EAASlD,EAAS0D,EAAMD,GAAOA,GAAOP,EAAIQ,GAAOR,IAAOO,GAAOP,EAAIQ,GAAOR,GAC1FO,EAAMC,GAAOD,EAAMC,GAAO,GAGjCb,EAAQe,OAAOP,EAAK3D,EAAI4D,GAAMD,EAAK1D,EAAI2D,IACvCT,EAAQgB,IAAI,EAAG,EAAGR,EAAIC,EAAKC,GACvBD,IAAQG,GAAOF,IAAQG,EACzB,GAAI3K,EAAY,CACd,IAA6C+K,EAAMN,GAAzCzK,EAAWiK,MAAM5O,KAAM4N,WAA2B+B,GAAON,EAAMC,GAAO,EAChFb,EAAQmB,iBAAiB,EAAG,EAAGF,EAAMpE,EAAI+D,GAAMK,EAAMnE,EAAI8D,IACzDZ,EAAQoB,OAAOT,EAAK9D,EAAIqE,GAAMP,EAAK7D,EAAIoE,IACvClB,EAAQoB,OAAOH,EAAMpE,EAAIgE,GAAMI,EAAMnE,EAAI+D,GAC3C,MACEb,EAAQmB,iBAAiB,EAAG,EAAGR,EAAK9D,EAAI+D,GAAMD,EAAK7D,EAAI8D,IACvDZ,EAAQgB,IAAI,EAAG,EAAGL,EAAIC,EAAKC,GAM/B,GAHAb,EAAQmB,iBAAiB,EAAG,EAAGX,EAAK3D,EAAI4D,GAAMD,EAAK1D,EAAI2D,IACvDT,EAAQqB,YAEJpB,EAAQ,OAAOD,EAAU,KAAMC,EAAS,IAAM,IACpD,CA0CA,OAxCI/J,IAAY6J,EAAO7J,WAAa,SAASwH,GAC3C,OAAOyB,UAAU1B,QAAUvH,EAA0B,mBAANwH,EAAmBA,EAAI,GAAUA,GAAIqC,GAAU7J,CAChG,GAEA6J,EAAOtK,OAAS,SAASiI,GACvB,OAAOyB,UAAU1B,QAAU3H,EAAeC,EAA4B,mBAAN2H,EAAmBA,EAAI,GAAUA,GAAIqC,GAAUjK,CACjH,EAEAiK,EAAOjK,aAAe,SAAS4H,GAC7B,OAAOyB,UAAU1B,QAAU3H,EAA4B,mBAAN4H,EAAmBA,EAAI,GAAUA,GAAIqC,GAAUjK,CAClG,EAEAiK,EAAOhK,aAAe,SAAS2H,GAC7B,OAAOyB,UAAU1B,QAAU1H,EAA4B,mBAAN2H,EAAmBA,EAAI,GAAUA,GAAIqC,GAAUhK,CAClG,EAEAgK,EAAOpK,WAAa,SAAS+H,GAC3B,OAAOyB,UAAU1B,QAAU9H,EAA0B,mBAAN+H,EAAmBA,EAAI,GAAUA,GAAIqC,GAAUpK,CAChG,EAEAoK,EAAOf,SAAW,SAAStB,GACzB,OAAOyB,UAAU1B,QAAUuB,EAAwB,mBAANtB,EAAmBA,EAAI,GAAUA,GAAIqC,GAAUf,CAC9F,EAEAe,EAAOrK,SAAW,SAASgI,GACzB,OAAOyB,UAAU1B,QAAU/H,EAAwB,mBAANgI,EAAmBA,EAAI,GAAUA,GAAIqC,GAAUrK,CAC9F,EAEAqK,EAAOlG,OAAS,SAAS6D,GACvB,OAAOyB,UAAU1B,QAAU5D,EAAS6D,EAAGqC,GAAUlG,CACnD,EAEAkG,EAAOlM,OAAS,SAAS6J,GACvB,OAAOyB,UAAU1B,QAAU5J,EAAS6J,EAAGqC,GAAUlM,CACnD,EAEAkM,EAAOC,QAAU,SAAStC,GACxB,OAAOyB,UAAU1B,QAAWuC,EAAe,MAALtC,EAAY,KAAOA,EAAIqC,GAAUC,CACzE,EAEOD,CACT,CAEe,aACb,OAAOA,GACT,CAEO,SAASuB,IACd,OAAOvB,EAAOD,EAChB,CCrIe,SAASyB,EAAU7C,EAAGC,GACnC,OAAY,MAALD,GAAkB,MAALC,EAAY6C,IAAM9C,EAAIC,GAAK,EAAID,EAAIC,EAAI,EAAID,GAAKC,EAAI,EAAI6C,GAC9E,CCFe,SAASC,EAAW/C,EAAGC,GACpC,OAAY,MAALD,GAAkB,MAALC,EAAY6C,IAC5B7C,EAAID,GAAK,EACTC,EAAID,EAAI,EACRC,GAAKD,EAAI,EACT8C,GACN,C,QCQA,MAAO5E,IAAG,MAAK,SAASlB,KAwFxB,SAASgG,EAAKtB,GACZ,MAAO,CAACsB,KAAMtB,EAChB,CCxGO,SAAS,EAAUzC,EAAGpI,EAAGF,GAC9B9D,KAAKoM,EAAIA,EACTpM,KAAKgE,EAAIA,EACThE,KAAK8D,EAAIA,CACX,CDsBW,CAAC,IAAK,KAAKsM,IAAID,GAOf,CAAC,IAAK,KAAKC,IAAID,GAOf,CAAC,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,MAAMC,IAAID,GClC5D,EAAUpC,UAAY,CACpBsC,YAAa,EACbC,MAAO,SAASlE,GACd,OAAa,IAANA,EAAUpM,KAAO,IAAI,EAAUA,KAAKoM,EAAIA,EAAGpM,KAAKgE,EAAGhE,KAAK8D,EACjE,EACAyM,UAAW,SAASvM,EAAGF,GACrB,OAAa,IAANE,EAAgB,IAANF,EAAU9D,KAAO,IAAI,EAAUA,KAAKoM,EAAGpM,KAAKgE,EAAIhE,KAAKoM,EAAIpI,EAAGhE,KAAK8D,EAAI9D,KAAKoM,EAAItI,EACjG,EACA8K,MAAO,SAAS9D,GACd,MAAO,CAACA,EAAM,GAAK9K,KAAKoM,EAAIpM,KAAKgE,EAAG8G,EAAM,GAAK9K,KAAKoM,EAAIpM,KAAK8D,EAC/D,EACA0M,OAAQ,SAASxM,GACf,OAAOA,EAAIhE,KAAKoM,EAAIpM,KAAKgE,CAC3B,EACAyM,OAAQ,SAAS3M,GACf,OAAOA,EAAI9D,KAAKoM,EAAIpM,KAAK8D,CAC3B,EACA4M,OAAQ,SAASC,GACf,MAAO,EAAEA,EAAS,GAAK3Q,KAAKgE,GAAKhE,KAAKoM,GAAIuE,EAAS,GAAK3Q,KAAK8D,GAAK9D,KAAKoM,EACzE,EACAwE,QAAS,SAAS5M,GAChB,OAAQA,EAAIhE,KAAKgE,GAAKhE,KAAKoM,CAC7B,EACAyE,QAAS,SAAS/M,GAChB,OAAQA,EAAI9D,KAAK8D,GAAK9D,KAAKoM,CAC7B,EACA0E,SAAU,SAAS9M,GACjB,OAAOA,EAAE+M,OAAOC,OAAOhN,EAAE6H,QAAQuE,IAAIpQ,KAAK4Q,QAAS5Q,MAAMoQ,IAAIpM,EAAE0M,OAAQ1M,GACzE,EACAiN,SAAU,SAASnN,GACjB,OAAOA,EAAEiN,OAAOC,OAAOlN,EAAE+H,QAAQuE,IAAIpQ,KAAK6Q,QAAS7Q,MAAMoQ,IAAItM,EAAE4M,OAAQ5M,GACzE,EACAoN,SAAU,WACR,MAAO,aAAelR,KAAKgE,EAAI,IAAMhE,KAAK8D,EAAI,WAAa9D,KAAKoM,EAAI,GACtE,GAGoB,IAAI,EAAU,EAAG,EAAG,GAEpB,EAAU2B,U,iCC/BzB,MAAMoD,UAAiB5L,EAAA,EAA9B,c,oBAKC,qC,wDASD,EAJC,qC,gDAAkC,aAClC,sC,gDAA0CA,EAAA,EAAU2F,WAAWC,OAAO,CAACgG,EAAS/F,cCiJ1E,MAAegG,UAAkBhM,EAAA,EAAxC,c,oBAcC,qC,gDAA8C,IAAIiM,EAAA,EACjDC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMqQ,EAAA,EAAMC,KAAKxR,KAAKC,MAAO,CAAEwR,UAAW,CAAC,SAAW,CAACzR,KAAK0R,OAAOC,eAQpE,oC,gDAAgD,IAAIN,EAAA,EACnDC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMiQ,EAASK,KAAKxR,KAAKC,MAAO,CAAEwR,UAAW,CAAC,SAAW,CAACzR,KAAK2F,MAAMgM,eAQtE,2C,iDAAsB,GA+UvB,CA7UW,SAAAnM,GACTxF,KAAK0F,OAAOJ,KAAK,UAAW,OAAQ,QAEpCtF,KAAKsG,IAAI,UAAW,MACpBtG,KAAKsG,IAAI,YAAa,MACtBtG,KAAKsG,IAAI,YAAa,QACtBtG,KAAKsG,IAAI,eAAgB,WAEzBtG,KAAKqF,SAASC,KAAKtF,KAAK6F,kBAExB/F,MAAM0F,WACP,CAEU,YAAA0B,GACT,MAAMlG,EAAShB,KAAK6B,IAAI,UACpBb,GACHA,EAAO4Q,QAGR,MAAMC,EAAW7R,KAAK6B,IAAI,YACtBgQ,GACHA,EAASD,QAGV5R,KAAKmH,cAAe,CACrB,CAEU,eAAArB,CAAgBzD,GAIzB,GAHAvC,MAAMgG,gBAAgBzD,GACtBA,EAASkG,OAAO,eAAgB,CAAEuJ,KAAMzP,EAASR,IAAI,MAAOQ,SAAUA,IAE1C,MAAxBA,EAASR,IAAI,QAAiB,CACjC,IAAIb,EAAShB,KAAK6B,IAAI,UAClBb,GACHqB,EAASkG,OAAO,OAAQvH,EAAO+Q,O,CAIjC,GAAmC,MAA/B1P,EAASR,IAAI,eAAwB,CACxC,IAAIgQ,EAAW7R,KAAK6B,IAAI,YACpBgQ,GACHxP,EAASkG,OAAO,cAAesJ,EAASE,O,CAI1C,MAAMC,EAAOhS,KAAKiS,SAAS5P,GAC3BA,EAASkG,OAAO,OAAQyJ,GAExB,MAAME,EAAgBlS,KAAK6B,IAAI,iBAE/B,GAAIqQ,EAAe,CAClB,MAAMC,EAAc9P,EAAS8P,YACzBA,GACCA,EAAYD,IACflS,KAAKoS,KAAKlQ,OAAOmQ,KAAK,cAAc,KACnCrS,KAAKsS,gBAAgBjQ,EAAU,EAAE,G,CAKtC,CAKO,QAAA4P,CAAS5P,EAA+CkQ,GAE9D,MAAMP,EAAOhS,KAAK2F,MAAM6M,OAyCxB,OAvCAxS,KAAK2F,MAAML,KAAK0M,GAEZO,GACHP,EAAKS,OAAOF,GAGTlQ,EAASR,IAAI,YAChBmQ,EAAKS,OAAO,WAGbzS,KAAKqF,SAASC,KAAK0M,GACnBA,EAAKU,aAAarQ,GAClB2P,EAAKW,OAAS3S,KAEdgS,EAAK9P,OAAOC,GAAG,SAAUC,IACxB,MAAM4P,EAAO5P,EAAEE,OACf,GAA6B,YAAzB0P,EAAKnQ,IAAI,aAA4B,CACxC,MAAMQ,EAAW2P,EAAK3P,SAClBA,IACCA,EAASuQ,WACZ5S,KAAK6S,eAAexQ,GAGpBrC,KAAKsS,gBAAgBjQ,G,KAMzBA,EAASF,GAAG,QAAQ,KACnBnC,KAAK8S,iBAAiBzQ,EAAS,IAGhCA,EAASF,GAAG,eAAe,KAC1BnC,KAAK8S,iBAAiBzQ,EAAS,IAIhCA,EAASiE,IAAI,OAAQ0L,GACdA,CACR,CAEO,gBAAAc,CAAiBC,GAExB,CAKO,eAAA1J,CAAgBhH,GACtBvC,MAAMuJ,gBAAgBhH,GACtB,IAAI2P,EAAO3P,EAASR,IAAI,QACpBmQ,IACHhS,KAAK2F,MAAM4D,YAAYyI,GACvBA,EAAKxI,WAEN,IAAIwJ,EAAQ3Q,EAASR,IAAI,SACrBmR,IACHhT,KAAK0R,OAAOnI,YAAYyJ,GACxBA,EAAMxJ,UAER,CAKO,eAAA9C,CAAgBrE,EAA+CI,GACrE,IAAIG,EAAWP,EAASR,IAAI,iBACvBe,IACJA,EAAW,GACXP,EAASiE,IAAI,gBAAiB1D,IAE/BA,EAAS0C,KAAK7C,EACf,CAKO,eAAAgE,CAAgBpE,EAA+CI,GACrE,IAAIF,EAAWF,EAASR,IAAI,iBACvBU,IACJA,EAAW,GACXF,EAASiE,IAAI,gBAAiB/D,IAE/BA,EAAS+C,KAAK7C,EACf,CASa,YAAAgI,CAAapI,EAA+CqH,G,uHACxE,MAAMC,EAAW,CAAC,EAAMc,aAAY,UAACpI,EAAUqH,IACzC9D,EAAO5F,KAAK4F,KAElB,GAAIA,EAAM,CAET,MAAMoM,EAAO3P,EAASR,IAAI,QACtBmQ,GACHA,EAAKtH,OAGN,IAAIsI,EAAQ3Q,EAASR,IAAI,SAErBmR,GACHA,EAAMtI,KAAKhB,GAGZ,IAAIJ,EAAQjH,EAASR,IAAI,iBACrByH,GACH,OAAYA,GAAQ7G,IACnBmD,EAAK6E,aAAahI,EAAMiH,EAAS,IAInCJ,EAAQjH,EAASR,IAAI,iBACjByH,GACH,OAAYA,GAAQ7G,IACnBmD,EAAK6E,aAAahI,EAAMiH,EAAS,G,OAK9BC,CACP,G,CASa,YAAAF,CAAapH,EAA+CqH,G,uHACxE,MAAMC,EAAW,CAAC,EAAMF,aAAY,UAACpH,EAAUqH,IAEzC9D,EAAO5F,KAAK4F,KAElB,GAAIA,EAAM,CAET,MAAMoM,EAAO3P,EAASR,IAAI,QACtBmQ,GACHA,EAAK1H,OAGN,IAAI0I,EAAQ3Q,EAASR,IAAI,SAErBmR,GACHA,EAAM1I,KAAKZ,GAGZ,IAAIJ,EAAQjH,EAASR,IAAI,iBAErByH,GACH,OAAYA,GAAQ7G,IACnBmD,EAAK6D,aAAahH,EAAMiH,EAAS,IAInCJ,EAAQjH,EAASR,IAAI,iBAEjByH,GACH,OAAYA,GAAQ7G,IACnBmD,EAAK6D,aAAahH,EAAMiH,EAAS,G,OAI9BC,CACP,G,CASa,cAAAkJ,CAAexQ,EAA+CqH,G,uHAC1E,MAAMC,EAAW,CAAC,EAAMc,aAAY,UAACpI,EAAUqH,IACzC9D,EAAO5F,KAAK4F,KAElB,GAAIA,EAAM,CAET,MAAMoM,EAAO3P,EAASR,IAAI,QACtBmQ,GACHhS,KAAKoS,KAAKlQ,OAAOmQ,KAAK,cAAc,KACnCL,EAAK1L,IAAI,YAAY,EAAM,IAK7B,IAAI0M,EAAQ3Q,EAASR,IAAI,SAErBmR,GACHA,EAAM1M,IAAI,YAAY,GAGvB,IAAIgD,EAAQjH,EAASR,IAAI,iBACrByH,GACH,OAAYA,GAAQ7G,IACnBmD,EAAK6E,aAAahI,EAAMiH,EAAS,IAInCJ,EAAQjH,EAASR,IAAI,iBACjByH,GACH,OAAYA,GAAQ7G,IACnBmD,EAAK6E,aAAahI,EAAMiH,EAAS,G,OAK9BC,CACP,G,CASa,eAAA2I,CAAgBjQ,EAA+CqH,G,uHAC3E,MAAMC,EAAW,CAAC,EAAMF,aAAY,UAACpH,EAAUqH,IAEzC9D,EAAO5F,KAAK4F,KAElB,GAAIA,EAAM,CAET,MAAMoM,EAAO3P,EAASR,IAAI,QACtBmQ,GACHhS,KAAKoS,KAAKlQ,OAAOmQ,KAAK,cAAc,KACnCL,EAAK1L,IAAI,YAAY,EAAK,IAI5B,IAAI0M,EAAQ3Q,EAASR,IAAI,SAErBmR,GACHA,EAAM1M,IAAI,YAAY,GAGvB,IAAIgD,EAAQjH,EAASR,IAAI,iBAErByH,GACH,OAAYA,GAAQ7G,IACnBmD,EAAK6D,aAAahH,EAAMiH,EAAS,IAInCJ,EAAQjH,EAASR,IAAI,iBAEjByH,GACH,OAAYA,GAAQ7G,IACnBmD,EAAK6D,aAAahH,EAAMiH,EAAS,G,OAI9BC,CACP,G,EA9WA,qC,gDAAkC,cAClC,sC,gDAA0CvE,EAAA,EAAO8F,WAAWC,OAAO,CAACiG,EAAUhG,c,gCCrIxE,MAAM6H,WAAmB7B,EAAhC,c,oBASC,qC,gDAAoD,IAAIC,EAAA,EACvDC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMgS,EAAA,EAAY1B,KAAKxR,KAAKC,MAAO,CAAC,EAAG,CAACD,KAAK0R,OAAOC,eAWrD,mC,yDAEA,sC,gDAA4B,IAO5B,qC,gDAA8C,IAAIN,EAAA,EACjDC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMiS,EAAA,EAAM3B,KAAKxR,KAAKC,MAAO,CAAEwR,UAAW,CAAC,UAAY,CAACzR,KAAKoT,OAAOzB,eAOrE,yC,gDAA6B3R,KAAKoT,QAuFnC,CAlFQ,QAAAnB,CAAS5P,GACf,MAAM2P,EAAOlS,MAAMmS,SAAS5P,EAAU,SAEhCyL,EAAQkE,EAAK3M,SAASgO,YAAY,EAAGrT,KAAKoT,OAAOZ,QACvDnQ,EAASiE,IAAI,QAASwH,GACtBA,EAAMwF,SAAS,OAAQjR,EAASR,IAAI,SACpCiM,EAAMwF,SAAS,cAAejR,EAASR,IAAI,gBAE3C,MAAMmR,EAAQhT,KAAK0R,OAAOc,OA8B1B,OA7BAxS,KAAK0R,OAAOpM,KAAK0N,GACjBA,EAAMP,OAAO,QACbO,EAAMP,OAAO,SACbO,EAAMP,OAAO,QAEbT,EAAK3M,SAASC,KAAK0N,GACnB3Q,EAASiE,IAAI,QAAS0M,GAEtBhB,EAAK9P,OAAOC,GAAG,aAAcC,IAC5B,IAAI0I,EAAQ9K,KAAKuT,QAAQnR,EAAE0I,OAC3B,MAAMG,EAAQ,WAAe,CAAEjH,EAAG,EAAGF,EAAG,GAAKgH,GACzC9K,KAAK4F,OACR5F,KAAKwT,QAAUxT,KAAK4F,KAAK/D,IAAI,aAAc,GAAKoJ,E,IAIlD+G,EAAK9P,OAAOC,GAAG,WAAYC,IAC1B,IAAI0I,EAAQ9K,KAAKuT,QAAQnR,EAAE0I,OAC3B,MAAMG,EAAQ,WAAe,CAAEjH,EAAG,EAAGF,EAAG,GAAKgH,GAE7CkH,EAAK1R,OAAO,CAAE0D,EAAG,EAAGF,EAAG,IACnB9D,KAAK4F,MACR5F,KAAK4F,KAAKU,IAAI,aAAc2E,EAAQjL,KAAKwT,Q,IAI3CR,EAAMN,aAAarQ,GACnByL,EAAM4E,aAAarQ,GAEZ2P,CACR,CAEO,eAAArH,CAAgBC,GACtB,MAAMC,EAASD,EAAO/I,IAAI,UAC1B,GAAIgJ,EAAQ,CACX,MAAMxI,EAAWwI,EAAOxI,SACxB,GAAIA,EAAU,CACb,MAAMwI,EAASD,EAAO/I,IAAI,UAC1B,GAAIgJ,EAAQ,CACX,MAAMiD,EAAQzL,EAASR,IAAI,SACrB4R,EAAY7I,EAAO/I,IAAI,YAAa,IACpC6R,EAAY9I,EAAO/I,IAAI,YAAa,IAC1C,GAAIiM,EAAO,CACV,MAAM5J,EAAS4J,EAAMjM,IAAI,SAAU,GAC7B8R,EAAc7F,EAAMjM,IAAI,cAAe,GACvC+R,EAAeD,GAAezP,EAASyP,GAAeD,EACtDzI,EAAQ6C,EAAMjM,IAAI,aAAc,GAAKiM,EAAMjM,IAAI,MAAO,GAAK4R,EACjE5I,EAAOvK,OAAO,CAAE0D,EAAG4P,EAAe,MAAU3I,GAAQnH,EAAG8P,EAAe,MAAU3I,I,IAKrF,CAEO,gBAAA6H,CAAiBzQ,GACvB,MAAMyL,EAAQzL,EAASR,IAAI,SACxBiM,IACFA,EAAMxH,IAAI,OAAQjE,EAASR,IAAI,SAC/BiM,EAAMxH,IAAI,cAAejE,EAASR,IAAI,gBAExC,CAKO,eAAAwH,CAAgBhH,GACtBvC,MAAMuJ,gBAAgBhH,GACtB,IAAIyL,EAAQzL,EAASR,IAAI,SACrBiM,IACH9N,KAAKoT,OAAO7J,YAAYuE,GACxBA,EAAMtE,UAER,EA7HA,sC,gDAAkC,eAClC,uC,gDAA0C4H,EAAUlG,WAAWC,OAAO,CAAC8H,GAAW7H,c,eCH5E,MAAeyI,WAAiBC,GAAA,EAAvC,c,oBAEC,qC,yDAUA,4C,yDACA,8C,wDAwBD,CAtBQ,QAAAC,GAEN,GADAjU,MAAMiU,WACF/T,KAAKgU,QAAQ,aAAc,CAC9B,MAAMrB,EAAS3S,KAAK2S,OACdtQ,EAAWrC,KAAKqC,SAClBsQ,GAAUtQ,GACbsQ,EAAO1L,iBAAiB5E,E,CAG3B,CAIO,gBAAA4R,GACN,IAAIC,EAAWlU,KAAK6B,IAAI,YACpBsS,EAAW,GAKf,OAJID,aAAoBE,EAAA,KACvBD,EAAWD,EAASpM,OAGd9H,KAAK+K,SAASoJ,EACtB,EA7BA,sC,gDAAkC,aAClC,uC,gDAA0CL,GAAA,EAAS5I,WAAWC,OAAO,CAAC0I,GAASzI,cCRzE,MAAMiJ,WAAkBR,GAA/B,c,oBAEC,kC,yDACA,kC,yDAEA,oC,wDA4BD,CAhBQ,QAAA9I,CAAS4F,GACf,GAAI3Q,KAAKsU,KAAOtU,KAAKuU,IAAK,CACzB,GAAmB,SAAfvU,KAAKwU,MAAkB,CAC1B,IAAIC,EAAI,iBAAqBzU,KAAKsU,IAAKtU,KAAKuU,IAAK5D,GACjD,MAAO,CAAE3M,EAAGyQ,EAAEzQ,EAAGF,EAAG2Q,EAAE3Q,EAAGmH,MAAO,WAAejL,KAAKsU,IAAKtU,KAAKuU,K,CAE1D,CACJ,IAAIG,EAAK,2BAA+B1U,KAAKsU,IAAKtU,KAAKuU,IAAK,CAAEvQ,EAAG,EAAGF,EAAG,GAAKqG,KAAKC,IAAI,EAAGuG,EAAW,MAC/FgE,EAAK,2BAA+B3U,KAAKsU,IAAKtU,KAAKuU,IAAK,CAAEvQ,EAAG,EAAGF,EAAG,GAAKqG,KAAKyK,IAAI,EAAGjE,EAAW,MAC/F8D,EAAI,2BAA+BzU,KAAKsU,IAAKtU,KAAKuU,IAAK,CAAEvQ,EAAG,EAAGF,EAAG,GAAK6M,GAE3E,MAAO,CAAE3M,EAAGyQ,EAAEzQ,EAAGF,EAAG2Q,EAAE3Q,EAAGmH,MAAO,WAAeyJ,EAAIC,G,EAGrD,MAAO,CAAE3Q,EAAG,EAAGF,EAAG,EAAGmH,MAAO,EAC7B,EAtBA,sC,gDAAkC,cAClC,uC,gDAA0C4I,GAAS3I,WAAWC,OAAO,CAACkJ,GAAUjJ,c,eCwC1E,MAAMyJ,WAAc1P,EAA3B,c,oBAUC,oC,gDAAiD,IAAIkM,EAAA,EACpDC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMmT,GAAU7C,KAAKxR,KAAKC,MAAO,CAAEwR,UAAW,CAAC,OAAQ,UAAY,CAACzR,KAAKsJ,MAAMqI,eAQhF,oC,gDAAoC3R,KAAKqF,SAASC,KAAK2N,GAAW/R,IAAIlB,KAAKC,MAAO,CAAC,MAOnF,uC,gDbpGQ,GAAM,GAAO,KaqGrB,2C,gDAA4L,KAC5L,sC,gDAAiB,KAuKlB,CArKW,SAAAuF,GACTxF,KAAK8U,UAAUrD,UAAY,aAAiBzR,KAAK8U,UAAUrD,UAAW,CAAC,UACvEzR,KAAK+U,eAAezU,OAAO,CAAE0D,EAAG,KAAKF,EAAG,OACxC9D,KAAK6F,iBAAiBvF,OAAO,CAAE0D,EAAG,KAAKF,EAAG,OAC1ChE,MAAM0F,WACP,CAEU,UAAAwP,CAAWxG,GACpBA,EAAOpK,YAAY6J,GACXA,EAAE7J,WAAapE,KAAK6B,IAAI,aAAc,GAAK,UAAgBsI,KAAKsB,GAAK,IAG7E+C,EAAOf,UAAUQ,GACTA,EAAER,SAAWzN,KAAK6B,IAAI,aAAc,GAAK,UAAgBsI,KAAKsB,GAAK,GAE5E,CAKO,QAAA9E,CAAStE,GACf,MAAMI,EAAOzC,KAAK+U,eAAe1P,SAASC,KAAKtF,KAAKsJ,MAAMkJ,QAM1D,OALAxS,KAAKsJ,MAAMhE,KAAK7C,GAChBA,EAAKiQ,aAAarQ,GAClBI,EAAK6D,IAAI,SAAUjE,EAASR,IAAI,WAChCY,EAAK6D,IAAI,SAAUjE,EAASR,IAAI,WAChCY,EAAKkQ,OAAS3S,KACPyC,CACR,CAEU,WAAAwS,GACT,MAAMtI,EAAqB,GA2B3B,OA1BA,OAAY3M,KAAK2F,MAAMkB,WAAYZ,IAClC,MAAMiP,EAAkB,GACxBvI,EAAOrH,KAAK4P,GACZ,IAAI3S,EAAW0D,EAAepE,IAAI,iBAElC,OAAY7B,KAAK2F,MAAMkB,WAAYL,IAClC,IAAIsB,EAAQ,EACRvF,GACH,OAAYA,GAAW4S,IAClBA,EAAatT,IAAI,YAAc2E,IAClCsB,EAAQqN,EAAatT,IAAI,iBAG1B,IAAI2F,EAAWxH,KAAKoV,WAAW,WAAY,GACvCtU,EAAUd,KAAK6B,IAAI,UAAW,GAC/BiG,EAAQ,GAAKhH,EAAU,GACtBgH,EAAQN,EAAW1G,IACrBgH,EAAQN,EAAW1G,E,IAMvBoU,EAAM5P,KAAKwC,EAAM,GAChB,IAEI6E,CACR,CAEO,gBAAAvF,GACNtH,MAAMsH,mBAENpH,KAAKgV,WAAWhV,KAAKqV,SAErB,IAAIC,GAAe,EAEnB,GAAItV,KAAKyH,cAAgBzH,KAAKuV,YAAcvV,KAAKgU,QAAQ,aAAehU,KAAKgU,QAAQ,cAAe,CAEnG,MAAMrH,EAAS3M,KAAKiV,cAEpBjV,KAAKwV,SAASrR,SAASnE,KAAK6B,IAAI,WAAY,GAAK,WACjD,MAAMwC,EAAOrE,KAAK6B,IAAI,QAET,cAATwC,EACHrE,KAAKwV,SAASjJ,WAAWyD,GAER,eAAT3L,GACRrE,KAAKwV,SAASjJ,WAAW2D,GAS1BlQ,KAAKyV,aAAezV,KAAKwV,SAAS7I,GAElC2I,GAAe,C,CAGhB,GAAIA,GAAgBtV,KAAKgU,QAAQ,WAAahU,KAAKgU,QAAQ,aAAc,CACxE,IAAI9P,EAAS,mBAAuBlE,KAAK6B,IAAI,SAAU,GAAIsI,KAAKyK,IAAI5U,KAAK0V,aAAc1V,KAAK2V,gBAAkB,EAE1G7J,EAAI,EAER,MAAM8J,EAAkB5V,KAAK6B,IAAI,aAAc,GACzCuB,EAAYpD,KAAK6B,IAAI,YAAa,GAExC,OAAY7B,KAAK2F,MAAMkB,WAAYxE,IAClC,MAAMyL,EAAQzL,EAASR,IAAI,SACrBgU,EAAc7V,KAAKyV,aAAqBxI,OAAOnB,GAC/C1H,EAAayR,EAAWzR,WAAa,UAAgBwR,EACrDnI,EAAWoI,EAAWpI,SAAW,UAAgBmI,EACvD9H,EAAMxN,OAAO,CAAE4D,OAAQA,EAAQyP,YAAazP,EAASd,EAAWgB,WAAYA,EAAsBqL,IAAKhC,EAAWrJ,IAElH,MAAM4O,EAAQ3Q,EAASR,IAAI,SAC3BmR,EAAM1S,OAAO,CAAEwV,WAAY1R,GAAcqJ,EAAWrJ,GAAc,IAClE4O,EAAM+C,WAAW,SAAU7R,GAC3B8O,EAAM+C,WAAW,cAAe,IAChCjK,GAAG,IAGJ,MAAMkK,EAAa9R,EAASlE,KAAK6B,IAAI,YAAa,GAElD,OAAY7B,KAAKyV,cAAe/I,IAE/B,IAAIrK,EAAWrC,KAAKgH,cAAc0F,EAAMpE,OAAOkF,MAAQ,IAAMd,EAAMpK,OAAOkL,OAM1E,GAJKnL,IACJA,EAAWrC,KAAKgH,cAAc0F,EAAMpK,OAAOkL,MAAQ,IAAMd,EAAMpE,OAAOkF,QAGnEnL,EAAU,CACb,MAAMI,EAAOJ,EAASR,IAAI,QAC1B7B,KAAKiW,eAAexT,EAAMuT,EAAYtJ,GACtC1M,KAAKkW,YAAYlW,KAAKqV,QAAS5S,EAAMuT,EAAYtJ,E,KAIrD,CAEU,cAAAuJ,CAAexT,EAAiBuT,EAAoBG,GAC7D,MAAM7N,EAAS6N,EAAgB7N,OACzBhG,EAAS6T,EAAgB7T,OAEzBsT,EAAkB5V,KAAK6B,IAAI,aAAc,GAAK,UAEpD,GAAIyG,GAAUhG,EAAQ,CACrB,MAAM8T,EAAc9N,EAAOlE,WAErBiS,EAASD,GADG9N,EAAOmF,SACiB2I,GAAe,EAAIR,EAEvDU,EAAchU,EAAO8B,WAErBmS,EAASD,GADGhU,EAAOmL,SACiB6I,GAAe,EAAIV,EAE7DnT,EAAK6R,IAAM,CAAEtQ,EAAGgS,EAAa7L,KAAKmB,IAAI+K,GAASvS,EAAGkS,EAAa7L,KAAKoB,IAAI8K,IACxE5T,EAAK8R,IAAM,CAAEvQ,EAAGgS,EAAa7L,KAAKmB,IAAIiL,GAASzS,EAAGkS,EAAa7L,KAAKoB,IAAIgL,G,CAE1E,CAEU,WAAAL,CAAY1H,EAAsD/L,EAAiBuT,EAAoBG,GAC5GA,IACH3H,EAAOjK,aAAa,mBAAuB9B,EAAKZ,IAAI,eAAgB,MAAOmU,IAC3ExH,EAAOhK,aAAa,mBAAuB/B,EAAKZ,IAAI,eAAgB,MAAOmU,IAE3EvT,EAAK6D,IAAI,QAASkQ,IACjBhI,EAAOC,QAAQ+H,GACfhI,EAAO2H,EAAgB,IAG1B,EAjMA,sC,gDAAkC,UAClC,uC,gDAA0ChR,EAAK+F,WAAWC,OAAO,CAAC0J,GAAMzJ,cCnElE,MAAMqL,WAA0BpC,GAW5B,SAAA7O,GACTxF,KAAK8U,UAAUrD,UAAY,aAAiBzR,KAAK8U,UAAUrD,UAAW,CAAC,QAAS,OAAQ,aAExF3R,MAAM0F,WACP,EAXA,sC,gDAAkC,sBAClC,uC,gDAA0C6O,GAAUnJ,WAAWC,OAAO,CAACsL,GAAkBrL,cCSnF,MAAMsL,WAAsB7B,GAAnC,c,oBAUC,uC,gDfzBQ,GAAM,GAAM,Ke0BpB,2C,gDAAyE9E,MAOzE,oC,gDAAyD,IAAIsB,EAAA,EAC5DC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMuV,GAAkBjF,KAAKxR,KAAKC,MAAO,CAAEwR,UAAW,CAAC,OAAQ,UAAY,CAACzR,KAAKsJ,MAAMqI,cAsCzF,CAjCQ,QAAAhL,CAAStE,GACf,MAAMI,EAAOzC,KAAK+U,eAAe1P,SAASC,KAAKtF,KAAKsJ,MAAMkJ,QAO1D,OANA/P,EAAKiQ,aAAarQ,GAClBI,EAAK6D,IAAI,SAAUjE,EAASR,IAAI,WAChCY,EAAK6D,IAAI,SAAUjE,EAASR,IAAI,WAEhCY,EAAKkQ,OAAS3S,KAEPyC,CACR,CAEU,SAAA+C,GACTxF,KAAK8U,UAAUrD,UAAY,aAAiBzR,KAAK8U,UAAUrD,UAAW,CAAC,aACvE3R,MAAM0F,YACNxF,KAAK2W,cAAc,iBACpB,CAEO,gBAAAvP,GACN,MAAM9C,EAAiB,iBACvB,GAAItE,KAAKgU,QAAQ1P,GAAiB,CACjC,MAAMK,EAAa3E,KAAK6B,IAAIyC,GAC5B,GAAkB,MAAdK,EACH3E,KAAKqV,QAAU,QAEX,CACJ,IAAI7G,EAASuB,IACbvB,EAAO7J,WAAWA,GAClB3E,KAAKqV,QAAU7G,C,EAIjB1O,MAAMsH,kBACP,EAvDA,sC,gDAAkC,kBAClC,uC,gDAA0CyN,GAAM3J,WAAWC,OAAO,CAACuL,GAActL,cCH3E,MAAMwL,WAAuB/B,GASzB,SAAArP,GACTxF,KAAK8U,UAAUrD,UAAY,aAAiBzR,KAAK8U,UAAUrD,UAAW,CAAC,QAAS,UAEhF3R,MAAM0F,WACP,CAEU,WAAAyP,GACT,MAAMtI,EAAqB,GAe3B,OAdA,OAAY3M,KAAK2F,MAAMkB,WAAYZ,IAClC,MAAMiP,EAAkB,GACxBvI,EAAOrH,KAAK4P,GAEZ,OAAYlV,KAAK2F,MAAMkB,WAAYL,IAClC,IAAIsB,EAAQ,EAER7B,IAAmBO,IACtBsB,EAAQ,GAGToN,EAAM5P,KAAKwC,EAAM,GAChB,IAEI6E,CACR,CAEU,WAAAuJ,CAAYb,EAAuD5S,EAAiBoU,EAAqBV,GAElH,GADA1T,EAAK+R,MAAQxU,KAAK6B,IAAI,YAClBsU,EAAiB,CACpB,MAAMvR,EAAW5E,KAAK6B,IAAI,YAE1BY,EAAK6D,IAAI,QAASkQ,IACjB,IAAI9B,EAAKjS,EAAK6R,IACVK,EAAKlS,EAAK8R,IACVG,GAAMC,IACT6B,EAAQhH,OAAOkF,EAAG1Q,EAAG0Q,EAAG5Q,GACR,QAAZc,EACH4R,EAAQ3G,OAAO8E,EAAG3Q,EAAG2Q,EAAG7Q,GAGxB0S,EAAQ5G,iBAAiB,EAAG,EAAG+E,EAAG3Q,EAAG2Q,EAAG7Q,G,IAK7C,CAEU,cAAAmS,CAAexT,EAAiBuT,EAAoBc,GAC7D,MAAMxO,EAAS7F,EAAKZ,IAAI,UAClBS,EAASG,EAAKZ,IAAI,UAExB,GAAIyG,GAAUhG,EAAQ,CAErB,MAAMyU,EAAczO,EAAOzG,IAAI,SACzBmV,EAAc1U,EAAOT,IAAI,SAIzBwU,EAFcU,EAAYlV,IAAI,aAAc,GACrCkV,EAAYlV,IAAI,MAAO,GACA,EAI9B0U,EAFcS,EAAYnV,IAAI,aAAc,GACrCmV,EAAYnV,IAAI,MAAO,GACA,EAEpCY,EAAK6R,IAAM,CAAEtQ,EAAGgS,EAAa,MAAUK,GAASvS,EAAGkS,EAAa,MAAUK,IAC1E5T,EAAK8R,IAAM,CAAEvQ,EAAGgS,EAAa,MAAUO,GAASzS,EAAGkS,EAAa,MAAUO,G,CAE5E,EAzEA,sC,gDAAkC,mBAClC,uC,gDAA0C1B,GAAM3J,WAAWC,OAAO,CAACyL,GAAexL,c,eChB5E,MAAM6L,WAAoB7F,EAAjC,c,oBAcC,yC,gDAA6D,IAAIC,EAAA,EAChEC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMgW,GAAA,EAAiB1F,KAAKxR,KAAKC,MAAO,CAAEwR,UAAW,CAAC,UAAY,CAACzR,KAAKmX,WAAWxF,eAMpF,mC,wDAwFD,CAnFQ,QAAAM,CAAS5P,GACf,MAAMuD,EAAO5F,KAAK4F,KAEZoM,EAAOlS,MAAMmS,SAAS5P,EAAU,UAEhCQ,EAAYmP,EAAK3M,SAASgO,YAAY,EAAGrT,KAAKmX,WAAW3E,QAC/DxS,KAAKmX,WAAW7R,KAAKzC,GACrBA,EAAUyQ,SAAS,OAAQjR,EAASR,IAAI,SACxCgB,EAAUyQ,SAAS,cAAejR,EAASR,IAAI,gBAE/CQ,EAASiE,IAAI,YAAazD,GAE1BmP,EAAK9P,OAAOC,GAAG,WAAW,KACzB,MAAMwF,EAAgBqK,EAAK3P,SAA4CR,IAAI,gBACvE8F,GACC/B,IAC4B,cAA3BA,EAAK/D,IAAI,gBACZ8F,EAAa0F,GAAK2E,EAAKhO,IACvB2D,EAAayP,GAAKpF,EAAKlO,MAGvB6D,EAAa0F,GAAK2E,EAAKlO,IACvB6D,EAAayP,GAAKpF,EAAKhO,KAGxB4B,EAAKyR,e,IAKR,MAAMrE,EAAQhT,KAAK0R,OAAOc,OAY1B,OAXAxS,KAAK0R,OAAOpM,KAAK0N,GAEbpN,GACHoN,EAAMP,OAAO7M,EAAK/D,IAAI,cAAe,KAEtCmQ,EAAK3M,SAASC,KAAK0N,GACnB3Q,EAASiE,IAAI,QAAS0M,GAEtBA,EAAMN,aAAarQ,GACnBQ,EAAU6P,aAAarQ,GAEhB2P,CACR,CAEO,eAAArH,CAAgBC,GACtB,MAAMC,EAASD,EAAO/I,IAAI,UAC1B,GAAIgJ,EAAQ,CACX,MAAMxI,EAAWwI,EAAOxI,SACxB,GAAIA,EAAU,CACb,MAAMwI,EAASD,EAAO/I,IAAI,UAC1B,GAAIgJ,EAAQ,CACX,MAAMhI,EAAYR,EAASR,IAAI,aACzBmQ,EAAO3P,EAASR,IAAI,QACpB4R,EAAY7I,EAAO/I,IAAI,YAAa,IACpC6R,EAAY9I,EAAO/I,IAAI,YAAa,IACtCgB,GACHgI,EAAOvK,OAAO,CAAE0D,EAAGgO,EAAKhO,IAAMnB,EAAUtC,QAAUkT,EAAW3P,EAAGkO,EAAKlO,IAAMjB,EAAUrC,SAAWkT,G,GAKrG,CAKO,eAAArK,CAAgBhH,GACtBvC,MAAMuJ,gBAAgBhH,GACtB,IAAIQ,EAAYR,EAASR,IAAI,aACzBgB,IACH7C,KAAKmX,WAAW5N,YAAY1G,GAC5BA,EAAU2G,UAEZ,CAEO,gBAAAsJ,CAAiBzQ,GACvB,MAAMQ,EAAYR,EAASR,IAAI,aAC5BgB,IACFA,EAAUyD,IAAI,OAAQjE,EAASR,IAAI,SACnCgB,EAAUyD,IAAI,cAAejE,EAASR,IAAI,gBAE5C,EA5GA,sC,gDAAkC,gBAClC,uC,gDAA0CuP,EAAUlG,WAAWC,OAAO,CAAC8L,GAAY7L,cCsB7E,MAAMkM,WAAmBzD,GAAhC,c,oBAWC,uC,gDAAqC0D,SAASC,gBAAgB,6BAA8B,UAC5F,2C,gDAAiC,GAyRlC,CAtRQ,cAAAC,GAGN,GAFA3X,MAAM2X,iBAEFzX,KAAKgU,QAAQ,UAAW,CAC3B,MAAM1L,EAAStI,KAAK6B,IAAI,UACxB,GAAIyG,EAAQ,CACX,MAAMoP,EAAapP,EAAOzG,IAAI,QAC9B7B,KAAK2X,WAAWrS,KAAKoS,EAAWxV,OAAOC,GAAG,mBAAmB,KAC5DnC,KAAK4X,WAAW,I,EAInB,GAAI5X,KAAKgU,QAAQ,UAAW,CAC3B,MAAM1R,EAAStC,KAAK6B,IAAI,UACxB,GAAIS,EAAQ,CACX,MAAMuV,EAAavV,EAAOT,IAAI,QAC9B7B,KAAK2X,WAAWrS,KAAKuS,EAAW3V,OAAOC,GAAG,mBAAmB,KAC5DnC,KAAK4X,WAAW,I,EAKnB,GAAI5X,KAAK8X,eAAe,eAAgB,CACvC,MAAMnF,EAAS3S,KAAK2S,OACdtQ,EAAWrC,KAAKqC,SAClBA,GAAYsQ,GACfA,EAAO1L,iBAAiB5E,E,CAI1B,MAAMC,EAAStC,KAAK6B,IAAI,UAClByG,EAAStI,KAAK6B,IAAI,UAExB,IAAI6V,EACAG,EAEJ,GAAIvP,GAAUhG,EAAQ,CACrBtC,KAAK+X,QAAS,EACdL,EAAapP,EAAOzG,IAAI,QACxBgW,EAAavV,EAAOT,IAAI,QAExB,IAAIwL,EAAK,EACL2K,EAAK,EACLZ,EAAK,EACLa,EAAK,EAELC,EAAM,EACNC,EAAM,EAENC,EAAM,EACNC,EAAM,EAENC,EAAM,EACNC,EAAM,EAENC,EAAM,EACNC,EAAM,EAENC,EAAM,EACNC,EAAM,EAENC,EAAM,EACNC,EAAM,EAENxC,EAAS,EACTE,EAAS,EAGb,MAAMlU,EAAWrC,KAAKqC,SACtB,GAAIA,EAAU,CACb,MAAMgG,EAAehG,EAASR,IAAI,gBAClC,GAAIwG,EAAc,CAEjB,IAAIyQ,EAAIzQ,EAAa9H,OAAS,EAE1ByC,EAAchD,KAAKoV,WAAW,eAEf,YAAfpS,GACC0U,IACHN,EAAKM,EAAW5T,IAAM4T,EAAW7V,IAAI,KAAM,IAExCgW,IACHI,EAAKJ,EAAW/T,IAAM+T,EAAWhW,IAAI,KAAM,IAG5CwU,EAAS,GACTE,EAAS,GAETlJ,EAAKhF,EAAa+O,IAAM,EACxBY,EAAK3P,EAAa4P,IAAM,EAExB5K,GAAMqK,EAAW7V,IAAI,KAAM,GAC3BmW,GAAMH,EAAWhW,IAAI,KAAM,GAEvBoW,EAAKb,KACP/J,EAAI2K,GAAM,CAACA,EAAI3K,IACf+J,EAAIa,GAAM,CAACA,EAAIb,IAGb9O,EAAOzG,IAAI,aACdwL,EAAK2K,EACLZ,IAAWa,EAAKb,GAAM,GAGnB9U,EAAOT,IAAI,aACdmW,EAAK3K,EACL4K,EAAKb,GAAMa,EAAKb,GAAM,GAIvBc,EAAM7K,EAAKyL,EAAI,EACfX,EAAMf,EAENgB,EAAMJ,EAAKc,EAAI,EACfT,EAAMJ,EAENK,EAAMjL,EAAKyL,EAAI,EACfP,EAAMP,EAAKc,EAAI,EAEfN,EAAMpB,EACNqB,EAAMR,EAENS,EAAMrL,EACNsL,EAAMX,EAENY,EAAMxB,EACNyB,EAAMZ,IAGFP,IACHrK,EAAKqK,EAAW1T,IAAM0T,EAAW7V,IAAI,KAAM,IAExCgW,IACHG,EAAKH,EAAW7T,IAAM6T,EAAWhW,IAAI,KAAM,IAG5CuV,EAAK/O,EAAa+O,IAAM,EACxBa,EAAK5P,EAAa4P,IAAM,EAExBb,GAAMM,EAAW7V,IAAI,KAAM,GAC3BoW,GAAMJ,EAAWhW,IAAI,KAAM,GAEvBmW,EAAK3K,KACPA,EAAI2K,GAAM,CAACA,EAAI3K,IACf+J,EAAIa,GAAM,CAACA,EAAIb,IAGb9O,EAAOzG,IAAI,aACduV,EAAKa,EACL5K,IAAW2K,EAAK3K,GAAM,GAGnB/K,EAAOT,IAAI,aACdoW,EAAKb,EACLY,EAAK3K,GAAM2K,EAAK3K,GAAM,GAGvB6K,EAAM7K,EACN8K,EAAMf,EAAK0B,EAAI,EAEfV,EAAMJ,EACNK,EAAMJ,EAAKa,EAAI,EAEfR,EAAMjL,EACNkL,EAAMP,EAENQ,EAAMpB,EAAK0B,EAAI,EACfL,EAAMR,EAAKa,EAAI,EAEfJ,EAAMrL,EACNsL,EAAMX,EAENY,EAAMxB,EACNyB,EAAMZ,GAGH,QAAYC,EAAK,IAAM,QAAYE,EAAK,KAC3CA,GAAO,KAGJ,QAAYD,EAAK,IAAM,QAAYE,EAAK,KAC3CA,GAAO,KAGJ,QAAYC,EAAK,IAAM,QAAYC,EAAK,KAC3CA,GAAO,KAGJ,QAAYC,EAAK,IAAM,QAAYC,EAAK,KAC3CA,GAAO,KAGR,IAAIM,EAAM/Y,KAAK6B,IAAI,uBAAwB,IAC3CkX,EAAM5O,KAAKyK,IAAI,MAAQmE,GAEvB,IAAIC,EAAOd,GAAOE,EAAMF,GAAOa,EAAM,MAAU1C,GAC3C4C,EAAOd,GAAOE,EAAMF,GAAOY,EAAM,MAAU1C,GAE3C6C,EAAOd,GAAOA,EAAMF,GAAOa,EAAM,MAAUxC,GAC3C4C,EAAOd,GAAOA,EAAMF,GAAOY,EAAM,MAAUxC,GAE3C6C,EAAOV,GAAOC,EAAMD,GAAOK,EAAM,MAAU1C,GAC3CgD,EAAOT,GAAOC,EAAMD,GAAOG,EAAM,MAAU1C,GAE3CiD,EAAOX,GAAOA,EAAMD,GAAOK,EAAM,MAAUxC,GAC3CgD,EAAOV,GAAOA,EAAMD,GAAOG,EAAM,MAAUxC,GAE3CtL,EAAQ,WAAe,CAAEjH,EAAGgV,EAAMlV,EAAGmV,GAAQ,CAAEjV,EAAGkV,EAAMpV,EAAGqV,IAE3DvM,GAAMkM,EAAI,MAAU7N,GAAS6N,GAAK,MAAU7N,GAAS,MAAUoL,GAC/DmD,GAAMV,EAAI,MAAU7N,GAAS6N,GAAK,MAAU7N,GAAS,MAAUoL,GAE/DoD,GAAQ7M,EAAK,EAAI0L,GAAOC,EAAMD,GAAOS,EAAM,MAAU1C,GACrDqD,GAAQF,EAAK,EAAIhB,GAAOC,EAAMD,GAAOO,EAAM,MAAU1C,GAErDsD,GAAQ/M,EAAK,EAAI2L,GAAOA,EAAMD,GAAOS,EAAM,MAAUxC,GACrDqD,GAAQJ,EAAK,EAAIf,GAAOA,EAAMD,GAAOO,EAAM,MAAUxC,GAEzDyC,GAAQpM,EAAK,EACbqM,GAAQO,EAAK,EAEbN,GAAQtM,EAAK,EACbuM,GAAQK,EAAK,EAEM,YAAfxW,GACHiW,EAAO9O,KAAKyK,IAAIyD,EAAKlO,KAAKC,IAAI+N,EAAM,EAAGc,IACvCS,EAAOvP,KAAKyK,IAAI6D,EAAKtO,KAAKC,IAAIoO,EAAM,EAAGkB,IAEvCP,EAAOhP,KAAKC,IAAI+N,EAAKhO,KAAKyK,IAAIyD,EAAM,EAAGc,IACvCS,EAAOzP,KAAKC,IAAIoO,EAAKrO,KAAKyK,IAAI6D,EAAM,EAAGmB,MAGvCZ,EAAO7O,KAAKyK,IAAIwD,EAAKjO,KAAKC,IAAI8N,EAAM,EAAGc,IACvCS,EAAOtP,KAAKyK,IAAI2D,EAAKpO,KAAKC,IAAIkO,EAAM,EAAGmB,IAEvCP,EAAO/O,KAAKC,IAAI8N,EAAK/N,KAAKyK,IAAIwD,EAAM,EAAGc,IACvCS,EAAOxP,KAAKC,IAAIkO,EAAKnO,KAAKyK,IAAI2D,EAAM,EAAGoB,KAGxC,IAAIE,EAAU,CAAC,CAAC3B,EAAKC,EAAKG,EAAKE,GAAM,CAACQ,EAAMC,EAAMQ,EAAMC,GAAO,CAACR,EAAMC,EAAMQ,EAAMC,GAAO,CAACxB,EAAKC,EAAKE,EAAKE,IAEzGzY,KAAKsG,IAAI,QAASkQ,IACjB,MAAM7D,EAAS3S,KAAK2S,OACpBA,EAAOmH,eAAerL,QAAQ+H,GAC9B7D,EAAOmH,eAAeD,EAA8B,IAGrD,IAAIE,EAAgB,CAAC,CAACrB,EAAKE,GAAM,CAACQ,EAAMC,GAAO,CAACC,EAAMC,GAAO,CAACZ,EAAKE,IAEnE,MAAMtJ,EAAOvP,KAAK2S,OAAQqH,iBAAiBD,GAEvCxK,IACHvP,KAAKia,SAASC,aAAa,IAAK3K,GAChCvP,KAAKma,aAAena,KAAKia,SAASG,iB,GAKlCpa,KAAK2S,QAAU3S,KAAKqC,UACvBrC,KAAK2S,OAAO0H,iBAAiBra,KAAKqC,SAEpC,CAEO,QAAA0I,CAAS4F,GACf,GAAI3Q,KAAKia,UACJja,KAAKia,SAASK,aAAa,KAAM,CACpC,IAAI5F,EAAK1U,KAAKia,SAASM,iBAAiB5J,EAAW3Q,KAAKma,aAAe,IACnExF,EAAK3U,KAAKia,SAASM,iBAAiB5J,EAAW3Q,KAAKma,aAAe,IACnE1F,EAAIzU,KAAKwa,SAASxa,KAAKia,SAASM,iBAAiB5J,EAAW3Q,KAAKma,eACrE,MAAO,CAAEnW,EAAGyQ,EAAEzQ,EAAGF,EAAG2Q,EAAE3Q,EAAGmH,MAAO,WAAeyJ,EAAIC,G,CAGrD,MAAO,CAAE3Q,EAAG,EAAGF,EAAG,EAAGmH,MAAO,EAC7B,CAEO,gBAAAgJ,GACN,OAAOjU,KAAKuT,QAAQzT,MAAMmU,mBAC3B,EAhSA,sC,gDAAkC,eAClC,uC,gDAA0CJ,GAAS3I,WAAWC,OAAO,CAACmM,GAAWlM,c,+CCvDnE,SAASqP,GAAI9M,EAAQ+M,GAClC,IAAID,EAAM,EACV,QAAgBtR,IAAZuR,EACF,IAAK,IAAI5S,KAAS6F,GACZ7F,GAASA,KACX2S,GAAO3S,OAGN,CACL,IAAI0F,GAAS,EACb,IAAK,IAAI1F,KAAS6F,GACZ7F,GAAS4S,EAAQ5S,IAAS0F,EAAOG,MACnC8M,GAAO3S,EAGb,CACA,OAAO2S,CACT,CCjBe,SAAS,GAAI9M,EAAQ+M,GAClC,IAAItQ,EACJ,QAAgBjB,IAAZuR,EACF,IAAK,MAAM5S,KAAS6F,EACL,MAAT7F,IACIsC,EAAMtC,QAAkBqB,IAARiB,GAAqBtC,GAASA,KACpDsC,EAAMtC,OAGL,CACL,IAAI0F,GAAS,EACb,IAAK,IAAI1F,KAAS6F,EACiC,OAA5C7F,EAAQ4S,EAAQ5S,IAAS0F,EAAOG,MAC7BvD,EAAMtC,QAAkBqB,IAARiB,GAAqBtC,GAASA,KACpDsC,EAAMtC,EAGZ,CACA,OAAOsC,CACT,CCnBe,SAAS,GAAIuD,EAAQ+M,GAClC,IAAI9F,EACJ,QAAgBzL,IAAZuR,EACF,IAAK,MAAM5S,KAAS6F,EACL,MAAT7F,IACI8M,EAAM9M,QAAkBqB,IAARyL,GAAqB9M,GAASA,KACpD8M,EAAM9M,OAGL,CACL,IAAI0F,GAAS,EACb,IAAK,IAAI1F,KAAS6F,EACiC,OAA5C7F,EAAQ4S,EAAQ5S,IAAS0F,EAAOG,MAC7BiH,EAAM9M,QAAkBqB,IAARyL,GAAqB9M,GAASA,KACpD8M,EAAM9M,EAGZ,CACA,OAAO8M,CACT,CCjBA,SAAS+F,GAAY1M,GACnB,OAAOA,EAAE3L,OAAOsY,KAClB,CAEO,SAASC,GAAK7I,GACnB,OAAOA,EAAK4I,KACd,CAEO,SAASE,GAAM9I,EAAMnF,GAC1B,OAAOA,EAAI,EAAImF,EAAKxR,MACtB,CAEO,SAASua,GAAQ/I,EAAMnF,GAC5B,OAAOmF,EAAKgJ,YAAY9O,OAAS8F,EAAK4I,MAAQ/N,EAAI,CACpD,CAEO,SAASoO,GAAOjJ,GACrB,OAAOA,EAAKkJ,YAAYhP,OAAS8F,EAAK4I,MAChC5I,EAAKgJ,YAAY9O,OAAS,GAAI8F,EAAKgJ,YAAaL,IAAe,EAC/D,CACR,CCtBe,SAAS,GAAS3W,GAC/B,OAAO,WACL,OAAOA,CACT,CACF,CCAA,SAASmX,GAAuBhO,EAAGC,GACjC,OAAOgO,GAAiBjO,EAAE7E,OAAQ8E,EAAE9E,SAAW6E,EAAEK,MAAQJ,EAAEI,KAC7D,CAEA,SAAS6N,GAAuBlO,EAAGC,GACjC,OAAOgO,GAAiBjO,EAAE7K,OAAQ8K,EAAE9K,SAAW6K,EAAEK,MAAQJ,EAAEI,KAC7D,CAEA,SAAS4N,GAAiBjO,EAAGC,GAC3B,OAAOD,EAAEiK,GAAKhK,EAAEgK,EAClB,CAEA,SAAStP,GAAMmG,GACb,OAAOA,EAAEnG,KACX,CAEA,SAASwT,GAAUrN,GACjB,OAAOA,EAAET,KACX,CAEA,SAAS+N,GAAaC,GACpB,OAAOA,EAAM7V,KACf,CAEA,SAAS8V,GAAaD,GACpB,OAAOA,EAAMlS,KACf,CAEA,SAASoS,GAAKC,EAAUtV,GACtB,MAAM2L,EAAO2J,EAAS9Z,IAAIwE,GAC1B,IAAK2L,EAAM,MAAM,IAAI4J,MAAM,YAAcvV,GACzC,OAAO2L,CACT,CAEA,SAAS6J,IAAoB,MAAClW,IAC5B,IAAK,MAAMqM,KAAQrM,EAAO,CACxB,IAAIyR,EAAKpF,EAAKoF,GACVa,EAAKb,EACT,IAAK,MAAM3U,KAAQuP,EAAKgJ,YACtBvY,EAAK2U,GAAKA,EAAK3U,EAAKlC,MAAQ,EAC5B6W,GAAM3U,EAAKlC,MAEb,IAAK,MAAMkC,KAAQuP,EAAKkJ,YACtBzY,EAAKwV,GAAKA,EAAKxV,EAAKlC,MAAQ,EAC5B0X,GAAMxV,EAAKlC,KAEf,CACF,CAEe,SAAS,KACtB,IAEYub,EAGRzX,EACA0X,EANA1O,EAAK,EAAG+J,EAAK,EAAGY,EAAK,EAAGC,EAAK,EAC7BrL,EAAK,GACL4M,EAAK,EACLnT,EAAKiV,GACLU,EAAQjB,GAGRpV,EAAQ4V,GACRjS,EAAQmS,GACRQ,EAAa,EAEjB,SAASC,IACP,MAAMV,EAAQ,CAAC7V,MAAOA,EAAMiJ,MAAM,KAAMhB,WAAYtE,MAAOA,EAAMsF,MAAM,KAAMhB,YAO7E,OAoDF,UAA0B,MAACjI,EAAK,MAAE2D,IAChC,IAAK,MAAOwC,EAAGkG,KAASrM,EAAMwW,UAC5BnK,EAAKxE,MAAQ1B,EACbkG,EAAKgJ,YAAc,GACnBhJ,EAAKkJ,YAAc,GAErB,MAAMS,EAAW,IAAIS,IAAIzW,EAAMyK,KAAI,CAACnC,EAAGnC,IAAM,CAACzF,EAAG4H,EAAGnC,EAAGnG,GAAQsI,MAC/D,IAAK,MAAOnC,EAAGrJ,KAAS6G,EAAM6S,UAAW,CACvC1Z,EAAK+K,MAAQ1B,EACb,IAAI,OAACxD,EAAM,OAAEhG,GAAUG,EACD,iBAAX6F,IAAqBA,EAAS7F,EAAK6F,OAASoT,GAAKC,EAAUrT,IAChD,iBAAXhG,IAAqBA,EAASG,EAAKH,OAASoZ,GAAKC,EAAUrZ,IACtEgG,EAAO0S,YAAY1V,KAAK7C,GACxBH,EAAO4Y,YAAY5V,KAAK7C,EAC1B,CACA,GAAgB,MAAZsZ,EACF,IAAK,MAAM,YAACf,EAAW,YAAEE,KAAgBvV,EACvCqV,EAAY3W,KAAK0X,GACjBb,EAAY7W,KAAK0X,EAGvB,CA/EEM,CAAiBb,GAiFnB,UAA2B,MAAC7V,IAC1B,IAAK,MAAMqM,KAAQrM,EACjBqM,EAAKlK,WAA4BqB,IAApB6I,EAAKsK,WACZnS,KAAKC,IAAIqQ,GAAIzI,EAAKgJ,YAAalT,IAAQ2S,GAAIzI,EAAKkJ,YAAapT,KAC7DkK,EAAKsK,UAEf,CAtFEC,CAAkBf,GAwFpB,UAA2B,MAAC7V,IAC1B,MAAMkH,EAAIlH,EAAMuG,OAChB,IAAIsQ,EAAU,IAAIC,IAAI9W,GAClBoM,EAAO,IAAI0K,IACXzY,EAAI,EACR,KAAOwY,EAAQE,MAAM,CACnB,IAAK,MAAM1K,KAAQwK,EAAS,CAC1BxK,EAAK4I,MAAQ5W,EACb,IAAK,MAAM,OAAC1B,KAAW0P,EAAKgJ,YAC1BjJ,EAAK4K,IAAIra,EAEb,CACA,KAAM0B,EAAI6I,EAAG,MAAM,IAAI+O,MAAM,iBAC7BY,EAAUzK,EACVA,EAAO,IAAI0K,GACb,CACF,CAvGEG,CAAkBpB,GAyGpB,UAA4B,MAAC7V,IAC3B,MAAMkH,EAAIlH,EAAMuG,OAChB,IAAIsQ,EAAU,IAAIC,IAAI9W,GAClBoM,EAAO,IAAI0K,IACXzY,EAAI,EACR,KAAOwY,EAAQE,MAAM,CACnB,IAAK,MAAM1K,KAAQwK,EAAS,CAC1BxK,EAAKxR,OAASwD,EACd,IAAK,MAAM,OAACsE,KAAW0J,EAAKkJ,YAC1BnJ,EAAK4K,IAAIrU,EAEb,CACA,KAAMtE,EAAI6I,EAAG,MAAM,IAAI+O,MAAM,iBAC7BY,EAAUzK,EACVA,EAAO,IAAI0K,GACb,CACF,CAxHEI,CAAmBrB,GAkKrB,SAA6BA,GAC3B,MAAMsB,EAzCR,UAA2B,MAACnX,IAC1B,MAAM3B,EAAI,GAAI2B,GAAOsI,GAAKA,EAAE2M,QAAS,EAC/BmC,GAAM/E,EAAK3K,EAAKT,IAAO5I,EAAI,GAC3B8Y,EAAU,IAAI9Q,MAAMhI,GAC1B,IAAK,MAAMgO,KAAQrM,EAAO,CACxB,MAAMmG,EAAI3B,KAAKC,IAAI,EAAGD,KAAKyK,IAAI5Q,EAAI,EAAGmG,KAAK6S,MAAMhB,EAAMhN,KAAK,KAAMgD,EAAMhO,MACxEgO,EAAKiL,MAAQnR,EACbkG,EAAK3E,GAAKA,EAAKvB,EAAIiR,EACnB/K,EAAKgG,GAAKhG,EAAK3E,GAAKT,EAChBkQ,EAAQhR,GAAIgR,EAAQhR,GAAGxG,KAAK0M,GAC3B8K,EAAQhR,GAAK,CAACkG,EACrB,CACA,GAAI3N,EAAM,IAAK,MAAM6Y,KAAUJ,EAC7BI,EAAO7Y,KAAKA,GAEd,OAAOyY,CACT,CAyBkBK,CAAkB3B,GAClCM,EAAK3R,KAAKyK,IAAI4E,GAAKvB,EAAKb,IAAO,GAAI0F,GAASM,GAAKA,EAAElR,SAAU,IAxB/D,SAAgC4Q,GAC9B,MAAMO,EAAK,GAAIP,GAASM,IAAMnF,EAAKb,GAAMgG,EAAElR,OAAS,GAAK4P,GAAMrB,GAAI2C,EAAGtV,MACtE,IAAK,MAAMnC,KAASmX,EAAS,CAC3B,IAAIhZ,EAAIsT,EACR,IAAK,MAAMpF,KAAQrM,EAAO,CACxBqM,EAAKoF,GAAKtT,EACVkO,EAAKiG,GAAKnU,EAAIkO,EAAKlK,MAAQuV,EAC3BvZ,EAAIkO,EAAKiG,GAAK6D,EACd,IAAK,MAAMrZ,KAAQuP,EAAKgJ,YACtBvY,EAAKlC,MAAQkC,EAAKqF,MAAQuV,CAE9B,CACAvZ,GAAKmU,EAAKnU,EAAIgY,IAAOnW,EAAMuG,OAAS,GACpC,IAAK,IAAIJ,EAAI,EAAGA,EAAInG,EAAMuG,SAAUJ,EAAG,CACrC,MAAMkG,EAAOrM,EAAMmG,GACnBkG,EAAKoF,IAAMtT,GAAKgI,EAAI,GACpBkG,EAAKiG,IAAMnU,GAAKgI,EAAI,EACtB,CACAwR,EAAa3X,EACf,CACF,CAKE4X,CAAuBT,GACvB,IAAK,IAAIhR,EAAI,EAAGA,EAAImQ,IAAcnQ,EAAG,CACnC,MAAM0R,EAAQrT,KAAKsT,IAAI,IAAM3R,GACvB4R,EAAOvT,KAAKC,IAAI,EAAIoT,GAAQ1R,EAAI,GAAKmQ,GAC3C0B,EAAiBb,EAASU,EAAOE,GACjCE,EAAiBd,EAASU,EAAOE,EACnC,CACF,CA3KEG,CAAoBrC,GACpBK,GAAoBL,GACbA,CACT,CA2KA,SAASoC,EAAiBd,EAASU,EAAOE,GACxC,IAAK,IAAI5R,EAAI,EAAGe,EAAIiQ,EAAQ5Q,OAAQJ,EAAIe,IAAKf,EAAG,CAC9C,MAAMoR,EAASJ,EAAQhR,GACvB,IAAK,MAAMxJ,KAAU4a,EAAQ,CAC3B,IAAIpZ,EAAI,EACJgV,EAAI,EACR,IAAK,MAAM,OAACxQ,EAAM,MAAER,KAAUxF,EAAO4Y,YAAa,CAChD,IAAI4C,EAAIhW,GAASxF,EAAO2a,MAAQ3U,EAAO2U,OACvCnZ,GAAKia,EAAUzV,EAAQhG,GAAUwb,EACjChF,GAAKgF,CACP,CACA,KAAMhF,EAAI,GAAI,SACd,IAAIU,GAAM1V,EAAIgV,EAAIxW,EAAO8U,IAAMoG,EAC/Blb,EAAO8U,IAAMoC,EACblX,EAAO2V,IAAMuB,EACbwE,EAAiB1b,EACnB,MACa6G,IAAT9E,GAAoB6Y,EAAO7Y,KAAK+W,IACpC6C,EAAkBf,EAAQQ,EAC5B,CACF,CAGA,SAASC,EAAiBb,EAASU,EAAOE,GACxC,IAAK,IAAwB5R,EAAhBgR,EAAQ5Q,OAAgB,EAAGJ,GAAK,IAAKA,EAAG,CACnD,MAAMoR,EAASJ,EAAQhR,GACvB,IAAK,MAAMxD,KAAU4U,EAAQ,CAC3B,IAAIpZ,EAAI,EACJgV,EAAI,EACR,IAAK,MAAM,OAACxW,EAAM,MAAEwF,KAAUQ,EAAO0S,YAAa,CAChD,IAAI8C,EAAIhW,GAASxF,EAAO2a,MAAQ3U,EAAO2U,OACvCnZ,GAAKoa,EAAU5V,EAAQhG,GAAUwb,EACjChF,GAAKgF,CACP,CACA,KAAMhF,EAAI,GAAI,SACd,IAAIU,GAAM1V,EAAIgV,EAAIxQ,EAAO8O,IAAMoG,EAC/BlV,EAAO8O,IAAMoC,EACblR,EAAO2P,IAAMuB,EACbwE,EAAiB1V,EACnB,MACaa,IAAT9E,GAAoB6Y,EAAO7Y,KAAK+W,IACpC6C,EAAkBf,EAAQQ,EAC5B,CACF,CAEA,SAASO,EAAkBtY,EAAO6X,GAChC,MAAM1R,EAAInG,EAAMuG,QAAU,EACpBiS,EAAUxY,EAAMmG,GACtBsS,EAA6BzY,EAAOwY,EAAQ/G,GAAK0E,EAAIhQ,EAAI,EAAG0R,GAC5Da,EAA6B1Y,EAAOwY,EAAQlG,GAAK6D,EAAIhQ,EAAI,EAAG0R,GAC5DY,EAA6BzY,EAAOsS,EAAItS,EAAMuG,OAAS,EAAGsR,GAC1Da,EAA6B1Y,EAAOyR,EAAI,EAAGoG,EAC7C,CAGA,SAASa,EAA6B1Y,EAAO7B,EAAGgI,EAAG0R,GACjD,KAAO1R,EAAInG,EAAMuG,SAAUJ,EAAG,CAC5B,MAAMkG,EAAOrM,EAAMmG,GACb0N,GAAM1V,EAAIkO,EAAKoF,IAAMoG,EACvBhE,EAAK,OAAMxH,EAAKoF,IAAMoC,EAAIxH,EAAKiG,IAAMuB,GACzC1V,EAAIkO,EAAKiG,GAAK6D,CAChB,CACF,CAGA,SAASsC,EAA6BzY,EAAO7B,EAAGgI,EAAG0R,GACjD,KAAO1R,GAAK,IAAKA,EAAG,CAClB,MAAMkG,EAAOrM,EAAMmG,GACb0N,GAAMxH,EAAKiG,GAAKnU,GAAK0Z,EACvBhE,EAAK,OAAMxH,EAAKoF,IAAMoC,EAAIxH,EAAKiG,IAAMuB,GACzC1V,EAAIkO,EAAKoF,GAAK0E,CAChB,CACF,CAEA,SAASkC,GAAiB,YAAChD,EAAW,YAAEE,IACtC,QAAiB/R,IAAb4S,EAAwB,CAC1B,IAAK,MAAOzT,QAAQ,YAAC0S,MAAiBE,EACpCF,EAAY3W,KAAKgX,IAEnB,IAAK,MAAO/Y,QAAQ,YAAC4Y,MAAiBF,EACpCE,EAAY7W,KAAK8W,GAErB,CACF,CAEA,SAASmC,EAAa3X,GACpB,QAAiBwD,IAAb4S,EACF,IAAK,MAAM,YAACf,EAAW,YAAEE,KAAgBvV,EACvCqV,EAAY3W,KAAKgX,IACjBH,EAAY7W,KAAK8W,GAGvB,CAGA,SAAS4C,EAAUzV,EAAQhG,GACzB,IAAIwB,EAAIwE,EAAO8O,IAAM9O,EAAO0S,YAAY9O,OAAS,GAAK4P,EAAK,EAC3D,IAAK,MAAOxZ,OAAQ0P,EAAI,MAAEzR,KAAU+H,EAAO0S,YAAa,CACtD,GAAIhJ,IAAS1P,EAAQ,MACrBwB,GAAKvD,EAAQub,CACf,CACA,IAAK,MAAOxT,OAAQ0J,EAAI,MAAEzR,KAAU+B,EAAO4Y,YAAa,CACtD,GAAIlJ,IAAS1J,EAAQ,MACrBxE,GAAKvD,CACP,CACA,OAAOuD,CACT,CAGA,SAASoa,EAAU5V,EAAQhG,GACzB,IAAIwB,EAAIxB,EAAO8U,IAAM9U,EAAO4Y,YAAYhP,OAAS,GAAK4P,EAAK,EAC3D,IAAK,MAAOxT,OAAQ0J,EAAI,MAAEzR,KAAU+B,EAAO4Y,YAAa,CACtD,GAAIlJ,IAAS1J,EAAQ,MACrBxE,GAAKvD,EAAQub,CACf,CACA,IAAK,MAAOxZ,OAAQ0P,EAAI,MAAEzR,KAAU+H,EAAO0S,YAAa,CACtD,GAAIhJ,IAAS1P,EAAQ,MACrBwB,GAAKvD,CACP,CACA,OAAOuD,CACT,CAEA,OAnSAoY,EAAOoC,OAAS,SAAS9C,GAEvB,OADAK,GAAoBL,GACbA,CACT,EAEAU,EAAOqC,OAAS,SAASpS,GACvB,OAAOyB,UAAU1B,QAAU7F,EAAkB,mBAAN8F,EAAmBA,EAAI,GAASA,GAAI+P,GAAU7V,CACvF,EAEA6V,EAAOjZ,UAAY,SAASkJ,GAC1B,OAAOyB,UAAU1B,QAAU8P,EAAqB,mBAAN7P,EAAmBA,EAAI,GAASA,GAAI+P,GAAUF,CAC1F,EAEAE,EAAOsC,SAAW,SAASrS,GACzB,OAAOyB,UAAU1B,QAAU7H,EAAO8H,EAAG+P,GAAU7X,CACjD,EAEA6X,EAAO9Y,UAAY,SAAS+I,GAC1B,OAAOyB,UAAU1B,QAAUU,GAAMT,EAAG+P,GAAUtP,CAChD,EAEAsP,EAAO/Y,YAAc,SAASgJ,GAC5B,OAAOyB,UAAU1B,QAAUsN,EAAKsC,GAAM3P,EAAG+P,GAAU1C,CACrD,EAEA0C,EAAOvW,MAAQ,SAASwG,GACtB,OAAOyB,UAAU1B,QAAUvG,EAAqB,mBAANwG,EAAmBA,EAAI,GAASA,GAAI+P,GAAUvW,CAC1F,EAEAuW,EAAO5S,MAAQ,SAAS6C,GACtB,OAAOyB,UAAU1B,QAAU5C,EAAqB,mBAAN6C,EAAmBA,EAAI,GAASA,GAAI+P,GAAU5S,CAC1F,EAEA4S,EAAOH,SAAW,SAAS5P,GACzB,OAAOyB,UAAU1B,QAAU6P,EAAW5P,EAAG+P,GAAUH,CACrD,EAEAG,EAAOQ,KAAO,SAASvQ,GACrB,OAAOyB,UAAU1B,QAAUmB,EAAK+J,EAAK,EAAGY,GAAM7L,EAAE,GAAI8L,GAAM9L,EAAE,GAAI+P,GAAU,CAAClE,EAAK3K,EAAI4K,EAAKb,EAC3F,EAEA8E,EAAOuC,OAAS,SAAStS,GACvB,OAAOyB,UAAU1B,QAAUmB,GAAMlB,EAAE,GAAG,GAAI6L,GAAM7L,EAAE,GAAG,GAAIiL,GAAMjL,EAAE,GAAG,GAAI8L,GAAM9L,EAAE,GAAG,GAAI+P,GAAU,CAAC,CAAC7O,EAAI+J,GAAK,CAACY,EAAIC,GACnH,EAEAiE,EAAOD,WAAa,SAAS9P,GAC3B,OAAOyB,UAAU1B,QAAU+P,GAAc9P,EAAG+P,GAAUD,CACxD,EAoPOC,CACT,CCjRO,MAAMwC,WAAevZ,EAA5B,c,oBAUC,oC,gDAAkD,IAAIkM,EAAA,EACrDC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMoW,GAAW9F,KAAKxR,KAAKC,MAAO,CAAEwR,UAAW,CAAC,OAAQ,UAAY,CAACzR,KAAKsJ,MAAMqI,eA8BjF,oC,gDAAqC3R,KAAKqF,SAASC,KAAK2R,GAAY/V,IAAIlB,KAAKC,MAAO,CAAC,MAOrF,wC,gDAAmB,OACnB,uC,yDAEA,6C,iDAAwB,YACxB,+C,iDAA0B,EAAA0e,GAAA,MAkL3B,CAxNW,SAAAnZ,GACTxF,KAAK8U,UAAUrD,UAAY,aAAiBzR,KAAK8U,UAAUrD,UAAW,CAAC,SAAUzR,KAAK8U,UAAU9R,aAAe,eAE/GhD,KAAK8Z,eAAe1C,IAAG,SAAS3C,GAC/B,OAAOA,EAAE,EACV,IAEAzU,KAAK8Z,eAAezM,IAAG,SAASoH,GAC/B,OAAOA,EAAE,EACV,IAEAzU,KAAK8Z,eAAe7B,IAAG,SAASxD,GAC/B,OAAOA,EAAE,EACV,IAEAzU,KAAK8Z,eAAe9B,IAAG,SAASvD,GAC/B,OAAOA,EAAE,EACV,IAEA3U,MAAM0F,WACP,CAuBO,QAAAmB,CAAStE,GACf,MAAMiG,EAASjG,EAASR,IAAI,UACtBS,EAASD,EAASR,IAAI,UAEtBY,EAAOzC,KAAKsJ,MAAMkJ,OAoBxB,OAlBIlK,EAAOzG,IAAI,aACdY,EAAKgQ,OAAO,UACZhQ,EAAKgQ,OAAO,YAGTnQ,EAAOT,IAAI,aACdY,EAAKgQ,OAAO,UACZhQ,EAAKgQ,OAAO,YAGbzS,KAAK+U,eAAe1P,SAASC,KAAK7C,GAClCA,EAAKiQ,aAAarQ,GAClBI,EAAK6D,IAAI,SAAUgC,GACnB7F,EAAK6D,IAAI,SAAUhE,GACnBG,EAAKkQ,OAAS3S,KAEdA,KAAKsJ,MAAMhE,KAAK7C,GAETA,CACR,CAKO,YAAA4U,GACN,MAAMuH,EAAU5e,KAAK6e,SACjBD,IACH5e,KAAK8e,UAAUR,OAAOM,GAEtB,OAAY5e,KAAK6G,WAAYxE,IAC5B,MAAMI,EAAOJ,EAASR,IAAI,QAC1BY,EAAKsT,WAAW,cAAe/V,KAAK6B,IAAI,gBACxCY,EAAKmV,WAAW,IAGnB,CAEO,gBAAA3Q,CAAiB5E,GACvBvC,MAAMmH,iBAAiB5E,GACvB,MAAMW,EAAchD,KAAK6B,IAAI,eACvBkd,EAAe1c,EAASR,IAAI,QAAQiH,cACpCkW,EAAiB3c,EAASR,IAAI,QAAQuH,gBAEzB,YAAfpG,GACC+b,GACHA,EAAazY,IAAI,WAAY,IAE1B0Y,GACHA,EAAe1Y,IAAI,WAAY,MAI5ByY,GACHA,EAAazY,IAAI,WAAY,GAE1B0Y,GACHA,EAAe1Y,IAAI,WAAY,GAIlC,CAEU,kBAAA0E,CAAmBJ,GAC5B,MAA+B,YAA3B5K,KAAK6B,IAAI,eACL+I,EAAO/I,IAAI,YAAa,GAGxB+I,EAAO/I,IAAI,YAAa,EAEjC,CAEO,gBAAAuF,GACNtH,MAAMsH,mBACN,IAAI6X,GAAW,EAKf,GAJ+B,YAA3Bjf,KAAK6B,IAAI,iBACZod,GAAW,GAGRjf,KAAKgU,QAAQ,gBAAkBhU,KAAKgU,QAAQ,eAAgB,CAC/D,MAAM9Q,EAAclD,KAAK6B,IAAI,cAAe,IACxCod,GACHjf,KAAK8Z,eAAeoF,OAAM,QAAsBhc,IAChDlD,KAAKga,iBAAiBkF,OAAM,QAAsBhc,MAGlDlD,KAAK8Z,eAAeoF,OAAM,QAAsBhc,IAChDlD,KAAKga,iBAAiBkF,OAAM,QAAsBhc,I,CAIpD,IAAIlD,KAAKyH,cAAgBzH,KAAKuV,YAAcvV,KAAKgU,QAAQ,gBAAkBhU,KAAKgU,QAAQ,cAAgBhU,KAAKgU,QAAQ,cAAgBhU,KAAKgU,QAAQ,aAAehU,KAAKgU,QAAQ,gBAAkBhU,KAAKgU,QAAQ,gBAAkBhU,KAAKgU,QAAQ,cACvOhU,KAAK0H,WAAWwE,OAAS,EAAG,CAC/B,MAAMiT,EAAWnf,KAAK8e,UACtB,IAAIhG,EAAI9Y,KAAK0V,aACT0J,EAAIpf,KAAK2V,cAYb,OAVIsJ,KACFnG,EAAGsG,GAAK,CAACA,EAAGtG,IAGdqG,EAASzC,KAAK,CAAC5D,EAAGsG,IAClBD,EAAShc,YAAYnD,KAAK6B,IAAI,cAAe,KAC7Csd,EAAS/b,UAAUpD,KAAK6B,IAAI,YAAa,KACzCsd,EAASX,SAASxe,KAAK6B,IAAI,WAAY,OACtCsd,EAAiBpD,SAAS/b,KAAK6B,IAAI,aAE5B7B,KAAK6B,IAAI,cAChB,IAAK,QACJsd,EAASlc,UAAU,IACnB,MACD,IAAK,UACJkc,EAASlc,UAAU,IACnB,MACD,IAAK,SACJkc,EAASlc,UAAU,IACnB,MACD,QACCkc,EAASlc,UAAU,IAIrBjD,KAAK6e,SAAWM,EAAS,CAAExZ,MAAO3F,KAAK0H,WAAY4B,MAAOtJ,KAAKmI,aAE/D,OAAYnI,KAAK6G,WAAYxE,IAC5B,MAAMI,EAAOJ,EAASR,IAAI,QAC1BY,EAAKsT,WAAW,cAAe/V,KAAK6B,IAAI,gBACxCY,EAAKmV,WAAW,IAGjB,MAAMgH,EAAU5e,KAAK6e,SAErB,GAAID,EAAS,CACZ,MAAMjZ,EAAQiZ,EAAQjZ,MAEtB,OAAYA,GAAQgC,IACnB,MAAMtF,EAAYsF,EAAqBtF,SACjC2P,EAAO3P,EAASR,IAAI,QAE1B,IAAIwL,EACA2K,EACAZ,EACAa,EAEAgH,GACH5R,EAAK1F,EAAayP,GAClBY,EAAKrQ,EAAasQ,GAClBb,EAAKzP,EAAa0F,GAClB4K,EAAKtQ,EAAaqQ,KAGlB3K,EAAK1F,EAAa0F,GAClB2K,EAAKrQ,EAAaqQ,GAClBZ,EAAKzP,EAAayP,GAClBa,EAAKtQ,EAAasQ,IAGf,WAAe5K,IAAO,WAAe2K,IAAO,WAAeZ,IAAO,WAAea,KACpFjG,EAAK1R,OAAO,CAAE0D,EAAGqJ,EAAIvJ,EAAGsT,EAAI7W,MAAOyX,EAAK3K,EAAI7M,OAAQyX,EAAKb,IAEvC/U,EAASR,IAAI,aACrBvB,OAAO,CAAEC,MAAOyX,EAAK3K,EAAI7M,OAAQyX,EAAKb,I,KAMtD,EApOA,sC,gDAAkC,WAClC,uC,gDAA0CjS,EAAK+F,WAAWC,OAAO,CAACuT,GAAOtT,c,eClEnE,MAAMiU,WAAwBjO,EAArC,c,oBASC,qC,gDAA8C,IAAIC,EAAA,EACjDC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMqQ,EAAA,EAAMC,KAAKxR,KAAKC,MAAO,CAAC,EAAG,CAACD,KAAK0R,OAAOC,eAW/C,mC,yDAEA,sC,gDAA4B,IAO5B,sC,gDAAgD,IAAIN,EAAA,EACnDC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMoe,GAAAC,EAAO/N,KAAKxR,KAAKC,MAAO,CAAEwR,UAAW,CAAC,UAAY,CAACzR,KAAKwf,QAAQ7N,cAgDxE,CA1CQ,QAAAM,CAAS5P,GACf,MAAM2P,EAAOlS,MAAMmS,SAAS5P,EAAU,cAEhCod,EAASzN,EAAK3M,SAASgO,YAAY,EAAGrT,KAAKwf,QAAQhN,QACzDnQ,EAASiE,IAAI,SAAUmZ,GACvBA,EAAOnM,SAAS,OAAQjR,EAASR,IAAI,SACrC4d,EAAOnM,SAAS,cAAejR,EAASR,IAAI,gBAE5C,MAAMmR,EAAQhT,KAAK0R,OAAOc,OAY1B,OAXAxS,KAAK0R,OAAOpM,KAAK0N,GACjBA,EAAMP,OAAO,QACbO,EAAMP,OAAO,cACbO,EAAMP,OAAO,QAEbT,EAAK3M,SAASC,KAAK0N,GACnB3Q,EAASiE,IAAI,QAAS0M,GAEtBA,EAAMN,aAAarQ,GACnBod,EAAO/M,aAAarQ,GAEb2P,CACR,CAKO,eAAA3I,CAAgBhH,GACtBvC,MAAMuJ,gBAAgBhH,GACtB,IAAIod,EAASpd,EAASR,IAAI,UACtB4d,IACHzf,KAAKwf,QAAQjW,YAAYkW,GACzBA,EAAOjW,UAET,CAEO,gBAAAsJ,CAAiBzQ,GACvB,MAAMod,EAASpd,EAASR,IAAI,UACxB4d,IACHA,EAAOnZ,IAAI,OAAQjE,EAASR,IAAI,SAChC4d,EAAOnZ,IAAI,cAAejE,EAASR,IAAI,gBAEzC,EA/EA,sC,gDAAkC,oBAClC,uC,gDAA0CuP,EAAUlG,WAAWC,OAAO,CAACkU,GAAgBjU,cCHjF,MAAMsU,WAAuB7L,GAApC,c,oBAEC,kC,yDACA,kC,yDACA,sC,gDAAyB,GAsH1B,CA1GQ,cAAA4D,GAGN,GAFA3X,MAAM2X,iBAEFzX,KAAKgU,QAAQ,UAAW,CAC3B,MAAM1L,EAAStI,KAAK6B,IAAI,UACxB,GAAIyG,EAAQ,CACX,MAAMoP,EAAapP,EAAOzG,IAAI,QAC9B7B,KAAK2X,WAAWrS,KAAKoS,EAAWxV,OAAOC,GAAG,mBAAmB,KAC5DnC,KAAK2W,cAAc,SAAS,I,EAI/B,GAAI3W,KAAKgU,QAAQ,UAAW,CAC3B,MAAM1R,EAAStC,KAAK6B,IAAI,UACxB,GAAIS,EAAQ,CACX,MAAMuV,EAAavV,EAAOT,IAAI,QAC9B7B,KAAK2X,WAAWrS,KAAKuS,EAAW3V,OAAOC,GAAG,mBAAmB,KAC5DnC,KAAK2W,cAAc,SAAS,I,EAK/B,GAAI3W,KAAK8X,eAAe,eAAgB,CACvC,MAAMnF,EAAS3S,KAAK2S,OACdtQ,EAAWrC,KAAKqC,SAClBA,GAAYsQ,GACfA,EAAO1L,iBAAiB5E,E,CAItBrC,KAAK2S,QAAU3S,KAAKqC,UACvBrC,KAAK2S,OAAO0H,iBAAiBra,KAAKqC,UAGJ,YAA3BrC,KAAK6B,IAAI,eACZ7B,KAAKsG,IAAI,cAAc,GAGvBtG,KAAKsG,IAAI,cAAc,EAEzB,CAEO,QAAAyN,GACNjU,MAAMiU,WAEF/T,KAAK+X,QACR/X,KAAK2f,OAEP,CAEO,KAAAA,GACN,MAAMrd,EAAStC,KAAK6B,IAAI,UAClByG,EAAStI,KAAK6B,IAAI,UAExB,GAAIyG,GAAUhG,EAAQ,CACrB,IAAIoV,EAAapP,EAAOzG,IAAI,QACxBgW,EAAavV,EAAOT,IAAI,QAE5B,MAAMwL,EAAKqK,EAAW1T,IAChBoT,EAAKM,EAAW5T,IAEhBkU,EAAKH,EAAW7T,IAChBiU,EAAKJ,EAAW/T,IAEtB9D,KAAKsU,IAAM,CAAEtQ,EAAGqJ,EAAIvJ,EAAGsT,GACvBpX,KAAKuU,IAAM,CAAEvQ,EAAGgU,EAAIlU,EAAGmU,GAEvB,IAAI/T,EAAS,EAEb,GAAsC,YAAlClE,KAAKoV,WAAW,eAA8B,CACjDlR,GAAU+T,EAAKb,GAAM,EACrB,IAAInJ,EAAI,EACJmJ,EAAKa,IACRhK,GAAK,GAGNjO,KAAK4f,SAASnQ,IAAIpC,EAAI+J,EAAKlT,EAAQA,EAAS+J,GAAI9D,KAAKsB,GAAK,EAAGtB,KAAKsB,GAAK,E,KAEnE,CACJvH,GAAU8T,EAAK3K,GAAM,EACrB,IAAIY,EAAI,EACJZ,EAAK2K,IACR/J,GAAK,GAENjO,KAAK4f,SAASnQ,IAAIpC,EAAKnJ,EAAQkT,EAAIlT,EAAS+J,GAAI9D,KAAKsB,GAAI,E,CAE1DzL,KAAK6f,QAAU3b,C,CAEjB,CAGO,QAAA6G,CAAS4F,GACf,GAAI3Q,KAAKsU,KAAOtU,KAAKuU,IAAK,CACzB,MAAMrQ,EAASlE,KAAK6f,QAEpB,GAAsC,YAAlC7f,KAAKoV,WAAW,eAA8B,CACjD,IAAInK,EAAc,IAAM0F,EAAX,GACb,MAAO,CAAE7M,EAAG9D,KAAKsU,IAAIxQ,EAAII,EAASA,EAAS,MAAU+G,GAAQjH,EAAGE,EAAS,MAAU+G,GAAQA,MAAOA,EAAQ,G,CAEtG,CACJ,IAAIA,EAAQ,IAAM,IAAM0F,EACxB,MAAO,CAAE3M,EAAGhE,KAAKsU,IAAItQ,EAAIE,EAASA,EAAS,MAAU+G,GAAQnH,EAAGI,EAAS,MAAU+G,GAAQA,MAAOA,EAAQ,G,EAG5G,MAAO,CAAEjH,EAAG,EAAGF,EAAG,EAAGmH,MAAO,EAC7B,EAhHA,sC,gDAAkC,mBAClC,uC,gDAA0C4I,GAAS3I,WAAWC,OAAO,CAACuU,GAAetU,cCqC/E,MAAM0U,WAAmB3a,EAAhC,c,oBAUC,oC,gDAAsD,IAAIkM,EAAA,EACzDC,EAAA,GAASpQ,IAAI,CAAC,IACd,IAAMwe,GAAelO,KAAKxR,KAAKC,MAAO,CAAEwR,UAAW,CAAC,OAAQ,UAAY,CAACzR,KAAKsJ,MAAMqI,eAQrF,oC,gDAAyC3R,KAAKqF,SAASC,KAAK+Z,GAAgBne,IAAIlB,KAAKC,MAAO,CAAC,KAyI9F,CAlIW,SAAAuF,GACTxF,KAAK8U,UAAUrD,UAAY,aAAiBzR,KAAK8U,UAAUrD,UAAW,CAAC,aAAczR,KAAK8U,UAAU9R,aAAe,eAEnHlD,MAAM0F,YACNxF,KAAK2F,MAAMN,SAASC,KAAKtF,KAAK6F,iBAC/B,CAMO,QAAAc,CAAStE,GACf,MAAMI,EAAOzC,KAAK2F,MAAMN,SAAS0a,UAAU/f,KAAKsJ,MAAMkJ,OAAQ,GAM9D,OALAxS,KAAKsJ,MAAMhE,KAAK7C,GAChBA,EAAKiQ,aAAarQ,GAClBI,EAAK6D,IAAI,SAAUjE,EAASR,IAAI,WAChCY,EAAK6D,IAAI,SAAUjE,EAASR,IAAI,WAChCY,EAAKkQ,OAAS3S,KACPyC,CACR,CAEO,gBAAA2E,GAGN,GAFAtH,MAAMsH,mBAEFpH,KAAKyH,cAAgBzH,KAAKuV,YAAcvV,KAAKgU,QAAQ,eAAgB,CACxE,IAAIzT,EAAQ,EACZ,MAAMyC,EAAchD,KAAK6B,IAAI,eAE7B,OAAY7B,KAAK6G,WAAYxE,IACfA,EAASR,IAAI,QACrBkU,WAAW,cAAe/V,KAAK6B,IAAI,eAAe,IAIvDtB,EADkB,YAAfyC,EACKhD,KAAK2V,cAGL3V,KAAK0V,aAGd,IAAI+E,EAAM,EACNuF,EAAM1Y,IACNvC,EAAY/E,KAAK6B,IAAI,aAER,QAAbkD,GACH,OAAY/E,KAAK2F,MAAMkB,WAAYxE,IAClC,IAAIyF,EAAQzF,EAASR,IAAIkD,EAAY,WACrC0V,GAAO3S,EACPkY,EAAM7V,KAAKyK,IAAIoL,EAAKlY,EAAM,IAI5B,MAAMmY,EAAQjgB,KAAK2F,MAAMkB,UAAUqF,OAC7B/I,EAAcnD,KAAK6B,IAAI,cAAe,IACtCiD,EAAY9E,KAAK6B,IAAI,YAAa,GAExCtB,GAAgB0f,GAAS9c,EAA0B,EAAZ2B,GAEnCvE,GAAS,IACZA,EAAQ,GAGT,IACI6c,EAAI7c,GADOka,EAAMwF,EAAQD,GAGzBE,EAAY,EAChB,MAAMC,EAAoBngB,KAAK6B,IAAI,oBAAqB,GAClDmD,EAAkBhF,KAAK6B,IAAI,mBAEjC,OAAY7B,KAAK2F,MAAMkB,WAAYxE,IAClC,IAAIyF,EAAQzF,EAASR,IAAIkD,EAAY,WAErC,MAAMiN,EAAO3P,EAASR,IAAI,QAC1B,IAAIqC,EAASY,EAAYsY,GAAKtV,EAAQkY,GAAO,EAM7C,GAJiB,QAAbjb,IACHb,EAASY,EAAYvE,EAAQ0f,EAAQ,GAGnB,YAAfjd,EAA2B,CAC9BgP,EAAK1L,IAAI,IAAK,GAEd,MAAMxC,EAAIoc,EAAY/c,EAAce,EACpB,GAAZ8N,EAAKlO,IACRkO,EAAK1L,IAAI,IAAKxC,GAGdkO,EAAKhI,QAAQ,CAAEC,IAAK,IAAKC,GAAIpG,EAAG4F,SAAUyW,EAAmBpW,OAAQ/E,G,KAGlE,CACJgN,EAAK1L,IAAI,IAAK,GACd,MAAMtC,EAAIkc,EAAY/c,EAAce,EACpB,GAAZ8N,EAAKhO,IACRgO,EAAK1L,IAAI,IAAKtC,GAGdgO,EAAKhI,QAAQ,CAAEC,IAAK,IAAKC,GAAIlG,EAAG0F,SAAUyW,EAAmBpW,OAAQ/E,G,CAIvEkb,EAAYA,EAAY/c,EAAuB,EAATe,EACtC7B,EAASR,IAAI,UAAUyE,IAAI,SAAUpC,EAAO,G,CAG/C,CAEO,gBAAA+C,CAAiB5E,GACvBvC,MAAMmH,iBAAiB5E,GACvB,MAAMW,EAAchD,KAAK6B,IAAI,eACvBkd,EAAe1c,EAASR,IAAI,QAAQiH,cACpCkW,EAAiB3c,EAASR,IAAI,QAAQuH,gBAEzB,YAAfpG,GACC+b,GACHA,EAAazY,IAAI,WAAY,IAE1B0Y,GACHA,EAAe1Y,IAAI,WAAY,MAI5ByY,GACHA,EAAazY,IAAI,WAAY,GAE1B0Y,GACHA,EAAe1Y,IAAI,WAAY,GAGlC,EA1JA,sC,gDAAkC,eAClC,uC,gDAA0CnB,EAAK+F,WAAWC,OAAO,CAAC2U,GAAW1U,a,mEChFvE,MAAMgV,E", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/charts/flow/FlowDefaultTheme.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/Flow.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-chord/src/math.js", "webpack://@amcharts/amcharts5/./node_modules/d3-chord/src/chord.js", "webpack://@amcharts/amcharts5/./node_modules/d3-chord/src/array.js", "webpack://@amcharts/amcharts5/./node_modules/d3-chord/src/constant.js", "webpack://@amcharts/amcharts5/./node_modules/d3-chord/src/ribbon.js", "webpack://@amcharts/amcharts5/./node_modules/d3-array/src/ascending.js", "webpack://@amcharts/amcharts5/./node_modules/d3-array/src/descending.js", "webpack://@amcharts/amcharts5/./node_modules/d3-brush/src/brush.js", "webpack://@amcharts/amcharts5/./node_modules/d3-zoom/src/transform.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/FlowNode.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/FlowNodes.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/ChordNodes.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/FlowLink.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/ChordLink.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/Chord.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/ChordLinkDirected.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/ChordDirected.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/ChordNonRibbon.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/SankeyNodes.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/SankeyLink.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-sankey/node_modules/d3-array/src/sum.js", "webpack://@amcharts/amcharts5/./node_modules/d3-sankey/node_modules/d3-array/src/max.js", "webpack://@amcharts/amcharts5/./node_modules/d3-sankey/node_modules/d3-array/src/min.js", "webpack://@amcharts/amcharts5/./node_modules/d3-sankey/src/align.js", "webpack://@amcharts/amcharts5/./node_modules/d3-sankey/src/constant.js", "webpack://@amcharts/amcharts5/./node_modules/d3-sankey/src/sankey.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/Sankey.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/ArcDiagramNodes.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/ArcDiagramLink.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/flow/ArcDiagram.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/flow.js"], "sourcesContent": ["import type { DataItem } from \"../../core/render/Component\";\r\nimport type { IFlowNodesDataItem } from \"../../charts/flow/FlowNodes\";\r\n\r\nimport { Theme } from \"../../core/Theme\";\r\nimport { percent, p100, p50 } from \"../../core/util/Percent\";\r\nimport { ColorSet } from \"../../core/util/ColorSet\";\r\nimport { setColor } from \"../../themes/DefaultTheme\";\r\n\r\nimport * as $array from \"../../core/util/Array\";\r\nimport * as $ease from \"../../core/util/Ease\";\r\n\r\n\r\n/**\r\n * @ignore\r\n */\r\nexport class FlowDefaultTheme extends Theme {\r\n\tprotected setupDefaultRules() {\r\n\t\tsuper.setupDefaultRules();\r\n\r\n\t\tconst ic = this._root.interfaceColors;\r\n\t\tconst r = this.rule.bind(this);\r\n\t\t/**\r\n\t\t * ========================================================================\r\n\t\t * charts/flow\r\n\t\t * ========================================================================\r\n\t\t */\r\n\r\n\t\tr(\"Flow\").setAll({\r\n\t\t\twidth: p100,\r\n\t\t\theight: p100,\r\n\t\t\tpaddingLeft: 10,\r\n\t\t\tpaddingRight: 10,\r\n\t\t\tpaddingTop: 10,\r\n\t\t\tpaddingBottom: 10,\r\n\t\t\thiddenSize: 0.05,\r\n\t\t\tminSize: 0,\r\n\t\t\tminHiddenValue: 0\r\n\t\t});\r\n\r\n\t\tr(\"FlowNodes\").setAll({\r\n\t\t\tcolors: ColorSet.new(this._root, {}),\r\n\t\t\tlegendLabelText: \"{name}\",\r\n\t\t\tlegendValueText: \"{sumOutgoing.formatNumber('#.#')}\"\r\n\t\t});\r\n\r\n\r\n\t\tr(\"FlowNode\").setAll({\r\n\t\t\tsetStateOnChildren: true,\r\n\t\t\tcursorOverStyle: \"pointer\",\r\n\t\t\ttoggleKey: \"disabled\"\r\n\t\t})\r\n\r\n\r\n\t\tr(\"FlowNode\").states.create(\"disabled\", {}); // do not remove\r\n\r\n\t\tr(\"FlowNode\", [\"unknown\"]).setAll({\r\n\t\t\tdraggable: false,\r\n\t\t\topacity: 0\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"flow\"]).states.create(\"disabled\", {\r\n\t\t\tfill: ic.get(\"disabled\")\r\n\t\t})\r\n\r\n\t\tr(\"RadialLabel\", [\"flow\", \"node\"]).setAll({\r\n\t\t\ttext: \"{name}\",\r\n\t\t\tpopulateText: true\r\n\t\t});\r\n\r\n\t\tr(\"FlowLink\").setAll({\r\n\t\t\tfillStyle: \"gradient\",\r\n\t\t\tstrokeStyle: \"gradient\"\r\n\t\t});\r\n\r\n\t\tr(\"FlowLink\", [\"source\", \"unknown\"]).setAll({\r\n\t\t});\r\n\r\n\t\tr(\"FlowLink\", [\"target\", \"unknown\"]).setAll({\r\n\t\t});\r\n\r\n\r\n\t\tr(\"FlowNode\").events.on(\"pointerover\", (e) => {\r\n\t\t\tconst dataItem = e.target.dataItem as DataItem<IFlowNodesDataItem>;\r\n\t\t\tif (dataItem) {\r\n\t\t\t\tconst outgoing = dataItem.get(\"outgoingLinks\")\r\n\t\t\t\tif (outgoing) {\r\n\t\t\t\t\t$array.each(outgoing, (linkDataItem) => {\r\n\t\t\t\t\t\tconst link = (linkDataItem as any).get(\"link\");\r\n\t\t\t\t\t\tlink.hover();\r\n\t\t\t\t\t\tlink.hideTooltip();\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tconst incoming = dataItem.get(\"incomingLinks\")\r\n\t\t\t\tif (incoming) {\r\n\t\t\t\t\t$array.each(incoming, (linkDataItem) => {\r\n\t\t\t\t\t\tconst link = (linkDataItem as any).get(\"link\");\r\n\t\t\t\t\t\tlink.hover();\r\n\t\t\t\t\t\tlink.hideTooltip();\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tlet rectangle = (<any>dataItem).get(\"slice\") || (<any>dataItem).get(\"rectangle\");\r\n\t\t\tif (rectangle && rectangle.get(\"tooltipText\")) {\r\n\t\t\t\trectangle.showTooltip();\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\tr(\"FlowNode\").events.on(\"pointerout\", (e) => {\r\n\t\t\tconst dataItem = e.target.dataItem as DataItem<IFlowNodesDataItem>;\r\n\t\t\tif (dataItem) {\r\n\t\t\t\tconst outgoing = dataItem.get(\"outgoingLinks\")\r\n\t\t\t\tif (outgoing) {\r\n\t\t\t\t\t$array.each(outgoing, (linkDataItem) => {\r\n\t\t\t\t\t\t(linkDataItem as any).get(\"link\").unhover();\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tconst incoming = dataItem.get(\"incomingLinks\")\r\n\t\t\t\tif (incoming) {\r\n\t\t\t\t\t$array.each(incoming, (linkDataItem) => {\r\n\t\t\t\t\t\t(linkDataItem as any).get(\"link\").unhover();\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t});\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/flow: Sankey\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"Sankey\").setAll({\r\n\t\t\torientation: \"horizontal\",\r\n\t\t\tnodeAlign: \"justify\",\r\n\t\t\tlinkTension: 0.5,\r\n\t\t\tnodePadding: 10,\r\n\t\t\tnodeWidth: 10\r\n\t\t});\r\n\r\n\t\t// Class: RoundedRectangle\r\n\t\tr(\"RoundedRectangle\", [\"sankey\", \"node\", \"shape\"]).setAll({\r\n\t\t\tcornerRadiusTL: 0,\r\n\t\t\tcornerRadiusBL: 0,\r\n\t\t\tcornerRadiusTR: 0,\r\n\t\t\tcornerRadiusBR: 0\r\n\t\t});\r\n\r\n\t\tr(\"RoundedRectangle\", [\"shape\"]).states.create(\"disabled\", {\r\n\t\t\tfill: ic.get(\"disabled\")\r\n\t\t})\r\n\r\n\t\tr(\"SankeyLink\").setAll({\r\n\t\t\tcontrolPointDistance: 0.2\r\n\t\t});\r\n\r\n\t\tr(\"FlowNode\", [\"sankey\"]).setAll({\r\n\t\t\tdraggable: true\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Graphics\", [\"sankey\", \"link\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tfillOpacity: 0.2,\r\n\t\t\t\tstrokeOpacity: 0,\r\n\t\t\t\tinteractive: true,\r\n\t\t\t\ttooltipText: \"{sourceId} - {targetId}: {value}\"\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"grid\");\r\n\t\t}\r\n\r\n\t\tr(\"Graphics\", [\"sankey\", \"link\"]).states.create(\"hover\", { fillOpacity: 0.5 });\r\n\r\n\t\tr(\"Label\", [\"sankey\", \"node\"]).setAll({\r\n\t\t\ttext: \"{name}\",\r\n\t\t\tpopulateText: true\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"sankey\", \"horizontal\"]).setAll({\r\n\t\t\ty: p50,\r\n\t\t\tcenterY: p50,\r\n\t\t\tpaddingLeft: 15\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"sankey\", \"vertical\"]).setAll({\r\n\t\t\tx: p50,\r\n\t\t\tcenterX: p50,\r\n\t\t\tpaddingTop: 15\r\n\t\t});\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/flow: Chord\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"Chord\").setAll({\r\n\t\t\tradius: percent(90),\r\n\t\t\tnodeWidth: 10,\r\n\t\t\tpadAngle: 1,\r\n\t\t\tstartAngle: 0,\r\n\t\t\tsort:\"none\"\r\n\t\t});\r\n\r\n\t\tr(\"ChordDirected\").setAll({\r\n\t\t\tlinkHeadRadius: 10\r\n\t\t});\r\n\r\n\t\tr(\"ChordNodes\").setAll({\r\n\t\t\tx: p50,\r\n\t\t\ty: p50\r\n\t\t});\r\n\r\n\t\tr(\"FlowNode\", [\"chord\"]).setAll({\r\n\t\t\tdraggable: true\r\n\t\t});\r\n\r\n\t\tr(\"ChordLink\").setAll({\r\n\t\t\tsourceRadius: p100,\r\n\t\t\ttargetRadius: p100,\r\n\t\t\tfillStyle: \"solid\",\r\n\t\t\tstrokeStyle: \"solid\",\r\n\t\t\ttooltipText: \"{sourceId} - {targetId}: {value}\"\r\n\t\t});\r\n\r\n\t\tr(\"Slice\", [\"chord\", \"node\", \"shape\"]).setAll({\r\n\t\t\tcornerRadius: 0\r\n\t\t})\r\n\r\n\t\tr(\"Slice\", [\"shape\"]).states.create(\"disabled\", {\r\n\t\t\tfill: ic.get(\"disabled\")\r\n\t\t})\r\n\r\n\t\tr(\"RadialLabel\", [\"chord\", \"node\"]).setAll({\r\n\t\t\tradius: 5,\r\n\t\t\ttextType: \"circular\"\r\n\t\t});\r\n\r\n\t\tr(\"ChordLinkDirected\").setAll({\r\n\t\t\theadRadius: 10\r\n\t\t});\r\n\r\n\t\t// Class: Graphics\r\n\t\t{\r\n\t\t\tconst rule = r(\"Graphics\", [\"chord\", \"link\", \"shape\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tfillOpacity: 0.2,\r\n\t\t\t\tstrokeOpacity: 0,\r\n\t\t\t\tinteractive: true\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"grid\");\r\n\t\t\tsetColor(rule, \"stroke\", ic, \"grid\");\r\n\t\t}\r\n\r\n\t\tr(\"Graphics\", [\"chord\", \"link\", \"shape\"]).states.create(\"hover\", { fillOpacity: 0.5 });\r\n\r\n\t\tr(\"ChordNonRibbon\").setAll({\r\n\t\t\tlinkType: \"curve\" // \"line\" | \"curve\"\r\n\t\t})\r\n\r\n\t\tr(\"ChordLink\", [\"basic\"]).setAll({\r\n\t\t\tfillStyle: \"none\",\r\n\t\t\tstrokeStyle: \"source\"\r\n\t\t});\r\n\r\n\t\tr(\"Graphics\", [\"chord\", \"link\", \"shape\", \"basic\"]).setAll({\r\n\t\t\tstrokeOpacity: 0.4\r\n\t\t});\r\n\r\n\t\tr(\"Graphics\", [\"chord\", \"link\", \"shape\", \"basic\"]).states.create(\"hover\", { strokeWidth: 2, strokeOpacity: 1 });\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/flow: ArcDiagram\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"ArcDiagram\").setAll({\r\n\t\t\torientation: \"horizontal\",\r\n\t\t\tnodePadding: 5,\r\n\t\t\tminRadius: 5,\r\n\t\t\tradiusKey: \"sum\",\r\n\t\t\tanimationEasing: $ease.out($ease.cubic)\r\n\t\t});\r\n\r\n\t\tr(\"ArcDiagramNodes\", [\"horizontal\"]).setAll({\r\n\t\t\ty: p100,\r\n\t\t\tcenterY: p100\r\n\t\t});\r\n\r\n\t\tr(\"ArcDiagramNodes\", [\"vertical\"]).setAll({\r\n\t\t\tcenterX: 0\r\n\t\t});\r\n\r\n\t\tr(\"Circle\", [\"arcdiagram\", \"node\", \"shape\"]).setAll({\r\n\t\t\ttooltipText: \"{name}: {sum}\"\r\n\t\t});\r\n\r\n\t\tr(\"Circle\", [\"arcdiagram\", \"node\", \"shape\"]).states.create(\"disabled\", {\r\n\t\t\tfill: ic.get(\"disabled\")\r\n\t\t})\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"ArcDiagramLink\", [\"link\", \"shape\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tstrokeOpacity: 0.15,\r\n\t\t\t\tstrokeStyle: \"solid\",\r\n\t\t\t\tfillStyle: \"none\",\r\n\t\t\t\tisMeasured: false\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"stroke\", ic, \"grid\");\r\n\t\t}\r\n\r\n\t\tr(\"ArcDiagramLink\", [\"link\", \"shape\"]).states.create(\"hover\", {\r\n\t\t\tstrokeOpacity: 1\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"arcdiagram\", \"node\"]).setAll({\r\n\t\t\ttext: \"{name}\",\r\n\t\t\tpopulateText: true\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"arcdiagram\", \"horizontal\"]).setAll({\r\n\t\t\ty: 0,\r\n\t\t\tcenterY: p50,\r\n\t\t\tcenterX: p100,\r\n\t\t\trotation: -90\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"arcdiagram\", \"vertical\"]).setAll({\r\n\t\t\tcenterY: p50,\r\n\t\t\tcenterX: p100,\r\n\t\t\tpaddingRight: 15\r\n\t\t});\r\n\t}\r\n}\r\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { Color } from \"../../core/util/Color\";\nimport type { FlowLink } from \"./FlowLink\";\nimport type { FlowNodes, IFlowNodesDataItem } from \"./FlowNodes\";\nimport type { ListTemplate } from \"../../core/util/List\";\nimport type { Bullet } from \"../../core/render/Bullet\";\nimport type * as d3sankey from \"d3-sankey\";\n\nimport { FlowDefaultTheme } from \"./FlowDefaultTheme\";\nimport { Series, ISeriesSettings, ISeriesDataItem, ISeriesPrivate, ISeriesEvents } from \"../../core/render/Series\";\nimport { Container } from \"../../core/render/Container\";\nimport { LinearGradient } from \"../../core/render/gradients/LinearGradient\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $type from \"../../core/util/Type\";\n\nexport interface IFlowDataItem extends ISeriesDataItem {\n\n\t/**\n\t * Link value.\n\t */\n\tvalue: number;\n\n\t/**\n\t * @ignore\n\t */\n\tvalueWorking: number;\n\n\t/**\n\t * Associated link element.\n\t */\n\tlink: FlowLink;\n\n\t/**\n\t * Link's color.\n\t */\n\tfill: Color;\n\n\t/**\n\t * @ignore\n\t */\n\td3SankeyLink: d3sankey.SankeyLink<d3sankey.SankeyExtraProperties, d3sankey.SankeyExtraProperties>;\n\n\t/**\n\t * An ID of the target node.\n\t */\n\ttargetId: string;\n\n\t/**\n\t * An ID of the source node.\n\t */\n\tsourceId: string;\n\n\t/**\n\t * A data item of the source node.\n\t */\n\tsource: DataItem<IFlowNodesDataItem>;\n\n\t/**\n\t * A data item of the target node.\n\t */\n\ttarget: DataItem<IFlowNodesDataItem>;\n\n}\n\nexport interface IFlowSettings extends ISeriesSettings {\n\n\t/**\n\t * A field in data which holds source node ID.\n\t */\n\tsourceIdField?: string;\n\n\t/**\n\t * A field in data which holds target node ID.\n\t */\n\ttargetIdField?: string;\n\n\t/**\n\t * The thickness of node strip in pixels.\n\t *\n\t * @default 10\n\t */\n\tnodeWidth?: number;\n\n\t/**\n\t * Minimum gap between adjacent nodes.\n\t *\n\t * @default 10\n\t */\n\tnodePadding?: number;\n\n\t/**\n\t * Minimum size of the link.\n\t * \n\t * It's a relative value to the sum of all values in the series. If set,\n\t * this relative value will be used for small value nodes when calculating\n\t * their size. For example, if it's set to `0.01`, small nodes will be\n\t * sized like their value is 1% of the total sum of all values in series.\n\t * \n\t * @default 0\n\t * @since 5.1.5\n\t */\n\tminSize?: number;\n\n\t/**\n\t * Relative size of hidden links.\n\t * \n\t * Links are hidden when user clicks on nodes (if `toggleKey` on nodes is set\n\t * to `\"disabled\"`).\n\t * \n\t * This allows hidden node to remain visible so that user could click on it\n\t * again to show it and its links.\n\t * \n\t * @default 0.05\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/flow-charts/#Node_toggling} for more info\n\t * @since 5.4.1\n\t */\n\thiddenSize?: number;\n\n\t/**\n\t * Minimum value of hidden links.\n\t * \n\t * Links are hidden when user clicks on nodes (if `toggleKey` on nodes is set\n\t * to `\"disabled\"`).\n\t * \n\t * @default 0\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/flow-charts/#Node_toggling} for more info\n\t * @since 5.4.1\n\t */\n\tminHiddenValue?: number;\n}\n\nexport interface IFlowPrivate extends ISeriesPrivate {\n\tvalueSum?: number;\n\tvalueLow?: number;\n\tvalueHigh?: number;\n}\n\nexport interface IFlowEvents extends ISeriesEvents {\n}\n\n/**\n * A base class for all flow type series: [[Sankey]] and [[Chord]].\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/flow-charts/} for more info\n */\nexport abstract class Flow extends Series {\n\tpublic static className: string = \"Flow\";\n\tpublic static classNames: Array<string> = Series.classNames.concat([Flow.className]);\n\n\tdeclare public _settings: IFlowSettings;\n\tdeclare public _privateSettings: IFlowPrivate;\n\tdeclare public _dataItemSettings: IFlowDataItem;\n\tdeclare public _events: IFlowEvents;\n\n\t/**\n\t * @ignore\n\t */\n\tdeclare public readonly nodes: FlowNodes;\n\n\t/**\n\t * Container series will place their links in.\n\t *\n\t * @default Container.new()\n\t */\n\tpublic readonly linksContainer = this.children.push(Container.new(this._root, {}));\n\n\t/**\n\t * @ignore\n\t */\n\tpublic abstract readonly links: ListTemplate<FlowLink>;\n\n\tprotected _nodesData: d3sankey.SankeyNodeMinimal<{}, {}>[] = [];\n\tprotected _linksData: { source: d3sankey.SankeyNodeMinimal<{}, {}>, target: d3sankey.SankeyNodeMinimal<{}, {}>, value: number }[] = [];\n\tprotected _index = 0;\n\tprotected _nodesDataSet: boolean = false;\n\n\tprotected _linksByIndex: { [index: string]: any } = {};\n\tprotected _afterNew() {\n\t\tthis._defaultThemes.push(FlowDefaultTheme.new(this._root));\n\n\t\tthis.fields.push(\"disabled\", \"sourceId\", \"targetId\");\n\n\t\tif (this.nodes) {\n\t\t\tthis.nodes.flow = this;\n\t\t}\n\n\t\tsuper._afterNew();\n\n\t\tthis.children.push(this.bulletsContainer);\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic abstract makeLink(dataItem: DataItem<this[\"_dataItemSettings\"]>): FlowLink;\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\n\t\tconst nodes = this.nodes;\n\t\tif (nodes) {\n\t\t\tlet unknown = false;\n\t\t\tlet sourceId = dataItem.get(\"sourceId\");\n\t\t\tlet sourceDataItem = nodes.getDataItemById(sourceId);\n\n\t\t\tif (!sourceDataItem) {\n\t\t\t\tif (sourceId == null) {\n\t\t\t\t\tsourceId = \"undefined\" + this._index;\n\t\t\t\t\tthis._index++;\n\t\t\t\t\tunknown = true;\n\t\t\t\t}\n\t\t\t\tnodes.data.push({ id: sourceId, unknown: unknown });\n\t\t\t\tsourceDataItem = nodes.getDataItemById(sourceId)!;\n\t\t\t\tif (!unknown) {\n\t\t\t\t\tsourceDataItem.set(\"name\", sourceId);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tunknown = false;\n\t\t\tlet targetId = dataItem.get(\"targetId\");\n\n\t\t\tlet targetDataItem = nodes.getDataItemById(targetId);\n\t\t\tif (!targetDataItem) {\n\t\t\t\tif (targetId == null) {\n\t\t\t\t\ttargetId = \"undefined\" + this._index;\n\t\t\t\t\tthis._index++;\n\t\t\t\t\tunknown = true;\n\t\t\t\t}\n\n\t\t\t\tnodes.data.push({ id: targetId, unknown: unknown });\n\t\t\t\ttargetDataItem = nodes.getDataItemById(targetId)!;\n\t\t\t\tif (!unknown) {\n\t\t\t\t\ttargetDataItem.set(\"name\", targetId);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (sourceDataItem) {\n\t\t\t\tdataItem.set(\"source\", sourceDataItem);\n\t\t\t\tnodes.addOutgoingLink(sourceDataItem, dataItem);\n\t\t\t}\n\n\t\t\tif (targetDataItem) {\n\t\t\t\tdataItem.set(\"target\", targetDataItem);\n\t\t\t\tnodes.addincomingLink(targetDataItem, dataItem);\n\t\t\t}\n\n\t\t\tdataItem.set(\"link\", this.makeLink(dataItem));\n\n\t\t\tconst sourceIndex = this.nodes.dataItems.indexOf(sourceDataItem);\n\t\t\tconst targetIndex = this.nodes.dataItems.indexOf(targetDataItem);\n\n\t\t\tthis._linksByIndex[sourceIndex + \"_\" + targetIndex] = dataItem;\n\n\t\t\tif (sourceDataItem.get(\"unknown\")) {\n\t\t\t\tif (targetDataItem) {\n\t\t\t\t\tsourceDataItem.set(\"fill\", targetDataItem.get(\"fill\"));\n\t\t\t\t}\n\n\t\t\t\tdataItem.get(\"link\").set(\"fillStyle\", \"gradient\");\n\t\t\t}\n\n\n\t\t\tif (targetDataItem.get(\"unknown\")) {\n\t\t\t\tif (sourceDataItem) {\n\t\t\t\t\ttargetDataItem.set(\"fill\", sourceDataItem.get(\"fill\"));\n\t\t\t\t}\n\n\t\t\t\tdataItem.get(\"link\").set(\"fillStyle\", \"gradient\");\n\t\t\t}\n\n\t\t\tthis._updateLinkColor(dataItem);\n\t\t}\n\t}\n\n\tprotected _onDataClear() {\n\t\tif (!this.nodes._userDataSet) {\n\t\t\tthis.nodes.data.setAll([]);\n\t\t\tthis.nodes._userDataSet = false;\n\t\t}\n\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tlet valueLow = Infinity;\n\t\tlet valueHigh = -Infinity;\n\t\tlet valueSum = 0;\n\n\t\tif (this._valuesDirty) {\n\t\t\tthis._nodesData = [];\n\t\t\tconst nodes = this.nodes;\n\t\t\tif (nodes) {\n\t\t\t\t$array.each(nodes.dataItems, (dataItem) => {\n\t\t\t\t\tconst d3SankeyNode = dataItem.get(\"d3SankeyNode\");\n\t\t\t\t\tthis._nodesData.push(d3SankeyNode);\n\n\t\t\t\t\tconst incoming = dataItem.get(\"incomingLinks\")\n\n\t\t\t\t\tlet sumIncoming = 0;\n\t\t\t\t\tlet sumIncomingWorking = 0;\n\t\t\t\t\tif (incoming) {\n\t\t\t\t\t\t$array.each(incoming, (link) => {\n\t\t\t\t\t\t\tconst value = link.get(\"value\");\n\t\t\t\t\t\t\tconst workingValue = link.get(\"valueWorking\");\n\t\t\t\t\t\t\tsumIncoming += value;\n\t\t\t\t\t\t\tsumIncomingWorking += workingValue;\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\n\t\t\t\t\tdataItem.set(\"sumIncoming\", sumIncoming);\n\t\t\t\t\tdataItem.set(\"sumIncomingWorking\", sumIncomingWorking);\n\n\t\t\t\t\tconst outgoing = dataItem.get(\"outgoingLinks\")\n\t\t\t\t\tlet sumOutgoing = 0;\n\t\t\t\t\tlet sumOutgoingWorking = 0;\n\t\t\t\t\tif (outgoing) {\n\t\t\t\t\t\t$array.each(outgoing, (link) => {\n\t\t\t\t\t\t\tconst value = link.get(\"value\");\n\t\t\t\t\t\t\tconst workingValue = link.get(\"valueWorking\");\n\t\t\t\t\t\t\tsumOutgoing += value;\n\t\t\t\t\t\t\tsumOutgoingWorking += workingValue;\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\n\t\t\t\t\tdataItem.set(\"sumOutgoing\", sumOutgoing);\n\t\t\t\t\tdataItem.set(\"sumOutgoingWorking\", sumOutgoingWorking);\n\n\t\t\t\t\tdataItem.set(\"sum\", sumIncoming + sumOutgoing);\n\t\t\t\t\tdataItem.set(\"sumWorking\", sumIncomingWorking + sumOutgoingWorking);\n\n\t\t\t\t\tnodes.updateLegendValue(dataItem);\n\t\t\t\t})\n\t\t\t}\n\t\t\tthis._linksData = [];\n\n\t\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\t\tlet value = dataItem.get(\"value\");\n\t\t\t\tif ($type.isNumber(value)) {\n\t\t\t\t\tif (value < valueLow) {\n\t\t\t\t\t\tvalueLow = value;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (value > valueHigh) {\n\t\t\t\t\t\tvalueHigh = value;\n\t\t\t\t\t}\n\t\t\t\t\tvalueSum += value;\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\t\tlet value = dataItem.get(\"value\");\n\t\t\t\tif ($type.isNumber(value)) {\n\t\t\t\t\tlet valueWorking = dataItem.get(\"valueWorking\");\n\t\t\t\t\tlet minSize = this.get(\"minSize\", 0);\n\t\t\t\t\tif (minSize > 0) {\n\t\t\t\t\t\tif (valueWorking < minSize * valueSum) {\n\t\t\t\t\t\t\tvalueWorking = minSize * valueSum;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tlet d3SankeyLink = { source: dataItem.get(\"source\").get(\"d3SankeyNode\"), target: dataItem.get(\"target\").get(\"d3SankeyNode\"), value: valueWorking };\n\t\t\t\t\tdataItem.setRaw(\"d3SankeyLink\", d3SankeyLink);\n\t\t\t\t\tthis._linksData.push(d3SankeyLink);\n\t\t\t\t\tthis.updateLegendValue(dataItem);\n\t\t\t\t}\n\t\t\t})\n\n\t\t\tthis.setPrivateRaw(\"valueHigh\", valueHigh);\n\t\t\tthis.setPrivateRaw(\"valueLow\", valueLow);\n\t\t\tthis.setPrivateRaw(\"valueSum\", valueSum);\n\t\t}\n\t}\n\n\tpublic _updateLinkColor(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst link = dataItem.get(\"link\");\n\n\t\tconst fillStyle = link.get(\"fillStyle\");\n\t\tconst strokeStyle = link.get(\"strokeStyle\");\n\t\tconst source = dataItem.get(\"source\");\n\t\tconst target = dataItem.get(\"target\");\n\t\tconst sourceFill = source.get(\"fill\");\n\t\tconst targetFill = target.get(\"fill\");\n\t\tlink.remove(\"fillGradient\");\n\t\tlink.remove(\"strokeGradient\");\n\n\t\tswitch (fillStyle) {\n\n\t\t\tcase \"solid\":\n\t\t\t\tlink._applyTemplates();\n\t\t\t\tbreak;\n\t\t\tcase \"source\":\n\t\t\t\tlink.set(\"fill\", sourceFill);\n\t\t\t\tbreak;\n\n\t\t\tcase \"target\":\n\t\t\t\tlink.set(\"fill\", targetFill);\n\t\t\t\tbreak;\n\n\t\t\tcase \"gradient\":\n\t\t\t\tlet gradient = link._fillGradient;\n\t\t\t\tif (!gradient) {\n\t\t\t\t\tgradient = LinearGradient.new(this._root, {});\n\t\t\t\t}\n\t\t\t\tconst sourceStop: any = { color: sourceFill }\n\t\t\t\tif (source.get(\"unknown\")) {\n\t\t\t\t\tsourceStop.opacity = 0;\n\t\t\t\t}\n\t\t\t\tconst targetStop: any = { color: targetFill };\n\t\t\t\tif (target.get(\"unknown\")) {\n\t\t\t\t\ttargetStop.opacity = 0;\n\t\t\t\t}\n\n\t\t\t\tgradient.set(\"stops\", [sourceStop, targetStop]);\n\t\t\t\tlink._fillGradient = gradient;\n\n\t\t\t\tlink.set(\"fillGradient\", gradient);\n\t\t\t\tbreak;\n\t\t\tcase \"none\":\n\t\t\t\tlink.set(\"fill\", undefined); // do not use remove!\n\t\t\t\tbreak;\n\t\t}\n\n\t\tswitch (strokeStyle) {\n\t\t\tcase \"solid\":\n\t\t\t\tlink._applyTemplates();\n\t\t\t\tbreak;\n\n\t\t\tcase \"source\":\n\t\t\t\tlink.set(\"stroke\", sourceFill);\n\t\t\t\tbreak;\n\n\t\t\tcase \"target\":\n\t\t\t\tlink.set(\"stroke\", targetFill);\n\t\t\t\tbreak;\n\t\t\tcase \"gradient\":\n\t\t\t\tlet gradient = link._strokeGradient;\n\t\t\t\tif (!gradient) {\n\t\t\t\t\tgradient = LinearGradient.new(this._root, {});\n\t\t\t\t\tconst sourceStop: any = { color: sourceFill }\n\t\t\t\t\tif (source.get(\"unknown\")) {\n\t\t\t\t\t\tsourceStop.opacity = 0;\n\t\t\t\t\t}\n\t\t\t\t\tconst targetStop: any = { color: targetFill };\n\t\t\t\t\tif (target.get(\"unknown\")) {\n\t\t\t\t\t\ttargetStop.opacity = 0;\n\t\t\t\t\t}\n\n\t\t\t\t\tgradient.set(\"stops\", [sourceStop, targetStop]);\n\t\t\t\t\tlink._strokeGradient = gradient;\n\t\t\t\t}\n\t\t\t\tlink.set(\"strokeGradient\", gradient);\n\t\t\t\tbreak;\n\n\t\t\tcase \"none\":\n\t\t\t\tlink.remove(\"stroke\");\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tlet link = dataItem.get(\"link\");\n\t\tif (link) {\n\t\t\tthis.links.removeValue(link);\n\t\t\tlink.dispose();\n\t\t}\n\t}\n\n\t/**\n\t * Shows diagram's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async hideDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.hideDataItem(dataItem, duration)];\n\t\tconst hiddenState = this.states.create(\"hidden\", {})\n\n\t\tconst stateAnimationDuration = \"stateAnimationDuration\";\n\t\tconst stateAnimationEasing = \"stateAnimationEasing\";\n\n\t\tif (!$type.isNumber(duration)) {\n\t\t\tduration = hiddenState.get(stateAnimationDuration, this.get(stateAnimationDuration, 0));\n\t\t}\n\n\t\tconst easing = hiddenState.get(stateAnimationEasing, this.get(stateAnimationEasing));\n\n\t\tpromises.push(dataItem.animate({\n\t\t\tkey: \"valueWorking\" as any,\n\t\t\tto: Math.max(this.get(\"minHiddenValue\", 0), this.get(\"hiddenSize\", 0) * dataItem.get(\"value\")),\n\t\t\tduration: duration,\n\t\t\teasing: easing\n\t\t}).waitForStop());\n\n\t\tconst linkGraphics = dataItem.get(\"link\");\n\t\tlinkGraphics.hide();\n\n\t\tawait Promise.all(promises);\n\t}\n\n\t/**\n\t * Shows diagram's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async showDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.showDataItem(dataItem, duration)];\n\n\t\tif (!$type.isNumber(duration)) {\n\t\t\tduration = this.get(\"stateAnimationDuration\", 0);\n\t\t}\n\n\t\tconst easing = this.get(\"stateAnimationEasing\");\n\n\t\tpromises.push(dataItem.animate({ key: \"valueWorking\" as any, to: dataItem.get(\"value\"), duration: duration, easing: easing }).waitForStop());\n\n\t\tconst linkGraphics = dataItem.get(\"link\");\n\t\tlinkGraphics.show();\n\n\t\tawait Promise.all(promises);\n\t}\n\n\tpublic _positionBullet(bullet: Bullet) {\n\t\tconst sprite = bullet.get(\"sprite\");\n\n\t\tif (sprite) {\n\t\t\tconst dataItem = sprite.dataItem as DataItem<this[\"_dataItemSettings\"]>;\n\t\t\tif (dataItem) {\n\t\t\t\tconst link = dataItem.get(\"link\");\n\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\n\t\t\t\tif (sprite) {\n\t\t\t\t\tconst point = link.getPoint(this._getBulletLocation(bullet));\n\t\t\t\t\tsprite.setAll({ x: point.x, y: point.y });\n\n\t\t\t\t\tif (bullet.get(\"autoRotate\")) {\n\t\t\t\t\t\tsprite.set(\"rotation\", point.angle + bullet.get(\"autoRotateAngle\", 0));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _getBulletLocation(bullet: Bullet): number {\n\t\treturn bullet.get(\"locationY\", 0);\n\t}\n}\n", "export var abs = Math.abs;\nexport var cos = Math.cos;\nexport var sin = Math.sin;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var tau = pi * 2;\nexport var max = Math.max;\nexport var epsilon = 1e-12;\n", "import {max, tau} from \"./math.js\";\n\nfunction range(i, j) {\n  return Array.from({length: j - i}, (_, k) => i + k);\n}\n\nfunction compareValue(compare) {\n  return function(a, b) {\n    return compare(\n      a.source.value + a.target.value,\n      b.source.value + b.target.value\n    );\n  };\n}\n\nexport default function() {\n  return chord(false, false);\n}\n\nexport function chordTranspose() {\n  return chord(false, true);\n}\n\nexport function chordDirected() {\n  return chord(true, false);\n}\n\nfunction chord(directed, transpose) {\n  var padAngle = 0,\n      sortGroups = null,\n      sortSubgroups = null,\n      sortChords = null;\n\n  function chord(matrix) {\n    var n = matrix.length,\n        groupSums = new Array(n),\n        groupIndex = range(0, n),\n        chords = new Array(n * n),\n        groups = new Array(n),\n        k = 0, dx;\n\n    matrix = Float64Array.from({length: n * n}, transpose\n        ? (_, i) => matrix[i % n][i / n | 0]\n        : (_, i) => matrix[i / n | 0][i % n]);\n\n    // Compute the scaling factor from value to angle in [0, 2pi].\n    for (let i = 0; i < n; ++i) {\n      let x = 0;\n      for (let j = 0; j < n; ++j) x += matrix[i * n + j] + directed * matrix[j * n + i];\n      k += groupSums[i] = x;\n    }\n    k = max(0, tau - padAngle * n) / k;\n    dx = k ? padAngle : tau / n;\n\n    // Compute the angles for each group and constituent chord.\n    {\n      let x = 0;\n      if (sortGroups) groupIndex.sort((a, b) => sortGroups(groupSums[a], groupSums[b]));\n      for (const i of groupIndex) {\n        const x0 = x;\n        if (directed) {\n          const subgroupIndex = range(~n + 1, n).filter(j => j < 0 ? matrix[~j * n + i] : matrix[i * n + j]);\n          if (sortSubgroups) subgroupIndex.sort((a, b) => sortSubgroups(a < 0 ? -matrix[~a * n + i] : matrix[i * n + a], b < 0 ? -matrix[~b * n + i] : matrix[i * n + b]));\n          for (const j of subgroupIndex) {\n            if (j < 0) {\n              const chord = chords[~j * n + i] || (chords[~j * n + i] = {source: null, target: null});\n              chord.target = {index: i, startAngle: x, endAngle: x += matrix[~j * n + i] * k, value: matrix[~j * n + i]};\n            } else {\n              const chord = chords[i * n + j] || (chords[i * n + j] = {source: null, target: null});\n              chord.source = {index: i, startAngle: x, endAngle: x += matrix[i * n + j] * k, value: matrix[i * n + j]};\n            }\n          }\n          groups[i] = {index: i, startAngle: x0, endAngle: x, value: groupSums[i]};\n        } else {\n          const subgroupIndex = range(0, n).filter(j => matrix[i * n + j] || matrix[j * n + i]);\n          if (sortSubgroups) subgroupIndex.sort((a, b) => sortSubgroups(matrix[i * n + a], matrix[i * n + b]));\n          for (const j of subgroupIndex) {\n            let chord;\n            if (i < j) {\n              chord = chords[i * n + j] || (chords[i * n + j] = {source: null, target: null});\n              chord.source = {index: i, startAngle: x, endAngle: x += matrix[i * n + j] * k, value: matrix[i * n + j]};\n            } else {\n              chord = chords[j * n + i] || (chords[j * n + i] = {source: null, target: null});\n              chord.target = {index: i, startAngle: x, endAngle: x += matrix[i * n + j] * k, value: matrix[i * n + j]};\n              if (i === j) chord.source = chord.target;\n            }\n            if (chord.source && chord.target && chord.source.value < chord.target.value) {\n              const source = chord.source;\n              chord.source = chord.target;\n              chord.target = source;\n            }\n          }\n          groups[i] = {index: i, startAngle: x0, endAngle: x, value: groupSums[i]};\n        }\n        x += dx;\n      }\n    }\n\n    // Remove empty chords.\n    chords = Object.values(chords);\n    chords.groups = groups;\n    return sortChords ? chords.sort(sortChords) : chords;\n  }\n\n  chord.padAngle = function(_) {\n    return arguments.length ? (padAngle = max(0, _), chord) : padAngle;\n  };\n\n  chord.sortGroups = function(_) {\n    return arguments.length ? (sortGroups = _, chord) : sortGroups;\n  };\n\n  chord.sortSubgroups = function(_) {\n    return arguments.length ? (sortSubgroups = _, chord) : sortSubgroups;\n  };\n\n  chord.sortChords = function(_) {\n    return arguments.length ? (_ == null ? sortChords = null : (sortChords = compareValue(_))._ = _, chord) : sortChords && sortChords._;\n  };\n\n  return chord;\n}\n", "export var slice = Array.prototype.slice;\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {path} from \"d3-path\";\nimport {slice} from \"./array.js\";\nimport constant from \"./constant.js\";\nimport {abs, cos, epsilon, halfPi, sin} from \"./math.js\";\n\nfunction defaultSource(d) {\n  return d.source;\n}\n\nfunction defaultTarget(d) {\n  return d.target;\n}\n\nfunction defaultRadius(d) {\n  return d.radius;\n}\n\nfunction defaultStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction defaultEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction defaultPadAngle() {\n  return 0;\n}\n\nfunction defaultArrowheadRadius() {\n  return 10;\n}\n\nfunction ribbon(headRadius) {\n  var source = defaultSource,\n      target = defaultTarget,\n      sourceRadius = defaultRadius,\n      targetRadius = defaultRadius,\n      startAngle = defaultStartAngle,\n      endAngle = defaultEndAngle,\n      padAngle = defaultPadAngle,\n      context = null;\n\n  function ribbon() {\n    var buffer,\n        s = source.apply(this, arguments),\n        t = target.apply(this, arguments),\n        ap = padAngle.apply(this, arguments) / 2,\n        argv = slice.call(arguments),\n        sr = +sourceRadius.apply(this, (argv[0] = s, argv)),\n        sa0 = startAngle.apply(this, argv) - halfPi,\n        sa1 = endAngle.apply(this, argv) - halfPi,\n        tr = +targetRadius.apply(this, (argv[0] = t, argv)),\n        ta0 = startAngle.apply(this, argv) - halfPi,\n        ta1 = endAngle.apply(this, argv) - halfPi;\n\n    if (!context) context = buffer = path();\n\n    if (ap > epsilon) {\n      if (abs(sa1 - sa0) > ap * 2 + epsilon) sa1 > sa0 ? (sa0 += ap, sa1 -= ap) : (sa0 -= ap, sa1 += ap);\n      else sa0 = sa1 = (sa0 + sa1) / 2;\n      if (abs(ta1 - ta0) > ap * 2 + epsilon) ta1 > ta0 ? (ta0 += ap, ta1 -= ap) : (ta0 -= ap, ta1 += ap);\n      else ta0 = ta1 = (ta0 + ta1) / 2;\n    }\n\n    context.moveTo(sr * cos(sa0), sr * sin(sa0));\n    context.arc(0, 0, sr, sa0, sa1);\n    if (sa0 !== ta0 || sa1 !== ta1) {\n      if (headRadius) {\n        var hr = +headRadius.apply(this, arguments), tr2 = tr - hr, ta2 = (ta0 + ta1) / 2;\n        context.quadraticCurveTo(0, 0, tr2 * cos(ta0), tr2 * sin(ta0));\n        context.lineTo(tr * cos(ta2), tr * sin(ta2));\n        context.lineTo(tr2 * cos(ta1), tr2 * sin(ta1));\n      } else {\n        context.quadraticCurveTo(0, 0, tr * cos(ta0), tr * sin(ta0));\n        context.arc(0, 0, tr, ta0, ta1);\n      }\n    }\n    context.quadraticCurveTo(0, 0, sr * cos(sa0), sr * sin(sa0));\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  if (headRadius) ribbon.headRadius = function(_) {\n    return arguments.length ? (headRadius = typeof _ === \"function\" ? _ : constant(+_), ribbon) : headRadius;\n  };\n\n  ribbon.radius = function(_) {\n    return arguments.length ? (sourceRadius = targetRadius = typeof _ === \"function\" ? _ : constant(+_), ribbon) : sourceRadius;\n  };\n\n  ribbon.sourceRadius = function(_) {\n    return arguments.length ? (sourceRadius = typeof _ === \"function\" ? _ : constant(+_), ribbon) : sourceRadius;\n  };\n\n  ribbon.targetRadius = function(_) {\n    return arguments.length ? (targetRadius = typeof _ === \"function\" ? _ : constant(+_), ribbon) : targetRadius;\n  };\n\n  ribbon.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), ribbon) : startAngle;\n  };\n\n  ribbon.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), ribbon) : endAngle;\n  };\n\n  ribbon.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), ribbon) : padAngle;\n  };\n\n  ribbon.source = function(_) {\n    return arguments.length ? (source = _, ribbon) : source;\n  };\n\n  ribbon.target = function(_) {\n    return arguments.length ? (target = _, ribbon) : target;\n  };\n\n  ribbon.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), ribbon) : context;\n  };\n\n  return ribbon;\n}\n\nexport default function() {\n  return ribbon();\n}\n\nexport function ribbonArrow() {\n  return ribbon(defaultArrowheadRadius);\n}\n", "export default function ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function descending(a, b) {\n  return a == null || b == null ? NaN\n    : b < a ? -1\n    : b > a ? 1\n    : b >= a ? 0\n    : NaN;\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolate} from \"d3-interpolate\";\nimport {pointer, select} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport BrushEvent from \"./event.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\nvar MODE_DRAG = {name: \"drag\"},\n    MODE_SPACE = {name: \"space\"},\n    MODE_HANDLE = {name: \"handle\"},\n    MODE_CENTER = {name: \"center\"};\n\nconst {abs, max, min} = Math;\n\nfunction number1(e) {\n  return [+e[0], +e[1]];\n}\n\nfunction number2(e) {\n  return [number1(e[0]), number1(e[1])];\n}\n\nvar X = {\n  name: \"x\",\n  handles: [\"w\", \"e\"].map(type),\n  input: function(x, e) { return x == null ? null : [[+x[0], e[0][1]], [+x[1], e[1][1]]]; },\n  output: function(xy) { return xy && [xy[0][0], xy[1][0]]; }\n};\n\nvar Y = {\n  name: \"y\",\n  handles: [\"n\", \"s\"].map(type),\n  input: function(y, e) { return y == null ? null : [[e[0][0], +y[0]], [e[1][0], +y[1]]]; },\n  output: function(xy) { return xy && [xy[0][1], xy[1][1]]; }\n};\n\nvar XY = {\n  name: \"xy\",\n  handles: [\"n\", \"w\", \"e\", \"s\", \"nw\", \"ne\", \"sw\", \"se\"].map(type),\n  input: function(xy) { return xy == null ? null : number2(xy); },\n  output: function(xy) { return xy; }\n};\n\nvar cursors = {\n  overlay: \"crosshair\",\n  selection: \"move\",\n  n: \"ns-resize\",\n  e: \"ew-resize\",\n  s: \"ns-resize\",\n  w: \"ew-resize\",\n  nw: \"nwse-resize\",\n  ne: \"nesw-resize\",\n  se: \"nwse-resize\",\n  sw: \"nesw-resize\"\n};\n\nvar flipX = {\n  e: \"w\",\n  w: \"e\",\n  nw: \"ne\",\n  ne: \"nw\",\n  se: \"sw\",\n  sw: \"se\"\n};\n\nvar flipY = {\n  n: \"s\",\n  s: \"n\",\n  nw: \"sw\",\n  ne: \"se\",\n  se: \"ne\",\n  sw: \"nw\"\n};\n\nvar signsX = {\n  overlay: +1,\n  selection: +1,\n  n: null,\n  e: +1,\n  s: null,\n  w: -1,\n  nw: -1,\n  ne: +1,\n  se: +1,\n  sw: -1\n};\n\nvar signsY = {\n  overlay: +1,\n  selection: +1,\n  n: -1,\n  e: null,\n  s: +1,\n  w: null,\n  nw: -1,\n  ne: -1,\n  se: +1,\n  sw: +1\n};\n\nfunction type(t) {\n  return {type: t};\n}\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultExtent() {\n  var svg = this.ownerSVGElement || this;\n  if (svg.hasAttribute(\"viewBox\")) {\n    svg = svg.viewBox.baseVal;\n    return [[svg.x, svg.y], [svg.x + svg.width, svg.y + svg.height]];\n  }\n  return [[0, 0], [svg.width.baseVal.value, svg.height.baseVal.value]];\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\n// Like d3.local, but with the name “__brush” rather than auto-generated.\nfunction local(node) {\n  while (!node.__brush) if (!(node = node.parentNode)) return;\n  return node.__brush;\n}\n\nfunction empty(extent) {\n  return extent[0][0] === extent[1][0]\n      || extent[0][1] === extent[1][1];\n}\n\nexport function brushSelection(node) {\n  var state = node.__brush;\n  return state ? state.dim.output(state.selection) : null;\n}\n\nexport function brushX() {\n  return brush(X);\n}\n\nexport function brushY() {\n  return brush(Y);\n}\n\nexport default function() {\n  return brush(XY);\n}\n\nfunction brush(dim) {\n  var extent = defaultExtent,\n      filter = defaultFilter,\n      touchable = defaultTouchable,\n      keys = true,\n      listeners = dispatch(\"start\", \"brush\", \"end\"),\n      handleSize = 6,\n      touchending;\n\n  function brush(group) {\n    var overlay = group\n        .property(\"__brush\", initialize)\n      .selectAll(\".overlay\")\n      .data([type(\"overlay\")]);\n\n    overlay.enter().append(\"rect\")\n        .attr(\"class\", \"overlay\")\n        .attr(\"pointer-events\", \"all\")\n        .attr(\"cursor\", cursors.overlay)\n      .merge(overlay)\n        .each(function() {\n          var extent = local(this).extent;\n          select(this)\n              .attr(\"x\", extent[0][0])\n              .attr(\"y\", extent[0][1])\n              .attr(\"width\", extent[1][0] - extent[0][0])\n              .attr(\"height\", extent[1][1] - extent[0][1]);\n        });\n\n    group.selectAll(\".selection\")\n      .data([type(\"selection\")])\n      .enter().append(\"rect\")\n        .attr(\"class\", \"selection\")\n        .attr(\"cursor\", cursors.selection)\n        .attr(\"fill\", \"#777\")\n        .attr(\"fill-opacity\", 0.3)\n        .attr(\"stroke\", \"#fff\")\n        .attr(\"shape-rendering\", \"crispEdges\");\n\n    var handle = group.selectAll(\".handle\")\n      .data(dim.handles, function(d) { return d.type; });\n\n    handle.exit().remove();\n\n    handle.enter().append(\"rect\")\n        .attr(\"class\", function(d) { return \"handle handle--\" + d.type; })\n        .attr(\"cursor\", function(d) { return cursors[d.type]; });\n\n    group\n        .each(redraw)\n        .attr(\"fill\", \"none\")\n        .attr(\"pointer-events\", \"all\")\n        .on(\"mousedown.brush\", started)\n      .filter(touchable)\n        .on(\"touchstart.brush\", started)\n        .on(\"touchmove.brush\", touchmoved)\n        .on(\"touchend.brush touchcancel.brush\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  brush.move = function(group, selection, event) {\n    if (group.tween) {\n      group\n          .on(\"start.brush\", function(event) { emitter(this, arguments).beforestart().start(event); })\n          .on(\"interrupt.brush end.brush\", function(event) { emitter(this, arguments).end(event); })\n          .tween(\"brush\", function() {\n            var that = this,\n                state = that.__brush,\n                emit = emitter(that, arguments),\n                selection0 = state.selection,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(this, arguments) : selection, state.extent),\n                i = interpolate(selection0, selection1);\n\n            function tween(t) {\n              state.selection = t === 1 && selection1 === null ? null : i(t);\n              redraw.call(that);\n              emit.brush();\n            }\n\n            return selection0 !== null && selection1 !== null ? tween : tween(1);\n          });\n    } else {\n      group\n          .each(function() {\n            var that = this,\n                args = arguments,\n                state = that.__brush,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(that, args) : selection, state.extent),\n                emit = emitter(that, args).beforestart();\n\n            interrupt(that);\n            state.selection = selection1 === null ? null : selection1;\n            redraw.call(that);\n            emit.start(event).brush(event).end(event);\n          });\n    }\n  };\n\n  brush.clear = function(group, event) {\n    brush.move(group, null, event);\n  };\n\n  function redraw() {\n    var group = select(this),\n        selection = local(this).selection;\n\n    if (selection) {\n      group.selectAll(\".selection\")\n          .style(\"display\", null)\n          .attr(\"x\", selection[0][0])\n          .attr(\"y\", selection[0][1])\n          .attr(\"width\", selection[1][0] - selection[0][0])\n          .attr(\"height\", selection[1][1] - selection[0][1]);\n\n      group.selectAll(\".handle\")\n          .style(\"display\", null)\n          .attr(\"x\", function(d) { return d.type[d.type.length - 1] === \"e\" ? selection[1][0] - handleSize / 2 : selection[0][0] - handleSize / 2; })\n          .attr(\"y\", function(d) { return d.type[0] === \"s\" ? selection[1][1] - handleSize / 2 : selection[0][1] - handleSize / 2; })\n          .attr(\"width\", function(d) { return d.type === \"n\" || d.type === \"s\" ? selection[1][0] - selection[0][0] + handleSize : handleSize; })\n          .attr(\"height\", function(d) { return d.type === \"e\" || d.type === \"w\" ? selection[1][1] - selection[0][1] + handleSize : handleSize; });\n    }\n\n    else {\n      group.selectAll(\".selection,.handle\")\n          .style(\"display\", \"none\")\n          .attr(\"x\", null)\n          .attr(\"y\", null)\n          .attr(\"width\", null)\n          .attr(\"height\", null);\n    }\n  }\n\n  function emitter(that, args, clean) {\n    var emit = that.__brush.emitter;\n    return emit && (!clean || !emit.clean) ? emit : new Emitter(that, args, clean);\n  }\n\n  function Emitter(that, args, clean) {\n    this.that = that;\n    this.args = args;\n    this.state = that.__brush;\n    this.active = 0;\n    this.clean = clean;\n  }\n\n  Emitter.prototype = {\n    beforestart: function() {\n      if (++this.active === 1) this.state.emitter = this, this.starting = true;\n      return this;\n    },\n    start: function(event, mode) {\n      if (this.starting) this.starting = false, this.emit(\"start\", event, mode);\n      else this.emit(\"brush\", event);\n      return this;\n    },\n    brush: function(event, mode) {\n      this.emit(\"brush\", event, mode);\n      return this;\n    },\n    end: function(event, mode) {\n      if (--this.active === 0) delete this.state.emitter, this.emit(\"end\", event, mode);\n      return this;\n    },\n    emit: function(type, event, mode) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new BrushEvent(type, {\n          sourceEvent: event,\n          target: brush,\n          selection: dim.output(this.state.selection),\n          mode,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function started(event) {\n    if (touchending && !event.touches) return;\n    if (!filter.apply(this, arguments)) return;\n\n    var that = this,\n        type = event.target.__data__.type,\n        mode = (keys && event.metaKey ? type = \"overlay\" : type) === \"selection\" ? MODE_DRAG : (keys && event.altKey ? MODE_CENTER : MODE_HANDLE),\n        signX = dim === Y ? null : signsX[type],\n        signY = dim === X ? null : signsY[type],\n        state = local(that),\n        extent = state.extent,\n        selection = state.selection,\n        W = extent[0][0], w0, w1,\n        N = extent[0][1], n0, n1,\n        E = extent[1][0], e0, e1,\n        S = extent[1][1], s0, s1,\n        dx = 0,\n        dy = 0,\n        moving,\n        shifting = signX && signY && keys && event.shiftKey,\n        lockX,\n        lockY,\n        points = Array.from(event.touches || [event], t => {\n          const i = t.identifier;\n          t = pointer(t, that);\n          t.point0 = t.slice();\n          t.identifier = i;\n          return t;\n        });\n\n    interrupt(that);\n    var emit = emitter(that, arguments, true).beforestart();\n\n    if (type === \"overlay\") {\n      if (selection) moving = true;\n      const pts = [points[0], points[1] || points[0]];\n      state.selection = selection = [[\n          w0 = dim === Y ? W : min(pts[0][0], pts[1][0]),\n          n0 = dim === X ? N : min(pts[0][1], pts[1][1])\n        ], [\n          e0 = dim === Y ? E : max(pts[0][0], pts[1][0]),\n          s0 = dim === X ? S : max(pts[0][1], pts[1][1])\n        ]];\n      if (points.length > 1) move(event);\n    } else {\n      w0 = selection[0][0];\n      n0 = selection[0][1];\n      e0 = selection[1][0];\n      s0 = selection[1][1];\n    }\n\n    w1 = w0;\n    n1 = n0;\n    e1 = e0;\n    s1 = s0;\n\n    var group = select(that)\n        .attr(\"pointer-events\", \"none\");\n\n    var overlay = group.selectAll(\".overlay\")\n        .attr(\"cursor\", cursors[type]);\n\n    if (event.touches) {\n      emit.moved = moved;\n      emit.ended = ended;\n    } else {\n      var view = select(event.view)\n          .on(\"mousemove.brush\", moved, true)\n          .on(\"mouseup.brush\", ended, true);\n      if (keys) view\n          .on(\"keydown.brush\", keydowned, true)\n          .on(\"keyup.brush\", keyupped, true)\n\n      dragDisable(event.view);\n    }\n\n    redraw.call(that);\n    emit.start(event, mode.name);\n\n    function moved(event) {\n      for (const p of event.changedTouches || [event]) {\n        for (const d of points)\n          if (d.identifier === p.identifier) d.cur = pointer(p, that);\n      }\n      if (shifting && !lockX && !lockY && points.length === 1) {\n        const point = points[0];\n        if (abs(point.cur[0] - point[0]) > abs(point.cur[1] - point[1]))\n          lockY = true;\n        else\n          lockX = true;\n      }\n      for (const point of points)\n        if (point.cur) point[0] = point.cur[0], point[1] = point.cur[1];\n      moving = true;\n      noevent(event);\n      move(event);\n    }\n\n    function move(event) {\n      const point = points[0], point0 = point.point0;\n      var t;\n\n      dx = point[0] - point0[0];\n      dy = point[1] - point0[1];\n\n      switch (mode) {\n        case MODE_SPACE:\n        case MODE_DRAG: {\n          if (signX) dx = max(W - w0, min(E - e0, dx)), w1 = w0 + dx, e1 = e0 + dx;\n          if (signY) dy = max(N - n0, min(S - s0, dy)), n1 = n0 + dy, s1 = s0 + dy;\n          break;\n        }\n        case MODE_HANDLE: {\n          if (points[1]) {\n            if (signX) w1 = max(W, min(E, points[0][0])), e1 = max(W, min(E, points[1][0])), signX = 1;\n            if (signY) n1 = max(N, min(S, points[0][1])), s1 = max(N, min(S, points[1][1])), signY = 1;\n          } else {\n            if (signX < 0) dx = max(W - w0, min(E - w0, dx)), w1 = w0 + dx, e1 = e0;\n            else if (signX > 0) dx = max(W - e0, min(E - e0, dx)), w1 = w0, e1 = e0 + dx;\n            if (signY < 0) dy = max(N - n0, min(S - n0, dy)), n1 = n0 + dy, s1 = s0;\n            else if (signY > 0) dy = max(N - s0, min(S - s0, dy)), n1 = n0, s1 = s0 + dy;\n          }\n          break;\n        }\n        case MODE_CENTER: {\n          if (signX) w1 = max(W, min(E, w0 - dx * signX)), e1 = max(W, min(E, e0 + dx * signX));\n          if (signY) n1 = max(N, min(S, n0 - dy * signY)), s1 = max(N, min(S, s0 + dy * signY));\n          break;\n        }\n      }\n\n      if (e1 < w1) {\n        signX *= -1;\n        t = w0, w0 = e0, e0 = t;\n        t = w1, w1 = e1, e1 = t;\n        if (type in flipX) overlay.attr(\"cursor\", cursors[type = flipX[type]]);\n      }\n\n      if (s1 < n1) {\n        signY *= -1;\n        t = n0, n0 = s0, s0 = t;\n        t = n1, n1 = s1, s1 = t;\n        if (type in flipY) overlay.attr(\"cursor\", cursors[type = flipY[type]]);\n      }\n\n      if (state.selection) selection = state.selection; // May be set by brush.move!\n      if (lockX) w1 = selection[0][0], e1 = selection[1][0];\n      if (lockY) n1 = selection[0][1], s1 = selection[1][1];\n\n      if (selection[0][0] !== w1\n          || selection[0][1] !== n1\n          || selection[1][0] !== e1\n          || selection[1][1] !== s1) {\n        state.selection = [[w1, n1], [e1, s1]];\n        redraw.call(that);\n        emit.brush(event, mode.name);\n      }\n    }\n\n    function ended(event) {\n      nopropagation(event);\n      if (event.touches) {\n        if (event.touches.length) return;\n        if (touchending) clearTimeout(touchending);\n        touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n      } else {\n        dragEnable(event.view, moving);\n        view.on(\"keydown.brush keyup.brush mousemove.brush mouseup.brush\", null);\n      }\n      group.attr(\"pointer-events\", \"all\");\n      overlay.attr(\"cursor\", cursors.overlay);\n      if (state.selection) selection = state.selection; // May be set by brush.move (on start)!\n      if (empty(selection)) state.selection = null, redraw.call(that);\n      emit.end(event, mode.name);\n    }\n\n    function keydowned(event) {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          shifting = signX && signY;\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_HANDLE) {\n            if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n            if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n            mode = MODE_CENTER;\n            move(event);\n          }\n          break;\n        }\n        case 32: { // SPACE; takes priority over ALT\n          if (mode === MODE_HANDLE || mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1 - dx; else if (signX > 0) w0 = w1 - dx;\n            if (signY < 0) s0 = s1 - dy; else if (signY > 0) n0 = n1 - dy;\n            mode = MODE_SPACE;\n            overlay.attr(\"cursor\", cursors.selection);\n            move(event);\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent(event);\n    }\n\n    function keyupped(event) {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          if (shifting) {\n            lockX = lockY = shifting = false;\n            move(event);\n          }\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n            if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n            mode = MODE_HANDLE;\n            move(event);\n          }\n          break;\n        }\n        case 32: { // SPACE\n          if (mode === MODE_SPACE) {\n            if (event.altKey) {\n              if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n              if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n              mode = MODE_CENTER;\n            } else {\n              if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n              if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n              mode = MODE_HANDLE;\n            }\n            overlay.attr(\"cursor\", cursors[type]);\n            move(event);\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent(event);\n    }\n  }\n\n  function touchmoved(event) {\n    emitter(this, arguments).moved(event);\n  }\n\n  function touchended(event) {\n    emitter(this, arguments).ended(event);\n  }\n\n  function initialize() {\n    var state = this.__brush || {selection: null};\n    state.extent = number2(extent.apply(this, arguments));\n    state.dim = dim;\n    return state;\n  }\n\n  brush.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant(number2(_)), brush) : extent;\n  };\n\n  brush.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), brush) : filter;\n  };\n\n  brush.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), brush) : touchable;\n  };\n\n  brush.handleSize = function(_) {\n    return arguments.length ? (handleSize = +_, brush) : handleSize;\n  };\n\n  brush.keyModifiers = function(_) {\n    return arguments.length ? (keys = !!_, brush) : keys;\n  };\n\n  brush.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? brush : value;\n  };\n\n  return brush;\n}\n", "export function Transform(k, x, y) {\n  this.k = k;\n  this.x = x;\n  this.y = y;\n}\n\nTransform.prototype = {\n  constructor: Transform,\n  scale: function(k) {\n    return k === 1 ? this : new Transform(this.k * k, this.x, this.y);\n  },\n  translate: function(x, y) {\n    return x === 0 & y === 0 ? this : new Transform(this.k, this.x + this.k * x, this.y + this.k * y);\n  },\n  apply: function(point) {\n    return [point[0] * this.k + this.x, point[1] * this.k + this.y];\n  },\n  applyX: function(x) {\n    return x * this.k + this.x;\n  },\n  applyY: function(y) {\n    return y * this.k + this.y;\n  },\n  invert: function(location) {\n    return [(location[0] - this.x) / this.k, (location[1] - this.y) / this.k];\n  },\n  invertX: function(x) {\n    return (x - this.x) / this.k;\n  },\n  invertY: function(y) {\n    return (y - this.y) / this.k;\n  },\n  rescaleX: function(x) {\n    return x.copy().domain(x.range().map(this.invertX, this).map(x.invert, x));\n  },\n  rescaleY: function(y) {\n    return y.copy().domain(y.range().map(this.invertY, this).map(y.invert, y));\n  },\n  toString: function() {\n    return \"translate(\" + this.x + \",\" + this.y + \") scale(\" + this.k + \")\";\n  }\n};\n\nexport var identity = new Transform(1, 0, 0);\n\ntransform.prototype = Transform.prototype;\n\nexport default function transform(node) {\n  while (!node.__zoom) if (!(node = node.parentNode)) return identity;\n  return node.__zoom;\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { IFlowNodesDataItem, FlowNodes } from \"./FlowNodes\";\n\nimport { Container, IContainerPrivate, IContainerSettings } from \"../../core/render/Container\";\n\nexport interface IFlowNodeSettings extends IContainerSettings {\n}\n\nexport interface IFlowNodePrivate extends IContainerPrivate {\n}\n\n/**\n * Base class for flow chart nodes.\n */\nexport class FlowNode extends Container {\n\n\t/**\n\t * Related series.\n\t */\n\tpublic series: FlowNodes | undefined;\n\n\tdeclare public _settings: IFlowNodeSettings;\n\tdeclare public _privateSettings: IFlowNodePrivate;\n\n\tpublic static className: string = \"FlowNode\";\n\tpublic static classNames: Array<string> = Container.classNames.concat([FlowNode.className]);\n\n\tdeclare protected _dataItem: DataItem<IFlowNodesDataItem> | undefined;\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { Color } from \"../../core/util/Color\";\nimport type { Time } from \"../../core/util/Animation\";\nimport type { Flow, IFlowDataItem } from \"./Flow\";\nimport type { ColorSet } from \"../../core/util/ColorSet\";\nimport type { PatternSet } from \"../../core/util/PatternSet\";\nimport type { Pattern } from \"../../core/render/patterns/Pattern\";\n\nimport type * as d3sankey from \"d3-sankey\";\n\nimport { Label } from \"../../core/render/Label\";\nimport { Series, ISeriesSettings, ISeriesDataItem, ISeriesPrivate, ISeriesEvents } from \"../../core/render/Series\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { FlowNode } from \"./FlowNode\";\n\nimport * as $array from \"../../core/util/Array\";\n\nexport interface IFlowNodesDataItem extends ISeriesDataItem {\n\n\t/**\n\t * Node name.\n\t */\n\tname: string;\n\n\t/**\n\t * An associated node instance.\n\t */\n\tnode: FlowNode;\n\n\t/**\n\t * Node label.\n\t */\n\tlabel: Label;\n\n\t/**\n\t * Node color.\n\t */\n\tfill: Color;\n\n\t/**\n\t * Node pattern.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/colors-gradients-and-patterns/patterns/} for more info\n\t * @since 5.10.0\n\t */\n\tfillPattern: Pattern;\n\n\t/**\n\t * Indicates \"unknown\" node.\n\t */\n\tunknown: boolean;\n\n\t/**\n\t * @ignore\n\t */\n\td3SankeyNode: d3sankey.SankeyNode<d3sankey.SankeyExtraProperties, d3sankey.SankeyExtraProperties>;\n\n\t/**\n\t * Sum of values of all incoming links.\n\t */\n\tsumIncoming: number;\n\n\t/**\n\t * Sum of values of all outgoing links.\n\t */\n\tsumOutgoing: number;\n\n\t/**\n\t * @ignore\n\t */\n\tsumIncomingWorking: number;\n\n\t/**\n\t * @ignore\n\t */\n\tsumOutgoingWorking: number;\n\n\t/**\n\t * Sum of values of all links: incoming and outgoing.\n\t */\n\tsum: number;\n\n\t/**\n\t * @ignore\n\t */\n\tsumWorking: number;\n\n\t/**\n\t * A list of incoming link data items.\n\t */\n\tincomingLinks: Array<DataItem<IFlowDataItem>>;\n\n\t/**\n\t * A list of outgoing link data items.\n\t */\n\toutgoingLinks: Array<DataItem<IFlowDataItem>>;\n\n\t/**\n\t * Depth of the node.\n\t */\n\tdepth: number;\n\n}\n\nexport interface IFlowNodesSettings extends ISeriesSettings {\n\n\t/**\n\t * A field in data boolean setting if the node is \"unknown\".\n\t *\n\t * @default \"unknown\"\n\t */\n\tunknownField?: string;\n\n\t/**\n\t * A field in data that holds name for the node.\n\t *\n\t * @default \"id\"\n\t */\n\tnameField?: string;\n\n\t/**\n\t * A field in data that holds boolean value indicating if node is\n\t * disabled (collapsed).\n\t *\n\t * @since 5.4.2\n\t */\n\tdisabledField?: string;\n\n\t/**\n\t * A field in data that holds color used for fills nodes.\n\t *\n\t * @default \"fill\"\n\t */\n\tfillField?: string;\n\n\t/**\n\t * A [[ColorSet]] that series will use to apply to its nodes.\n\t */\n\tcolors?: ColorSet;\n\n\t/**\n\t * A [[PatternSet]] that series will use to apply to its nodes.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/colors-gradients-and-patterns/patterns/#Pattern_sets} for more info\n\t * @since 5.10.0\n\t */\n\tpatterns?: PatternSet;\n\n\t/**\n\t * Animation duration in ms.\n\t */\n\tanimationDuration?: number;\n\n\t/**\n\t * Easing function to use for node animations.\n\t */\n\tanimationEasing?: (t: Time) => Time;\n}\n\nexport interface IFlowNodesPrivate extends ISeriesPrivate {\n}\n\nexport interface IFlowNodesEvents extends ISeriesEvents {\n}\n\n\n/**\n * Holds instances of nodes for a [[Flow]] series.\n */\nexport abstract class FlowNodes extends Series {\n\tpublic static className: string = \"FlowNodes\";\n\tpublic static classNames: Array<string> = Series.classNames.concat([FlowNodes.className]);\n\n\tdeclare public _settings: IFlowNodesSettings;\n\tdeclare public _privateSettings: IFlowNodesPrivate;\n\tdeclare public _dataItemSettings: IFlowNodesDataItem;\n\tdeclare public _events: IFlowNodesEvents;\n\n\t/**\n\t * List of label elements.\n\t *\n\t * @default new ListTemplate<Label>\n\t */\n\tpublic readonly labels: ListTemplate<Label> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => Label._new(this._root, { themeTags: [\"flow\"] }, [this.labels.template])\n\t);\n\n\t/**\n\t * List of node elements.\n\t *\n\t * @default new ListTemplate<FlowNode>\n\t */\n\tpublic readonly nodes: ListTemplate<FlowNode> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => FlowNode._new(this._root, { themeTags: [\"node\"] }, [this.nodes.template])\n\t);\n\n\t/**\n\t * Related [[Flow]] series.\n\t */\n\tpublic abstract flow: Flow | undefined;\n\n\tpublic _userDataSet = false;\n\n\tprotected _afterNew() {\n\t\tthis.fields.push(\"unknown\", \"name\", \"fill\");\n\n\t\tthis.set(\"idField\", \"id\");\n\t\tthis.set(\"nameField\", \"id\");\n\t\tthis.set(\"fillField\", \"fill\");\n\t\tthis.set(\"unknownField\", \"unknown\");\n\n\t\tthis.children.push(this.bulletsContainer);\n\n\t\tsuper._afterNew();\n\t}\n\n\tprotected _onDataClear() {\n\t\tconst colors = this.get(\"colors\");\n\t\tif (colors) {\n\t\t\tcolors.reset();\n\t\t}\n\n\t\tconst patterns = this.get(\"patterns\");\n\t\tif (patterns) {\n\t\t\tpatterns.reset();\n\t\t}\n\n\t\tthis._userDataSet = true;\n\t}\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\t\tdataItem.setRaw(\"d3SankeyNode\", { name: dataItem.get(\"id\"), dataItem: dataItem });\n\n\t\tif (dataItem.get(\"fill\") == null) {\n\t\t\tlet colors = this.get(\"colors\");\n\t\t\tif (colors) {\n\t\t\t\tdataItem.setRaw(\"fill\", colors.next());\n\t\t\t}\n\t\t}\n\n\t\tif (dataItem.get(\"fillPattern\") == null) {\n\t\t\tlet patterns = this.get(\"patterns\");\n\t\t\tif (patterns) {\n\t\t\t\tdataItem.setRaw(\"fillPattern\", patterns.next());\n\t\t\t}\n\t\t}\n\n\t\tconst node = this.makeNode(dataItem);\n\t\tdataItem.setRaw(\"node\", node);\n\n\t\tconst disabledField = this.get(\"disabledField\");\n\n\t\tif (disabledField) {\n\t\t\tconst dataContext = dataItem.dataContext as any;\n\t\t\tif (dataContext) {\n\t\t\t\tif (dataContext[disabledField]) {\n\t\t\t\t\tthis.root.events.once(\"frameended\", () => {\n\t\t\t\t\t\tthis.disableDataItem(dataItem, 0);\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>, themeTag?: string): FlowNode {\n\n\t\tconst node = this.nodes.make();\n\n\t\tthis.nodes.push(node);\n\n\t\tif (themeTag) {\n\t\t\tnode.addTag(themeTag);\n\t\t}\n\n\t\tif (dataItem.get(\"unknown\")) {\n\t\t\tnode.addTag(\"unknown\");\n\t\t}\n\n\t\tthis.children.push(node);\n\t\tnode._setDataItem(dataItem);\n\t\tnode.series = this;\n\n\t\tnode.events.on(\"click\", (e) => {\n\t\t\tconst node = e.target;\n\t\t\tif (node.get(\"toggleKey\") == \"disabled\") {\n\t\t\t\tconst dataItem = node.dataItem as DataItem<IFlowNodesDataItem>;\n\t\t\t\tif (dataItem) {\n\t\t\t\t\tif (dataItem.isHidden()) {\n\t\t\t\t\t\tthis.enableDataItem(dataItem);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.disableDataItem(dataItem);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\tdataItem.on(\"fill\", () => {\n\t\t\tthis._updateNodeColor(dataItem);\n\t\t})\n\n\t\tdataItem.on(\"fillPattern\", () => {\n\t\t\tthis._updateNodeColor(dataItem);\n\t\t})\n\n\n\t\tdataItem.set(\"node\", node);\n\t\treturn node;\n\t}\n\n\tpublic _updateNodeColor(_dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tlet node = dataItem.get(\"node\");\n\t\tif (node) {\n\t\t\tthis.nodes.removeValue(node);\n\t\t\tnode.dispose();\n\t\t}\n\t\tlet label = dataItem.get(\"label\");\n\t\tif (label) {\n\t\t\tthis.labels.removeValue(label);\n\t\t\tlabel.dispose();\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic addincomingLink(dataItem: DataItem<this[\"_dataItemSettings\"]>, link: DataItem<IFlowDataItem>) {\n\t\tlet incoming = dataItem.get(\"incomingLinks\");\n\t\tif (!incoming) {\n\t\t\tincoming = [];\n\t\t\tdataItem.set(\"incomingLinks\", incoming)\n\t\t}\n\t\tincoming.push(link);\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic addOutgoingLink(dataItem: DataItem<this[\"_dataItemSettings\"]>, link: DataItem<IFlowDataItem>) {\n\t\tlet outgoing = dataItem.get(\"outgoingLinks\");\n\t\tif (!outgoing) {\n\t\t\toutgoing = [];\n\t\t\tdataItem.set(\"outgoingLinks\", outgoing)\n\t\t}\n\t\toutgoing.push(link);\n\t}\n\n\t/**\n\t * Shows node's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async showDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.showDataItem(dataItem, duration)];\n\t\tconst flow = this.flow;\n\n\t\tif (flow) {\n\n\t\t\tconst node = dataItem.get(\"node\");\n\t\t\tif (node) {\n\t\t\t\tnode.show();\n\t\t\t}\n\n\t\t\tlet label = dataItem.get(\"label\");\n\n\t\t\tif (label) {\n\t\t\t\tlabel.show(duration);\n\t\t\t}\n\n\t\t\tlet links = dataItem.get(\"outgoingLinks\");\n\t\t\tif (links) {\n\t\t\t\t$array.each(links, (link) => {\n\t\t\t\t\tflow.showDataItem(link, duration);\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tlinks = dataItem.get(\"incomingLinks\");\n\t\t\tif (links) {\n\t\t\t\t$array.each(links, (link) => {\n\t\t\t\t\tflow.showDataItem(link, duration);\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\tawait promises;\n\t}\n\n\t/**\n\t * Hides series's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async hideDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.hideDataItem(dataItem, duration)];\n\n\t\tconst flow = this.flow;\n\n\t\tif (flow) {\n\n\t\t\tconst node = dataItem.get(\"node\");\n\t\t\tif (node) {\n\t\t\t\tnode.hide();\n\t\t\t}\n\n\t\t\tlet label = dataItem.get(\"label\");\n\n\t\t\tif (label) {\n\t\t\t\tlabel.hide(duration);\n\t\t\t}\n\n\t\t\tlet links = dataItem.get(\"outgoingLinks\");\n\n\t\t\tif (links) {\n\t\t\t\t$array.each(links, (link) => {\n\t\t\t\t\tflow.hideDataItem(link, duration);\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tlinks = dataItem.get(\"incomingLinks\");\n\n\t\t\tif (links) {\n\t\t\t\t$array.each(links, (link) => {\n\t\t\t\t\tflow.hideDataItem(link, duration);\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t\tawait promises;\n\t}\n\n\t/**\n\t * Shows node's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async enableDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.showDataItem(dataItem, duration)];\n\t\tconst flow = this.flow;\n\n\t\tif (flow) {\n\n\t\t\tconst node = dataItem.get(\"node\");\n\t\t\tif (node) {\n\t\t\t\tthis.root.events.once(\"frameended\", () => {\n\t\t\t\t\tnode.set(\"disabled\", false);\n\t\t\t\t})\n\n\t\t\t}\n\n\t\t\tlet label = dataItem.get(\"label\");\n\n\t\t\tif (label) {\n\t\t\t\tlabel.set(\"disabled\", false);\n\t\t\t}\n\n\t\t\tlet links = dataItem.get(\"outgoingLinks\");\n\t\t\tif (links) {\n\t\t\t\t$array.each(links, (link) => {\n\t\t\t\t\tflow.showDataItem(link, duration);\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tlinks = dataItem.get(\"incomingLinks\");\n\t\t\tif (links) {\n\t\t\t\t$array.each(links, (link) => {\n\t\t\t\t\tflow.showDataItem(link, duration);\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\tawait promises;\n\t}\n\n\t/**\n\t * Hides series's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async disableDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.hideDataItem(dataItem, duration)];\n\n\t\tconst flow = this.flow;\n\n\t\tif (flow) {\n\n\t\t\tconst node = dataItem.get(\"node\");\n\t\t\tif (node) {\n\t\t\t\tthis.root.events.once(\"frameended\", () => {\n\t\t\t\t\tnode.set(\"disabled\", true);\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tlet label = dataItem.get(\"label\");\n\n\t\t\tif (label) {\n\t\t\t\tlabel.set(\"disabled\", true);\n\t\t\t}\n\n\t\t\tlet links = dataItem.get(\"outgoingLinks\");\n\n\t\t\tif (links) {\n\t\t\t\t$array.each(links, (link) => {\n\t\t\t\t\tflow.hideDataItem(link, duration);\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tlinks = dataItem.get(\"incomingLinks\");\n\n\t\t\tif (links) {\n\t\t\t\t$array.each(links, (link) => {\n\t\t\t\t\tflow.hideDataItem(link, duration);\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t\tawait promises;\n\t}\n\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { FlowNode } from \"./FlowNode\";\nimport type { Chord } from \"./Chord\";\nimport type { Bullet } from \"../../core/render/Bullet\";\n\nimport { FlowNodes, IFlowNodesSettings, IFlowNodesDataItem, IFlowNodesPrivate, IFlowNodesEvents } from \"./FlowNodes\";\nimport { Slice } from \"../../core/render/Slice\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { RadialLabel } from \"../../core/render/RadialLabel\";\n\nimport * as $math from \"../../core/util/Math\";\n\nexport interface IChordNodesDataItem extends IFlowNodesDataItem {\n\n\t/**\n\t * Node [[Slice]] element.\n\t */\n\tslice: Slice;\n\n\t/**\n\t * Node label element.\n\t */\n\tlabel: RadialLabel;\n\n}\n\nexport interface IChordNodesSettings extends IFlowNodesSettings {\n}\n\nexport interface IChordNodesPrivate extends IFlowNodesPrivate {\n}\n\nexport interface IChordNodesEvents extends IFlowNodesEvents {\n}\n\n/**\n * Holds instances of nodes for a [[Chord]] series.\n */\nexport class ChordNodes extends FlowNodes {\n\tpublic static className: string = \"ChordNodes\";\n\tpublic static classNames: Array<string> = FlowNodes.classNames.concat([ChordNodes.className]);\n\n\t/**\n\t * List of label elements.\n\t *\n\t * @default new ListTemplate<RadialLabel>\n\t */\n\tpublic readonly labels: ListTemplate<RadialLabel> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => RadialLabel._new(this._root, {}, [this.labels.template])\n\t);\n\n\tdeclare public _settings: IChordNodesSettings;\n\tdeclare public _privateSettings: IChordNodesPrivate;\n\tdeclare public _dataItemSettings: IChordNodesDataItem;\n\tdeclare public _events: IChordNodesEvents;\n\n\t/**\n\t * Related [[Chord]] series.\n\t */\n\tpublic flow: Chord | undefined;\n\n\tprotected _dAngle: number = 0;\n\n\t/**\n\t * List of slice elements.\n\t *\n\t * @default new ListTemplate<Slice>\n\t */\n\tpublic readonly slices: ListTemplate<Slice> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => Slice._new(this._root, { themeTags: [\"shape\"] }, [this.slices.template])\n\t);\n\n\t/**\n\t * @ignore\n\t * added to solve old naming bug\n\t */\n\tpublic readonly rectangles = this.slices;\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>): FlowNode {\n\t\tconst node = super.makeNode(dataItem, \"chord\");\n\n\t\tconst slice = node.children.insertIndex(0, this.slices.make());\n\t\tdataItem.set(\"slice\", slice);\n\t\tslice._setSoft(\"fill\", dataItem.get(\"fill\"));\n\t\tslice._setSoft(\"fillPattern\", dataItem.get(\"fillPattern\"));\n\n\t\tconst label = this.labels.make();\n\t\tthis.labels.push(label);\n\t\tlabel.addTag(\"flow\");\n\t\tlabel.addTag(\"chord\");\n\t\tlabel.addTag(\"node\");\n\n\t\tnode.children.push(label);\n\t\tdataItem.set(\"label\", label);\n\n\t\tnode.events.on(\"dragstart\", (e) => {\n\t\t\tlet point = this.toLocal(e.point);\n\t\t\tconst angle = $math.getAngle({ x: 0, y: 0 }, point);\n\t\t\tif (this.flow) {\n\t\t\t\tthis._dAngle = this.flow.get(\"startAngle\", 0) - angle;\n\t\t\t}\n\t\t})\n\n\t\tnode.events.on(\"dragged\", (e) => {\n\t\t\tlet point = this.toLocal(e.point);\n\t\t\tconst angle = $math.getAngle({ x: 0, y: 0 }, point);\n\n\t\t\tnode.setAll({ x: 0, y: 0 });\n\t\t\tif (this.flow) {\n\t\t\t\tthis.flow.set(\"startAngle\", angle + this._dAngle);\n\t\t\t}\n\t\t})\n\n\t\tlabel._setDataItem(dataItem);\n\t\tslice._setDataItem(dataItem);\n\n\t\treturn node;\n\t}\n\n\tpublic _positionBullet(bullet: Bullet) {\n\t\tconst sprite = bullet.get(\"sprite\");\n\t\tif (sprite) {\n\t\t\tconst dataItem = sprite.dataItem as DataItem<this[\"_dataItemSettings\"]>;\n\t\t\tif (dataItem) {\n\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\t\tif (sprite) {\n\t\t\t\t\tconst slice = dataItem.get(\"slice\");\n\t\t\t\t\tconst locationX = bullet.get(\"locationX\", 0.5);\n\t\t\t\t\tconst locationY = bullet.get(\"locationY\", 0.5);\n\t\t\t\t\tif (slice) {\n\t\t\t\t\t\tconst radius = slice.get(\"radius\", 0);\n\t\t\t\t\t\tconst innerRadius = slice.get(\"innerRadius\", 0);\n\t\t\t\t\t\tconst bulletRadius = innerRadius + (radius - innerRadius) * locationY;\n\t\t\t\t\t\tconst angle = slice.get(\"startAngle\", 0) + slice.get(\"arc\", 0) * locationX;\n\t\t\t\t\t\tsprite.setAll({ x: bulletRadius * $math.cos(angle), y: bulletRadius * $math.sin(angle) });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic _updateNodeColor(dataItem: DataItem<this[\"_dataItemSettings\"]>){\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tif(slice){\n\t\t\tslice.set(\"fill\", dataItem.get(\"fill\"));\n\t\t\tslice.set(\"fillPattern\", dataItem.get(\"fillPattern\"));\n\t\t}\n\t}\t\t\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tlet slice = dataItem.get(\"slice\");\n\t\tif (slice) {\n\t\t\tthis.slices.removeValue(slice);\n\t\t\tslice.dispose();\n\t\t}\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport { Graphics, IGraphicsSettings, IGraphicsPrivate } from \"../../core/render/Graphics\";\nimport type { IFlowNodesDataItem } from \"./FlowNodes\";\nimport type { IFlowDataItem, Flow } from \"./Flow\";\nimport type { IOrientationPoint, IPoint } from \"../../core/util/IPoint\";\nimport type { LinearGradient } from \"../../core/render/gradients/LinearGradient\";\nimport { Percent } from \"../../core/util/Percent\";\n\nexport interface IFlowLinkSettings extends IGraphicsSettings {\n\n\t/**\n\t * Source node data item.\n\t */\n\tsource?: DataItem<IFlowNodesDataItem>;\n\n\t/**\n\t * Source node data item.\n\t */\n\ttarget?: DataItem<IFlowNodesDataItem>;\n\n\t/**\n\t * Type of fill to use for links.\n\t */\n\tfillStyle?: \"solid\" | \"source\" | \"target\" | \"gradient\" | \"none\";\n\n\t/**\n\t * Type of outline to use for links.\n\t */\n\tstrokeStyle?: \"solid\" | \"source\" | \"target\" | \"gradient\" | \"none\";\n\n}\n\nexport interface IFlowLinkPrivate extends IGraphicsPrivate {\n}\n\n/**\n * A base class for a flow link.\n */\nexport abstract class FlowLink extends Graphics {\n\n\tpublic series: Flow | undefined;\n\n\tdeclare public _settings: IFlowLinkSettings;\n\tdeclare public _privateSettings: IFlowLinkPrivate;\n\n\tpublic static className: string = \"FlowLink\";\n\tpublic static classNames: Array<string> = Graphics.classNames.concat([FlowLink.className]);\n\n\tdeclare protected _dataItem: DataItem<IFlowDataItem> | undefined;\n\n\tpublic _fillGradient: LinearGradient | undefined;\n\tpublic _strokeGradient: LinearGradient | undefined;\n\n\tpublic _changed() {\n\t\tsuper._changed();\n\t\tif (this.isDirty(\"fillStyle\")) {\n\t\t\tconst series = this.series;\n\t\t\tconst dataItem = this.dataItem as DataItem<IFlowDataItem>;\n\t\t\tif (series && dataItem) {\n\t\t\t\tseries._updateLinkColor(dataItem);\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic abstract getPoint(location: number): IOrientationPoint;\n\n\tpublic _getTooltipPoint(): IPoint {\n\t\tlet tooltipY = this.get(\"tooltipY\");\n\t\tlet position = 0.5;\n\t\tif (tooltipY instanceof Percent) {\n\t\t\tposition = tooltipY.value;\n\t\t}\n\n\t\treturn this.getPoint(position);\n\t}\n}\n", "import { FlowLink, IFlowLinkPrivate, IFlowLinkSettings } from \"./FlowLink\";\nimport type { DataItem } from \"../../core/render/Component\";\nimport type { IChordNodesDataItem } from \"./ChordNodes\";\nimport type { Percent } from \"../../core/util/Percent\";\nimport type { Chord, IChordDataItem } from \"./Chord\";\nimport type { IOrientationPoint, IPoint } from \"../../core/util/IPoint\";\nimport * as $math from \"../../core/util/Math\";\n\nexport interface IChordLinkSettings extends IFlowLinkSettings {\n\n\t/**\n\t * Source data item.\n\t */\n\tsource?: DataItem<IChordNodesDataItem>;\n\n\t/**\n\t * target data item.\n\t */\n\ttarget?: DataItem<IChordNodesDataItem>;\n\n\t/**\n\t * Radius of the link at the source.\n\t */\n\tsourceRadius?: number | Percent;\n\n\t/**\n\t * Radius of the link at the end (target).\n\t */\n\ttargetRadius?: number | Percent;\n\n}\n\nexport interface IChordLinkPrivate extends IFlowLinkPrivate {\n}\n\n/**\n * A link element used in [[Chord]] chart.\n */\nexport class ChordLink extends FlowLink {\n\n\tpublic _p0: IPoint | undefined;\n\tpublic _p1: IPoint | undefined;\n\n\tpublic _type: \"line\" | \"curve\" | undefined;\n\n\tdeclare public _settings: IChordLinkSettings;\n\tdeclare public _privateSettings: IChordLinkPrivate;\n\n\tpublic static className: string = \"ChordLink\";\n\tpublic static classNames: Array<string> = FlowLink.classNames.concat([ChordLink.className]);\n\n\tdeclare protected _dataItem: DataItem<IChordDataItem> | undefined;\n\n\tdeclare public series: Chord | undefined;\n\n\tpublic getPoint(location: number): IOrientationPoint {\n\t\tif (this._p0 && this._p1) {\n\t\t\tif (this._type === \"line\") {\n\t\t\t\tlet p = $math.getPointOnLine(this._p0, this._p1, location);\n\t\t\t\treturn { x: p.x, y: p.y, angle: $math.getAngle(this._p0, this._p1) };\n\t\t\t}\n\t\t\telse {\n\t\t\t\tlet p0 = $math.getPointOnQuadraticCurve(this._p0, this._p1, { x: 0, y: 0 }, Math.max(0, location - 0.01));\n\t\t\t\tlet p1 = $math.getPointOnQuadraticCurve(this._p0, this._p1, { x: 0, y: 0 }, Math.min(1, location + 0.01));\n\t\t\t\tlet p = $math.getPointOnQuadraticCurve(this._p0, this._p1, { x: 0, y: 0 }, location);\n\n\t\t\t\treturn { x: p.x, y: p.y, angle: $math.getAngle(p0, p1) };\n\t\t\t}\n\t\t}\n\t\treturn { x: 0, y: 0, angle: 0 };\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\n\nimport { Flow, IFlowSettings, IFlowDataItem, IFlowPrivate, IFlowEvents } from \"./Flow\";\nimport { chord, ribbon, RibbonGenerator, RibbonSubgroup, Ribbon } from \"d3-chord\";\nimport { ascending, descending } from \"d3\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { ChordNodes, IChordNodesDataItem } from \"./ChordNodes\";\nimport { ChordLink } from \"./ChordLink\";\nimport { Percent, p100, p50 } from \"../../core/util/Percent\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $utils from \"../../core/util/Utils\";\nimport * as $math from \"../../core/util/Math\";\n\n\nexport interface IChordDataItem extends IFlowDataItem {\n\n\t/**\n\t * A link element.\n\t */\n\tlink: ChordLink;\n\n\t/**\n\t * Source node data item.\n\t */\n\tsource: DataItem<IChordNodesDataItem>;\n\n\t/**\n\t * Target node data item.\n\t */\n\ttarget: DataItem<IChordNodesDataItem>;\n\n}\n\nexport interface IChordSettings extends IFlowSettings {\n\n\t/**\n\t * Angle of a gap between each node, in degrees.\n\t *\n\t * @default 1\n\t */\n\tpadAngle?: number;\n\n\t/**\n\t * Radius of the diagram in percent or pixels.\n\t *\n\t * If set in percent, it will be relative to the whole area available for\n\t * the series.\n\t *\n\t * @default 90%\n\t */\n\tradius?: number | Percent;\n\n\t/**\n\t * The thickness of node strip in pixels.\n\t *\n\t * @default 10\n\t */\n\tnodeWidth?: number;\n\n\t/**\n\t * Starting angle in degrees.\n\t *\n\t * @default 0\n\t */\n\tstartAngle?: number;\n\n\t/**\n\t * How to sort nodes by their value.\n\t *\n\t * @default \"descending\"\n\t */\n\tsort?: \"ascending\" | \"descending\" | \"none\"\n\n}\n\nexport interface IChordPrivate extends IFlowPrivate {\n}\n\nexport interface IChordEvents extends IFlowEvents {\n}\n\n/**\n * Regular chord series.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/flow-charts/} for more information\n * @important\n */\nexport class Chord extends Flow {\n\n\tpublic static className: string = \"Chord\";\n\tpublic static classNames: Array<string> = Flow.classNames.concat([Chord.className]);\n\n\t/**\n\t * List of link elements.\n\t *\n\t * @default new ListTemplate<ChordLink>\n\t */\n\tpublic readonly links: ListTemplate<ChordLink> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => ChordLink._new(this._root, { themeTags: [\"link\", \"shape\"] }, [this.links.template])\n\t);\n\n\t/**\n\t * A series for all chord nodes.\n\t *\n\t * @default ChordNodes.new()\n\t */\n\tpublic readonly nodes: ChordNodes = this.children.push(ChordNodes.new(this._root, {}));\n\n\tdeclare public _settings: IChordSettings;\n\tdeclare public _privateSettings: IChordPrivate;\n\tdeclare public _dataItemSettings: IChordDataItem;\n\tdeclare public _events: IChordEvents;\n\n\tpublic _d3chord = chord();\n\tpublic _chordLayout: { source: { index: number, startAngle: number, endAngle: number, value: number }, target: { index: number, startAngle: number, endAngle: number, value: number } }[] = [];\n\tpublic _ribbon = ribbon();\n\n\tprotected _afterNew() {\n\t\tthis._settings.themeTags = $utils.mergeTags(this._settings.themeTags, [\"chord\"]);\n\t\tthis.linksContainer.setAll({ x: p50, y: p50 })\n\t\tthis.bulletsContainer.setAll({ x: p50, y: p50 });\n\t\tsuper._afterNew();\n\t}\n\n\tprotected _fixRibbon(ribbon: RibbonGenerator<any, Ribbon, RibbonSubgroup>) {\n\t\tribbon.startAngle((d) => {\n\t\t\treturn d.startAngle + this.get(\"startAngle\", 0) * $math.RADIANS + Math.PI / 2;\n\t\t})\n\n\t\tribbon.endAngle((d) => {\n\t\t\treturn d.endAngle + this.get(\"startAngle\", 0) * $math.RADIANS + Math.PI / 2;\n\t\t})\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeLink(dataItem: DataItem<this[\"_dataItemSettings\"]>): ChordLink {\n\t\tconst link = this.linksContainer.children.push(this.links.make());\n\t\tthis.links.push(link);\n\t\tlink._setDataItem(dataItem);\n\t\tlink.set(\"source\", dataItem.get(\"source\"));\n\t\tlink.set(\"target\", dataItem.get(\"target\"));\n\t\tlink.series = this;\n\t\treturn link;\n\t}\n\n\tprotected _makeMatrix(): number[][] {\n\t\tconst matrix: number[][] = [];\n\t\t$array.each(this.nodes.dataItems, (sourceDataItem) => {\n\t\t\tconst group: number[] = [];\n\t\t\tmatrix.push(group);\n\t\t\tlet outgoing = sourceDataItem.get(\"outgoingLinks\");\n\n\t\t\t$array.each(this.nodes.dataItems, (targetDataItem) => {\n\t\t\t\tlet value = 0;\n\t\t\t\tif (outgoing) {\n\t\t\t\t\t$array.each(outgoing, (outgoingLink) => {\n\t\t\t\t\t\tif (outgoingLink.get(\"target\") === targetDataItem) {\n\t\t\t\t\t\t\tvalue = outgoingLink.get(\"valueWorking\");\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlet valueSum = this.getPrivate(\"valueSum\", 0);\n\t\t\t\t\t\tlet minSize = this.get(\"minSize\", 0);\n\t\t\t\t\t\tif(value > 0 && minSize > 0){\n\t\t\t\t\t\t\tif(value < valueSum * minSize){\n\t\t\t\t\t\t\t\tvalue = valueSum * minSize;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\t\t\t\t\t\t\t\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\tgroup.push(value);\n\t\t\t})\n\t\t})\n\t\treturn matrix;\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tthis._fixRibbon(this._ribbon);\n\n\t\tlet chordChanged = false;\n\n\t\tif (this._valuesDirty || this._sizeDirty || this.isDirty(\"padAngle\") || this.isDirty(\"startAngle\")) {\n\n\t\t\tconst matrix = this._makeMatrix();\n\n\t\t\tthis._d3chord.padAngle(this.get(\"padAngle\", 0) * $math.RADIANS);\n\t\t\tconst sort = this.get(\"sort\");\n\n\t\t\tif (sort === \"ascending\") {\n\t\t\t\tthis._d3chord.sortGroups(ascending);\n\t\t\t}\n\t\t\telse if (sort === \"descending\") {\n\t\t\t\tthis._d3chord.sortGroups(descending);\n\t\t\t}\n/*\n\t\t\tthis._d3chord.sortSubgroups((a, b)=>{\n\t\t\t\tif (a != Math.round(a)) return false\n\t\t\t\tif (b != Math.round(b)) return true\n\t\t\t\treturn b < a ? -1 : b > a ? 1 : 0;\n\t\t\t})\n*/\n\t\t\tthis._chordLayout = this._d3chord(matrix);\n\n\t\t\tchordChanged = true;\n\t\t}\n\n\t\tif (chordChanged || this.isDirty(\"radius\") || this.isDirty(\"nodeWidth\")) {\n\t\t\tlet radius = $utils.relativeToValue(this.get(\"radius\", 0), Math.min(this.innerWidth(), this.innerHeight())) / 2;\n\n\t\t\tlet i = 0;\n\n\t\t\tconst chordStartAngle = this.get(\"startAngle\", 0);\n\t\t\tconst nodeWidth = this.get(\"nodeWidth\", 0);\n\n\t\t\t$array.each(this.nodes.dataItems, (dataItem) => {\n\t\t\t\tconst slice = dataItem.get(\"slice\");\n\t\t\t\tconst chordGroup = (this._chordLayout as any).groups[i];\n\t\t\t\tconst startAngle = chordGroup.startAngle * $math.DEGREES + chordStartAngle;\n\t\t\t\tconst endAngle = chordGroup.endAngle * $math.DEGREES + chordStartAngle;\n\t\t\t\tslice.setAll({ radius: radius, innerRadius: radius - nodeWidth, startAngle: startAngle as number, arc: endAngle - startAngle })\n\n\t\t\t\tconst label = dataItem.get(\"label\");\n\t\t\t\tlabel.setAll({ labelAngle: startAngle + (endAngle - startAngle) / 2 });\n\t\t\t\tlabel.setPrivate(\"radius\", radius);\n\t\t\t\tlabel.setPrivate(\"innerRadius\", 0.1);\n\t\t\t\ti++;\n\t\t\t})\n\n\t\t\tconst linkRadius = radius - this.get(\"nodeWidth\", 0);\n\n\t\t\t$array.each(this._chordLayout, (chord) => {\n\n\t\t\t\tlet dataItem = this._linksByIndex[chord.source.index + \"_\" + chord.target.index];\n\n\t\t\t\tif (!dataItem) {\n\t\t\t\t\tdataItem = this._linksByIndex[chord.target.index + \"_\" + chord.source.index];\n\t\t\t\t}\n\n\t\t\t\tif (dataItem) {\n\t\t\t\t\tconst link = dataItem.get(\"link\");\n\t\t\t\t\tthis._getLinkPoints(link, linkRadius, chord);\n\t\t\t\t\tthis._updateLink(this._ribbon, link, linkRadius, chord);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tprotected _getLinkPoints(link: ChordLink, linkRadius: number, chordLayoutItem: any) {\n\t\tconst source = chordLayoutItem.source;\n\t\tconst target = chordLayoutItem.target;\n\n\t\tconst chordStartAngle = this.get(\"startAngle\", 0) * $math.RADIANS;\n\n\t\tif (source && target) {\n\t\t\tconst startAngle0 = source.startAngle;\n\t\t\tconst endAngle0 = source.endAngle\n\t\t\tconst angle0 = startAngle0 + (endAngle0 - startAngle0) / 2 + chordStartAngle;\n\n\t\t\tconst startAngle1 = target.startAngle;\n\t\t\tconst endAngle1 = target.endAngle\n\t\t\tconst angle1 = startAngle1 + (endAngle1 - startAngle1) / 2 + chordStartAngle;\n\n\t\t\tlink._p0 = { x: linkRadius * Math.cos(angle0), y: linkRadius * Math.sin(angle0) };\n\t\t\tlink._p1 = { x: linkRadius * Math.cos(angle1), y: linkRadius * Math.sin(angle1) };\n\t\t}\n\t}\n\n\tprotected _updateLink(ribbon: RibbonGenerator<any, Ribbon, RibbonSubgroup>, link: ChordLink, linkRadius: number, chordLayoutItem: any) {\n\t\tif (chordLayoutItem) {\n\t\t\tribbon.sourceRadius($utils.relativeToValue(link.get(\"sourceRadius\", p100), linkRadius));\n\t\t\tribbon.targetRadius($utils.relativeToValue(link.get(\"targetRadius\", p100), linkRadius));\n\n\t\t\tlink.set(\"draw\", (display) => {\n\t\t\t\tribbon.context(display as any);\n\t\t\t\tribbon(chordLayoutItem);\n\t\t\t})\n\t\t}\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { ChordDirected, IChordDirectedDataItem } from \"./ChordDirected\";\n\nimport { ChordLink, IChordLinkPrivate, IChordLinkSettings } from \"./ChordLink\";\n\nimport * as $utils from \"../../core/util/Utils\";\n\n\nexport interface IChordLinkDirectedSettings extends IChordLinkSettings {\n\n\t/**\n\t * Length of the link arrow in pixels.\n\t *\n\t * @default 10\n\t */\n\theadRadius?: number;\n\n}\n\nexport interface IChordLinkDirectedPrivate extends IChordLinkPrivate {\n}\n\n/**\n * A link element used in [[ChordDirected]] chart.\n */\nexport class ChordLinkDirected extends ChordLink {\n\tdeclare public _settings: IChordLinkDirectedSettings;\n\tdeclare public _privateSettings: IChordLinkDirectedPrivate;\n\n\tpublic static className: string = \"ChordLinkDirected\";\n\tpublic static classNames: Array<string> = ChordLink.classNames.concat([ChordLinkDirected.className]);\n\n\tdeclare protected _dataItem: DataItem<IChordDirectedDataItem> | undefined;\n\n\tdeclare public series: ChordDirected | undefined;\n\n\tprotected _afterNew() {\n\t\tthis._settings.themeTags = $utils.mergeTags(this._settings.themeTags, [\"chord\", \"link\", \"directed\"]);\n\n\t\tsuper._afterNew();\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\n\nimport { Chord, IChordSettings, IChordDataItem, IChordPrivate, IChordEvents } from \"./Chord\";\nimport { ChordLinkDirected } from \"./ChordLinkDirected\";\nimport { chordDirected, ribbonArrow, ribbon, RibbonArrowGenerator, Ribbon, RibbonSubgroup } from \"d3-chord\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\n\nimport * as $utils from \"../../core/util/Utils\";\n\nexport interface IChordDirectedDataItem extends IChordDataItem {\n\n}\n\nexport interface IChordDirectedSettings extends IChordSettings {\n\n\t/**\n\t * Length of the link arrow in pixels.\n\t *\n\t * Set to `null` to disable arrowheads.\n\t *\n\t * @default 10\n\t */\n\tlinkHeadRadius?: number | undefined;\n\n}\n\nexport interface IChordDirectedPrivate extends IChordPrivate {\n}\n\nexport interface IChordDirectedEvents extends IChordEvents {\n}\n\n/**\n * Directed chord series.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/flow-charts/} for more information\n * @important\n */\nexport class ChordDirected extends Chord {\n\n\tpublic static className: string = \"ChordDirected\";\n\tpublic static classNames: Array<string> = Chord.classNames.concat([ChordDirected.className]);\n\n\tdeclare public _settings: IChordDirectedSettings;\n\tdeclare public _privateSettings: IChordDirectedPrivate;\n\tdeclare public _dataItemSettings: IChordDirectedDataItem;\n\tdeclare public _events: IChordDirectedEvents;\n\n\tpublic _d3chord = chordDirected();\n\tpublic _ribbonArrow: RibbonArrowGenerator<any, Ribbon, RibbonSubgroup> = ribbonArrow();\n\n\t/**\n\t * List of link elements.\n\t *\n\t * @default new ListTemplate<ChordLinkDirected>\n\t */\n\tpublic readonly links: ListTemplate<ChordLinkDirected> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => ChordLinkDirected._new(this._root, { themeTags: [\"link\", \"shape\"] }, [this.links.template])\n\t);\n\t/**\n\t * @ignore\n\t */\n\tpublic makeLink(dataItem: DataItem<this[\"_dataItemSettings\"]>): ChordLinkDirected {\n\t\tconst link = this.linksContainer.children.push(this.links.make());\n\t\tlink._setDataItem(dataItem);\n\t\tlink.set(\"source\", dataItem.get(\"source\"));\n\t\tlink.set(\"target\", dataItem.get(\"target\"));\n\n\t\tlink.series = this;\n\n\t\treturn link;\n\t}\n\n\tprotected _afterNew() {\n\t\tthis._settings.themeTags = $utils.mergeTags(this._settings.themeTags, [\"directed\"]);\n\t\tsuper._afterNew();\n\t\tthis._markDirtyKey(\"linkHeadRadius\");\n\t}\n\n\tpublic _prepareChildren() {\n\t\tconst linkHeadRadius = \"linkHeadRadius\";\n\t\tif (this.isDirty(linkHeadRadius)) {\n\t\t\tconst headRadius = this.get(linkHeadRadius);\n\t\t\tif (headRadius == null) {\n\t\t\t\tthis._ribbon = ribbon();\n\t\t\t}\n\t\t\telse {\n\t\t\t\tlet ribbon = ribbonArrow();\n\t\t\t\tribbon.headRadius(headRadius);\n\t\t\t\tthis._ribbon = ribbon;\n\t\t\t}\n\t\t}\n\n\t\tsuper._prepareChildren();\n\t}\n}\n", "import type { ChordLink } from \"./ChordLink\";\nimport type { RibbonGenerator, RibbonSubgroup, Ribbon } from \"d3-chord\";\n\nimport { Chord, IChordSettings, IChordDataItem, IChordPrivate, IChordEvents } from \"./Chord\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $math from \"../../core/util/Math\";\nimport * as $utils from \"../../core/util/Utils\";\n\nexport interface IChordNonRibbonDataItem extends IChordDataItem {\n\n}\n\nexport interface IChordNonRibbonSettings extends IChordSettings {\n\n\t/**\n\t * Type of the link:\n\t *\n\t * `\"curve\"` (default) will display link as a curved line.\n\t * `\"line\"` will display link as a straight line.\n\t *\n\t * @default \"curve\"\n\t */\n\tlinkType?: \"curve\" | \"line\"\n\n}\n\nexport interface IChordNonRibbonPrivate extends IChordPrivate {\n}\n\nexport interface IChordNonRibbonEvents extends IChordEvents {\n}\n\n/**\n * Chord series with think line links.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/flow-charts/} for more information\n * @important\n */\nexport class ChordNonRibbon extends Chord {\n\n\tpublic static className: string = \"ChordNonRibbon\";\n\tpublic static classNames: Array<string> = Chord.classNames.concat([ChordNonRibbon.className]);\n\n\tdeclare public _settings: IChordNonRibbonSettings;\n\tdeclare public _privateSettings: IChordNonRibbonPrivate;\n\tdeclare public _dataItemSettings: IChordNonRibbonDataItem;\n\n\tprotected _afterNew() {\n\t\tthis._settings.themeTags = $utils.mergeTags(this._settings.themeTags, [\"chord\", \"basic\"]);\n\n\t\tsuper._afterNew();\n\t}\n\n\tprotected _makeMatrix(): number[][] {\n\t\tconst matrix: number[][] = [];\n\t\t$array.each(this.nodes.dataItems, (sourceDataItem) => {\n\t\t\tconst group: number[] = [];\n\t\t\tmatrix.push(group);\n\n\t\t\t$array.each(this.nodes.dataItems, (targetDataItem) => {\n\t\t\t\tlet value = 1;\n\n\t\t\t\tif (sourceDataItem === targetDataItem) {\n\t\t\t\t\tvalue = 0;\n\t\t\t\t}\n\n\t\t\t\tgroup.push(value);\n\t\t\t})\n\t\t})\n\t\treturn matrix;\n\t}\n\n\tprotected _updateLink(_ribbon: RibbonGenerator<any, Ribbon, RibbonSubgroup>, link: ChordLink, _linkRadius: number, chordLayoutItem: any) {\n\t\tlink._type = this.get(\"linkType\");\n\t\tif (chordLayoutItem) {\n\t\t\tconst linkType = this.get(\"linkType\");\n\n\t\t\tlink.set(\"draw\", (display) => {\n\t\t\t\tlet p0 = link._p0;\n\t\t\t\tlet p1 = link._p1;\n\t\t\t\tif (p0 && p1) {\n\t\t\t\t\tdisplay.moveTo(p0.x, p0.y);\n\t\t\t\t\tif (linkType == \"line\") {\n\t\t\t\t\t\tdisplay.lineTo(p1.x, p1.y);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tdisplay.quadraticCurveTo(0, 0, p1.x, p1.y);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tprotected _getLinkPoints(link: ChordLink, linkRadius: number, _chordLayoutItem: any) {\n\t\tconst source = link.get(\"source\");\n\t\tconst target = link.get(\"target\");\n\n\t\tif (source && target) {\n\n\t\t\tconst sourceSlice = source.get(\"slice\");\n\t\t\tconst targetSlice = target.get(\"slice\");\n\n\t\t\tconst startAngle0 = sourceSlice.get(\"startAngle\", 0);\n\t\t\tconst arc0 = sourceSlice.get(\"arc\", 0)\n\t\t\tconst angle0 = startAngle0 + arc0 / 2;\n\n\t\t\tconst startAngle1 = targetSlice.get(\"startAngle\", 0);\n\t\t\tconst arc1 = targetSlice.get(\"arc\", 0)\n\t\t\tconst angle1 = startAngle1 + arc1 / 2;\n\n\t\t\tlink._p0 = { x: linkRadius * $math.cos(angle0), y: linkRadius * $math.sin(angle0) };\n\t\t\tlink._p1 = { x: linkRadius * $math.cos(angle1), y: linkRadius * $math.sin(angle1) };\n\t\t}\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { FlowNode } from \"./FlowNode\";\nimport type { Sankey } from \"./Sankey\";\nimport type { Bullet } from \"../../core/render/Bullet\";\n\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { FlowNodes, IFlowNodesSettings, IFlowNodesDataItem, IFlowNodesPrivate, IFlowNodesEvents } from \"./FlowNodes\";\nimport { RoundedRectangle } from \"../../core/render/RoundedRectangle\";\n\nexport interface ISankeyNodesDataItem extends IFlowNodesDataItem {\n\trectangle: RoundedRectangle;\n}\n\nexport interface ISankeyNodesSettings extends IFlowNodesSettings {\n}\n\nexport interface ISankeyNodesPrivate extends IFlowNodesPrivate {\n}\n\nexport interface ISankeyNodesEvents extends IFlowNodesEvents {\n}\n\n/**\n * Holds instances of nodes for a [[Sankey]] series.\n */\nexport class SankeyNodes extends FlowNodes {\n\tpublic static className: string = \"SankeyNodes\";\n\tpublic static classNames: Array<string> = FlowNodes.classNames.concat([SankeyNodes.className]);\n\n\tdeclare public _settings: ISankeyNodesSettings;\n\tdeclare public _privateSettings: ISankeyNodesPrivate;\n\tdeclare public _dataItemSettings: ISankeyNodesDataItem;\n\tdeclare public _events: ISankeyNodesEvents;\n\n\t/**\n\t * List of rectangle elements.\n\t *\n\t * @default new ListTemplate<RoundedRectangle>\n\t */\n\tpublic readonly rectangles: ListTemplate<RoundedRectangle> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => RoundedRectangle._new(this._root, { themeTags: [\"shape\"] }, [this.rectangles.template])\n\t);\n\n\t/**\n\t * Related [[Sankey]] series.\n\t */\n\tpublic flow: Sankey | undefined;\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>): FlowNode {\n\t\tconst flow = this.flow;\n\n\t\tconst node = super.makeNode(dataItem, \"sankey\");\n\n\t\tconst rectangle = node.children.insertIndex(0, this.rectangles.make());\n\t\tthis.rectangles.push(rectangle);\n\t\trectangle._setSoft(\"fill\", dataItem.get(\"fill\"));\n\t\trectangle._setSoft(\"fillPattern\", dataItem.get(\"fillPattern\"));\n\t\t\n\t\tdataItem.set(\"rectangle\", rectangle);\n\n\t\tnode.events.on(\"dragged\", () => {\n\t\t\tconst d3SankeyNode = (node.dataItem as DataItem<ISankeyNodesDataItem>).get(\"d3SankeyNode\");\n\t\t\tif (d3SankeyNode) {\n\t\t\t\tif (flow) {\n\t\t\t\t\tif (flow.get(\"orientation\") == \"horizontal\") {\n\t\t\t\t\t\td3SankeyNode.x0 = node.x();\n\t\t\t\t\t\td3SankeyNode.y0 = node.y();\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\td3SankeyNode.x0 = node.y();\n\t\t\t\t\t\td3SankeyNode.y0 = node.x();\n\t\t\t\t\t}\n\n\t\t\t\t\tflow.updateSankey();\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\tconst label = this.labels.make();\n\t\tthis.labels.push(label);\n\n\t\tif (flow) {\n\t\t\tlabel.addTag(flow.get(\"orientation\", \"\"));\n\t\t}\n\t\tnode.children.push(label);\n\t\tdataItem.set(\"label\", label);\n\n\t\tlabel._setDataItem(dataItem);\n\t\trectangle._setDataItem(dataItem);\n\n\t\treturn node;\n\t}\n\n\tpublic _positionBullet(bullet: Bullet) {\n\t\tconst sprite = bullet.get(\"sprite\");\n\t\tif (sprite) {\n\t\t\tconst dataItem = sprite.dataItem as DataItem<this[\"_dataItemSettings\"]>;\n\t\t\tif (dataItem) {\n\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\t\tif (sprite) {\n\t\t\t\t\tconst rectangle = dataItem.get(\"rectangle\");\n\t\t\t\t\tconst node = dataItem.get(\"node\");\n\t\t\t\t\tconst locationX = bullet.get(\"locationX\", 0.5);\n\t\t\t\t\tconst locationY = bullet.get(\"locationY\", 0.5);\n\t\t\t\t\tif (rectangle) {\n\t\t\t\t\t\tsprite.setAll({ x: node.x() + rectangle.width() * locationX, y: node.y() + rectangle.height() * locationY });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tlet rectangle = dataItem.get(\"rectangle\");\n\t\tif (rectangle) {\n\t\t\tthis.rectangles.removeValue(rectangle);\n\t\t\trectangle.dispose();\n\t\t}\n\t}\n\n\tpublic _updateNodeColor(dataItem: DataItem<this[\"_dataItemSettings\"]>){\n\t\tconst rectangle = dataItem.get(\"rectangle\");\n\t\tif(rectangle){\n\t\t\trectangle.set(\"fill\", dataItem.get(\"fill\"));\n\t\t\trectangle.set(\"fillPattern\", dataItem.get(\"fillPattern\"));\n\t\t}\n\t}\t\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { FlowNode } from \"./FlowNode\";\nimport type { Sankey, ISankeyDataItem } from \"./Sankey\";\nimport type { ISankeyNodesDataItem } from \"./SankeyNodes\";\nimport type { IOrientationPoint } from \"../../core/util/IPoint\";\n\nimport { FlowLink, IFlowLinkPrivate, IFlowLinkSettings } from \"./FlowLink\";\nimport type { IPoint } from \"../../core/util/IPoint\";\nimport * as $math from \"../../core/util/Math\";\n\n\nexport interface ISankeyLinkSettings extends IFlowLinkSettings {\n\n\t/**\n\t * Source node data item.\n\t */\n\tsource?: DataItem<ISankeyNodesDataItem>;\n\n\t/**\n\t * Source node data item.\n\t */\n\ttarget?: DataItem<ISankeyNodesDataItem>;\n\n\t/**\n\t * Type of fill to use for links.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/flow-charts/sankey-diagram/#Color_mode} for more info\n\t * @default \"gradient\"\n\t */\n\tfillStyle?: \"solid\" | \"gradient\" | \"source\" | \"target\";\n\n\t/**\n\t * A relative distance from node for link \"elbow\" (bend point).\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/flow-charts/sankey-diagram/#Bend_point} for more info\n\t * @default 0.2\n\t */\n\tcontrolPointDistance?: number;\n\n}\n\nexport interface ISankeyLinkPrivate extends IFlowLinkPrivate {\n\n\t/**\n\t * Link orientation.\n\t */\n\torientation?: \"horizontal\" | \"vertical\";\n\n}\n\nexport class SankeyLink extends FlowLink {\n\tdeclare public _settings: ISankeyLinkSettings;\n\tdeclare public _privateSettings: ISankeyLinkPrivate;\n\n\tpublic static className: string = \"SankeyLink\";\n\tpublic static classNames: Array<string> = FlowLink.classNames.concat([SankeyLink.className]);\n\n\tdeclare protected _dataItem: DataItem<ISankeyDataItem> | undefined;\n\n\tdeclare public series: Sankey | undefined;\n\n\tprotected _svgPath: SVGPathElement = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n\tprotected _totalLength: number = 0;\n\n\n\tpublic _beforeChanged() {\n\t\tsuper._beforeChanged();\n\n\t\tif (this.isDirty(\"source\")) {\n\t\t\tconst source = this.get(\"source\");\n\t\t\tif (source) {\n\t\t\t\tconst sourceNode = source.get(\"node\");\n\t\t\t\tthis._disposers.push(sourceNode.events.on(\"positionchanged\", () => {\n\t\t\t\t\tthis.markDirty();\n\t\t\t\t}))\n\t\t\t}\n\t\t}\n\t\tif (this.isDirty(\"target\")) {\n\t\t\tconst target = this.get(\"target\");\n\t\t\tif (target) {\n\t\t\t\tconst targetNode = target.get(\"node\");\n\t\t\t\tthis._disposers.push(targetNode.events.on(\"positionchanged\", () => {\n\t\t\t\t\tthis.markDirty();\n\t\t\t\t}))\n\t\t\t}\n\t\t}\n\n\t\tif (this.isPrivateDirty(\"orientation\")) {\n\t\t\tconst series = this.series;\n\t\t\tconst dataItem = this.dataItem as DataItem<ISankeyDataItem>;\n\t\t\tif (dataItem && series) {\n\t\t\t\tseries._updateLinkColor(dataItem);\n\t\t\t}\n\t\t}\n\n\t\tconst target = this.get(\"target\");\n\t\tconst source = this.get(\"source\");\n\n\t\tlet sourceNode: FlowNode | undefined;\n\t\tlet targetNode: FlowNode | undefined;\n\n\t\tif (source && target) {\n\t\t\tthis._clear = true;\n\t\t\tsourceNode = source.get(\"node\");\n\t\t\ttargetNode = target.get(\"node\");\n\n\t\t\tlet x0 = 0;\n\t\t\tlet x1 = 0;\n\t\t\tlet y0 = 0;\n\t\t\tlet y1 = 0;\n\n\t\t\tlet xt0 = 0;\n\t\t\tlet yt0 = 0;\n\n\t\t\tlet xt1 = 0;\n\t\t\tlet yt1 = 0;\n\n\t\t\tlet xb0 = 0;\n\t\t\tlet xb1 = 0;\n\n\t\t\tlet yb0 = 0;\n\t\t\tlet yb1 = 0;\n\n\t\t\tlet xm0 = 0;\n\t\t\tlet xm1 = 0;\n\n\t\t\tlet ym0 = 0;\n\t\t\tlet ym1 = 0;\n\n\t\t\tlet angle0 = 0;\n\t\t\tlet angle1 = 0;\n\n\n\t\t\tconst dataItem = this.dataItem as DataItem<ISankeyDataItem>;\n\t\t\tif (dataItem) {\n\t\t\t\tconst d3SankeyLink = dataItem.get(\"d3SankeyLink\");\n\t\t\t\tif (d3SankeyLink) {\n\n\t\t\t\t\tlet w = d3SankeyLink.width || 0;\n\n\t\t\t\t\tlet orientation = this.getPrivate(\"orientation\");\n\n\t\t\t\t\tif (orientation == \"vertical\") {\n\t\t\t\t\t\tif (sourceNode) {\n\t\t\t\t\t\t\ty0 = sourceNode.y() + sourceNode.get(\"dy\", 0);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (targetNode) {\n\t\t\t\t\t\t\ty1 = targetNode.y() + targetNode.get(\"dy\", 0);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tangle0 = 90;\n\t\t\t\t\t\tangle1 = 90;\n\n\t\t\t\t\t\tx0 = d3SankeyLink.y0 || 0;\n\t\t\t\t\t\tx1 = d3SankeyLink.y1 || 0;\n\n\t\t\t\t\t\tx0 += sourceNode.get(\"dx\", 0);\n\t\t\t\t\t\tx1 += targetNode.get(\"dx\", 0);\n\n\t\t\t\t\t\tif (y1 < y0) {\n\t\t\t\t\t\t\t[x0, x1] = [x1, x0];\n\t\t\t\t\t\t\t[y0, y1] = [y1, y0];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (source.get(\"unknown\")) {\n\t\t\t\t\t\t\tx0 = x1;\n\t\t\t\t\t\t\ty0 = y0 + (y1 - y0) / 2;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (target.get(\"unknown\")) {\n\t\t\t\t\t\t\tx1 = x0;\n\t\t\t\t\t\t\ty1 = y0 + (y1 - y0) / 2;\n\t\t\t\t\t\t}\n\n\n\t\t\t\t\t\txt0 = x0 - w / 2;\n\t\t\t\t\t\tyt0 = y0;\n\n\t\t\t\t\t\txt1 = x1 - w / 2;\n\t\t\t\t\t\tyt1 = y1;\n\n\t\t\t\t\t\txb0 = x0 + w / 2;\n\t\t\t\t\t\txb1 = x1 + w / 2;\n\n\t\t\t\t\t\tyb0 = y0;\n\t\t\t\t\t\tyb1 = y1;\n\n\t\t\t\t\t\txm0 = x0;\n\t\t\t\t\t\txm1 = x1;\n\n\t\t\t\t\t\tym0 = y0;\n\t\t\t\t\t\tym1 = y1;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tif (sourceNode) {\n\t\t\t\t\t\t\tx0 = sourceNode.x() + sourceNode.get(\"dx\", 0);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (targetNode) {\n\t\t\t\t\t\t\tx1 = targetNode.x() + targetNode.get(\"dx\", 0);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\ty0 = d3SankeyLink.y0 || 0;\n\t\t\t\t\t\ty1 = d3SankeyLink.y1 || 0;\n\n\t\t\t\t\t\ty0 += sourceNode.get(\"dy\", 0);\n\t\t\t\t\t\ty1 += targetNode.get(\"dy\", 0);\n\n\t\t\t\t\t\tif (x1 < x0) {\n\t\t\t\t\t\t\t[x0, x1] = [x1, x0];\n\t\t\t\t\t\t\t[y0, y1] = [y1, y0];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (source.get(\"unknown\")) {\n\t\t\t\t\t\t\ty0 = y1;\n\t\t\t\t\t\t\tx0 = x0 + (x1 - x0) / 2;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (target.get(\"unknown\")) {\n\t\t\t\t\t\t\ty1 = y0;\n\t\t\t\t\t\t\tx1 = x0 + (x1 - x0) / 2;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\txt0 = x0;\n\t\t\t\t\t\tyt0 = y0 - w / 2;\n\n\t\t\t\t\t\txt1 = x1;\n\t\t\t\t\t\tyt1 = y1 - w / 2;\n\n\t\t\t\t\t\txb0 = x0;\n\t\t\t\t\t\txb1 = x1;\n\n\t\t\t\t\t\tyb0 = y0 + w / 2;\n\t\t\t\t\t\tyb1 = y1 + w / 2;\n\n\t\t\t\t\t\txm0 = x0;\n\t\t\t\t\t\txm1 = x1;\n\n\t\t\t\t\t\tym0 = y0;\n\t\t\t\t\t\tym1 = y1;\n\t\t\t\t\t}\n\n\t\t\t\t\tif ($math.round(xt0, 3) == $math.round(xt1, 3)) {\n\t\t\t\t\t\txt1 += 0.01;\n\t\t\t\t\t}\n\n\t\t\t\t\tif ($math.round(yt0, 3) == $math.round(yt1, 3)) {\n\t\t\t\t\t\tyt1 += 0.01;\n\t\t\t\t\t}\n\n\t\t\t\t\tif ($math.round(xb0, 3) == $math.round(xb1, 3)) {\n\t\t\t\t\t\txb1 += 0.01;\n\t\t\t\t\t}\n\n\t\t\t\t\tif ($math.round(yb0, 3) == $math.round(yb1, 3)) {\n\t\t\t\t\t\tyb1 += 0.01;\n\t\t\t\t\t}\n\n\t\t\t\t\tlet cpd = this.get(\"controlPointDistance\", 0.2);\n\t\t\t\t\tcpd = Math.min(0.4999, cpd);\n\n\t\t\t\t\tlet kxt0 = xt0 + (xt1 - xt0) * cpd * $math.cos(angle0);\n\t\t\t\t\tlet kyt0 = yt0 + (yt1 - yt0) * cpd * $math.sin(angle0);\n\n\t\t\t\t\tlet kxt1 = xt1 - (xt1 - xt0) * cpd * $math.cos(angle1);\n\t\t\t\t\tlet kyt1 = yt1 - (yt1 - yt0) * cpd * $math.sin(angle1);\n\n\t\t\t\t\tlet kxm0 = xm0 + (xm1 - xm0) * cpd * $math.cos(angle0);\n\t\t\t\t\tlet kym0 = ym0 + (ym1 - ym0) * cpd * $math.sin(angle0);\n\n\t\t\t\t\tlet kxm1 = xm1 - (xm1 - xm0) * cpd * $math.cos(angle1);\n\t\t\t\t\tlet kym1 = ym1 - (ym1 - ym0) * cpd * $math.sin(angle1);\n\n\t\t\t\t\tlet angle = $math.getAngle({ x: kxt0, y: kyt0 }, { x: kxt1, y: kyt1 });\n\n\t\t\t\t\tlet dx = (w / $math.cos(angle) - w) / $math.tan(angle) * $math.cos(angle0);\n\t\t\t\t\tlet dy = (w / $math.sin(angle) - w) * $math.tan(angle) * $math.sin(angle0);\n\n\t\t\t\t\tlet kxb0 = -dx / 2 + xb0 + (xb1 - xb0) * cpd * $math.cos(angle0);\n\t\t\t\t\tlet kyb0 = -dy / 2 + yb0 + (yb1 - yb0) * cpd * $math.sin(angle0);\n\n\t\t\t\t\tlet kxb1 = -dx / 2 + xb1 - (xb1 - xb0) * cpd * $math.cos(angle1);\n\t\t\t\t\tlet kyb1 = -dy / 2 + yb1 - (yb1 - yb0) * cpd * $math.sin(angle1);\n\n\t\t\t\t\tkxt0 += dx / 2;\n\t\t\t\t\tkyt0 += dy / 2;\n\n\t\t\t\t\tkxt1 += dx / 2;\n\t\t\t\t\tkyt1 += dy / 2;\n\n\t\t\t\t\tif (orientation == \"vertical\") {\n\t\t\t\t\t\tkyt0 = Math.min(yt1, Math.max(yt0 + 1, kyt0));\n\t\t\t\t\t\tkyb0 = Math.min(yb1, Math.max(yb0 + 1, kyb0));\n\n\t\t\t\t\t\tkyt1 = Math.max(yt0, Math.min(yt1 - 1, kyt1));\n\t\t\t\t\t\tkyb1 = Math.max(yb0, Math.min(yb1 - 1, kyb1));\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tkxt0 = Math.min(xt1, Math.max(xt0 + 1, kxt0));\n\t\t\t\t\t\tkxb0 = Math.min(xb1, Math.max(xb0 + 1, kxb0));\n\n\t\t\t\t\t\tkxt1 = Math.max(xt0, Math.min(xt1 - 1, kxt1));\n\t\t\t\t\t\tkxb1 = Math.max(xb0, Math.min(xb1 - 1, kxb1));\n\t\t\t\t\t}\n\n\t\t\t\t\tlet segment = [[xt0, yt0, xb0, yb0], [kxt0, kyt0, kxb0, kyb0], [kxt1, kyt1, kxb1, kyb1], [xt1, yt1, xb1, yb1]];\n\n\t\t\t\t\tthis.set(\"draw\", (display) => {\n\t\t\t\t\t\tconst series = this.series!;\n\t\t\t\t\t\tseries._fillGenerator.context(display as any);\n\t\t\t\t\t\tseries._fillGenerator(segment as [number, number][]);\n\t\t\t\t\t})\n\n\t\t\t\t\tlet middleSegment = [[xm0, ym0], [kxm0, kym0], [kxm1, kym1], [xm1, ym1]];\n\n\t\t\t\t\tconst path = this.series!._strokeGenerator(middleSegment as [number, number][]);\n\n\t\t\t\t\tif (path) {\n\t\t\t\t\t\tthis._svgPath.setAttribute(\"d\", path);\n\t\t\t\t\t\tthis._totalLength = this._svgPath.getTotalLength();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (this.series && this.dataItem) {\n\t\t\tthis.series._positionBullets(this.dataItem as any);\n\t\t}\n\t}\n\n\tpublic getPoint(location: number): IOrientationPoint {\n\t\tif (this._svgPath) {\n\t\t\tif (this._svgPath.getAttribute(\"d\")) {\n\t\t\t\tlet p0 = this._svgPath.getPointAtLength(location * this._totalLength - 0.1);\n\t\t\t\tlet p1 = this._svgPath.getPointAtLength(location * this._totalLength + 0.1);\n\t\t\t\tlet p = this.toGlobal(this._svgPath.getPointAtLength(location * this._totalLength));\n\t\t\t\treturn { x: p.x, y: p.y, angle: $math.getAngle(p0, p1) };\n\t\t\t}\n\t\t}\n\t\treturn { x: 0, y: 0, angle: 0 }\n\t}\n\n\tpublic _getTooltipPoint(): IPoint {\n\t\treturn this.toLocal(super._getTooltipPoint());\n\t}\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "import {min} from \"d3-array\";\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length ? node.depth\n      : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1\n      : 0;\n}\n", "export default function constant(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {max, min, sum} from \"d3-array\";\nimport {justify} from \"./align.js\";\nimport constant from \"./constant.js\";\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\n\nfunction computeLinkBreadths({nodes}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport default function Sankey() {\n  let x0 = 0, y0 = 0, x1 = 1, y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8, py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey() {\n    const graph = {nodes: nodes.apply(null, arguments), links: links.apply(null, arguments)};\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function(graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function(_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n\n  sankey.nodeAlign = function(_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n\n  sankey.nodeSort = function(_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n\n  sankey.nodeWidth = function(_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n\n  sankey.nodePadding = function(_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n\n  sankey.nodes = function(_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n\n  sankey.links = function(_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n\n  sankey.linkSort = function(_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n\n  sankey.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n\n  sankey.iterations = function(_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n\n  function computeNodeLinks({nodes, links}) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {source, target} = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({nodes}) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined\n          ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {target} of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeHeights({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {source} of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeLayers({nodes}) {\n    const x = max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {source, value} of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {target, value} of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({sourceLinks, targetLinks}) {\n    if (linkSort === undefined) {\n      for (const {source: {sourceLinks}} of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {target: {targetLinks}} of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n", "import type { DataItem } from \"../../core/render/Component\";\n\nimport { Flow, IFlowSettings, IFlowDataItem, IFlowPrivate, IFlowEvents } from \"./Flow\";\nimport { SankeyNodes, ISankeyNodesDataItem } from \"./SankeyNodes\";\nimport { SankeyLink } from \"./SankeyLink\";\nimport { area, line } from \"d3-shape\";\nimport { curveMonotoneXTension } from \"../../core/render/MonotoneXTension\";\nimport { curveMonotoneYTension } from \"../../core/render/MonotoneYTension\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $type from \"../../core/util/Type\";\nimport * as $utils from \"../../core/util/Utils\";\nimport * as d3sankey from \"d3-sankey\";\n\nimport type { Bullet } from \"../../core/render/Bullet\";\n\nexport interface ISankeyDataItem extends IFlowDataItem {\n\n\t/**\n\t * Link element.\n\t */\n\tlink: SankeyLink;\n\n\t/**\n\t * Source node data item.\n\t */\n\tsource: DataItem<ISankeyNodesDataItem>;\n\n\t/**\n\t * Target node data item.\n\t */\n\ttarget: DataItem<ISankeyNodesDataItem>;\n\n}\n\nexport interface ISankeySettings extends IFlowSettings {\n\n\t/**\n\t * Orientation of the series.\n\t *\n\t * @default \"horizontal\"\n\t */\n\torientation?: \"horizontal\" | \"vertical\";\n\n\t/**\n\t * Alignment of nodes.\n\t *\n\t * @default \"left\"\n\t */\n\tnodeAlign?: \"left\" | \"right\" | \"justify\" | \"center\"\n\n\t/**\n\t * Tension setting of the link curve.\n\t *\n\t * Accepts values from `0` to `1`.\n\t *\n\t * `1` will result in perfectly straight lines.\n\t *\n\t * @default 0.5\n\t */\n\tlinkTension?: number;\n\n\t/**\n\t * A custom function to use when sorting nodes.\n\t *\n\t * @todo test\n\t * @ignore\n\t */\n\tnodeSort?: (a: d3sankey.SankeyNodeMinimal<{}, {}>, b: d3sankey.SankeyNodeMinimal<{}, {}>) => number | null;\n\n\t/**\n\t * A custom function to use when sorting links.\n\t * \n\t * Use `null` to sort links exactly the way they are presented in data.\n\t * \n\t * @since 5.4.4\n\t */\n\tlinkSort?: null | ((a: d3sankey.SankeyLinkMinimal<{}, {}>, b: d3sankey.SankeyLinkMinimal<{}, {}>) => number | null);\n\n}\n\nexport interface ISankeyPrivate extends IFlowPrivate {\n}\n\nexport interface ISankeyEvents extends IFlowEvents {\n}\n\n/**\n * Sankey series.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/flow-charts/} for more information\n * @important\n */\nexport class Sankey extends Flow {\n\n\tpublic static className: string = \"Sankey\";\n\tpublic static classNames: Array<string> = Flow.classNames.concat([Sankey.className]);\n\n\t/**\n\t * List of link elements.\n\t *\n\t * @default new ListTemplate<SankeyLink>\n\t */\n\tpublic readonly links: ListTemplate<SankeyLink> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => SankeyLink._new(this._root, { themeTags: [\"link\", \"shape\"] }, [this.links.template])\n\t);\n\n\tprotected _afterNew() {\n\t\tthis._settings.themeTags = $utils.mergeTags(this._settings.themeTags, [\"sankey\", this._settings.orientation || \"horizontal\"]);\n\n\t\tthis._fillGenerator.y0(function(p: number[]) {\n\t\t\treturn p[3];\n\t\t});\n\n\t\tthis._fillGenerator.x0(function(p: number[]) {\n\t\t\treturn p[2];\n\t\t});\n\n\t\tthis._fillGenerator.y1(function(p: number[]) {\n\t\t\treturn p[1];\n\t\t});\n\n\t\tthis._fillGenerator.x1(function(p: number[]) {\n\t\t\treturn p[0];\n\t\t});\n\n\t\tsuper._afterNew();\n\t}\n\n\t/**\n\t * A series representing sankey nodes.\n\t *\n\t * @default SankeyNodes.new()\n\t */\n\tpublic readonly nodes: SankeyNodes = this.children.push(SankeyNodes.new(this._root, {}));\n\n\tdeclare public _settings: ISankeySettings;\n\tdeclare public _privateSettings: ISankeyPrivate;\n\tdeclare public _dataItemSettings: ISankeyDataItem;\n\tdeclare public _events: ISankeyEvents;\n\n\tpublic _d3Sankey = d3sankey.sankey();\n\tpublic _d3Graph: d3sankey.SankeyGraph<{}, {}> | undefined;\n\n\tpublic _fillGenerator = area();\n\tpublic _strokeGenerator = line();\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeLink(dataItem: DataItem<this[\"_dataItemSettings\"]>): SankeyLink {\n\t\tconst source = dataItem.get(\"source\");\n\t\tconst target = dataItem.get(\"target\");\n\n\t\tconst link = this.links.make();\n\n\t\tif (source.get(\"unknown\")) {\n\t\t\tlink.addTag(\"source\");\n\t\t\tlink.addTag(\"unknown\");\n\t\t}\n\n\t\tif (target.get(\"unknown\")) {\n\t\t\tlink.addTag(\"target\");\n\t\t\tlink.addTag(\"unknown\");\n\t\t}\n\n\t\tthis.linksContainer.children.push(link);\n\t\tlink._setDataItem(dataItem);\n\t\tlink.set(\"source\", source);\n\t\tlink.set(\"target\", target);\n\t\tlink.series = this;\n\n\t\tthis.links.push(link);\n\n\t\treturn link;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic updateSankey() {\n\t\tconst d3Graph = this._d3Graph;\n\t\tif (d3Graph) {\n\t\t\tthis._d3Sankey.update(d3Graph);\n\n\t\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\t\tconst link = dataItem.get(\"link\");\n\t\t\t\tlink.setPrivate(\"orientation\", this.get(\"orientation\"));\n\t\t\t\tlink.markDirty();\n\t\t\t})\n\t\t}\n\t}\n\n\tpublic _updateLinkColor(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._updateLinkColor(dataItem);\n\t\tconst orientation = this.get(\"orientation\");\n\t\tconst fillGradient = dataItem.get(\"link\")._fillGradient;\n\t\tconst strokeGradient = dataItem.get(\"link\")._strokeGradient;\n\n\t\tif (orientation == \"vertical\") {\n\t\t\tif (fillGradient) {\n\t\t\t\tfillGradient.set(\"rotation\", 90);\n\t\t\t}\n\t\t\tif (strokeGradient) {\n\t\t\t\tstrokeGradient.set(\"rotation\", 90);\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tif (fillGradient) {\n\t\t\t\tfillGradient.set(\"rotation\", 0);\n\t\t\t}\n\t\t\tif (strokeGradient) {\n\t\t\t\tstrokeGradient.set(\"rotation\", 0);\n\t\t\t}\n\t\t}\n\n\t}\n\n\tprotected _getBulletLocation(bullet: Bullet): number {\n\t\tif (this.get(\"orientation\") == \"vertical\") {\n\t\t\treturn bullet.get(\"locationY\", 0);\n\t\t}\n\t\telse {\n\t\t\treturn bullet.get(\"locationX\", 0);\n\t\t}\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\t\tlet vertical = false;\n\t\tif (this.get(\"orientation\") == \"vertical\") {\n\t\t\tvertical = true;\n\t\t}\n\n\t\tif (this.isDirty(\"orientation\") || this.isDirty(\"linkTension\")) {\n\t\t\tconst linkTension = this.get(\"linkTension\", 0.5);\n\t\t\tif (vertical) {\n\t\t\t\tthis._fillGenerator.curve(curveMonotoneYTension(linkTension));\n\t\t\t\tthis._strokeGenerator.curve(curveMonotoneYTension(linkTension));\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis._fillGenerator.curve(curveMonotoneXTension(linkTension));\n\t\t\t\tthis._strokeGenerator.curve(curveMonotoneXTension(linkTension));\n\t\t\t}\n\t\t}\n\n\t\tif (this._valuesDirty || this._sizeDirty || this.isDirty(\"nodePadding\") || this.isDirty(\"nodeWidth\") || this.isDirty(\"nodeAlign\") || this.isDirty(\"nodeSort\") || this.isDirty(\"orientation\") || this.isDirty(\"linkTension\") || this.isDirty(\"linkSort\")) {\n\t\t\tif (this._nodesData.length > 0) {\n\t\t\t\tconst d3Sankey = this._d3Sankey;\n\t\t\t\tlet w = this.innerWidth();\n\t\t\t\tlet h = this.innerHeight();\n\n\t\t\t\tif (vertical) {\n\t\t\t\t\t[w, h] = [h, w];\n\t\t\t\t}\n\n\t\t\t\td3Sankey.size([w, h]);\n\t\t\t\td3Sankey.nodePadding(this.get(\"nodePadding\", 10));\n\t\t\t\td3Sankey.nodeWidth(this.get(\"nodeWidth\", 10));\n\t\t\t\td3Sankey.nodeSort(this.get(\"nodeSort\", null) as any);\n\t\t\t\t(d3Sankey as any).linkSort(this.get(\"linkSort\") as any);\n\n\t\t\t\tswitch (this.get(\"nodeAlign\")) {\n\t\t\t\t\tcase \"right\":\n\t\t\t\t\t\td3Sankey.nodeAlign(d3sankey.sankeyRight);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"justify\":\n\t\t\t\t\t\td3Sankey.nodeAlign(d3sankey.sankeyJustify);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"center\":\n\t\t\t\t\t\td3Sankey.nodeAlign(d3sankey.sankeyCenter);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\td3Sankey.nodeAlign(d3sankey.sankeyLeft);\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tthis._d3Graph = d3Sankey({ nodes: this._nodesData, links: this._linksData });\n\n\t\t\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\t\t\tconst link = dataItem.get(\"link\");\n\t\t\t\t\tlink.setPrivate(\"orientation\", this.get(\"orientation\"));\n\t\t\t\t\tlink.markDirty();\n\t\t\t\t})\n\n\t\t\t\tconst d3Graph = this._d3Graph;\n\n\t\t\t\tif (d3Graph) {\n\t\t\t\t\tconst nodes = d3Graph.nodes;\n\n\t\t\t\t\t$array.each(nodes, (d3SankeyNode) => {\n\t\t\t\t\t\tconst dataItem = (d3SankeyNode as any).dataItem as DataItem<ISankeyNodesDataItem>;\n\t\t\t\t\t\tconst node = dataItem.get(\"node\");\n\n\t\t\t\t\t\tlet x0: number | undefined;\n\t\t\t\t\t\tlet x1: number | undefined;\n\t\t\t\t\t\tlet y0: number | undefined;\n\t\t\t\t\t\tlet y1: number | undefined;\n\n\t\t\t\t\t\tif (vertical) {\n\t\t\t\t\t\t\tx0 = d3SankeyNode.y0;\n\t\t\t\t\t\t\tx1 = d3SankeyNode.y1;\n\t\t\t\t\t\t\ty0 = d3SankeyNode.x0;\n\t\t\t\t\t\t\ty1 = d3SankeyNode.x1;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tx0 = d3SankeyNode.x0;\n\t\t\t\t\t\t\tx1 = d3SankeyNode.x1;\n\t\t\t\t\t\t\ty0 = d3SankeyNode.y0;\n\t\t\t\t\t\t\ty1 = d3SankeyNode.y1;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif ($type.isNumber(x0) && $type.isNumber(x1) && $type.isNumber(y0) && $type.isNumber(y1)) {\n\t\t\t\t\t\t\tnode.setAll({ x: x0, y: y0, width: x1 - x0, height: y1 - y0 });\n\n\t\t\t\t\t\t\tconst rectangle = dataItem.get(\"rectangle\");\n\t\t\t\t\t\t\trectangle.setAll({ width: x1 - x0, height: y1 - y0 });\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\r\nimport type { FlowNode } from \"./FlowNode\";\r\nimport type { ArcDiagram } from \"./ArcDiagram\";\r\n\r\nimport { FlowNodes, IFlowNodesSettings, IFlowNodesDataItem, IFlowNodesPrivate, IFlowNodesEvents } from \"./FlowNodes\";\r\nimport { Circle } from \"../../core/render/Circle\";\r\nimport { Template } from \"../../core/util/Template\";\r\nimport { ListTemplate } from \"../../core/util/List\";\r\nimport { Label } from \"../../core/render/Label\";\r\n\r\nexport interface IArcDiagramNodesDataItem extends IFlowNodesDataItem {\r\n\r\n\t/**\r\n\t * Node [[Circle]] element.\r\n\t */\r\n\tcircle: Circle;\r\n\r\n\t/**\r\n\t * Node label element.\r\n\t */\r\n\tlabel: Label;\r\n}\r\n\r\nexport interface IArcDiagramNodesSettings extends IFlowNodesSettings { };\r\n\r\nexport interface IArcDiagramNodesPrivate extends IFlowNodesPrivate { };\r\n\r\nexport interface IArcDiagramNodesEvents extends IFlowNodesEvents { };\r\n\r\n/**\r\n * Holds instances of nodes for a [[ArcDiagram]] series.\r\n */\r\nexport class ArcDiagramNodes extends FlowNodes {\r\n\tpublic static className: string = \"ArcDiagramNodes\";\r\n\tpublic static classNames: Array<string> = FlowNodes.classNames.concat([ArcDiagramNodes.className]);\r\n\r\n\t/**\r\n\t * List of label elements.\r\n\t *\r\n\t * @default new ListTemplate<Label>\r\n\t */\r\n\tpublic readonly labels: ListTemplate<Label> = new ListTemplate(\r\n\t\tTemplate.new({}),\r\n\t\t() => Label._new(this._root, {}, [this.labels.template])\r\n\t);\r\n\r\n\tdeclare public _settings: IArcDiagramNodesSettings;\r\n\tdeclare public _privateSettings: IArcDiagramNodesPrivate;\r\n\tdeclare public _dataItemSettings: IArcDiagramNodesDataItem;\r\n\tdeclare public _events: IArcDiagramNodesEvents;\r\n\r\n\t/**\r\n\t * Related [[ArcDiagram]] series.\r\n\t */\r\n\tpublic flow: ArcDiagram | undefined;\r\n\r\n\tprotected _dAngle: number = 0;\r\n\r\n\t/**\r\n\t * List of slice elements.\r\n\t *\r\n\t * @default new ListTemplate<Slice>\r\n\t */\r\n\tpublic readonly circles: ListTemplate<Circle> = new ListTemplate(\r\n\t\tTemplate.new({}),\r\n\t\t() => Circle._new(this._root, { themeTags: [\"shape\"] }, [this.circles.template])\r\n\t);\r\n\r\n\t/**\r\n\t * @ignore\r\n\t */\r\n\tpublic makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>): FlowNode {\r\n\t\tconst node = super.makeNode(dataItem, \"ArcDiagram\");\r\n\r\n\t\tconst circle = node.children.insertIndex(0, this.circles.make());\r\n\t\tdataItem.set(\"circle\", circle);\r\n\t\tcircle._setSoft(\"fill\", dataItem.get(\"fill\"));\r\n\t\tcircle._setSoft(\"fillPattern\", dataItem.get(\"fillPattern\"));\r\n\r\n\t\tconst label = this.labels.make();\r\n\t\tthis.labels.push(label);\r\n\t\tlabel.addTag(\"flow\");\r\n\t\tlabel.addTag(\"arcdiagram\");\r\n\t\tlabel.addTag(\"node\");\r\n\r\n\t\tnode.children.push(label);\r\n\t\tdataItem.set(\"label\", label);\r\n\r\n\t\tlabel._setDataItem(dataItem);\r\n\t\tcircle._setDataItem(dataItem);\r\n\r\n\t\treturn node;\r\n\t}\r\n\r\n\t/**\r\n\t * @ignore\r\n\t */\r\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\r\n\t\tsuper.disposeDataItem(dataItem);\r\n\t\tlet circle = dataItem.get(\"circle\");\r\n\t\tif (circle) {\r\n\t\t\tthis.circles.removeValue(circle);\r\n\t\t\tcircle.dispose();\r\n\t\t}\r\n\t}\r\n\r\n\tpublic _updateNodeColor(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\r\n\t\tconst circle = dataItem.get(\"circle\");\r\n\t\tif (circle) {\r\n\t\t\tcircle.set(\"fill\", dataItem.get(\"fill\"));\r\n\t\t\tcircle.set(\"fillPattern\", dataItem.get(\"fillPattern\"));\r\n\t\t}\r\n\t}\r\n}\r\n", "import { FlowLink, IFlowLinkPrivate, IFlowLinkSettings } from \"./FlowLink\";\r\nimport type { DataItem } from \"../../core/render/Component\";\r\nimport type { IArcDiagramNodesDataItem } from \"./ArcDiagramNodes\";\r\nimport type { ArcDiagram, IArcDiagramDataItem } from \"./ArcDiagram\";\r\nimport type { IOrientationPoint, IPoint } from \"../../core/util/IPoint\";\r\nimport * as $math from \"../../core/util/Math\";\r\n\r\n\r\nexport interface IArcDiagramLinkSettings extends IFlowLinkSettings {\r\n\r\n\t/**\r\n\t * Source data item.\r\n\t */\r\n\tsource?: DataItem<IArcDiagramNodesDataItem>;\r\n\r\n\t/**\r\n\t * target data item.\r\n\t */\r\n\ttarget?: DataItem<IArcDiagramNodesDataItem>;\r\n}\r\n\r\nexport interface IArcDiagramLinkPrivate extends IFlowLinkPrivate {\r\n\t/**\r\n\t * Link orientation.\r\n\t */\r\n\torientation?: \"horizontal\" | \"vertical\";\r\n}\r\n\r\n/**\r\n * A link element used in [[ArcDiagram]] chart.\r\n */\r\nexport class ArcDiagramLink extends FlowLink {\r\n\r\n\tpublic _p0: IPoint | undefined;\r\n\tpublic _p1: IPoint | undefined;\r\n\tpublic _radius: number = 0;\r\n\r\n\tdeclare public _settings: IArcDiagramLinkSettings;\r\n\tdeclare public _privateSettings: IArcDiagramLinkPrivate;\r\n\r\n\tpublic static className: string = \"ArcDiagramLink\";\r\n\tpublic static classNames: Array<string> = FlowLink.classNames.concat([ArcDiagramLink.className]);\r\n\r\n\tdeclare protected _dataItem: DataItem<IArcDiagramDataItem> | undefined;\r\n\r\n\tdeclare public series: ArcDiagram | undefined;\r\n\r\n\tpublic _beforeChanged() {\r\n\t\tsuper._beforeChanged();\r\n\r\n\t\tif (this.isDirty(\"source\")) {\r\n\t\t\tconst source = this.get(\"source\");\r\n\t\t\tif (source) {\r\n\t\t\t\tconst sourceNode = source.get(\"node\");\r\n\t\t\t\tthis._disposers.push(sourceNode.events.on(\"positionchanged\", () => {\r\n\t\t\t\t\tthis._markDirtyKey(\"stroke\");\r\n\t\t\t\t}))\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (this.isDirty(\"target\")) {\r\n\t\t\tconst target = this.get(\"target\");\r\n\t\t\tif (target) {\r\n\t\t\t\tconst targetNode = target.get(\"node\");\r\n\t\t\t\tthis._disposers.push(targetNode.events.on(\"positionchanged\", () => {\r\n\t\t\t\t\tthis._markDirtyKey(\"stroke\");\r\n\t\t\t\t}))\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (this.isPrivateDirty(\"orientation\")) {\r\n\t\t\tconst series = this.series;\r\n\t\t\tconst dataItem = this.dataItem as DataItem<IArcDiagramDataItem>;\r\n\t\t\tif (dataItem && series) {\r\n\t\t\t\tseries._updateLinkColor(dataItem);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (this.series && this.dataItem) {\r\n\t\t\tthis.series._positionBullets(this.dataItem as any);\r\n\t\t}\r\n\r\n\t\tif (this.get(\"strokeStyle\") == \"gradient\") {\r\n\t\t\tthis.set(\"isMeasured\", true);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis.set(\"isMeasured\", false);\r\n\t\t}\r\n\t}\r\n\r\n\tpublic _changed() {\r\n\t\tsuper._changed();\r\n\r\n\t\tif (this._clear) {\r\n\t\t\tthis._draw();\r\n\t\t}\r\n\t}\r\n\r\n\tpublic _draw() {\r\n\t\tconst target = this.get(\"target\");\r\n\t\tconst source = this.get(\"source\");\r\n\r\n\t\tif (source && target) {\r\n\t\t\tlet sourceNode = source.get(\"node\");\r\n\t\t\tlet targetNode = target.get(\"node\");\r\n\r\n\t\t\tconst x0 = sourceNode.x();\r\n\t\t\tconst y0 = sourceNode.y();\r\n\r\n\t\t\tconst x1 = targetNode.x();\r\n\t\t\tconst y1 = targetNode.y();\r\n\r\n\t\t\tthis._p0 = { x: x0, y: y0 };\r\n\t\t\tthis._p1 = { x: x1, y: y1 };\r\n\r\n\t\t\tlet radius = 0;\r\n\r\n\t\t\tif (this.getPrivate(\"orientation\") == \"vertical\") {\r\n\t\t\t\tradius = (y1 - y0) / 2;\r\n\t\t\t\tlet d = 1;\r\n\t\t\t\tif (y0 > y1) {\r\n\t\t\t\t\td = -1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis._display.arc(x0, y0 + radius, radius * d, -Math.PI / 2, Math.PI / 2);\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tradius = (x1 - x0) / 2;\r\n\t\t\t\tlet d = 1;\r\n\t\t\t\tif (x0 > x1) {\r\n\t\t\t\t\td = -1;\r\n\t\t\t\t}\r\n\t\t\t\tthis._display.arc(x0 + radius, y0, radius * d, -Math.PI, 0);\r\n\t\t\t}\r\n\t\t\tthis._radius = radius;\r\n\t\t}\r\n\t}\r\n\r\n\r\n\tpublic getPoint(location: number): IOrientationPoint {\r\n\t\tif (this._p0 && this._p1) {\r\n\t\t\tconst radius = this._radius;\r\n\r\n\t\t\tif (this.getPrivate(\"orientation\") == \"vertical\") {\r\n\t\t\t\tlet angle = -90 + 180 * location;\r\n\t\t\t\treturn { y: this._p0.y + radius + radius * $math.sin(angle), x: radius * $math.cos(angle), angle: angle + 90 };\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tlet angle = 180 + 180 * location;\r\n\t\t\t\treturn { x: this._p0.x + radius + radius * $math.cos(angle), y: radius * $math.sin(angle), angle: angle + 90 };\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn { x: 0, y: 0, angle: 0 };\r\n\t}\r\n}\r\n", "import type { DataItem } from \"../../core/render/Component\";\r\n\r\nimport { Flow, IFlowSettings, IFlowDataItem, IFlowPrivate, IFlowEvents } from \"./Flow\";\r\nimport { Template } from \"../../core/util/Template\";\r\nimport { ListTemplate } from \"../../core/util/List\";\r\nimport { ArcDiagramNodes, IArcDiagramNodesDataItem } from \"./ArcDiagramNodes\";\r\nimport { ArcDiagramLink } from \"./ArcDiagramLink\";\r\nimport type { Easing } from \"../../core/util/Ease\";\r\n\r\nimport * as $utils from \"../../core/util/Utils\";\r\nimport * as $array from \"../../core/util/Array\";\r\n\r\n\r\n\r\nexport interface IArcDiagramDataItem extends IFlowDataItem {\r\n\r\n\t/**\r\n\t * A link element.\r\n\t */\r\n\tlink: ArcDiagramLink;\r\n\r\n\t/**\r\n\t * Source node data item.\r\n\t */\r\n\tsource: DataItem<IArcDiagramNodesDataItem>;\r\n\r\n\t/**\r\n\t * Target node data item.\r\n\t */\r\n\ttarget: DataItem<IArcDiagramNodesDataItem>;\r\n\r\n}\r\n\r\nexport interface IArcDiagramSettings extends IFlowSettings {\r\n\t/**\r\n\t * Orientation of the series. This setting can not be changed after the chart is initialized.\r\n\t *\r\n\t * @default \"horizontal\"\r\n\t */\r\n\torientation: \"horizontal\" | \"vertical\";\r\n\r\n\t/**\r\n\t * Minimum radius of a nodes circle.\r\n\t * Maximum radius is computed based on available space\r\n\t * @default 5\r\n\t */\r\n\tminRadius?: number;\r\n\r\n\t/**\r\n\t * Defines Which value should be used when calculating circle radius. Use \"none\" if you want all circles to be the same.\r\n\t * @martynas: gal cia reik naudot radiusField, biski no idea.\r\n\t * @default \"sum\"\r\n\t */\r\n\tradiusKey?: \"sum\" | \"sumIncoming\" | \"sumOutgoing\" | \"none\";\r\n\r\n\t/**\r\n\t * Duration for all drill animations in milliseconds.\r\n\t */\r\n\tanimationDuration?: number;\r\n\r\n\t/**\r\n\t * An easing function to use for drill animations.\r\n\t */\r\n\tanimationEasing?: Easing;\r\n}\r\n\r\nexport interface IArcDiagramPrivate extends IFlowPrivate {\r\n}\r\n\r\nexport interface IArcDiagramEvents extends IFlowEvents {\r\n}\r\n\r\n/**\r\n * Regular ArcDiagram series.\r\n *\r\n * @see {@link https://www.amcharts.com/docs/v5/charts/flow-charts/} for more information\r\n * @important\r\n */\r\nexport class ArcDiagram extends Flow {\r\n\r\n\tpublic static className: string = \"ArcDiagram\";\r\n\tpublic static classNames: Array<string> = Flow.classNames.concat([ArcDiagram.className]);\r\n\r\n\t/**\r\n\t * List of link elements.\r\n\t *\r\n\t * @default new ListTemplate<ArcDiagramLink>\r\n\t */\r\n\tpublic readonly links: ListTemplate<ArcDiagramLink> = new ListTemplate(\r\n\t\tTemplate.new({}),\r\n\t\t() => ArcDiagramLink._new(this._root, { themeTags: [\"link\", \"shape\"] }, [this.links.template])\r\n\t);\r\n\r\n\t/**\r\n\t * A series for all ArcDiagram nodes.\r\n\t *\r\n\t * @default ArcDiagramNodes.new()\r\n\t */\r\n\tpublic readonly nodes: ArcDiagramNodes = this.children.push(ArcDiagramNodes.new(this._root, {}));\r\n\r\n\tdeclare public _settings: IArcDiagramSettings;\r\n\tdeclare public _privateSettings: IArcDiagramPrivate;\r\n\tdeclare public _dataItemSettings: IArcDiagramDataItem;\r\n\tdeclare public _events: IArcDiagramEvents;\r\n\r\n\tprotected _afterNew() {\r\n\t\tthis._settings.themeTags = $utils.mergeTags(this._settings.themeTags, [\"arcdiagram\", this._settings.orientation || \"horizontal\"]);\r\n\r\n\t\tsuper._afterNew();\r\n\t\tthis.nodes.children.push(this.bulletsContainer);\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * @ignore\r\n\t */\r\n\tpublic makeLink(dataItem: DataItem<this[\"_dataItemSettings\"]>): ArcDiagramLink {\r\n\t\tconst link = this.nodes.children.moveValue(this.links.make(), 0);\r\n\t\tthis.links.push(link);\r\n\t\tlink._setDataItem(dataItem);\r\n\t\tlink.set(\"source\", dataItem.get(\"source\"));\r\n\t\tlink.set(\"target\", dataItem.get(\"target\"));\r\n\t\tlink.series = this;\r\n\t\treturn link;\r\n\t}\r\n\r\n\tpublic _prepareChildren() {\r\n\t\tsuper._prepareChildren();\r\n\r\n\t\tif (this._valuesDirty || this._sizeDirty || this.isDirty(\"orientation\")) {\r\n\t\t\tlet width = 1;\r\n\t\t\tconst orientation = this.get(\"orientation\");\r\n\r\n\t\t\t$array.each(this.dataItems, (dataItem) => {\r\n\t\t\t\tconst link = dataItem.get(\"link\");\r\n\t\t\t\tlink.setPrivate(\"orientation\", this.get(\"orientation\"));\r\n\t\t\t})\r\n\r\n\t\t\tif (orientation == \"vertical\") {\r\n\t\t\t\twidth = this.innerHeight();\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\twidth = this.innerWidth();\r\n\t\t\t}\r\n\r\n\t\t\tlet sum = 0;\r\n\t\t\tlet low = Infinity;\r\n\t\t\tlet radiusKey = this.get(\"radiusKey\");\r\n\r\n\t\t\tif (radiusKey != \"none\") {\r\n\t\t\t\t$array.each(this.nodes.dataItems, (dataItem) => {\r\n\t\t\t\t\tlet value = dataItem.get(radiusKey + \"Working\" as any);\r\n\t\t\t\t\tsum += value;\r\n\t\t\t\t\tlow = Math.min(low, value);\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t\tconst count = this.nodes.dataItems.length;\r\n\t\t\tconst nodePadding = this.get(\"nodePadding\", 10);\r\n\t\t\tconst minRadius = this.get(\"minRadius\", 5);\r\n\r\n\t\t\twidth = width - count * (nodePadding + minRadius * 2);\r\n\r\n\t\t\tif (width <= 0) {\r\n\t\t\t\twidth = 0;\r\n\t\t\t}\r\n\r\n\t\t\tlet sumNoLow = sum - count * low;\r\n\t\t\tlet c = width / sumNoLow;\r\n\r\n\t\t\tlet prevCoord = 0;\r\n\t\t\tconst animationDuration = this.get(\"animationDuration\", 0);\r\n\t\t\tconst animationEasing = this.get(\"animationEasing\");\r\n\r\n\t\t\t$array.each(this.nodes.dataItems, (dataItem) => {\r\n\t\t\t\tlet value = dataItem.get(radiusKey + \"Working\" as any);\r\n\r\n\t\t\t\tconst node = dataItem.get(\"node\");\r\n\t\t\t\tlet radius = minRadius + c * (value - low) / 2;\r\n\r\n\t\t\t\tif (radiusKey == \"none\") {\r\n\t\t\t\t\tradius = minRadius + width / count / 2;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (orientation == \"vertical\") {\r\n\t\t\t\t\tnode.set(\"x\", 0);\r\n\r\n\t\t\t\t\tconst y = prevCoord + nodePadding + radius;\r\n\t\t\t\t\tif (node.y() == 0) {\r\n\t\t\t\t\t\tnode.set(\"y\", y);\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tnode.animate({ key: \"y\", to: y, duration: animationDuration, easing: animationEasing });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tnode.set(\"y\", 0);\r\n\t\t\t\t\tconst x = prevCoord + nodePadding + radius;\r\n\t\t\t\t\tif (node.x() == 0) {\r\n\t\t\t\t\t\tnode.set(\"x\", x);\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tnode.animate({ key: \"x\", to: x, duration: animationDuration, easing: animationEasing });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tprevCoord = prevCoord + nodePadding + radius * 2;\r\n\t\t\t\tdataItem.get(\"circle\").set(\"radius\", radius);\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n\r\n\tpublic _updateLinkColor(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\r\n\t\tsuper._updateLinkColor(dataItem);\r\n\t\tconst orientation = this.get(\"orientation\");\r\n\t\tconst fillGradient = dataItem.get(\"link\")._fillGradient;\r\n\t\tconst strokeGradient = dataItem.get(\"link\")._strokeGradient;\r\n\r\n\t\tif (orientation == \"vertical\") {\r\n\t\t\tif (fillGradient) {\r\n\t\t\t\tfillGradient.set(\"rotation\", 90);\r\n\t\t\t}\r\n\t\t\tif (strokeGradient) {\r\n\t\t\t\tstrokeGradient.set(\"rotation\", 90);\r\n\t\t\t}\r\n\t\t}\r\n\t\telse {\r\n\t\t\tif (fillGradient) {\r\n\t\t\t\tfillGradient.set(\"rotation\", 0);\r\n\t\t\t}\r\n\t\t\tif (strokeGradient) {\r\n\t\t\t\tstrokeGradient.set(\"rotation\", 0);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n", "import * as m from \"./../../dist/es2015/flow.js\";\nexport const am5flow = m;"], "names": ["FlowDefaultTheme", "Theme", "setupDefaultRules", "super", "ic", "this", "_root", "interfaceColors", "r", "rule", "bind", "setAll", "width", "height", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "hiddenSize", "minSize", "minHiddenValue", "colors", "ColorSet", "new", "legendLabelText", "legendValueText", "setStateOnChildren", "cursorOverStyle", "to<PERSON><PERSON><PERSON>", "states", "create", "draggable", "opacity", "fill", "get", "text", "populateText", "fillStyle", "strokeStyle", "events", "on", "e", "dataItem", "target", "outgoing", "linkDataItem", "link", "hover", "hideTooltip", "incoming", "rectangle", "showTooltip", "unhover", "orientation", "nodeAlign", "linkTension", "nodePadding", "nodeWidth", "cornerRadiusTL", "cornerRadiusBL", "cornerRadiusTR", "cornerRadiusBR", "controlPointDistance", "fillOpacity", "strokeOpacity", "interactive", "tooltipText", "y", "centerY", "x", "centerX", "radius", "padAngle", "startAngle", "sort", "linkHeadRadius", "sourceRadius", "targetRadius", "cornerRadius", "textType", "head<PERSON><PERSON>us", "linkType", "strokeWidth", "minRadius", "radiusKey", "animationEasing", "isMeasured", "rotation", "Flow", "Series", "children", "push", "Container", "_afterNew", "_defaultThemes", "fields", "nodes", "flow", "bulletsContainer", "processDataItem", "unknown", "sourceId", "sourceDataItem", "getDataItemById", "_index", "data", "id", "set", "targetId", "targetDataItem", "addOutgoingLink", "addincomingLink", "makeLink", "sourceIndex", "dataItems", "indexOf", "targetIndex", "_linksByIndex", "_updateLinkColor", "_onDataClear", "_userDataSet", "_prepare<PERSON><PERSON><PERSON><PERSON>", "valueLow", "Infinity", "valueHigh", "valueSum", "_valuesDirty", "_nodesData", "d3SankeyNode", "sumIncoming", "sumIncomingWorking", "value", "workingValue", "sumOutgoing", "sumOutgoingWorking", "updateLegendValue", "_linksData", "valueWorking", "d3SankeyLink", "source", "setRaw", "setPrivateRaw", "sourceFill", "targetFill", "remove", "_applyTemplates", "gradient", "_fillGradient", "LinearGradient", "sourceStop", "color", "targetStop", "undefined", "_strokeGradient", "disposeDataItem", "links", "removeValue", "dispose", "hideDataItem", "duration", "promises", "hiddenState", "stateAnimationDuration", "stateAnimationEasing", "easing", "animate", "key", "to", "Math", "max", "waitForStop", "hide", "Promise", "all", "showDataItem", "show", "_positionBullet", "bullet", "sprite", "point", "getPoint", "_getBulletLocation", "angle", "classNames", "concat", "className", "abs", "cos", "sin", "pi", "PI", "halfPi", "tau", "epsilon", "range", "i", "j", "Array", "from", "length", "_", "k", "directed", "transpose", "sortGroups", "sortSubgroups", "sortChords", "chord", "matrix", "dx", "n", "groupSums", "groupIndex", "chords", "groups", "Float64Array", "a", "b", "x0", "subgroupIndex", "filter", "index", "endAngle", "Object", "values", "arguments", "compare", "slice", "prototype", "defaultSource", "d", "defaultTarget", "defaultRadius", "defaultStartAngle", "defaultEndAngle", "defaultPadAngle", "defaultArrowheadRadius", "ribbon", "context", "buffer", "s", "apply", "t", "ap", "argv", "call", "sr", "sa0", "sa1", "tr", "ta0", "ta1", "path", "moveTo", "arc", "tr2", "ta2", "quadraticCurveTo", "lineTo", "closePath", "ribbonArrow", "ascending", "NaN", "descending", "type", "map", "constructor", "scale", "translate", "applyX", "applyY", "invert", "location", "invertX", "invertY", "rescaleX", "copy", "domain", "rescaleY", "toString", "FlowNode", "FlowNodes", "List", "Template", "Label", "_new", "themeTags", "labels", "template", "reset", "patterns", "name", "next", "node", "makeNode", "<PERSON><PERSON><PERSON>", "dataContext", "root", "once", "disableDataItem", "themeTag", "make", "addTag", "_setDataItem", "series", "isHidden", "enableDataItem", "_updateNodeColor", "_dataItem", "label", "ChordNodes", "RadialLabel", "Slice", "slices", "insertIndex", "_setSoft", "toLocal", "_dAngle", "locationX", "locationY", "innerRadius", "bulletRadius", "FlowLink", "Graphics", "_changed", "isDirty", "_getTooltipPoint", "tooltipY", "position", "Percent", "ChordLink", "_p0", "_p1", "_type", "p", "p0", "p1", "min", "Chord", "_settings", "linksContainer", "_fixRibbon", "_makeMatrix", "group", "outgoingLink", "getPrivate", "_ribbon", "chordChanged", "_sizeDirty", "_d3chord", "_chordLayout", "innerWidth", "innerHeight", "chordStartAngle", "chordGroup", "labelAngle", "setPrivate", "linkRadius", "_getLinkPoints", "_updateLink", "chordLayoutItem", "startAngle0", "angle0", "startAngle1", "angle1", "display", "ChordLinkDirected", "ChordDirected", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChordNonRibbon", "_linkRadius", "_chordLayoutItem", "sourceSlice", "targetSlice", "SankeyNodes", "RoundedRectangle", "rectangles", "y0", "updateSankey", "SankeyLink", "document", "createElementNS", "_beforeChanged", "sourceNode", "_disposers", "<PERSON><PERSON><PERSON><PERSON>", "targetNode", "isPrivateDirty", "_clear", "x1", "y1", "xt0", "yt0", "xt1", "yt1", "xb0", "xb1", "yb0", "yb1", "xm0", "xm1", "ym0", "ym1", "w", "cpd", "kxt0", "kyt0", "kxt1", "kyt1", "kxm0", "kym0", "kxm1", "kym1", "dy", "kxb0", "kyb0", "kxb1", "kyb1", "segment", "_fillGenerator", "middleSegment", "_strokeGenerator", "_svgPath", "setAttribute", "_totalLength", "getTotalLength", "_positionBullets", "getAttribute", "getPointAtLength", "toGlobal", "sum", "valueof", "targetDepth", "depth", "left", "right", "justify", "sourceLinks", "center", "targetLinks", "ascendingSourceBreadth", "ascendingBreadth", "ascending<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultId", "defaultNodes", "graph", "defaultLinks", "find", "nodeById", "Error", "computeLinkBreadths", "py", "linkSort", "align", "iterations", "sankey", "entries", "Map", "computeNodeLinks", "fixedValue", "computeNodeValues", "current", "Set", "size", "add", "computeNodeDepths", "computeNodeHeights", "columns", "kx", "floor", "layer", "column", "computeNodeLayers", "c", "ky", "reorderLinks", "initializeNodeBreadths", "alpha", "pow", "beta", "relaxRightToLeft", "relaxLeftToRight", "computeNodeBreadths", "v", "targetTop", "reorderNodeLinks", "resolveCollisions", "sourceTop", "subject", "resolveCollisionsBottomToTop", "resolveCollisionsTopToBottom", "update", "nodeId", "nodeSort", "extent", "<PERSON><PERSON>", "line", "d3Graph", "_d3Graph", "_d3Sankey", "fillGradient", "strokeGradient", "vertical", "curve", "d3Sankey", "h", "ArcDiagramNodes", "Circle", "C", "circles", "circle", "ArcDiagramLink", "_draw", "_display", "_radius", "ArcDiagram", "moveValue", "low", "count", "prevCoord", "animationDuration", "am5flow"], "sourceRoot": ""}