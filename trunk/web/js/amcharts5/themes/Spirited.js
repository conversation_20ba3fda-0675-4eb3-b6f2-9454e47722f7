"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[6475],{9475:function(e,r,l){l.r(r),l.d(r,{am5themes_Spirited:function(){return f}});var o=l(1112),s=l(3409);class u extends s.Q{setupDefaultRules(){super.setupDefaultRules(),this.rule("ColorSet").setAll({colors:[o.Il.fromHex(6648718),o.Il.fromHex(7761041),o.<PERSON>.fromHex(7886447),o.Il.fromHex(5389144),o.<PERSON><PERSON>fromHex(8469309),o.<PERSON>.fromHex(12344914),o.<PERSON>.fromHex(15633272),o.Il.fromHex(16369797),o.Il.fromHex(15442012),o.Il.fromHex(10178868)],reuse:!0})}}const f=u}},function(e){var r=(9475,e(e.s=9475)),l=window;for(var o in r)l[o]=r[o];r.__esModule&&Object.defineProperty(l,"__esModule",{value:!0})}]);
//# sourceMappingURL=Spirited.js.map