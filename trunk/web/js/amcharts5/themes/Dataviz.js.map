{"version": 3, "file": "themes/Dataviz.js", "mappings": "iLAMO,MAAMA,UAAqBC,EAAA,EACvB,iBAAAC,GACTC,MAAMD,oBAENE,KAAKC,KAAK,YAAYC,OAAO,CAC5BC,OAAQ,CAACC,EAAA,GAAMC,QAAQ,SAAWD,EAAA,GAAMC,QAAQ,SAAWD,EAAA,GAAMC,QAAQ,UAAWD,EAAA,GAAMC,QAAQ,WAClGC,OAAO,EACPC,YAAa,CACZC,UAAW,IACXC,IAAK,IAGR,ECjBM,MAAMC,ECAb,C", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/themes/DatavizTheme.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/themes/Dataviz.js", "webpack://@amcharts/amcharts5/./src/themes/Dataviz.ts"], "sourcesContent": ["import { Color } from \"../core/util/Color\";\nimport { Theme } from \"../core/Theme\";\n\n/**\n * @ignore\n */\nexport class DatavizTheme extends Theme {\n\tprotected setupDefaultRules() {\n\t\tsuper.setupDefaultRules();\n\n\t\tthis.rule(\"ColorSet\").setAll({\n\t\t\tcolors: [Color.fromHex(0x283250), Color.fromHex(0x902c2d), Color.fromHex(0xd5433d), Color.fromHex(0xf05440)],\n\t\t\treuse: false,\n\t\t\tpassOptions: {\n\t\t\t\tlightness: 0.05,\n\t\t\t\thue: 0\n\t\t\t}\n\t\t});\n\t}\n}\n", "import m from \"./../../../dist/es2015/themes/Dataviz.js\";\nexport const am5themes_Dataviz = m;", "import { DatavizTheme } from \"../.internal/themes/DatavizTheme\";\nexport default DatavizTheme;"], "names": ["DatavizTheme", "Theme", "setupDefaultRules", "super", "this", "rule", "setAll", "colors", "Color", "fromHex", "reuse", "passOptions", "lightness", "hue", "am5themes_Dataviz"], "sourceRoot": ""}