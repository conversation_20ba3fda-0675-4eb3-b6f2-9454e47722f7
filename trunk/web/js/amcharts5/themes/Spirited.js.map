{"version": 3, "file": "themes/Spirited.js", "mappings": "kLAMO,MAAMA,UAAsBC,EAAA,EACxB,iBAAAC,GACTC,MAAMD,oBAENE,KAAKC,KAAK,YAAYC,OAAO,CAC5BC,OAAQ,CACPC,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,WAEfC,OAAO,GAET,ECxBM,MAAMC,ECAb,C", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/themes/SpiritedTheme.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/themes/Spirited.js", "webpack://@amcharts/amcharts5/./src/themes/Spirited.ts"], "sourcesContent": ["import { Color } from \"../core/util/Color\";\nimport { Theme } from \"../core/Theme\";\n\n/**\n * @ignore\n */\nexport class SpiritedTheme extends Theme {\n\tprotected setupDefaultRules() {\n\t\tsuper.setupDefaultRules();\n\n\t\tthis.rule(\"ColorSet\").setAll({\n\t\t\tcolors: [\n\t\t\t\tColor.fromHex(0x65738e),\n\t\t\t\tColor.fromHex(0x766c91),\n\t\t\t\tColor.fromHex(0x78566f),\n\t\t\t\tColor.fromHex(0x523b58),\n\t\t\t\tColor.fromHex(0x813b3d),\n\t\t\t\tColor.fromHex(0xbc5e52),\n\t\t\t\tColor.fromHex(0xee8b78),\n\t\t\t\tColor.fromHex(0xf9c885),\n\t\t\t\tColor.fromHex(0xeba05c),\n\t\t\t\tColor.fromHex(0x9b5134)\n\t\t\t],\n\t\t\treuse: true\n\t\t});\n\t}\n}\n", "import m from \"./../../../dist/es2015/themes/Spirited.js\";\nexport const am5themes_Spirited = m;", "import { SpiritedTheme } from \"../.internal/themes/SpiritedTheme\";\nexport default SpiritedTheme;"], "names": ["SpiritedTheme", "Theme", "setupDefaultRules", "super", "this", "rule", "setAll", "colors", "Color", "fromHex", "reuse", "am5themes_Spirited"], "sourceRoot": ""}