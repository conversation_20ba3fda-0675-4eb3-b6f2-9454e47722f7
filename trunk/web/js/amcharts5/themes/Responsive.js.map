{"version": 3, "file": "themes/Responsive.js", "mappings": "uMAuEO,MAAMA,UAAwBC,EAAA,EAkIpC,WAAAC,CAAYC,EAAYC,GACvBC,MAAMF,EAAMC,GAHb,kC,yDAqBA,8C,gDAA4C,KAjB3CE,KAAKC,IAAM,IAAI,KAAc,CAC5BD,KAAKE,MAAMC,eAAeC,UAAU,SAAUC,IACzCL,KAAKM,WACRN,KAAKO,kB,IAGPP,KAAKE,MAAMC,eAAeC,UAAU,UAAWI,IAC1CR,KAAKM,WACRN,KAAKO,kB,KAIT,CAnIA,eAAOE,CAASC,EAAeF,GAC9B,OAAOE,GAAShB,EAAgBiB,GACjC,CAEA,cAAOC,CAAQF,EAAeF,GAC7B,OAAOE,GAAShB,EAAgBmB,EACjC,CAEA,aAAOC,CAAOJ,EAAeF,GAC5B,OAAOE,GAAShB,EAAgBqB,CACjC,CAEA,aAAOC,CAAON,EAAeF,GAC5B,OAAOE,GAAShB,EAAgBuB,CACjC,CAEA,aAAOC,CAAOR,EAAeF,GAC5B,OAAOE,GAAShB,EAAgByB,CACjC,CAEA,cAAOC,CAAQV,EAAeF,GAC7B,OAAOE,GAAShB,EAAgB2B,EACjC,CAEA,eAAOC,CAASZ,EAAeF,GAC9B,OAAOE,GAAShB,EAAgB6B,GACjC,CAGA,gBAAOC,CAAUnB,EAAgBoB,GAChC,OAAOA,GAAU/B,EAAgBiB,GAClC,CAEA,eAAOe,CAASrB,EAAgBoB,GAC/B,OAAOA,GAAU/B,EAAgBmB,EAClC,CAEA,cAAOc,CAAQtB,EAAgBoB,GAC9B,OAAOA,GAAU/B,EAAgBqB,CAClC,CAEA,cAAOa,CAAQvB,EAAgBoB,GAC9B,OAAOA,GAAU/B,EAAgBuB,CAClC,CAEA,cAAOY,CAAQxB,EAAgBoB,GAC9B,OAAOA,GAAU/B,EAAgByB,CAClC,CAEA,eAAOW,CAASzB,EAAgBoB,GAC/B,OAAOA,GAAU/B,EAAgB2B,EAClC,CAEA,gBAAOU,CAAU1B,EAAgBoB,GAChC,OAAOA,GAAU/B,EAAgB6B,GAClC,CAGA,YAAOS,CAAMtB,EAAee,GAC3B,OAAQf,GAAShB,EAAgBiB,KAASc,GAAU/B,EAAgBiB,GACrE,CAEA,WAAOsB,CAAKvB,EAAee,GAC1B,OAAQf,GAAShB,EAAgBmB,IAAQY,GAAU/B,EAAgBmB,EACpE,CAEA,UAAOqB,CAAIxB,EAAee,GACzB,OAAQf,GAAShB,EAAgBqB,GAAOU,GAAU/B,EAAgBqB,CACnE,CAEA,UAAOoB,CAAIzB,EAAee,GACzB,OAAQf,GAAShB,EAAgBuB,GAAOQ,GAAU/B,EAAgBuB,CACnE,CAEA,UAAOmB,CAAI1B,EAAee,GACzB,OAAQf,GAAShB,EAAgByB,GAAOM,GAAU/B,EAAgByB,CACnE,CAEA,WAAOkB,CAAK3B,EAAee,GAC1B,OAAQf,GAAShB,EAAgB2B,IAAQI,GAAU/B,EAAgB2B,EACpE,CAEA,YAAOiB,CAAM5B,EAAee,GAC3B,OAAQf,GAAShB,EAAgB6B,KAASE,GAAU/B,EAAgB6B,GACrE,CAGA,eAAOgB,CAAS7B,EAAee,GAC9B,OAAQf,GAAShB,EAAgBiB,KAASc,GAAU/B,EAAgBiB,GACrE,CAEA,cAAO6B,CAAQ9B,EAAee,GAC7B,OAAQf,GAAShB,EAAgBmB,IAAQY,GAAU/B,EAAgBmB,EACpE,CAEA,aAAO4B,CAAO/B,EAAee,GAC5B,OAAQf,GAAShB,EAAgBqB,GAAOU,GAAU/B,EAAgBqB,CACnE,CAEA,aAAO2B,CAAOhC,EAAee,GAC5B,OAAQf,GAAShB,EAAgBuB,GAAOQ,GAAU/B,EAAgBuB,CACnE,CAEA,aAAO0B,CAAOjC,EAAee,GAC5B,OAAQf,GAAShB,EAAgByB,GAAOM,GAAU/B,EAAgByB,CACnE,CAEA,cAAOyB,CAAQlC,EAAee,GAC7B,OAAQf,GAAShB,EAAgB2B,IAAQI,GAAU/B,EAAgB2B,EACpE,CAEA,eAAOwB,CAASnC,EAAee,GAC9B,OAAQf,GAAShB,EAAgB6B,KAASE,GAAU/B,EAAgB6B,GACrE,CA4BA,eAAOuB,CAAoDjD,GAC1D,OAAO,IAAKG,KAAKH,GAAM,EACxB,CAUO,OAAAkD,CAAQC,GAOd,OANIA,EAAKC,OAASD,EAAKE,WACtBF,EAAKE,SAAiBlD,KAAMgD,KAAKA,EAAKC,KAAMD,EAAKG,OAGlDnD,KAAKoD,gBAAgBC,KAAKL,GAC1BhD,KAAKsD,gBAAgBN,GACdA,CACR,CAOO,UAAAO,CAAWP,GACjB,SAAchD,KAAKoD,gBAAiBJ,EACrC,CAEO,OAAAQ,GACFxD,KAAKC,KACRD,KAAKC,IAAIuD,SAEX,CAEU,OAAAlD,GACT,OAAmE,IAA5DN,KAAKE,MAAMC,eAAesD,IAAI,UAAWC,QAAQ1D,KACzD,CAEU,gBAAAO,GACT,OAAYP,KAAKoD,iBAAkBJ,IAClChD,KAAK2D,kBAAkBX,EAAK,IAE7B,OAAYhD,KAAKoD,iBAAkBJ,IAClChD,KAAKsD,gBAAgBN,EAAK,GAE5B,CAEU,eAAAM,CAAgBN,GACzB,GAAIA,EAAKY,QAAS,OAClB,MAAMC,EAAI7D,KAAKE,MAAMC,eAAe2D,WAAW,SACzCC,EAAI/D,KAAKE,MAAMC,eAAe2D,WAAW,UAC9Bd,EAAKgB,SAASC,KAAKjB,EAAca,EAAWE,KAE5Df,EAAKY,SAAU,EACXZ,EAAKE,UAAYF,EAAKkB,UACzBlB,EAAKE,SAASiB,OAAOnB,EAAKkB,UAEvBlB,EAAKoB,UACRpB,EAAKoB,SAASH,KAAKjB,GAGtB,CAEU,iBAAAW,CAAkBX,GAC3B,IAAKA,EAAKY,QAAS,OACnB,MAAMC,EAAI7D,KAAKE,MAAMC,eAAe2D,WAAW,SACzCC,EAAI/D,KAAKE,MAAMC,eAAe2D,WAAW,UAC9Bd,EAAKgB,SAASC,KAAKjB,EAAca,EAAWE,KAE5Df,EAAKY,SAAU,EACXZ,EAAKE,UACRF,EAAKE,SAASmB,YAEXrB,EAAKsB,UACRtB,EAAKsB,SAASL,KAAKjB,GAGtB,CAKU,iBAAAuB,GACTxE,MAAMwE,oBAEN,MAAMxB,EAAWC,GAA0BhD,KAAK+C,QAAQC,GAQxDD,EAAQ,CACPE,KAAM,QACNe,SAAUtE,EAAgBe,SAC1ByD,SAAU,CACTM,YAAa,EACbC,aAAc,KAIhB1B,EAAQ,CACPE,KAAM,QACNe,SAAUtE,EAAgB8B,UAC1B0C,SAAU,CACTQ,WAAY,EACZC,cAAe,KAIjB5B,EAAQ,CACPE,KAAM,SACNe,SAAUtE,EAAgBuC,KAC1BiC,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,SACNe,SAAUtE,EAAgBuC,KAC1BiC,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,aACNE,KAAM,CAAC,YACPa,SAAUtE,EAAgBkB,QAC1BsD,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,aACNE,KAAM,CAAC,cACPa,SAAUtE,EAAgBgC,SAC1BwC,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,aAAc,SACrBa,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,aAAc,OACrBa,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,SACNE,KAAM,CAAC,UACPa,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTU,aAAa,KAWf7B,EAAQ,CACPE,KAAM,gBACNe,SAAUtE,EAAgBgC,SAC1BwC,SAAU,CACTW,QAAQ,KAIV9B,EAAQ,CACPE,KAAM,gBACNe,SAAUtE,EAAgBkB,QAC1BsD,SAAU,CACTW,QAAQ,KAIV9B,EAAQ,CACPE,KAAM,qBACNe,SAAUtE,EAAgBgC,SAC1BwC,SAAU,CACTY,YAAa,GACbC,YAAa,MAIfhC,EAAQ,CACPE,KAAM,YACNE,KAAM,CAAC,KACPa,SAAUtE,EAAgBkB,QAC1BsD,SAAU,CACTc,QAAS,KACTD,YAAa,MAIfhC,EAAQ,CACPE,KAAM,YACNE,KAAM,CAAC,KACPa,SAAUtE,EAAgB8B,UAC1B0C,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,YACNE,KAAM,CAAC,IAAK,SACZa,SAAUtE,EAAgB4B,SAC1B4C,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,YACNE,KAAM,CAAC,KACPa,SAAUtE,EAAgBe,SAC1ByD,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,YACNE,KAAM,CAAC,IAAK,SACZa,SAAUtE,EAAgBqC,UAC1BmC,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,WACNE,KAAM,CAAC,KACPa,SAAUtE,EAAgBgC,SAC1BwC,SAAU,CACTW,QAAQ,EACRC,YAAa,GACbC,YAAa,MAIfhC,EAAQ,CACPE,KAAM,WACNE,KAAM,CAAC,KACPa,SAAUtE,EAAgBe,SAC1ByD,SAAU,CACTW,QAAQ,EACRC,YAAa,GACbC,YAAa,MAIfhC,EAAQ,CACPE,KAAM,OACNe,SAAUtE,EAAgB6C,SAC1B2B,SAAU,CACTU,aAAa,KAWf7B,EAAQ,CACPE,KAAM,cACNE,KAAM,CAAC,UACPa,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,cACNE,KAAM,CAAC,YACPa,SAAUtE,EAAgB+C,OAC1ByB,SAAU,CACTW,QAAQ,KAIV9B,EAAQ,CACPE,KAAM,WACNe,SAAUtE,EAAgB+C,OAC1ByB,SAAU,CACTW,QAAQ,KAIV9B,EAAQ,CACPE,KAAM,cACNE,KAAM,CAAC,YACPa,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,WACNE,KAAM,CAAC,YACPa,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTW,QAAQ,KAUV9B,EAAQ,CACPE,KAAM,WACNe,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTe,QAAQ,QAAQ,OAIlBlC,EAAQ,CACPE,KAAM,WACNe,SAAUtE,EAAgBsB,OAC1BkD,SAAU,CACTe,QAAQ,QAAQ,OAIlBlC,EAAQ,CACPE,KAAM,cACNE,KAAM,CAAC,OACPa,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,cACNE,KAAM,CAAC,OACPa,SAAUtE,EAAgBsB,OAC1BkD,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,OACNE,KAAM,CAAC,OACPa,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,OACNE,KAAM,CAAC,OACPa,SAAUtE,EAAgBsB,OAC1BkD,SAAU,CACTU,aAAa,KAWf7B,EAAQ,CACPE,KAAM,eACNe,SAAUtE,EAAgBsB,OAC1BkD,SAAU,CACTgB,aAAa,KAIfnC,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,SAAU,YACjBa,SAAUtE,EAAgBwB,OAC1BgD,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,OACNE,KAAM,CAAC,SAAU,YACjBa,SAAUtE,EAAgBwB,OAC1BgD,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,SAAU,cACjBa,SAAUtE,EAAgBiC,QAC1BuC,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,OACNE,KAAM,CAAC,SAAU,cACjBa,SAAUtE,EAAgBiC,QAC1BuC,SAAU,CACTU,aAAa,KAWf7B,EAAQ,CACPE,KAAM,gBACNe,SAAUtE,EAAgBsB,OAC1BkD,SAAU,CACTgB,aAAa,KAIfnC,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,UAAW,YAClBa,SAAUtE,EAAgBwB,OAC1BgD,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,OACNE,KAAM,CAAC,UAAW,YAClBa,SAAUtE,EAAgBwB,OAC1BgD,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,UAAW,cAClBa,SAAUtE,EAAgBiC,QAC1BuC,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,OACNE,KAAM,CAAC,UAAW,cAClBa,SAAUtE,EAAgBiC,QAC1BuC,SAAU,CACTU,aAAa,KAWf7B,EAAQ,CACPE,KAAM,yBACNe,SAAUtE,EAAgBsB,OAC1BkD,SAAU,CACTgB,aAAa,KAIfnC,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,YAAa,YACpBa,SAAUtE,EAAgBwB,OAC1BgD,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,OACNE,KAAM,CAAC,YAAa,YACpBa,SAAUtE,EAAgBwB,OAC1BgD,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,YAAa,cACpBa,SAAUtE,EAAgBiC,QAC1BuC,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,OACNE,KAAM,CAAC,YAAa,cACpBa,SAAUtE,EAAgBiC,QAC1BuC,SAAU,CACTU,aAAa,KAmBf7B,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,OAAQ,cACfa,SAAUtE,EAAgBoB,OAC1BoD,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,OAAQ,YACfa,SAAUtE,EAAgBiC,QAC1BuC,SAAU,CACTU,aAAa,KAIf7B,EAAQ,CACPE,KAAM,QACNe,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTe,QAAQ,QAAQ,OAWlBlC,EAAQ,CACPE,KAAM,QACNE,KAAM,CAAC,YAAa,QACpBa,SAAUtE,EAAgB8C,QAC1B0B,SAAU,CACTU,aAAa,IAIhB,EAjvBO,+B,gDAAM,MACN,8B,gDAAK,MACL,6B,gDAAI,MACJ,6B,gDAAI,MACJ,6B,gDAAI,MACJ,8B,gDAAK,MACL,+B,gDAAM,MC/EP,MAAMO,ECAb,C", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/themes/ResponsiveTheme.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/themes/Responsive.js", "webpack://@amcharts/amcharts5/./src/themes/Responsive.ts"], "sourcesContent": ["import { Theme } from \"../core/Theme\";\nimport type { Root } from \"../core/Root\";\nimport { MultiDisposer } from \"../core/util/Disposer\";\nimport { p100, percent } from \"../core/util/Percent\";\nimport type { Template } from \"../core/util/Template\";\nimport * as $array from \"../core/util/Array\";\n\n/**\n * An interface describing resonsive rule.\n *\n * @see {@link https://www.amcharts.com/docs/v5/concepts/responsive/} for more info\n * @important\n */\nexport interface IResponsiveRule {\n\n\t/**\n\t * A class name of the elements to apply rule to.\n\t */\n\tname?: string;\n\n\t/**\n\t * A class group of the elements to apply rule to.\n\t */\n\ttags?: string | string[];\n\n\t/**\n\t * Settings to apply when activating the responsive rule.\n\t */\n\tsettings?: any;\n\n\t/**\n\t * A callback function which should check and return `true` if rule is\n\t * applicable for current situation.\n\t */\n\trelevant: (width: number, height: number) => boolean;\n\n\t/**\n\t * A custom callback function which is called when applying the rule.\n\t */\n\tapplying?: () => void;\n\n\t/**\n\t * A custom callback function which is called when removing the rule.\n\t */\n\tremoving?: () => void;\n\n\t/**\n\t * Indicates if rule is currently applied.\n\t * @readonly\n\t */\n\tapplied?: boolean;\n\n\t/**\n\t * Reference to [[Template]] object associated with the rule.\n\t * @readonly\n\t */\n\ttemplate?: Template<any>;\n\n\t/**\n\t * @ignore\n\t */\n\t_dp?: MultiDisposer;\n\n}\n\n/**\n * A configurable theme that dynamically adapts chart settings for best fit\n * in available space.\n *\n * @see {@link https://www.amcharts.com/docs/v5/concepts/responsive/} for more info\n */\nexport class ResponsiveTheme extends Theme {\n\n\t// Named pixel breakpoints\n\tstatic XXS = 100;\n\tstatic XS = 200;\n\tstatic S = 300;\n\tstatic M = 400;\n\tstatic L = 600;\n\tstatic XL = 800;\n\tstatic XXL = 1000;\n\n\t// Breakpoint functions (for use in `relevant` clause of the responsive rules)\n\n\tstatic widthXXS(width: number, _height: number): boolean {\n\t\treturn width <= ResponsiveTheme.XXS;\n\t}\n\n\tstatic widthXS(width: number, _height: number): boolean {\n\t\treturn width <= ResponsiveTheme.XS;\n\t}\n\n\tstatic widthS(width: number, _height: number): boolean {\n\t\treturn width <= ResponsiveTheme.S;\n\t}\n\n\tstatic widthM(width: number, _height: number): boolean {\n\t\treturn width <= ResponsiveTheme.M;\n\t}\n\n\tstatic widthL(width: number, _height: number): boolean {\n\t\treturn width <= ResponsiveTheme.L;\n\t}\n\n\tstatic widthXL(width: number, _height: number): boolean {\n\t\treturn width <= ResponsiveTheme.XL;\n\t}\n\n\tstatic widthXXL(width: number, _height: number): boolean {\n\t\treturn width <= ResponsiveTheme.XXL;\n\t}\n\n\n\tstatic heightXXS(_width: number, height: number): boolean {\n\t\treturn height <= ResponsiveTheme.XXS;\n\t}\n\n\tstatic heightXS(_width: number, height: number): boolean {\n\t\treturn height <= ResponsiveTheme.XS;\n\t}\n\n\tstatic heightS(_width: number, height: number): boolean {\n\t\treturn height <= ResponsiveTheme.S;\n\t}\n\n\tstatic heightM(_width: number, height: number): boolean {\n\t\treturn height <= ResponsiveTheme.M;\n\t}\n\n\tstatic heightL(_width: number, height: number): boolean {\n\t\treturn height <= ResponsiveTheme.L;\n\t}\n\n\tstatic heightXL(_width: number, height: number): boolean {\n\t\treturn height <= ResponsiveTheme.XL;\n\t}\n\n\tstatic heightXXL(_width: number, height: number): boolean {\n\t\treturn height <= ResponsiveTheme.XXL;\n\t}\n\n\n\tstatic isXXS(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.XXS) && (height <= ResponsiveTheme.XXS);\n\t}\n\n\tstatic isXS(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.XS) && (height <= ResponsiveTheme.XS);\n\t}\n\n\tstatic isS(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.S) && (height <= ResponsiveTheme.S);\n\t}\n\n\tstatic isM(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.M) && (height <= ResponsiveTheme.M);\n\t}\n\n\tstatic isL(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.L) && (height <= ResponsiveTheme.L);\n\t}\n\n\tstatic isXL(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.XL) && (height <= ResponsiveTheme.XL);\n\t}\n\n\tstatic isXXL(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.XXL) && (height <= ResponsiveTheme.XXL);\n\t}\n\n\n\tstatic maybeXXS(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.XXS) || (height <= ResponsiveTheme.XXS);\n\t}\n\n\tstatic maybeXS(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.XS) || (height <= ResponsiveTheme.XS);\n\t}\n\n\tstatic maybeS(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.S) || (height <= ResponsiveTheme.S);\n\t}\n\n\tstatic maybeM(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.M) || (height <= ResponsiveTheme.M);\n\t}\n\n\tstatic maybeL(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.L) || (height <= ResponsiveTheme.L);\n\t}\n\n\tstatic maybeXL(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.XL) || (height <= ResponsiveTheme.XL);\n\t}\n\n\tstatic maybeXXL(width: number, height: number): boolean {\n\t\treturn (width <= ResponsiveTheme.XXL) || (height <= ResponsiveTheme.XXL);\n\t}\n\n\tprivate _dp?: MultiDisposer;\n\n\tconstructor(root: Root, isReal: boolean) {\n\t\tsuper(root, isReal);\n\t\tthis._dp = new MultiDisposer([\n\t\t\tthis._root._rootContainer.onPrivate(\"width\", (_width) => {\n\t\t\t\tif (this._isUsed()) {\n\t\t\t\t\tthis._maybeApplyRules();\n\t\t\t\t}\n\t\t\t}),\n\t\t\tthis._root._rootContainer.onPrivate(\"height\", (_height) => {\n\t\t\t\tif (this._isUsed()) {\n\t\t\t\t\tthis._maybeApplyRules();\n\t\t\t\t}\n\t\t\t})\n\t\t]);\n\t}\n\n\t/**\n\t * Currently added rules.\n\t */\n\tpublic responsiveRules: IResponsiveRule[] = [];\n\n\t/**\n\t * Instantiates the theme without adding default respomsive rules.\n\t */\n\tstatic newEmpty<T extends typeof ResponsiveTheme>(this: T, root: Root): InstanceType<T> {\n\t\treturn (new this(root, true)) as InstanceType<T>;\n\t}\n\n\n\t/**\n\t * Adds a responsive rule as well as retuns it.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/responsive/} for more info\n\t * @param   rule  Responsive rule\n\t * @return        Responsive rule\n\t */\n\tpublic addRule(rule: IResponsiveRule): IResponsiveRule {\n\t\tif (rule.name && !rule.template) {\n\t\t\trule.template = (<any>this).rule(rule.name, rule.tags);\n\t\t}\n\n\t\tthis.responsiveRules.push(rule);\n\t\tthis._maybeApplyRule(rule);\n\t\treturn rule;\n\t}\n\n\t/**\n\t * Removes the responsive rule.\n\t *\n\t * @param  rule  Responsive rule\n\t */\n\tpublic removeRule(rule: IResponsiveRule): void {\n\t\t$array.remove(this.responsiveRules, rule);\n\t}\n\n\tpublic dispose(): void {\n\t\tif (this._dp) {\n\t\t\tthis._dp.dispose();\n\t\t}\n\t}\n\n\tprotected _isUsed(): boolean {\n\t\treturn this._root._rootContainer.get(\"themes\")!.indexOf(this) !== -1;\n\t}\n\n\tprotected _maybeApplyRules(): void {\n\t\t$array.each(this.responsiveRules, (rule) => {\n\t\t\tthis._maybeUnapplyRule(rule);\n\t\t});\n\t\t$array.each(this.responsiveRules, (rule) => {\n\t\t\tthis._maybeApplyRule(rule);\n\t\t});\n\t}\n\n\tprotected _maybeApplyRule(rule: IResponsiveRule): void {\n\t\tif (rule.applied) return;\n\t\tconst w = this._root._rootContainer.getPrivate(\"width\");\n\t\tconst h = this._root._rootContainer.getPrivate(\"height\");\n\t\tconst relevant = rule.relevant.call(rule, <number>w, <number>h);\n\t\tif (relevant) {\n\t\t\trule.applied = true;\n\t\t\tif (rule.template && rule.settings) {\n\t\t\t\trule.template.setAll(rule.settings);\n\t\t\t}\n\t\t\tif (rule.applying) {\n\t\t\t\trule.applying.call(rule);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _maybeUnapplyRule(rule: IResponsiveRule): void {\n\t\tif (!rule.applied) return;\n\t\tconst w = this._root._rootContainer.getPrivate(\"width\");\n\t\tconst h = this._root._rootContainer.getPrivate(\"height\");\n\t\tconst relevant = rule.relevant.call(rule, <number>w, <number>h);\n\t\tif (!relevant) {\n\t\t\trule.applied = false;\n\t\t\tif (rule.template) {\n\t\t\t\trule.template.removeAll();\n\t\t\t}\n\t\t\tif (rule.removing) {\n\t\t\t\trule.removing.call(rule);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Adds default rules for various chart types and most standard scenarios.\n\t */\n\tprotected setupDefaultRules(): void {\n\t\tsuper.setupDefaultRules();\n\n\t\tconst addRule = (rule: IResponsiveRule) => this.addRule(rule);\n\n\t\t/**\n\t\t * ========================================================================\n\t\t * Universal\n\t\t * ========================================================================\n\t\t */\n\n\t\taddRule({\n\t\t\tname: \"Chart\",\n\t\t\trelevant: ResponsiveTheme.widthXXS,\n\t\t\tsettings: {\n\t\t\t\tpaddingLeft: 0,\n\t\t\t\tpaddingRight: 0\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Chart\",\n\t\t\trelevant: ResponsiveTheme.heightXXS,\n\t\t\tsettings: {\n\t\t\t\tpaddingTop: 0,\n\t\t\t\tpaddingBottom: 0\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Bullet\",\n\t\t\trelevant: ResponsiveTheme.isXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Legend\",\n\t\t\trelevant: ResponsiveTheme.isXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"HeatLegend\",\n\t\t\ttags: [\"vertical\"],\n\t\t\trelevant: ResponsiveTheme.widthXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"HeatLegend\",\n\t\t\ttags: [\"horizontal\"],\n\t\t\trelevant: ResponsiveTheme.heightXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"heatlegend\", \"start\"],\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"heatlegend\", \"end\"],\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Button\",\n\t\t\ttags: [\"resize\"],\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\n\t\t/**\n\t\t * ========================================================================\n\t\t * XY\n\t\t * ========================================================================\n\t\t */\n\n\t\taddRule({\n\t\t\tname: \"AxisRendererX\",\n\t\t\trelevant: ResponsiveTheme.heightXS,\n\t\t\tsettings: {\n\t\t\t\tinside: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisRendererY\",\n\t\t\trelevant: ResponsiveTheme.widthXS,\n\t\t\tsettings: {\n\t\t\t\tinside: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisRendererXLabel\",\n\t\t\trelevant: ResponsiveTheme.heightXS,\n\t\t\tsettings: {\n\t\t\t\tminPosition: 0.1,\n\t\t\t\tmaxPosition: 0.9\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisLabel\",\n\t\t\ttags: [\"y\"],\n\t\t\trelevant: ResponsiveTheme.widthXS,\n\t\t\tsettings: {\n\t\t\t\tcenterY: p100,\n\t\t\t\tmaxPosition: 0.9\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisLabel\",\n\t\t\ttags: [\"x\"],\n\t\t\trelevant: ResponsiveTheme.heightXXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisLabel\",\n\t\t\ttags: [\"x\", \"minor\"],\n\t\t\trelevant: ResponsiveTheme.widthXXL,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisLabel\",\n\t\t\ttags: [\"y\"],\n\t\t\trelevant: ResponsiveTheme.widthXXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisLabel\",\n\t\t\ttags: [\"y\", \"minor\"],\n\t\t\trelevant: ResponsiveTheme.heightXXL,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisTick\",\n\t\t\ttags: [\"x\"],\n\t\t\trelevant: ResponsiveTheme.heightXS,\n\t\t\tsettings: {\n\t\t\t\tinside: true,\n\t\t\t\tminPosition: 0.1,\n\t\t\t\tmaxPosition: 0.9\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisTick\",\n\t\t\ttags: [\"y\"],\n\t\t\trelevant: ResponsiveTheme.widthXXS,\n\t\t\tsettings: {\n\t\t\t\tinside: true,\n\t\t\t\tminPosition: 0.1,\n\t\t\t\tmaxPosition: 0.9\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Grid\",\n\t\t\trelevant: ResponsiveTheme.maybeXXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\n\t\t/**\n\t\t * ========================================================================\n\t\t * Radar\n\t\t * ========================================================================\n\t\t */\n\n\t\taddRule({\n\t\t\tname: \"RadialLabel\",\n\t\t\ttags: [\"radial\"],\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"RadialLabel\",\n\t\t\ttags: [\"circular\"],\n\t\t\trelevant: ResponsiveTheme.maybeS,\n\t\t\tsettings: {\n\t\t\t\tinside: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisTick\",\n\t\t\trelevant: ResponsiveTheme.maybeS,\n\t\t\tsettings: {\n\t\t\t\tinside: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"RadialLabel\",\n\t\t\ttags: [\"circular\"],\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"AxisTick\",\n\t\t\ttags: [\"circular\"],\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tinside: true\n\t\t\t}\n\t\t});\n\n\n\t\t/**\n\t\t * ========================================================================\n\t\t * Pie\n\t\t * ========================================================================\n\t\t */\n\t\taddRule({\n\t\t\tname: \"PieChart\",\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tradius: percent(99)\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"PieChart\",\n\t\t\trelevant: ResponsiveTheme.widthM,\n\t\t\tsettings: {\n\t\t\t\tradius: percent(99)\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"RadialLabel\",\n\t\t\ttags: [\"pie\"],\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"RadialLabel\",\n\t\t\ttags: [\"pie\"],\n\t\t\trelevant: ResponsiveTheme.widthM,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Tick\",\n\t\t\ttags: [\"pie\"],\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Tick\",\n\t\t\ttags: [\"pie\"],\n\t\t\trelevant: ResponsiveTheme.widthM,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\n\t\t/**\n\t\t * ========================================================================\n\t\t * Funnel\n\t\t * ========================================================================\n\t\t */\n\n\t\taddRule({\n\t\t\tname: \"FunnelSeries\",\n\t\t\trelevant: ResponsiveTheme.widthM,\n\t\t\tsettings: {\n\t\t\t\talignLabels: false\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"funnel\", \"vertical\"],\n\t\t\trelevant: ResponsiveTheme.widthL,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Tick\",\n\t\t\ttags: [\"funnel\", \"vertical\"],\n\t\t\trelevant: ResponsiveTheme.widthL,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"funnel\", \"horizontal\"],\n\t\t\trelevant: ResponsiveTheme.heightS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Tick\",\n\t\t\ttags: [\"funnel\", \"horizontal\"],\n\t\t\trelevant: ResponsiveTheme.heightS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\n\t\t/**\n\t\t * ========================================================================\n\t\t * Pyramid\n\t\t * ========================================================================\n\t\t */\n\n\t\taddRule({\n\t\t\tname: \"PyramidSeries\",\n\t\t\trelevant: ResponsiveTheme.widthM,\n\t\t\tsettings: {\n\t\t\t\talignLabels: false\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"pyramid\", \"vertical\"],\n\t\t\trelevant: ResponsiveTheme.widthL,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Tick\",\n\t\t\ttags: [\"pyramid\", \"vertical\"],\n\t\t\trelevant: ResponsiveTheme.widthL,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"pyramid\", \"horizontal\"],\n\t\t\trelevant: ResponsiveTheme.heightS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Tick\",\n\t\t\ttags: [\"pyramid\", \"horizontal\"],\n\t\t\trelevant: ResponsiveTheme.heightS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\n\t\t/**\n\t\t * ========================================================================\n\t\t * Pictorial\n\t\t * ========================================================================\n\t\t */\n\n\t\taddRule({\n\t\t\tname: \"PictorialStackedSeries\",\n\t\t\trelevant: ResponsiveTheme.widthM,\n\t\t\tsettings: {\n\t\t\t\talignLabels: false\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"pictorial\", \"vertical\"],\n\t\t\trelevant: ResponsiveTheme.widthL,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Tick\",\n\t\t\ttags: [\"pictorial\", \"vertical\"],\n\t\t\trelevant: ResponsiveTheme.widthL,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"pictorial\", \"horizontal\"],\n\t\t\trelevant: ResponsiveTheme.heightS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Tick\",\n\t\t\ttags: [\"pictorial\", \"horizontal\"],\n\t\t\trelevant: ResponsiveTheme.heightS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\n\t\t/**\n\t\t * ========================================================================\n\t\t * Map\n\t\t * ========================================================================\n\t\t */\n\n\t\t// Nothing to do here\n\n\t\t/**\n\t\t * ========================================================================\n\t\t * Flow (Sankey+Chord)\n\t\t * ========================================================================\n\t\t */\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"flow\", \"horizontal\"],\n\t\t\trelevant: ResponsiveTheme.widthS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"flow\", \"vertical\"],\n\t\t\trelevant: ResponsiveTheme.heightS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t\taddRule({\n\t\t\tname: \"Chord\",\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tradius: percent(99)\n\t\t\t}\n\t\t});\n\n\n\t\t/**\n\t\t * ========================================================================\n\t\t * Hierarchy (Treemap, Partition, Sunburst, Pack, ForceDirected)\n\t\t * ========================================================================\n\t\t */\n\n\t\taddRule({\n\t\t\tname: \"Label\",\n\t\t\ttags: [\"hierarchy\", \"node\"],\n\t\t\trelevant: ResponsiveTheme.maybeXS,\n\t\t\tsettings: {\n\t\t\t\tforceHidden: true\n\t\t\t}\n\t\t});\n\n\t}\n}\n", "import m from \"./../../../dist/es2015/themes/Responsive.js\";\nexport const am5themes_Responsive = m;", "import { ResponsiveTheme } from \"../.internal/themes/ResponsiveTheme\";\nexport default ResponsiveTheme;"], "names": ["ResponsiveTheme", "Theme", "constructor", "root", "isReal", "super", "this", "_dp", "_root", "_rootContainer", "onPrivate", "_width", "_isUsed", "_maybeApplyRules", "_height", "widthXXS", "width", "XXS", "widthXS", "XS", "widthS", "S", "widthM", "M", "widthL", "L", "widthXL", "XL", "widthXXL", "XXL", "heightXXS", "height", "heightXS", "heightS", "heightM", "heightL", "heightXL", "heightXXL", "isXXS", "isXS", "isS", "isM", "isL", "isXL", "isXXL", "maybeXXS", "maybeXS", "maybeS", "maybeM", "maybeL", "maybeXL", "maybeXXL", "newEmpty", "addRule", "rule", "name", "template", "tags", "responsiveRules", "push", "_maybeApplyRule", "removeRule", "dispose", "get", "indexOf", "_maybeUnapplyRule", "applied", "w", "getPrivate", "h", "relevant", "call", "settings", "setAll", "applying", "removeAll", "removing", "setupDefaultRules", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "forceHidden", "inside", "minPosition", "maxPosition", "centerY", "radius", "align<PERSON><PERSON><PERSON>", "am5themes_Responsive"], "sourceRoot": ""}