# 3rd-party libraries used in amCharts 5

## Used in Core package

|Package|Copyright (c)|License|
|-------|-------------|-------|
|d3|<PERSON>|[License](https://github.com/d3/d3/blob/master/LICENSE)|
|d3-chord|<PERSON>|[License](https://github.com/d3/d3-fchord/blob/master/LICENSE)|
|d3-geo|<PERSON>|[License](https://github.com/d3/d3-geo/blob/master/LICENSE)|
|d3-geo-projection|<PERSON>|[License](https://github.com/d3/d3-geo-projection/blob/master/LICENSE)|
|d3-force|<PERSON>|[License](https://github.com/d3/d3-force/blob/master/LICENSE)|
|d3-sankey|<PERSON>|[License](https://github.com/d3/d3-sankey/blob/master/LICENSE)|
|d3-selection|<PERSON>|[License](https://github.com/d3/d3-selection/blob/main/LICENSE)|
|d3-transition|<PERSON>|[License](https://github.com/d3/d3-transition/blob/main/LICENSE)|
|d3-voronoi-treemap|LEBEAU Franck|[License](https://github.com/Kcnarf/d3-voronoi-treemap/blob/master/LICENSE)|
|flatpickr|Gregory Petrosyan|[License](https://github.com/flatpickr/flatpickr/blob/master/LICENSE.md)|
|polylabel|Mapbox|[License](https://github.com/mapbox/polylabel/blob/master/LICENSE)|
|seedrandom|David Bau|[License](https://github.com/davidbau/seedrandom?tab=readme-ov-file#license-mit)|
|svg-arc-to-cubic-bezier|Colin Meinke|[License](https://github.com/colinmeinke/svg-arc-to-cubic-bezier/blob/master/LICENSE.md)|
|tslib|Microsoft|[License](https://github.com/microsoft/tslib/blob/master/LICENSE.txt)|


## Used in optional plugins

|Package|Copyright (c)|License|
|-------|-------------|-------|
|pdfmake|bpampuch|[License](https://github.com/bpampuch/pdfmake/blob/master/LICENSE)|
|SheetJS js-xlsx|SheetJS LLC|[License](https://github.com/SheetJS/js-xlsx/blob/master/LICENSE)|
|marker.js 2|Alan Mendelevich|[License](https://github.com/ailon/markerjs2/blob/master/LICENSE)|
