{"version": 3, "file": "plugins/exporting.js", "mappings": "qSA+BA,IAAIA,EAkmBG,MAAMC,UAAkBC,EAAA,GAWpB,SAAAC,GACTC,MAAMD,YACNE,KAAKC,eAAe,aAAc,SAClCD,KAAKC,eAAe,UAAW,SAC/BD,KAAKC,eAAe,gBAAiB,IACrCD,KAAKC,eAAe,aAAc,IAClCD,KAAKC,eAAe,iBAAkB,IACtCD,KAAKC,eAAe,cAAe,IACnCD,KAAKC,eAAe,aAAc,CAAEC,QAAS,EAAGC,oBAAoB,IACpEH,KAAKC,eAAe,aAAc,CAAEC,QAAS,GAAKC,oBAAoB,IACtEH,KAAKC,eAAe,eAAgB,CAAEC,QAAS,EAAGC,oBAAoB,EAAOC,MAAO,IAAKC,YAAa,SAAUC,YAAa,QAC7HN,KAAKC,eAAe,cAAe,CAAEM,OAAQ,EAAGC,cAAc,IAC9DR,KAAKC,eAAe,aAAc,CAAEQ,UAAW,IAAKC,gBAAgB,EAAMC,QAAS,GAAIC,QAAQ,IAC/FZ,KAAKC,eAAe,cAAe,CAAEU,QAAS,IAAKD,gBAAgB,IACnEV,KAAKC,eAAe,cAAe,CAAEU,QAAS,GAAID,gBAAgB,IAClEV,KAAKC,eAAe,aAAc,CAAEY,SAAU,GAAIP,YAAa,MAAOQ,MAAO,OAAQC,QAAQ,IAC7Ff,KAAKC,eAAe,iBAAkB,CAAEU,QAAS,GAAID,gBAAgB,IAErEV,KAAKgB,MAAMC,YAAYjB,KACxB,CAEO,cAAAkB,GAGN,GAFAnB,MAAMmB,iBAEFlB,KAAKmB,QAAQ,QAAS,CACzB,MAAMC,EAAOpB,KAAKqB,IAAI,QAClBD,IACHA,EAAKE,IAAI,YAAatB,MACtBA,KAAKuB,WAAWC,KAAKJ,G,CAGxB,CAEU,iBAAAK,CAAkBC,EAA0BC,GACrD,MAAMC,EAAkB,OAAmB5B,KAAMqB,IAAIK,EAAS,UAAW,CAAC,IAM1E,OALIC,GACH,OAAaA,GAAS,CAACE,EAAKC,KAC3BF,EAAWC,GAAOC,CAAK,IAGlBF,CACR,CAQa,QAAAG,CAASL,EAA0BM,G,+CAC/C,MAAMC,EAAgB,WAAVP,EAAsB,MAAQA,EACpCQ,EAAWlC,KAAKqB,IAAI,aAAc,SAAW,IAAMY,EACnDN,EAAU3B,KAAKyB,kBAAkBC,EAAQM,GAC/ChC,KAAKmC,OAAOC,SAAS,kBAAmB,CACvCC,KAAM,kBACNX,OAAQA,EACRC,QAASA,EACTO,SAAUA,EACVI,OAAQtC,OAET,MAAMuC,QAAYvC,KAAKwC,OAAOd,EAAQC,GACtC3B,KAAKyC,WAAWF,EAAKL,EAAWP,GAAkCA,EAASf,OAC5E,G,CAOa,KAAA8B,CAAMV,G,+CAClB,MAAML,EAAkC3B,KAAKyB,kBAAkB,QAASO,GACxEhC,KAAKmC,OAAOC,SAAS,eAAgB,CACpCC,KAAM,eACNX,OAAQ,QACRC,QAASA,EACTW,OAAQtC,OAET,MAAMuC,QAAYvC,KAAKwC,OAAOb,EAAQrB,aAAe,MAAOqB,GAC5D3B,KAAK2C,cAAcJ,EAAKZ,EAAS3B,KAAKqB,IAAI,SAC3C,G,CASa,OAAOK,EAA0BM,G,+CAC7C,MAAML,EAAU3B,KAAKyB,kBAAkBC,EAAQM,GAC/ChC,KAAKmC,OAAOC,SAAS,gBAAiB,CACrCC,KAAM,gBACNX,OAAQA,EACRC,QAASA,EACTW,OAAQtC,OAGT,IAAI4C,EAAoC,GACxC,OAAQlB,GACP,IAAK,MACL,IAAK,MACJ1B,KAAKgB,MAAM6B,gBACXD,EAAU5C,KAAK8C,YAAYpB,EAAQC,GACnC,MACD,IAAK,OACJiB,EAAU5C,KAAK+C,WAAWpB,GAC1B,MACD,IAAK,MACJiB,EAAU5C,KAAKgD,UAAUrB,GACzB,MACD,IAAK,OACJiB,EAAU5C,KAAKiD,WAAWtB,GAC1B,MACD,IAAK,OACJiB,EAAU5C,KAAKkD,WAAWvB,GAC1B,MACD,IAAK,MACJ3B,KAAKgB,MAAM6B,gBACXD,EAAU5C,KAAKmD,UAAUxB,GACzB,MACD,IAAK,UACJiB,EAAU5C,KAAKoD,cAAczB,GAS/B,OANA3B,KAAKmC,OAAOC,SAAS,iBAAkB,CACtCC,KAAM,iBACNX,OAAQA,EACRC,QAASA,EACTW,OAAQtC,OAEF4C,CACR,G,CAea,WAAAE,CAAYpB,EAA+BM,G,+CACvD,MAAML,EAAe3B,KAAKyB,kBAAkBC,EAAQM,GAC9CqB,QAAerD,KAAKsD,UAAU3B,GAC9B4B,EAAOF,EAAOG,UAAUxD,KAAKyD,eAAe/B,GAASC,EAAQzB,SAAW,GAE9E,OADAF,KAAK0D,cAAcL,GACZE,CACR,G,CAQa,YAAAI,CAAa3B,G,+CACzB,MAAML,EAAe3B,KAAKyB,kBAAkB,SAAUO,GAChDqB,QAAerD,KAAKsD,UAAU3B,GAC9B4B,EAAOF,EAAOG,UAAUxD,KAAKyD,eAAe,UAAW9B,EAAQzB,SAAW,GAEhF,OADAF,KAAK0D,cAAcL,GACZE,CACR,G,CAQa,SAAAD,CAAU3B,G,+CACtB,MAAMiC,EAAa5D,KAAKgB,MAAM6C,UAAUP,UAAUtD,KAAKgB,MAAM8C,eAAeC,SAAUpC,GAChFqC,EAAchE,KAAKqB,IAAI,cAAe,IAG5C,IAAI4C,EAAa,EACbC,EAAY,EACZC,EAAcP,EAAWQ,MACzBC,EAAeT,EAAWU,OAC1BC,EAAa,EACbC,EAAc,EAElB,MAAMC,EAAgB,GAEtB,OAAYT,GAAcU,IAGzB,IAAIC,EAGHA,EADGD,aAAqBE,EAAA,EAChB,CACPC,OAAQH,EACRI,SAAU,UAIoBJ,EAIhCC,EAAMG,SAAWH,EAAMG,UAAY,SACnCH,EAAMI,UAAYJ,EAAMI,WAAa,EACrCJ,EAAMK,YAAcL,EAAMK,aAAe,EACzCL,EAAMM,aAAeN,EAAMM,cAAgB,EAC3CN,EAAMO,WAAaP,EAAMO,YAAc,EAEvC,MAAMC,EAAcR,EAAME,OAAOhB,UAAUP,UAAUqB,EAAME,OAAOf,eAAeC,SAAUpC,GAErFyD,EAAaD,EAAYf,MAAQO,EAAMO,WAAaP,EAAMK,YAC1DK,EAAcF,EAAYb,OAASK,EAAMI,UAAYJ,EAAMM,aAE3C,OAAlBN,EAAMG,UACTX,EAAcQ,EAAMW,KAAOjB,EAAekB,KAAKC,IAAIrB,EAAaiB,GAChElB,GAAamB,GAEe,SAAlBV,EAAMG,UAChBT,EAAeM,EAAMW,KAAOjB,EAAekB,KAAKC,IAAInB,EAAcgB,GAClEd,GAAca,GAEc,QAAlBT,EAAMG,UAChBT,EAAeM,EAAMW,KAAOjB,EAAekB,KAAKC,IAAInB,EAAcgB,GAClEpB,GAAcmB,GAEe,WAAnBT,EAAMG,WAChBX,EAAcQ,EAAMW,KAAOjB,EAAekB,KAAKC,IAAIrB,EAAaiB,GAChEZ,GAAea,GAGhBZ,EAAOjD,KAAK,CACX6B,OAAQ8B,EACRL,SAAUH,EAAMG,SAChBW,KAAMd,EAAMO,WACZQ,IAAKf,EAAMI,UACXX,MAAOgB,EACPd,OAAQe,GACP,IAIH,MAAMM,EAAY3F,KAAK4F,sBAEvBD,EAAUvB,MAAQH,EAAaE,EAAcI,EAC7CoB,EAAUrB,OAASJ,EAAYG,EAAeG,EAE9C,MAAMqB,EAAMF,EAAUG,WAAW,MAG3BC,EAAa/F,KAAKqB,IAAI,kBAAmBrB,KAAKgG,oBAAoBhG,KAAKgB,MAAMiF,MAC7EC,EAAoBlG,KAAKqB,IAAI,oBAAqB,GAEpD0E,IACHF,EAAIM,UAAYJ,EAAWK,MAAMF,GACjCL,EAAIQ,SAAS,EAAG,EAAGV,EAAUvB,MAAOuB,EAAUrB,SAG/C,IAAImB,EAAOxB,EACPyB,EAAMxB,EACNoC,EAAQb,EAAOtB,EACfoC,EAASb,EAAMrB,EA0BnB,OAvBA,OAAYI,GAASE,IACE,OAAlBA,EAAMG,UACTY,GAAOf,EAAML,OACbuB,EAAIW,UAAU7B,EAAMtB,OAAQY,EAAaU,EAAMc,KAAMC,EAAMf,EAAMe,MAErC,SAAlBf,EAAMG,UAChBe,EAAIW,UAAU7B,EAAMtB,OAAQiD,EAAQ3B,EAAMc,KAAMvB,EAAYS,EAAMe,KAClEY,GAAS3B,EAAMP,OAEa,QAAlBO,EAAMG,UAChBW,GAAQd,EAAMP,MACdyB,EAAIW,UAAU7B,EAAMtB,OAAQoC,EAAOd,EAAMc,KAAMvB,EAAYS,EAAMe,MAEpC,WAAnBf,EAAMG,WAChBe,EAAIW,UAAU7B,EAAMtB,OAAQY,EAAaU,EAAMc,KAAMc,EAAS5B,EAAMe,KACpEa,GAAU5B,EAAML,O,IAMlBuB,EAAIW,UAAU5C,EAAYK,EAAYC,GAE/ByB,CACR,G,CAca,UAAA5C,CAAWf,G,+CACvB,MAAO,QAAUhC,KAAKyD,eAAe,QAAU,IAAMzD,KAAKqB,IAAI,UAAW,SAAW,IAAMoF,yBAAyBzG,KAAK0G,QAAQ1E,GACjI,G,CAQa,OAAA0E,CAAQ1E,G,+CACpB,MAAML,EAAe3B,KAAKyB,kBAAkB,OAAQO,GACpD,OAAO2E,KAAKC,UAAU5G,KAAK6G,QAAQ,OAAQ7E,EAAeL,EAAQnB,eAAe,CAACsG,EAAMhF,KACnF,WAAeA,IAClB,OAAaA,GAAO,CAACiF,EAAOC,KAC3BlF,EAAMiF,GAAS/G,KAAKiH,uBAAuBF,EAAOC,EAAMrF,EAAQ,IAG3DG,IACLH,EAAQpB,OACZ,G,CAca,SAAAyC,CAAUhB,G,+CACtB,MAAO,QAAUhC,KAAKyD,eAAe,OAAS,IAAMzD,KAAKqB,IAAI,UAAW,SAAW,IAAMoF,yBAAyBzG,KAAKkH,OAAOlF,GAC/H,G,CAQa,MAAAkF,CAAOlF,G,+CACnB,MAAML,EAAe3B,KAAKyB,kBAAkB,MAAOO,GAGnD,IAAImF,EAAM,GAGNC,EAAK,GACT,MAAM7D,EAAOvD,KAAK6G,QAAQ,MAAOlF,GAG3B0F,EAAarH,KAAKsH,cAAc/D,GAGtC,GAAI5B,EAAQ4F,MAAO,CAGlB,MAAMC,EAA4BxH,KAAKqB,IAAI,kBAAmB,IAE9D,cAAoBgG,GAAY,CAACxF,EAAK4F,KACrC,IAAIC,EAAU,GACV/F,EAAQjB,gBACXgH,EAAQlG,KAAKiG,GAEd,IAAK,IAAIE,EAAMpE,EAAKqE,OAAQC,EAAI,EAAGA,EAAIF,EAAKE,IAAK,CAChD,IAAIC,EAAYvE,EAAKsE,GAAGhG,GACxB6F,EAAQlG,KAAKxB,KAAKiH,uBAAuBpF,EAAKiG,EAAWnG,GAAS,G,CAEnEwF,GAAOC,EAAKpH,KAAK+H,UAAUL,EAAS/F,OAASqG,GAAW,GACxDZ,EAAK,IAAI,IACP,CAACa,EAAGC,KACN,IAAIC,EAAKX,EAAgBY,QAAQH,GAC7BI,EAAKb,EAAgBY,QAAQF,GACjC,OAAIC,EAAKE,GACA,EAEAF,EAAKE,EACN,EAED,CAAC,G,KAKL,CACJ,IAAK,IAAIV,EAAMpE,EAAKqE,OAAQC,EAAI,EAAGA,EAAIF,EAAKE,IAAK,CAChD,IAAIS,EAAMtI,KAAK+H,UAAUxE,EAAKsE,GAAIlG,EAAS0F,GACvC1F,EAAQ4G,QACXpB,EAAMmB,EAAMlB,EAAKD,EAGjBA,GAAOC,EAAKkB,EAEblB,EAAK,I,CAIFzF,EAAQjB,iBACXyG,EAAMnH,KAAK+H,UAAUV,EAAY1F,OAASqG,GAAW,GAAQZ,EAAKD,E,CAIpE,OAAOA,CAER,G,CAKO,SAAAY,CAAUO,EAAU3G,EAA+B0F,EAAiBmB,GAAgB,GAG1F,IAAI/H,EAAYkB,EAAQlB,WAAa,IACjCgI,EAAe,GAGdpB,IACJA,EAAa,CAAC,EACd,OAAaiB,GAAK,CAACzG,EAAKC,KACvBuF,EAAWxF,GAAOC,CAAK,KAKzB,MAAM0F,EAA4BxH,KAAKqB,IAAI,kBAAmB,IAwC9D,OArCA,cAAoBgG,GAAY,CAACxF,EAAK6G,KAGrC,IAAI5G,EAAQ9B,KAAK2I,kBAAkB9G,EAAKyG,EAAIzG,GAAMF,GAS9CqF,EAAOwB,EAAO1G,EAAQ9B,KAAKiH,uBAAuBpF,EAAKC,EAAOH,GAGlEqF,EAAO,GAAKA,EACZA,EAAOA,EAAK4B,QAAQ,KAAM,OAGtBjH,EAAQkH,aAAgB7B,EAAK8B,OAAO,IAAIC,OAAO,QAAWtI,EAAW,OAAS,KACjFuG,EAAO,IAAOA,EAAO,KAItByB,EAAMjH,KAAKwF,EAAK,IACd,CAACiB,EAAGC,KACN,IAAIC,EAAKX,EAAgBY,QAAQH,GAC7BI,EAAKb,EAAgBY,QAAQF,GACjC,OAAIC,EAAKE,EACD,EAECF,EAAKE,GACL,EAEF,CAAC,IAGFI,EAAMO,KAAKvI,EACnB,CAca,UAAAwC,CAAWjB,G,+CACvB,MAAO,QAAUhC,KAAKyD,eAAe,QAAU,IAAMzD,KAAKqB,IAAI,UAAW,SAAW,IAAMoF,yBAAyBzG,KAAKiJ,QAAQjH,GACjI,G,CAQa,OAAAiH,CAAQjH,G,+CACpB,MAAML,EAAe3B,KAAKyB,kBAAkB,OAAQO,GAGpD,IAAIkH,EAAO,UACPvH,EAAQwH,aACXD,EAAO,iBAAoBvH,EAAQwH,WAAa,MAIjD,MAAM5F,EAAOvD,KAAK6G,QAAQ,OAAQlF,GAC5B0F,EAAarH,KAAKsH,cAAc/D,GAGtC,GAAI5B,EAAQ4F,MAAO,CAGlB,MAAMC,EAA4BxH,KAAKqB,IAAI,kBAAmB,IAE9D6H,GAAQ,YAER,cAAoB7B,GAAY,CAACxF,EAAK4F,KACrC,IAAIC,EAAU,GACV/F,EAAQjB,gBACXgH,EAAQlG,KAAKiG,GAEd,IAAK,IAAIE,EAAMpE,EAAKqE,OAAQC,EAAI,EAAGA,EAAIF,EAAKE,IAAK,CAChD,IAAIC,EAAYvE,EAAKsE,GAAGhG,GACxB6F,EAAQlG,KAAKxB,KAAKiH,uBAAuBpF,EAAKiG,EAAWnG,GAAS,G,CAEnEuH,GAAQ,KAAOlJ,KAAKoJ,WAAW1B,EAAS/F,OAASqG,GAAW,EAAK,IAC/D,CAACC,EAAGC,KACN,IAAIC,EAAKX,EAAgBY,QAAQH,GAC7BI,EAAKb,EAAgBY,QAAQF,GACjC,OAAIC,EAAKE,GACA,EAEAF,EAAKE,EACN,EAED,CAAC,IAGTa,GAAQ,Y,KAIJ,CAEAvH,EAAQjB,iBACXwI,GAAQ,cAAgBlJ,KAAKoJ,WAAW/B,EAAY1F,OAASqG,GAAW,GAAM,GAAQ,cAGvFkB,GAAQ,YAER,IAAK,IAAIvB,EAAMpE,EAAKqE,OAAQC,EAAI,EAAGA,EAAIF,EAAKE,IAC3CqB,GAAQ,KAAOlJ,KAAKoJ,WAAW7F,EAAKsE,GAAIlG,EAAS0F,GAGlD6B,GAAQ,Y,CAKT,OAFAA,GAAQ,aAEDA,CAER,G,CAKO,UAAAE,CAAWd,EAAU3G,EAAgC0F,EAAiBmB,GAAgB,EAAOa,GAAqB,GAGxH,IAAIH,EAAO,SACPvH,EAAQ2H,WACXJ,EAAO,gBAAmBvH,EAAQ2H,SAAW,MAIzCjC,IACJA,EAAaiB,GAId,MAAMd,EAA4BxH,KAAKqB,IAAI,kBAAmB,IAGxDkI,EAAMF,EAAY,KAAO,KAG/B,IAAIG,GAAQ,EA4CZ,OA3CA,cAAoBnC,GAAY,CAACxF,EAAK6G,KAGrC,IAAI5G,EAAQ9B,KAAK2I,kBAAkB9G,EAAKyG,EAAIzG,GAAMF,GAG9CqF,EAAOwB,EAAO1G,EAAQ9B,KAAKiH,uBAAuBpF,EAAKC,EAAOH,GAGlEqF,EAAO,GAAKA,EACZA,EAAOA,EAAK4B,QAAQ,0BAA0B,SAASf,GACtD,MAAO,KAAOA,EAAE4B,WAAW,GAAK,GACjC,IAGA,IAAIC,EAASH,EACT5H,EAAQ4F,OAASiC,IACpBE,EAAS,MAIN/H,EAAQgI,UACXT,GAAQ,UAAYQ,EAAS,WAAc/H,EAAQgI,UAAY,KAAQ3C,EAAO,KAAO0C,EAAS,IAG9FR,GAAQ,UAAYQ,EAAS,IAAM1C,EAAO,KAAO0C,EAAS,IAG3DF,GAAQ,CAAK,IACX,CAACvB,EAAGC,KACN,IAAIC,EAAKX,EAAgBY,QAAQH,GAC7BI,EAAKb,EAAgBY,QAAQF,GACjC,OAAIC,EAAKE,EACD,EAECF,EAAKE,GACL,EAEF,CAAC,IAGTa,GAAQ,YAEDA,CACR,CAca,UAAAhG,CAAWlB,G,+CACvB,MAAO,QAAUhC,KAAKyD,eAAe,QAAU,IAAMzD,KAAKqB,IAAI,UAAW,SAAW,IAAMoF,yBAAyBzG,KAAK4J,QAAQ5H,GACjI,G,CAQa,OAAA4H,CAAQ5H,G,+CAEpB,MAAML,EAAe3B,KAAKyB,kBAAkB,OAAQO,GAGpD,IAAI6H,QAAa7J,KAAK8J,aAGlBC,EAAY,CACfC,SAAU,OACVC,SAAS,EACT5H,KAAM,UAKH6H,EAAYlK,KAAKmK,yBAAyBnK,KAAKqB,IAAI,QAASrB,KAAKoK,GAAG,UAGpEC,EAAK,CACRC,WAAiB,CAACJ,GAClBK,OAAa,CAAC,GAIXC,EAAqB,GAGzB,MAAMjH,EAAOvD,KAAK6G,QAAQ,OAAQlF,GAC5B0F,EAAarH,KAAKsH,cAAc/D,GAGtC,GAAI5B,EAAQ4F,MAAO,CAGlB,MAAMC,EAA4BxH,KAAKqB,IAAI,kBAAmB,IAE9D,cAAoBgG,GAAY,CAACxF,EAAK4F,KACrC,IAAIC,EAAU,GACV/F,EAAQjB,gBACXgH,EAAQlG,KAAKiG,GAEd,IAAK,IAAIE,EAAMpE,EAAKqE,OAAQC,EAAI,EAAGA,EAAIF,EAAKE,IAAK,CAChD,IAAIC,EAAYvE,EAAKsE,GAAGhG,GACxB6F,EAAQlG,KAAKxB,KAAKiH,uBAAuBpF,EAAKiG,EAAWnG,GAAS,G,CAEnE6I,EAAOhJ,KAAKxB,KAAKyK,WAAW/C,EAAS/F,OAASqG,GAAW,GAAM,IAC7D,CAACC,EAAGC,KACN,IAAIC,EAAKX,EAAgBY,QAAQH,GAC7BI,EAAKb,EAAgBY,QAAQF,GACjC,OAAIC,EAAKE,EACD,EAECF,EAAKE,GACL,EAEF,CAAC,G,KAKL,CAEA1G,EAAQjB,gBACX8J,EAAOhJ,KAAKxB,KAAKyK,WAAWpD,EAAY1F,OAASqG,GAAW,IAI7D,IAAK,IAAIL,EAAMpE,EAAKqE,OAAQC,EAAI,EAAGA,EAAIF,EAAKE,IAC3C2C,EAAOhJ,KAAKxB,KAAKyK,WAAWlH,EAAKsE,GAAIlG,EAAS0F,G,CAiBhD,OAZAgD,EAAGE,OAAOL,GAAaL,EAAKa,MAAMC,aAAaH,GAE/CxK,KAAKmC,OAAOC,SAAS,gBAAiB,CACrCC,KAAM,gBACNX,OAAQ,OACRC,QAASA,EACTiJ,SAAUP,EACVQ,gBAAiBd,EACjBe,KAAMjB,EACNvH,OAAQtC,OAGF6J,EAAKkB,MAAMV,EAAIN,EACvB,G,CAEQ,wBAAAI,CAAyBa,GAEhC,OADAA,EAAOA,EAAKpC,QAAQ,oBAAqB,MAC7BhB,OAAS,GAAKoD,EAAKC,OAAO,EAAG,IAAM,MAAQD,CACxD,CAKO,UAAAP,CAAWnC,EAAU3G,EAAgC0F,EAAiBmB,GAAgB,GAG5F,IAAIC,EAAe,GAGdpB,IACJA,EAAaiB,GAId,MAAMd,EAA4BxH,KAAKqB,IAAI,kBAAmB,IAwB9D,OArBA,cAAoBgG,GAAY,CAACxF,EAAK6G,KAGrC,IAAI5G,EAAQ9B,KAAK2I,kBAAkB9G,EAAKyG,EAAIzG,GAAMF,GAG9CqF,EAAOwB,EAAO1G,EAAQ9B,KAAKiH,uBAAuBpF,EAAKC,EAAOH,GAAS,GAE3E8G,EAAMjH,KAAKwF,EAAK,IACd,CAACiB,EAAGC,KACN,IAAIC,EAAKX,EAAgBY,QAAQH,GAC7BI,EAAKb,EAAgBY,QAAQF,GACjC,OAAIC,EAAKE,EACD,EAECF,EAAKE,GACL,EAEF,CAAC,IAGFI,CACR,CAMc,KAAAyC,G,+CACb,aAAa,8BACd,G,CAKO,UAAApB,GACN,OAAO9J,KAAKkL,OACb,CAca,SAAA/H,CAAUnB,G,+CACtB,MAAO,QAAUhC,KAAKyD,eAAe,OAAS,IAAMzD,KAAKqB,IAAI,UAAW,SAAW,IAAMoF,yBAAyBzG,KAAKmL,OAAOnJ,GAAe,GAC9I,G,CAQa,aAAAoB,CAAcpB,G,+CAC1B,MAAO,QAAUhC,KAAKyD,eAAe,OAAS,IAAMzD,KAAKqB,IAAI,UAAW,SAAW,IAAMoF,yBAAyBzG,KAAKmL,OAAOnJ,GAAe,GAAO,GACrJ,G,CASa,MAAAmJ,CAAOnJ,EAAsCoJ,GAAwB,EAAMC,GAAuB,G,+CAE9G,MAAM1J,EAAe3B,KAAKyB,kBAAkB,MAAOO,GAC7CsJ,EAAmBtL,KAAKyB,kBAAkB,UAAWO,GACrDuJ,EAAwC5J,EAAQ6J,iBAAmB,WAGzE,IAAIC,EACAL,IACHK,QAAczL,KAAK8C,YAAYnB,EAAQrB,aAAe,MAAOqB,IAI9D,MAAM+J,QAAgB1L,KAAK2L,aAM3B,IAAIC,EAAM,CACTC,SAAUlK,EAAQkK,UAAY,KAC9BL,gBAAiBD,EACjBO,YAAanK,EAAQmK,aANC,CAAC,GAAI,GAAI,GAAI,IAOnCC,aAAc,CACbC,KAAMrK,EAAQqK,KAAOrK,EAAQqK,KAAKhB,UAAOhD,GAG1CiE,QAAc,IAIf,MAAMC,EAAQlM,KAAKqB,IAAI,SAEvB,IAAI8K,EAAc,EAEdD,IACHN,EAAIK,QAAQzK,KAAK,CAChB4K,KAAMF,EACNrL,SAAUc,EAAQd,UAAY,GAC9BwL,MAAM,EACNC,OAAQ,CAAC,EAAG,EAAG,EAAG,MAInBH,GAAe,IAIZxK,EAAQZ,SACX6K,EAAIK,QAAQzK,KAAK,CAChB4K,KAAMpM,KAAKoK,GAAG,cAAgB,KAAOmC,SAASC,SAASC,KACvD5L,SAAUc,EAAQd,SAClByL,OAAQ,CAAC,EAAG,EAAG,EAAG,MAInBH,GAAe,IAIZf,GAAgBK,GACnBG,EAAIK,QAAQzK,KAAK,CAChBiK,MAAOA,EACPiB,UAAW/K,EAAQb,OAAS,OAC5B6L,IAAK3M,KAAK4M,eAAehB,EAAIC,SAAUD,EAAIE,YAAaK,EAAaZ,MAKlEF,GAAe1J,EAAQ0J,cAAgBrL,KAAK6M,WAChDjB,EAAIK,QAAQzK,KAAK,CAChBsL,YAAa9M,KAAK+M,WAAWzB,GAC7BzK,SAAUc,EAAQd,UAAY,KAIhC,IAAImM,EAAgE,KAChEC,EAAyC,KAE7C,SAASC,EAAQlB,GAChB,MAAMmB,EAAoC,CAAC,EAE3CA,EAAMC,OAASpB,EAAKoB,OAAOC,KAC3BJ,EAAKjB,EAAKoB,OAAOC,MAAQrB,EAAKoB,OAAOE,MAEjCtB,EAAKK,MACRc,EAAMd,KAAOL,EAAKK,KAAKgB,KACvBJ,EAAKjB,EAAKK,KAAKgB,MAAQrB,EAAKK,KAAKiB,OAGjCH,EAAMd,KAAOL,EAAKoB,OAAOC,KAGtBrB,EAAKuB,SACRJ,EAAMI,QAAUvB,EAAKuB,QAAQF,KAC7BJ,EAAKjB,EAAKuB,QAAQF,MAAQrB,EAAKuB,QAAQD,OAGvCH,EAAMI,QAAUvB,EAAKoB,OAAOC,KAGzBrB,EAAKwB,aACRL,EAAMK,YAAcxB,EAAKwB,YAAYH,KACrCJ,EAAKjB,EAAKwB,YAAYH,MAAQrB,EAAKwB,YAAYF,OAG/CH,EAAMK,YAAcxB,EAAKoB,OAAOC,KAGjCL,EAAOhB,EAAKhB,MAAQmC,CACrB,CAqBA,OAnBIxL,EAAQqK,OACXgB,EAAQ,CAAC,EACTC,EAAM,CAAC,EACPC,EAAQvL,EAAQqK,MAEZrK,EAAQ8L,YACX,OAAY9L,EAAQ8L,WAAYP,IAIlClN,KAAKmC,OAAOC,SAAS,cAAe,CACnCC,KAAM,cACNX,OAAQ,MACRC,QAASA,EACTiK,IAAKA,EACLtJ,OAAQtC,OAIF,IAAI0N,SAAgB,CAACC,EAASC,KACpClC,EAAQmC,UAAUjC,EAAK,KAAMoB,EAAOC,GAAKa,WAAWvL,IACnDoL,EAAQpL,EAAI,GACX,GAEJ,G,CAKa,UAAAwK,CAAW/K,G,+CAEvB,MAAML,EAAe3B,KAAKyB,kBAAkB,UAAWO,GAGvD,IAAIiK,EAAe,CAClB,KAAa,IAId,MAAM1I,EAAOvD,KAAK6G,QAAQ,OAAQlF,GAC5B0F,EAAarH,KAAKsH,cAAc/D,GAGtC,GAAI5B,EAAQ4F,MAAO,CAGlB,MAAMC,EAA4BxH,KAAKqB,IAAI,kBAAmB,IAE9D,cAAoBgG,GAAY,CAACxF,EAAK4F,KACrC,IAAIC,EAAU,GACV/F,EAAQjB,gBACXgH,EAAQlG,KAAKiG,GAEd,IAAK,IAAIE,EAAMpE,EAAKqE,OAAQC,EAAI,EAAGA,EAAIF,EAAKE,IAAK,CAChD,IAAIC,EAAYvE,EAAKsE,GAAGhG,GACxB6F,EAAQlG,KAAKxB,KAAKiH,uBAAuBpF,EAAKiG,EAAWnG,GAAS,G,CAEnEsK,EAAQ8B,KAAKvM,KAAKxB,KAAKgO,cAActG,EAAS/F,OAASqG,GAAW,GAAM,IACtE,CAACC,EAAGC,KACN,IAAIC,EAAKX,EAAgBY,QAAQH,GAC7BI,EAAKb,EAAgBY,QAAQF,GACjC,OAAIC,EAAKE,EACD,EAECF,EAAKE,GACL,EAEF,CAAC,G,KAKL,CAGA1G,EAAQjB,iBACXuL,EAAQ8B,KAAKvM,KAAKxB,KAAKgO,cAAc3G,EAAY1F,OAASqG,GAAW,IACrEiE,EAAQgC,WAAa,GAGtB,IAAK,IAAItG,EAAMpE,EAAKqE,OAAQC,EAAI,EAAGA,EAAIF,EAAKE,IAC3CoE,EAAQ8B,KAAKvM,KAAKxB,KAAKgO,cAAczK,EAAKsE,GAAIlG,EAAS0F,G,CAKzD,OAAO4E,CAER,G,CAKO,aAAA+B,CAAc1F,EAAU3G,EAAgC0F,EAAkBmB,GAAgB,GAGhG,IAAIC,EAAe,GAGdpB,IACJA,EAAaiB,GAId,MAAMd,EAA4BxH,KAAKqB,IAAI,kBAAmB,IA0B9D,OAvBA,cAAoBgG,GAAY,CAACxF,EAAK6G,KAGrC,IAAI5G,EAAQ9B,KAAK2I,kBAAkB9G,EAAKyG,EAAIzG,GAAMF,GAG9CqF,EAAOwB,EAAO1G,EAAQ9B,KAAKiH,uBAAuBpF,EAAKC,EAAOH,GAClEqF,EAAO,GAAKA,EAGZyB,EAAMjH,KAAKwF,EAAK,IACd,CAACiB,EAAGC,KACN,IAAIC,EAAKX,EAAgBY,QAAQH,GAC7BI,EAAKb,EAAgBY,QAAQF,GACjC,OAAIC,EAAKE,EACD,EAECF,EAAKE,GACL,EAEF,CAAC,IAGFI,CACR,CAQO,UAAAkD,GAKN,YAJuB3D,IAAnBrI,IACHA,EAvqDH,W,+CACC,IAAIsI,QAAUyF,QAAQQ,IAAI,CACzB,kCACA,gCAGGxC,EAAUzD,EAAE,GAAGkG,QACfC,EAAYnG,EAAE,GAAGkG,QACrB,MAAME,EAAcC,OAIpB,OAHAD,EAAOE,QAAUF,EAAOE,SAAW,CAAC,EACpCF,EAAOE,QAAQtB,IAAMmB,EACrB1C,EAAQuB,IAAMmB,EACP1C,CACR,G,CA0pDoB8C,IAGX7O,CACR,CAKO,cAAAiN,CAAef,EAAqB4C,EAA4BtC,EAAsB,EAAGZ,EAAwC,YAGvI,IAAImD,EAAa,CAAC,EAAG,EAAG,EAAG,GACvB,WAAeD,GAClBC,EAAa,CAACD,EAASA,EAASA,EAASA,GAEf,GAAlBA,EAAQ7G,OAChB8G,EAAa,CAACD,EAAQ,GAAIA,EAAQ,GAAIA,EAAQ,GAAIA,EAAQ,IAEhC,GAAlBA,EAAQ7G,SAChB8G,EAAaD,GAId,IAsDIE,EAtDQ,CACX,MAAO,CAAC,QAAS,SACjB,MAAO,CAAC,QAAS,SACjBC,GAAI,CAAC,QAAS,SACdC,GAAI,CAAC,QAAS,SACdC,GAAI,CAAC,QAAS,SACdC,GAAI,CAAC,OAAQ,SACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,MAAQ,QACbC,GAAI,CAAC,OAAQ,OACbC,IAAK,CAAC,KAAO,QACbC,GAAI,CAAC,QAAS,SACdC,GAAI,CAAC,QAAS,SACdC,GAAI,CAAC,QAAS,SACdC,GAAI,CAAC,QAAS,SACdC,GAAI,CAAC,OAAQ,SACbC,GAAI,CAAC,MAAQ,QACbC,GAAI,CAAC,OAAQ,OACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,OAAQ,QACbC,IAAK,CAAC,MAAO,QACbC,GAAI,CAAC,QAAS,SACdC,GAAI,CAAC,QAAS,SACdC,GAAI,CAAC,QAAS,SACdC,GAAI,CAAC,OAAQ,SACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,OAAQ,QACbC,GAAI,CAAC,OAAQ,QACbC,IAAK,CAAC,MAAO,QACbC,IAAK,CAAC,OAAS,SACfC,IAAK,CAAC,QAAS,QACfC,IAAK,CAAC,OAAS,SACfC,IAAK,CAAC,OAAQ,QACdC,IAAK,CAAC,OAAQ,QACdC,KAAM,CAAC,QAAS,SAChBC,KAAM,CAAC,QAAS,SAChBC,KAAM,CAAC,QAAS,SAChBC,KAAM,CAAC,OAAQ,SACfC,KAAM,CAAC,MAAQ,QACfC,UAAW,CAAC,OAAQ,KACpBC,MAAO,CAAC,IAAQ,KAChBC,MAAO,CAAC,IAAQ,MAChBC,OAAQ,CAAC,IAAQ,KACjBC,QAAS,CAAC,IAAQ,OAIC9F,GAMpB,MALmB,aAAfN,GACHoD,EAAQpG,UAEToG,EAAQ,IAAMD,EAAW,GAAKA,EAAW,GACzCC,EAAQ,IAAMD,EAAW,GAAKA,EAAW,GAAKvC,EACvCwC,CACR,CAcO,OAAA9B,GACN,MAAM+E,EAAa5R,KAAKqB,IAAI,cAC5B,SAAO,UAAcuQ,KAAeA,EAAWhK,OAChD,CAUO,OAAAf,CAAQnF,EAA0BM,EAAuCxB,GAAwB,GACvG,MAAMmB,EAAe3B,KAAKyB,kBAAkBC,EAAQM,GAC9C4P,EAAa5R,KAAKqB,IAAI,aAAc,IAC1C,IAAIkC,EAAYqO,EAGhB,MAAMvK,EAAarH,KAAKqB,IAAI,cACxBgG,GAAc,UAAcuK,KAC/BrO,EAAO,GACP,OAAYqO,GAAatJ,IACxB,GAAI,WAAeA,GAAM,CACxB,MAAMuJ,EAAc,CAAC,EACrB,OAAaxK,GAAY,CAACN,EAAOjF,KACN,MAAtBuF,EAAYN,KACf8K,EAAOrR,EAAesB,EAAQiF,GAAS/G,KAAKiH,uBAAuBF,EAAOuB,EAAIvB,GAAQpF,G,IAGxF4B,EAAK/B,KAAKqQ,E,MAKb,MAAMC,EAAa,CAClBzP,KAAM,gBACNX,OAAQA,EACRC,QAASA,EACT4B,KAAMA,EACNjB,OAAQtC,MAKT,OAFAA,KAAKmC,OAAOC,SAAS,gBAAiB0P,GAE/BA,EAAMvO,IACd,CAKO,aAAA+D,CAAc/D,GACpB,IAAI8D,EAAarH,KAAKqB,IAAI,cAa1B,OAZKgG,IACJA,EAAa,CAAC,EACV,UAAc9D,IAASA,EAAKqE,QAC/B,OAAYrE,GAAO+E,IAClB,OAAaA,GAAK,CAACzG,EAAKkQ,KACC,MAApB1K,EAAYxF,KACfwF,EAAYxF,GAAOA,E,GAEnB,KAIEwF,CACR,CAKO,iBAAAsB,CAAkBqJ,EAAgBlQ,EAAYH,GACpD,OAAgB,MAATG,EAAgBA,EAAQH,EAAQhB,OACxC,CAKO,sBAAAsG,CAAuBF,EAAYjF,EAAYH,EAAgCsQ,GAGrF,GAAoB,iBAATnQ,EACV,GAAI9B,KAAKkS,YAAYnL,GACpBjF,EAAQ,IAAIqQ,KAAKrQ,OAEb,IAAI9B,KAAKoS,eAAerL,IAAU/G,KAAKqB,IAAI,gBAC/C,OAAOrB,KAAKgB,MAAMqR,gBAAgB3Q,OAAOI,EAAO9B,KAAKqB,IAAI,iBAErD,GAAIrB,KAAKsS,gBAAgBvL,GAC7B,OAAO/G,KAAKgB,MAAMuR,kBAAkB7Q,OAAOI,EAAO9B,KAAKqB,IAAI,kBAAmBrB,KAAKqB,IAAI,gB,CAkBzF,OAdIS,aAAiBqQ,OAChBxQ,EAAQ6Q,cACX1Q,EAAQA,EAAM2Q,UAEN9Q,EAAQ+Q,UACXT,IACJnQ,EAAQA,EAAM6Q,kBAIf7Q,EAAQ9B,KAAKgB,MAAM4R,cAAclR,OAAOI,EAAO9B,KAAKqB,IAAI,gBAInDS,CACR,CAKO,WAAAoQ,CAAYnL,GAClB,OAAmD,IAA5C/G,KAAKqB,IAAI,cAAe+G,QAAQrB,EACxC,CAKO,cAAAqL,CAAerL,GACrB,OAAsD,IAA/C/G,KAAKqB,IAAI,iBAAkB+G,QAAQrB,EAC3C,CAKO,eAAAuL,CAAgBvL,GACtB,OAAuD,IAAhD/G,KAAKqB,IAAI,kBAAmB+G,QAAQrB,EAC5C,CAKO,cAAAtD,CAAepB,GACrB,IAAIwQ,EAAc,GAClB,OAAQxQ,GACP,IAAK,MACJwQ,EAAc,SAAWxQ,EACzB,MACD,IAAK,MACJwQ,EAAc,aACd,MACD,IAAK,MACJA,EAAc,WACd,MACD,IAAK,OACJA,EAAc,mBACd,MACD,IAAK,OACJA,EAAc,YACd,MACD,IAAK,MACL,IAAK,UACJA,EAAc,kBACd,MACD,IAAK,OACJA,EAAc,oEACd,MACD,QACCA,EAAc,2BAGhB,OAAOA,CACR,CAEU,mBAAAjN,GACT,IAAIvC,EAASkJ,SAASuG,cAAc,UAIpC,OAHAzP,EAAO0P,MAAMjO,SAAW,QACxBzB,EAAO0P,MAAMrN,IAAM,WACnB1F,KAAKgB,MAAMiF,IAAI+M,YAAY3P,GACpBA,CACR,CAEU,aAAAK,CAAcL,GACvBrD,KAAKgB,MAAMiF,IAAIgN,YAAY5P,EAC5B,CAMO,mBAAA2C,CAAoBkN,GAG1B,IAAIC,EAAU,EACVC,EAAeC,iBAAiBH,EAAS,oBAAoBI,iBAAiB,oBAOlF,IAJIF,EAAaG,MAAM,4BAA8C,eAAhBH,KACpDD,EAAU,GAGI,GAAXA,EAAc,CACjB,IAAIK,EAASN,EAAQO,cAGrB,OAAID,EACIxT,KAAKgG,oBAAoBwN,GAGzBE,EAAA,GAAMC,QAAQ,S,CAItB,OAAOD,EAAA,GAAME,QAAQR,EAGvB,CAUO,UAAA3Q,CAAWF,EAAaL,EAAkBtB,GAAkB,GAElE,GAAIZ,KAAK6T,sBAAuB,CAM/B,IAAIC,EAAOvH,SAASuG,cAAc,KAClCgB,EAAK/R,SAAWG,EAChBqK,SAASwB,KAAKiF,YAAYc,GAG1B,IAAIC,EAAQxR,EAAIyR,MAAM,KAClBnB,EAAckB,EAAOE,QAASrL,QAAQ,QAAS,IAInD,GAFArG,EAAM2R,mBAAmBH,EAAM/K,KAAK,KAAKJ,QAAQ,UAAW,MAEgC,GAAxF,CAAC,gBAAiB,mBAAoB,WAAY,aAAaR,QAAQyK,GAStE,CACAjS,IACH2B,EAAM,SAAWA,GAElB,IAAI4R,EAAO,IAAIC,KAAK,CAAC7R,GAAM,CAAEF,KAAMwQ,IAC/BwB,EAAM/F,OAAOgG,IAAIC,gBAAgBJ,GAQrC,OAPAL,EAAKrH,KAAO4H,EACZP,EAAK/R,SAAWG,EAChB4R,EAAKU,QACLC,YAAW,KACVlI,SAASwB,KAAKkF,YAAYa,GAC1BxF,OAAOgG,IAAII,gBAAgBL,EAAI,GAC7B,MACI,C,CArBP,IAEC9R,EADcoS,KAAKpS,E,CAElB,MAAOqS,GAER,OAAO,C,CAoBT,IAAIC,EAAQ,IAAIC,MAAMvS,EAAIqF,QAC1B,IAAK,IAAIC,EAAI,EAAGA,EAAItF,EAAIqF,SAAUC,EAAG,CACpC,IAAIkN,EAAWxS,EAAIkH,WAAW5B,GAC9BgN,EAAMhN,GAAKkN,C,CAGRnU,IACHiU,EAAQ,CAAC,IAAM,IAAM,KAAMG,OAAOH,IAEnC,IAAIV,EAAO,IAAIC,KAAK,CAAC,IAAIa,WAAWJ,IAAS,CAAExS,KAAMwQ,IACjDwB,EAAM/F,OAAOgG,IAAIC,gBAAgBJ,GACrCL,EAAKrH,KAAO4H,EACZP,EAAK/R,SAAWG,EAChBqK,SAASwB,KAAKiF,YAAYc,GAC1BA,EAAKU,QACLjI,SAASwB,KAAKkF,YAAYa,GAC1BW,YAAW,KACVnG,OAAOgG,IAAII,gBAAgBL,EAAI,GAC7B,I,MAIC,GAAIrU,KAAKkV,sBAAuB,CAMpC,IAAIpB,EAAOvH,SAASuG,cAAc,KAClCgB,EAAK/R,SAAWG,EAChB4R,EAAKrH,KAAOlK,EACZgK,SAASwB,KAAKiF,YAAYc,GAC1BA,EAAKU,QACLjI,SAASwB,KAAKkF,YAAYa,E,MAU1BxF,OAAO9B,SAASC,KAAOlK,EAGxB,OAAO,CAER,CAKO,eAAA4S,GACN,OAAOnV,KAAKkV,qBACb,CAKO,mBAAAA,GAGN,YADgC,IADxB3I,SAASuG,cAAc,KACZ/Q,QAEpB,CAKO,mBAAA8R,GACN,OAAsB,MAAfvF,OAAO8F,IACf,CAiBO,aAAAzR,CAAcY,EAAcvB,EAAwCkK,GAC1E,MAAMvK,EAAkC3B,KAAKyB,kBAAkB,QAASO,GAC7C,OAAvBL,EAAQtB,YACXL,KAAKoV,aAAa7R,EAAM5B,EAASuK,GAGjClM,KAAKqV,gBAAgB9R,EAAM5B,EAASuK,EAGtC,CAEU,YAAAkJ,CAAa7R,EAAcvB,EAAwCkK,GAE5E,IAoBIoJ,EApBAlV,EADoCJ,KAAKyB,kBAAkB,QAASO,GACpD5B,OAAS,IAGzBmV,EAAShJ,SAASiJ,gBAAgBC,WAAalJ,SAASwB,KAAK0H,UAG7DC,EAAO,IAAI,EAAAC,UAAU,gBAAqB3V,KAAKgB,MAAMiF,KAAM,WAAY,CAC1E,QAAW,OACX,SAAY,QACZ,WAAc,SACd,QAAW,IACX,SAAY,6CACVjG,KAAKgB,MAAM4U,OAEVC,EAAQ,IAAI,EAAAF,UAAU,gBAAqB3V,KAAKgB,MAAMiF,KAAM,OAAQ,CACvE,QAAW,IACX,OAAU,KACRjG,KAAKgB,MAAM4U,OAIV1J,GAASK,UAAYA,SAASL,QACjCoJ,EAAgB/I,SAASL,MACzBK,SAASL,MAAQA,GAIlB,IAAI4J,EAAM,IAAIC,MACdD,EAAIE,IAAMzS,EACVuS,EAAI/C,MAAMkD,SAAW,OACrBH,EAAI/C,MAAMmD,QAAU,QACpBJ,EAAI/C,MAAMjO,SAAW,WACrBgR,EAAI/C,MAAMoD,WAAa,UACvBL,EAAI/C,MAAMI,QAAU,IACpB2C,EAAI/C,MAAMqD,SAAW,OACrB7J,SAASwB,KAAKiF,YAAY8C,GAG1B9V,KAAKyU,YAAW,KACTnG,OAAQ5L,OAAO,GACnB,IAGS,mBAAmB2T,KAAKC,UAAUC,aAAqBjI,OAAQkI,UAC7DpW,EAAQ,IACrBA,EAAQ,IAEAA,EAAQ,MAChBA,EAAQ,KAITJ,KAAKyU,YAAW,KAGflI,SAASwB,KAAKkF,YAAY6C,GAG1BJ,EAAKe,UACLZ,EAAMY,UAGFnB,IACH/I,SAASL,MAAQK,SAASL,OAI3BK,SAASiJ,gBAAgBC,UAAYlJ,SAASwB,KAAK0H,UAAYF,CAAM,GAEnEnV,GAAS,IAEb,CAEU,eAAAiV,CAAgB9R,EAAcvB,EAAwCkK,GAE/E,IAAI9L,EADoCJ,KAAKyB,kBAAkB,QAASO,GACpD5B,OAAS,IAG7B,MAAMsW,EAASnK,SAASuG,cAAc,UACtC4D,EAAO3D,MAAMoD,WAAa,SAC1B5J,SAASwB,KAAKiF,YAAY0D,GAG1BA,EAAOC,cAAepK,SAASqK,OAC/BF,EAAOC,cAAepK,SAASsK,QAG/B,IAAIf,EAAM,IAAIC,MACdD,EAAIE,IAAMzS,EACVuS,EAAI/C,MAAMkD,SAAW,OACrBH,EAAI/C,MAAMzO,OAAS,OACf4H,IACHwK,EAAOC,cAAepK,SAASL,MAAQA,GAExCwK,EAAOC,cAAepK,SAASwB,KAAKiF,YAAY8C,GAE1CY,EAAQI,KAAO,WACpBJ,EAAOC,cAAepK,SAASwB,KAAKiF,YAAY8C,EACjD,EAGA9V,KAAKyU,YAAW,KACf,IACYiC,EAAQC,cAAcpK,SAASwK,YAAY,SAAS,EAAO,OAC/DL,EAAQC,cAAcjU,O,CAE5B,MAAOkS,GACF8B,EAAQC,cAAcjU,O,IAE3BtC,GAAS,IAGA,mBAAmBiW,KAAKC,UAAUC,aAAqBjI,OAAQkI,UAC7DpW,EAAQ,IACrBA,EAAQ,IAEAA,EAAQ,MAChBA,EAAQ,KAITJ,KAAKyU,YAAW,KAGflI,SAASwB,KAAKkF,YAAYyD,EAAO,GAE/BtW,EAAQ,IAAM,IAElB,CAOO,gBAAA4W,GACN,MAAMC,EAA0B,GAC1BpK,EAAU7M,KAAK6M,UACfsI,EAAkBnV,KAAKmV,kBAS7B,OARA,OAAgC,CAAC,MAAO,MAAO,SAAU,MAAO,OAAQ,MAAO,OAAQ,OAAQ,UAAW,UAAWzT,KAE3F,IADT1B,KAAKyB,kBAAkBC,GAC3BwV,YACwD,GAA/D,CAAC,OAAQ,MAAO,OAAQ,OAAQ,WAAW9O,QAAQ1G,IAAkBmL,GAAWsI,IACnF8B,EAAIzV,KAAuBE,E,IAIvBuV,CACR,CAOO,oBAAAE,GACN,MAAMF,EAAwB,CAAC,QAAS,SAIxC,OAHIjX,KAAKmV,mBAAqBnV,KAAK6M,WAClCoK,EAAIzV,KAAK,QAEHyV,CACR,EA7rDA,qC,gDAAkC,cAClC,sC,gDAA0CpX,EAAA,GAAOuX,WAAWpC,OAAO,CAACpV,EAAUyX,c,cC3nB/E,IAAIC,ECsIG,MAAMC,UAAsB1X,EAAA,GAAnC,c,oBAQC,2C,yDACA,2C,yDACA,2C,yDACA,4C,gDAA0C,KAC1C,6C,gDAA2C,KAE3C,2C,yDACA,0C,yDAEA,qC,iDAAyB,IAEzB,sC,iDAA2B,GAia5B,CA/ZW,SAAAC,GACTC,MAAMD,YACNE,KAAKC,eAAe,YAAaD,KAAKgB,MAAMwW,QAC5CxX,KAAKC,eAAe,QAAS,SAC7BD,KAAKC,eAAe,SAAU,OAC9BD,KAAKC,eAAe,iBAAiB,GACrCD,KAAKC,eAAe,aAAa,GACjCD,KAAKC,eAAe,kBAAkB,GACtCD,KAAKC,eAAe,QAAS,CAAC,CAC7BoC,KAAM,YACNoV,MAAOzX,KAAKoK,GAAG,WACb,CACF/H,KAAM,SACNX,OAAQ,MACRgW,WAAY,QACZD,MAAOzX,KAAKoK,GAAG,OACfuN,SAAU3X,KAAKoK,GAAG,UAChB,CACF/H,KAAM,SACNX,OAAQ,MACRgW,WAAY,QACZD,MAAOzX,KAAKoK,GAAG,OACfuN,SAAU3X,KAAKoK,GAAG,UAChB,CACF/H,KAAM,SACNX,OAAQ,MACRgW,WAAY,QACZD,MAAOzX,KAAKoK,GAAG,OACfuN,SAAU3X,KAAKoK,GAAG,UAChB,CACF/H,KAAM,YACNqV,WAAY,QAEV,CACFrV,KAAM,SACNX,OAAQ,OACRgW,WAAY,OACZD,MAAOzX,KAAKoK,GAAG,QACfuN,SAAU3X,KAAKoK,GAAG,SAChB,CACF/H,KAAM,SACNX,OAAQ,MACRgW,WAAY,OACZD,MAAOzX,KAAKoK,GAAG,OACfuN,SAAU3X,KAAKoK,GAAG,SAChB,CACF/H,KAAM,SACNX,OAAQ,OACRgW,WAAY,OACZD,MAAOzX,KAAKoK,GAAG,QACfuN,SAAU3X,KAAKoK,GAAG,SAChB,CACF/H,KAAM,SACNX,OAAQ,UACRgW,WAAY,OACZD,MAAOzX,KAAKoK,GAAG,OACfuN,SAAU3X,KAAKoK,GAAG,SAChB,CACF/H,KAAM,SACNX,OAAQ,OACRgW,WAAY,OACZD,MAAOzX,KAAKoK,GAAG,QACfuN,SAAU3X,KAAKoK,GAAG,SAChB,CACF/H,KAAM,aACJ,CACFA,KAAM,SACNX,OAAQ,QACRgW,WAAY,QACZD,MAAOzX,KAAKoK,GAAG,YAGhB,MAAMwN,EAAcrL,SAASuG,cAAc,OAC3C9S,KAAK6X,aAAeD,EACpB5X,KAAK8X,WAAW,cAAe9X,KAAK6X,cAEpC,MAAME,EAAcxL,SAASuG,cAAc,KAC3C9S,KAAKgY,aAAeD,EAEpB/X,KAAKiY,aAAe1L,SAASuG,cAAc,MAC3C9S,KAAKiY,aAAaC,aAAa,OAAQ,QACvClY,KAAK8X,WAAW,cAAe9X,KAAKiY,cACpCjY,KAAKmY,mBAELJ,EAAYK,UAAY,8LACxBL,EAAYG,aAAa,WAAYlY,KAAKgB,MAAMqX,SAASC,YACzDP,EAAYG,aAAa,aAAclY,KAAKoK,GAAG,UAAY,KAAOpK,KAAKoK,GAAG,wBAC1E2N,EAAYG,aAAa,OAAQ,UAE7B,WAAgB,mBACnBlY,KAAKuB,WAAWC,KAAK,mBAAwB+K,SAAU,WAAYgM,IAClE,MAAMC,EAAW,cAAmBD,GACpC,GAAIhM,SAASkM,eAAiBzY,KAAKgY,cAAgBhY,KAAK0Y,OACvD,GAAgB,UAAZF,EAEHxY,KAAK6W,aAED,GAAgB,SAAZ2B,EAEJxY,KAAK2Y,YACR3Y,KAAK4Y,aAAa5Y,KAAK2Y,aAGvB3Y,KAAK6Y,cAGF,GAAgB,WAAZL,GAAqC,aAAZA,EAAyB,CAC1D,MAAM/P,EAAQzI,KAAKqB,IAAI,QAAS,IAChC,IAAIyX,EAAqBrQ,EAAOL,QAAQpI,KAAK2Y,aACnB,OAAtB3Y,KAAKqB,IAAI,YAAuC,GAAjByX,IAClCA,EAAerQ,EAAMb,QAEtB,MAAMmR,EAAkB,WAAZP,GAAyB,EAAI,EACzC,IACIQ,EADAC,EAAWH,EAAeC,EAE9B,GACKE,EAAW,EACdA,EAAWxQ,EAAMb,OAAS,EAElBqR,EAAYxQ,EAAMb,OAAS,IACnCqR,EAAW,GAEgB,aAAxBxQ,EAAMwQ,GAAU5W,KACnB4W,GAAYF,EAGZC,EAAUvQ,EAAMwQ,UAERD,GAENA,GACHhZ,KAAKkZ,iBAAiBF,E,MAO3BhZ,KAAKuB,WAAWC,KAAK,mBAAwBuW,EAAa,SAAUQ,IACnEA,EAAGY,2BACHnZ,KAAK6Y,QAAQ,KAGdjB,EAAY5E,YAAYhT,KAAKgY,cAC7BJ,EAAY5E,YAAYhT,KAAKiY,cAE7B,MAAMmB,EAAYpZ,KAAKqB,IAAI,YAAarB,KAAKgB,MAAMwW,QAEnD4B,EAAUpG,YAAYhT,KAAK6X,cAE3B7X,KAAKuB,WAAWC,KAAK,mBAAwBoW,EAAa,mBAAwB,gBAAiByB,IAClGrZ,KAAKsZ,SAAU,EACXtZ,KAAKqB,IAAI,oBACZrB,KAAKgB,MAAM6C,UAAU0V,qBAAsB,E,KAI7CvZ,KAAKuB,WAAWC,KAAK,mBAAwBoW,EAAa,mBAAwB,eAAgByB,IAC7FrZ,KAAKqB,IAAI,oBAAsBrB,KAAK0Y,QAAU1Y,KAAKsZ,WACtDtZ,KAAKgB,MAAM6C,UAAU0V,qBAAsB,GAE5CvZ,KAAKsZ,SAAU,CAAK,KAGrBtZ,KAAKuB,WAAWC,KAAK,IAAIgY,EAAA,IAAS,KAC7BxZ,KAAK6X,cACRuB,EAAUnG,YAAYjT,KAAK6X,a,KAI7B7X,KAAKuB,WAAWC,KAAK,mBAAwB+K,SAAU,SAAU8M,IAC5DrZ,KAAK0Y,SAAW1Y,KAAKsZ,SACxBtZ,KAAK6W,O,KAIP7W,KAAKyZ,iBAELzZ,KAAKgB,MAAMC,YAAYjB,MAEvBA,KAAKmC,OAAOC,SAAS,cAAe,CACnCC,KAAM,cACNC,OAAQtC,MAEV,CAEO,aAAA0Z,GA2BN,GA1BA3Z,MAAM2Z,gBAE4B,GAA9B1Z,KAAK2Z,cAAe/R,QACvB5H,KAAK4Z,cAGF5Z,KAAKmB,QAAQ,mBACZnB,KAAKqB,IAAI,iBACZrB,KAAKyZ,iBAEGzZ,KAAK6Z,cACb7Z,KAAK6Z,aAAapD,WAIhBzW,KAAKmB,QAAQ,cACEnB,KAAKqB,IAAI,cAE1BrB,KAAK4Z,eAIH5Z,KAAKmB,QAAQ,UAAYnB,KAAKmB,QAAQ,YACzCnB,KAAKmY,mBAGFnY,KAAKmB,QAAQ,aAAc,CAC9B,MAAMiY,EAAYpZ,KAAKqB,IAAI,aACvB+X,GACHA,EAAUpG,YAAYhT,KAAK6X,a,CAG9B,CAEU,QAAAiC,GACT/Z,MAAM+Z,WAEN,OAAY9Z,KAAK+Z,gBAAiBC,IACjCA,EAAEvD,SAAS,GAEb,CAEQ,gBAAA0B,GACP,MAAMrX,EAAQd,KAAKqB,IAAI,QAAS,SAC1B4Y,EAASja,KAAKqB,IAAI,SAAU,OAC5B6Y,EAASla,KAAK0Y,OAAS,yBAA2B,2BAExD1Y,KAAK6X,aAAcR,UAAY,qDAAuDvW,EAAQ,wBAA0BmZ,EAAS,IAAMC,EACvIla,KAAKgY,aAAcX,UAAY,qDAAuDvW,EAAQ,wBAA0BmZ,EACxHja,KAAKiY,aAAcZ,UAAY,qDAAuDvW,EAAQ,wBAA0BmZ,CACzH,CAKO,WAAAL,GACN,MAAMO,EAAYna,KAAKqB,IAAI,aAC3B,IAAK8Y,EACJ,OAEDna,KAAKiY,aAAcG,UAAY,GAC/BpY,KAAK2Z,cAAgB,GACrB,MAAMlR,EAAQzI,KAAKqB,IAAI,QAAS,IAC1B2V,EAAmBmD,EAAUnD,mBAC7BG,EAAuBgD,EAAUhD,uBAEvC,OAAYnX,KAAK+Z,gBAAiBC,IACjCA,EAAEvD,SAAS,IAGZzW,KAAK+Z,eAAenS,OAAS,EAE7B,OAAYa,GAAQzB,IAEnB,GAAIA,EAAKtF,SAAoD,GAA1CsV,EAAiB5O,QAAQpB,EAAKtF,QAChD,OAGD,GAAIsF,EAAK0Q,aAAgE,GAAlDP,EAAqB/O,QAAQpB,EAAK0Q,YACxD,OAGD,MAAM0C,EAAK7N,SAASuG,cAAc,MAClCsH,EAAGlC,aAAa,OAAQ,YAExBkC,EAAG/C,UAAY,oDAAsDrQ,EAAK3E,KACtE2E,EAAKtF,SACR0Y,EAAG/C,WAAa,wBAA0BrQ,EAAKtF,QAGhD,MAAMuG,EAAIsE,SAASuG,cAAc,KAEjC,IAAIuH,EAAYra,KAAKoK,GAAG,UACpBpD,EAAKyQ,QACRxP,EAAEmQ,UAAYpR,EAAKyQ,MACnB4C,GAAa,IAAMrT,EAAKyQ,OAGrBzQ,EAAK2Q,WACR1P,EAAEmQ,WAAa,yCAA6CpR,EAAK2Q,SAAW,UAC5E0C,GAAa,KAAOrT,EAAK2Q,SAAW,KAGjC3Q,EAAKsT,UACRta,KAAK+Z,eAAevY,KAAK,mBAAwByG,EAAG,SAAUoR,IAC7DrS,EAAKsT,SAAUC,KAAKvT,EAAKwT,gBAAkBxa,KAAK,KAEjDiI,EAAEiQ,aAAa,WAAYlY,KAAKgB,MAAMqX,SAASC,aAEvCtR,EAAKtF,QAAUyY,IACvBna,KAAK+Z,eAAevY,KAAK,mBAAwByG,EAAG,SAAUoR,IAC7DrZ,KAAK4Y,aAAa5R,EAAK,KAExBhH,KAAK+Z,eAAevY,KAAK,mBAAwByG,EAAG,SAAUoR,IAC7DrZ,KAAKkZ,iBAAiBlS,EAAK,KAE5BhH,KAAK+Z,eAAevY,KAAK,mBAAwByG,EAAG,QAASoR,IAC5DrZ,KAAKya,gBAAgBzT,EAAK,KAE3BiB,EAAEiQ,aAAa,WAAYlY,KAAKgB,MAAMqX,SAASC,YAC/CrQ,EAAEiQ,aAAa,aAAcmC,IAG9BrT,EAAKkM,QAAUjL,EACfmS,EAAGpH,YAAY/K,GACfjI,KAAKiY,aAAcjF,YAAYoH,GAC/Bpa,KAAK2Z,cAAenY,KAAK4Y,EAAG,GAE9B,CAEQ,YAAAxB,CAAa5R,GACpB,MAAMmT,EAAYna,KAAKqB,IAAI,aACvBrB,KAAKqB,IAAI,cACZrB,KAAK6W,QAEa,SAAf7P,EAAKtF,OACRyY,EAAUzX,QAGVyX,EAAUpY,SAASiF,EAAKtF,OAE1B,CAEQ,gBAAAwX,CAAiBlS,GACpBA,GAAQhH,KAAK2Y,cACZ3Y,KAAK2Y,cACR3Y,KAAK2Y,YAAYzF,QAASmE,UAAY,IAEvCrX,KAAK2Y,YAAc3R,EACnBA,EAAKkM,QAASmE,UAAY,2BAC1BrQ,EAAKkM,QAASwH,QAEhB,CAEQ,eAAAD,CAAgBzT,GACvBA,EAAKkM,QAASmE,UAAY,GACtBrQ,GAAQhH,KAAK2Y,cAChB3Y,KAAK2Y,iBAAc3Q,GAEpBhI,KAAKyU,YAAW,KACVlI,SAASkM,eAAkB,WAAgBzY,KAAKqB,IAAI,aAAekL,SAASkM,gBAChFzY,KAAK6W,O,GAEJ,GAEJ,CAOO,cAAA4C,GACN,MAAMkB,ED9fO,SAASzH,EAA4B0H,EAAYC,GAI/D,MAAMC,EAAKF,EAAKG,gBAEhB,IAAKzD,EAAO,CACX,MAAMqD,EAAW,IAAI,KAAc,CAWlC,IAAI,EAAAhF,UAAUzC,EAAS,qBAAsB,CAC5C,MAAS4H,EAAGzZ,IAAI,uBAAwB+E,QACxC,YAAa,UAGd,IAAI,EAAAuP,UAAUzC,EAAS,uBAAwB,CAC9C,aAAc,aACd,WAAc,0BAGf,IAAI,EAAAyC,UAAUzC,EAAS,uBAAwB,CAC9C,QAAW,QACX,OAAU,YAGX,IAAI,EAAAyC,UAAUzC,EAAS,+BAAgC,CACtD,MAAS4H,EAAGzZ,IAAI,YAAa+E,QAC7B,gBAAiB,aAAe0U,EAAGzZ,IAAI,uBAAwB+E,UAGhE,IAAI,EAAAuP,UAAUzC,EAAS,0BAA2B,CACjD,MAAS4H,EAAGzZ,IAAI,YAAa+E,QAC7B,YAAa,UAGd,IAAI,EAAAuP,UAAUzC,EAAS,oDAAqD,CAC3E,OAAU,YAEX,IAAI,EAAAyC,UAAUzC,EAAS,0DAA2D,CACjF,WAAc,YAGf,IAAI,EAAAyC,UAAUzC,EAAS,qBAAsB,CAC5C,SAAY,WACZ,UAAW,OAUZ,IAAI,EAAAyC,UAAUzC,EAAS,qBAAsB,CAC5C,MAAS,OACT,OAAU,OACV,SAAY,WACZ,OAAU,MACV,QAAW,UACX,gBAAiB,MACjB,QAAW,MACX,WAAc,6BACd,qBAAsB,MAGvB,IAAI,EAAAyC,UAAUzC,EAAS,iGAAkG,CACxH,WAAc4H,EAAGzZ,IAAI,wBAAyB+E,QAC9C,QAAW,MAGZ,IAAI,EAAAuP,UAAUzC,EAAS,0BAA2B,CACjD,KAAQ4H,EAAGzZ,IAAI,uBAAwB+E,UAGxC,IAAI,EAAAuP,UAAUzC,EAAS,qBAAsB,CAC5C,QAAW,OACX,aAAc,OACd,kBAAmB,OACnB,OAAU,MACV,WAAc4H,EAAGzZ,IAAI,mBAAoB+E,QACzC,QAAW,QACX,OAAU,aAAe0U,EAAGzZ,IAAI,yBAA0B+E,QAC1D,gBAAiB,QAGlB,IAAI,EAAAuP,UAAUzC,EAAS,6CAA8C,CACpE,QAAW,UAGZ,IAAI,EAAAyC,UAAUzC,EAAS,qBAAsB,CAAC,GAG9C,IAAI,EAAAyC,UAAUzC,EAAS,uBAAwB,CAC9C,QAAW,aAGZ,IAAI,EAAAyC,UAAUzC,EAAS,4EAA6E,CACnG,WAAc4H,EAAGzZ,IAAI,wBAAyB+E,UAG/C,IAAI,EAAAuP,UAAUzC,EAAS,qIAAsI,CAC5J,KAAQ,MAGT,IAAI,EAAAyC,UAAUzC,EAAS,wIAAyI,CAC/J,MAAS,MAGV,IAAI,EAAAyC,UAAUzC,EAAS,oIAAqI,CAC3J,IAAO,MAGR,IAAI,EAAAyC,UAAUzC,EAAS,6IAA8I,CACpK,OAAU,MAGX,IAAI,EAAAyC,UAAUzC,EAAS,6CAA8C,CACpE,cAAe,SAGhB,IAAI,EAAAyC,UAAUzC,EAAS,8CAA+C,CACrE,eAAgB,WAUlBoE,EAAQ,IAAI,MAAgB,KAC3BA,OAAQtP,EACR2S,EAASlE,SAAS,G,CAIpB,OAAOa,EAAM0D,WACd,CC2WmB,CAAa,gBAAqBhb,KAAKgB,MAAMiF,KAAMjG,KAAKgB,OACzEhB,KAAKuB,WAAWC,KAAKmZ,GACrB3a,KAAK6Z,aAAec,CAKrB,CAKO,IAAA/D,GACN5W,KAAKyU,YAAW,KACfzU,KAAK0Y,QAAS,EACV1Y,KAAKqB,IAAI,oBACZrB,KAAKgB,MAAM6C,UAAU0V,qBAAsB,GAE5CvZ,KAAKmY,mBACLnY,KAAKmC,OAAOC,SAAS,aAAc,CAClCC,KAAM,aACNC,OAAQtC,MACP,GACA,EACJ,CAKO,KAAA6W,GACN7W,KAAK0Y,QAAS,EACV1Y,KAAKqB,IAAI,oBACZrB,KAAKgB,MAAM6C,UAAU0V,qBAAsB,GAE5C,SACAvZ,KAAKmY,mBACLnY,KAAKmC,OAAOC,SAAS,aAAc,CAClCC,KAAM,aACNC,OAAQtC,MAEV,CAKO,MAAA6Y,GACF7Y,KAAK0Y,OACR1Y,KAAK6W,QAGL7W,KAAK4W,MAEP,EAhbA,qC,gDAAkC,kBAClC,sC,gDAA0C/W,EAAA,GAAOuX,WAAWpC,OAAO,CAACuC,EAAcF,c,kCCxD5E,MAAM4D,UAAkBpb,EAAA,GAA/B,c,oBAQC,yC,yDACA,uC,yDACA,0C,yDACA,0C,iDAAgC,GA2KjC,CAtKW,SAAAC,GACTC,MAAMD,YACNE,KAAKC,eAAe,QAAS,KAC7BD,KAAKgB,MAAMC,YAAYjB,KACxB,CAEO,cAAAkB,GACNnB,MAAMmB,iBAEFlB,KAAKmB,QAAQ,gBAChBnB,KAAKkb,cAEP,CAMa,IAAAtE,G,+CAGZ5W,KAAKyU,YAAW,KACfzU,KAAKgB,MAAM6C,UAAU0V,qBAAsB,CAAK,GAC9C,KAEH,MAAM4B,QAAmBnb,KAAKob,gBAC9BD,EAAWE,OACXrb,KAAKsb,SAAUC,KAAK,GAChBvb,KAAKqB,IAAI,gBACZ8Z,EAAWK,aAAaxb,KAAKqB,IAAI,eAEnC,G,CAEa,YAAA6Z,G,+CACZ,MAAMC,QAAmBnb,KAAKob,gBAC1Bpb,KAAKqB,IAAI,iBACZrB,KAAKyb,aAAc,EACnBN,EAAWO,YAAY1b,KAAKqB,IAAI,gBAElC,G,CAKa,KAAAwV,G,sDAEa7W,KAAKob,iBAClBvE,QACZ7W,KAAK2b,iBAAc3T,CACpB,G,CAMa,MAAA4T,G,+CACZ5b,KAAKgB,MAAM6C,UAAU0V,qBAAsB,EAC3C,MAAM4B,QAAmBnb,KAAKob,gBAC9Bpb,KAAKsb,SAAUD,KAAK,GACpBF,EAAYtE,QACZ7W,KAAK2b,iBAAc3T,CAEpB,G,CAKO,KAAA6T,GACN7b,KAAKsB,IAAI,mBAAe0G,GACpBhI,KAAKsb,UACRtb,KAAKsb,SAASha,IAAI,MAAO,GAE3B,CAEa,MAAAuX,G,sDACa7Y,KAAKob,iBACf1C,OACd1Y,KAAK6W,QAGL7W,KAAK4W,MAEP,G,CAEO,OAAAH,GACN1W,MAAM0W,UACFzW,KAAK2b,aAAe3b,KAAK2b,YAAYjD,QACxC1Y,KAAK2b,YAAY9E,OAEnB,CAEc,UAAAiF,G,+CAkBb,GAfK9b,KAAK+b,aACT/b,KAAK+b,WAAa/b,KAAKgB,MAAMoY,UAAU4C,SAASxa,KAAKya,EAAA,EAAUC,IAAIlc,KAAKgB,MAAO,CAC9EoD,MAAO,KACPE,OAAQ,KACR6X,MAAOnc,KAAKqB,IAAI,SAChB+a,qBAAqB,KAGtBpc,KAAKsb,SAAWtb,KAAK+b,WAAWC,SAASxa,KAAK6a,EAAA,EAAQH,IAAIlc,KAAKgB,MAAO,CACrEoD,MAAO,KACPE,OAAQ,UAKLtE,KAAK2b,YAAa,CACtB,MAAMW,QAAkBtc,KAAKuc,eACvBlZ,EAASrD,KAAK+b,WAAWhY,SAAST,YAClC6X,EAAa,IAAImB,EAAUE,WAAWnZ,GAE5C8X,EAAWsB,gBAAgBC,aAAe,QAC1CvB,EAAWsB,gBAAgBE,OAAS,GACpCxB,EAAWyB,WAAavZ,EAAOoQ,cAG/B,MAAMoJ,EAAiB7c,KAAKqB,IAAI,iBAAkB,CAAC,GACnD,OAAawb,GAAgB,CAAChb,EAAKC,KAClCqZ,EAAW2B,SAASjb,GAAOC,CAAK,IAGjC,MAAMib,EAAsB/c,KAAKqB,IAAI,sBAAuB,CAAC,GAC7D,OAAa0b,GAAqB,CAAClb,EAAKC,KACvCqZ,EAAWsB,gBAAgB5a,GAAOC,CAAK,IAGxC9B,KAAKuB,WAAWC,KAAK,mBAAwB2Z,EAAY,SAAS,KACjEnb,KAAKgB,MAAM6C,UAAU0V,qBAAsB,EAC3CvZ,KAAKsb,SAAUD,KAAK,GACpBrb,KAAK2b,iBAAc3T,CAAS,KAG7BhI,KAAKuB,WAAWC,KAAK,mBAAwB2Z,EAAY,UAAWrJ,IACnD9R,KAAKsb,SACbha,IAAI,MAAOwQ,EAAMkL,SACpBhd,KAAKyb,aACTzb,KAAKsB,IAAI,cAAewQ,EAAMmL,OAE/Bjd,KAAKyb,aAAc,CAAK,KAGzBzb,KAAK2b,YAAcR,C,CAErB,G,CAKc,YAAAoB,G,+CACb,aAAa,8BACd,G,CAQa,aAAAnB,G,+CAEZ,aADMpb,KAAK8b,aACJ9b,KAAK2b,WACb,G,EAnLA,qC,gDAAkC,cAClC,sC,gDAA0C9b,EAAA,GAAOuX,WAAWpC,OAAO,CAACiG,EAAU5D,a,iFCzFxE,MAAM6F,E", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/plugins/exporting/Exporting.ts", "webpack://@amcharts/amcharts5/./src/.internal/plugins/exporting/ExportingCSS.ts", "webpack://@amcharts/amcharts5/./src/.internal/plugins/exporting/ExportingMenu.ts", "webpack://@amcharts/amcharts5/./src/.internal/plugins/exporting/Annotator.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/plugins/exporting.js"], "sourcesContent": ["import type { ExportingMenu } from \"./ExportingMenu\"\nimport type { TimeUnit } from \"../../core/util/Time\";\n\nimport { Entity, IEntitySettings, IEntityPrivate, IEntityEvents } from \"../../core/util/Entity\"\nimport { Color } from \"../../core/util/Color\";\nimport { Root } from \"../../core/Root\"\nimport { StyleRule } from \"../../core/util/Utils\"\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $object from \"../../core/util/Object\";\nimport * as $type from \"../../core/util/Type\";\nimport * as $utils from \"../../core/util/Utils\";\n\nexport interface IFile {\n\tpath: string;\n\tbytes: string;\n}\n\nexport interface IFont {\n\tname: string;\n\tnormal: IFile;\n\tbold?: IFile;\n\titalics?: IFile;\n\tbolditalics?: IFile;\n}\n\n/**\n * This is used to cache the pdfmake loading.\n *\n * @ignore\n */\nlet pdfmakePromise: Promise<any>;\n\n/**\n * Loads pdfmake dynamic module\n *\n * This is an asynchronous function. Check the description of `getImage()`\n * for description and example usage.\n *\n * @ignore\n */\nasync function _pdfmake(): Promise<any> {\n\tlet a = await Promise.all([\n\t\timport(/* webpackChunkName: \"pdfmake\" */ \"pdfmake/build/pdfmake.js\"),\n\t\timport(/* webpackChunkName: \"pdfmake\" */ \"./pdfmake/vfs_fonts\")\n\t]);\n\n\tlet pdfmake = a[0].default;\n\tlet vfs_fonts = a[1].default;\n\tconst global = <any>window;\n\tglobal.pdfMake = global.pdfMake || {};\n\tglobal.pdfMake.vfs = vfs_fonts;\n\tpdfmake.vfs = vfs_fonts;\n\treturn pdfmake;\n}\n\nexport type ExportingTypes = \"image\" | \"data\" | \"print\";\nexport type ExportingFormats = \"png\" | \"jpg\" | \"canvas\" | \"pdf\" | \"xlsx\" | \"csv\" | \"json\" | \"html\" | \"pdfdata\" | \"print\";\nexport type ExportingImageFormats = \"png\" | \"jpg\";\n\nexport interface IExportingImageSource {\n\n\t/**\n\t * A root object of an extra chart to add in export.\n\t */\n\tsource: Root;\n\n\t/**\n\t * Top margin in pixels.\n\t */\n\tmarginTop?: number;\n\n\t/**\n\t * Right margin in pixels.\n\t */\n\tmarginRight?: number;\n\n\t/**\n\t * Bottom margin in pixels.\n\t */\n\tmarginBottom?: number;\n\n\t/**\n\t * Left margin in pixels.\n\t */\n\tmarginLeft?: number;\n\n\t/**\n\t * Position to place extra image in releation to the main chart.\n\t *\n\t * @default \"bottom\"\n\t */\n\tposition?: \"left\" | \"right\" | \"top\" | \"bottom\";\n\n\t/**\n\t * Crop extra image if it's larger than the main chart.\n\t */\n\tcrop?: boolean;\n\n}\n\nexport interface IExportingSettings extends IEntitySettings {\n\n\t/**\n\t * A reference to [[ExportingMenu]] object.\n\t */\n\tmenu?: ExportingMenu;\n\n\t/**\n\t * Export will try to determine background color based on the DOM styles.\n\t *\n\t * You can use this setting to explicitly specify background color for\n\t * exported images.\n\t */\n\tbackgroundColor?: Color;\n\n\t/**\n\t * Opacity of the exported image background.\n\t *\n\t * * 0 - fully transparent.\n\t * * 1 - fully opaque (default).\n\t *\n\t * NOTE: some image formats like JPEG do not support transparency.\n\t *\n\t * @since 5.2.34\n\t */\n\tbackgroundOpacity?: number;\n\n\t/**\n\t * A string to prefix exported files with.\n\t *\n\t * @default \"chart\"\n\t */\n\tfilePrefix?: string;\n\n\t/**\n\t * Chart title. Used for print, PDF and Excel exports.\n\t */\n\ttitle?: string;\n\n\t/**\n\t * Charset to use for export.\n\t *\n\t * @default \"utf-8\"\n\t */\n\tcharset?: string;\n\n\t/**\n\t * Fields to include in data export.\n\t *\n\t * Key - field in data.\n\t * Value - column name.\n\t */\n\tdataFields?: { [index: string]: string },\n\n\t/**\n\t * Specifies the order of fields to export in data.\n\t */\n\tdataFieldsOrder?: string[],\n\n\t/**\n\t * Fields in data that are numeric.\n\t */\n\tnumericFields?: string[],\n\n\t/**\n\t * Use this number format on numeric values.\n\t */\n\tnumberFormat?: string | Intl.NumberFormatOptions,\n\n\t/**\n\t * Fields in data that have date/time value.\n\t */\n\tdateFields?: string[],\n\n\t/**\n\t * Use this date format on date values.\n\t */\n\tdateFormat?: string | Intl.DateTimeFormatOptions,\n\n\t/**\n\t * Fields in data that need to be formatted as \"duration\" as per `durationFormat`.\n\t *\n\t * @since 5.0.16\n\t */\n\tdurationFields?: string[],\n\n\t/**\n\t * Format to use when formatting values in `durationFields`.\n\t *\n\t * If not set, will use `durationFormat` as set in [[DurationFormatter]] of\n\t * the root element.\n\t *\n\t * @since 5.0.16\n\t */\n\tdurationFormat?: string,\n\n\t/**\n\t * Time unit to assume duration values are in.\n\t *\n\t * If not set, will use `baseUnit` as set in [[DurationFormatter]] of\n\t * the root element.\n\t *\n\t * @since 5.0.16\n\t */\n\tdurationUnit?: TimeUnit;\n\n\t/**\n\t * Include these images or other charts in image exports.\n\t */\n\textraImages?: Array<Root | IExportingImageSource>;\n\n\t/**\n\t * Data to export.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/#Exporting_data} for more info\n\t */\n\tdataSource?: any;\n\n\t/**\n\t * PNG format options.\n\t */\n\tpngOptions?: IExportingImageOptions;\n\n\t/**\n\t * JPEG format options.\n\t */\n\tjpgOptions?: IExportingImageOptions;\n\n\t/**\n\t * Canvas format options.\n\t */\n\tcanvasOptions?: IExportingImageOptions;\n\n\t/**\n\t * PDF format options.\n\t */\n\tpdfOptions?: IExportingPDFOptions;\n\n\t/**\n\t * PDF with data table format options.\n\t */\n\tpdfdataOptions?: IExportingDataOptions;\n\n\t/**\n\t * XSLX format options.\n\t */\n\txlsxOptions?: IExportingXLSXOptions;\n\n\t/**\n\t * CSV format options.\n\t */\n\tcsvOptions?: IExportingCSVOptions;\n\n\t/**\n\t * JSON format options.\n\t */\n\tjsonOptions?: IExportingJSONOptions;\n\n\t/**\n\t * HTML format options.\n\t */\n\thtmlOptions?: IExportingHTMLOptions;\n\n\t/**\n\t * Print options.\n\t */\n\tprintOptions?: IExportingPrintOptions;\n\n}\n\nexport interface IExportingPrivate extends IEntityPrivate {\n}\n\nexport interface IExportEvent {\n\n\t/**\n\t * Format.\n\t */\n\tformat: ExportingFormats,\n\n\t/**\n\t * Format options.\n\t */\n\toptions: IExportingFormatOptions\n\n}\n\nexport interface IExportingEvents extends IEntityEvents {\n\n\t/**\n\t * Invoked when export starts.\n\t */\n\texportstarted: IExportEvent;\n\n\t/**\n\t * Invoked when export finishes.\n\t */\n\texportfinished: IExportEvent;\n\n\t/**\n\t * Invoked when download of the export starts.\n\t */\n\tdownloadstarted: IExportEvent & {\n\t\tfileName: string,\n\t};\n\n\t/**\n\t * Invoked when print starts.\n\t */\n\tprintstarted: IExportEvent;\n\n\t/**\n\t * Invoked when data finishes pre-processing for export.\n\t */\n\tdataprocessed: IExportEvent & {\n\t\tdata: any\n\t}\n\n\t/**\n\t * Invoked when XLSX export finishes preparing a workbook.\n\t *\n\t * At this point it can still be modified for export.\n\t */\n\tworkbookready: IExportEvent & {\n\t\tworkbook: any,\n\t\tworkbookOptions: any,\n\t\txlsx: any\n\t}\n\n\t/**\n\t * Invoked when PDF export finishes preparing a document.\n\t *\n\t * At this point it can still be modified for export.\n\t */\n\tpdfdocready: IExportEvent & {\n\t\tdoc: any\n\t}\n\n}\n\nexport interface IExportingFormatOptions {\n\n\t/**\n\t * If set to `true`, this format will not appear in [[ExportMenu]].\n\t */\n\tdisabled?: boolean;\n\n}\n\nexport interface IExportingImageOptions extends IExportingFormatOptions {\n\n\t/**\n\t * Quality of the exported image: 0 to 1.\n\t */\n\tquality?: number;\n\n\t/**\n\t * Export images with hardware resolution (`false`), or the way they appear\n\t * on screen (`true`).\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/exporting-images/#Pixel_ratio} for more info\n\t * @default false\n\t */\n\tmaintainPixelRatio?: boolean;\n\n\t/**\n\t * Minimal width of exported image, in pixels.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/exporting-images/#Sizing_exported_image} for more info\n\t */\n\tminWidth?: number;\n\n\t/**\n\t * Maximal width of exported image, in pixels.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/exporting-images/#Sizing_exported_image} for more info\n\t */\n\tmaxWidth?: number;\n\n\t/**\n\t * Minimal height of exported image, in pixels.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/exporting-images/#Sizing_exported_image} for more info\n\t */\n\tminHeight?: number;\n\n\t/**\n\t * Maximal height of exported image, in pixels.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/exporting-images/#Sizing_exported_image} for more info\n\t */\n\tmaxHeight?: number;\n\n}\n\nexport interface IExportingPrintOptions extends IExportingImageOptions {\n\n\t/**\n\t * A delay in milliseconds to wait before initiating print.\n\t *\n\t * This delay is necessary to ensure DOM is prepared and repainted before\n\t * print dialog kicks in.\n\t *\n\t * @default 500\n\t */\n\tdelay?: number;\n\n\t/**\n\t * Method to use for printing.\n\t *\n\t * If one fails for your particular setup, try the other.\n\t *\n\t * \"css\" - inserts dynamic CSS that hides everything, except the image being printed.\n\t * \"iframe\" - creates a dynamic `<iframe>` with the image, then prints it.\n\t *\n\t * @default \"iframe\"\n\t */\n\tprintMethod?: \"css\" | \"iframe\";\n\n\t/**\n\t * Image format to use for printing.\n\t *\n\t * @default \"png\"\n\t */\n\timageFormat?: \"png\" | \"jpg\";\n\n}\n\n/**\n * Available PDF page sizes.\n */\nexport type pageSizes = \"4A0\" | \"2A0\" | \"A0\" | \"A1\" | \"A2\" | \"A3\" | \"A4\" | \"A5\" | \"A6\" | \"A7\" | \"A8\" | \"A9\" | \"A10\" |\n\t\"B0\" | \"B1\" | \"B2\" | \"B3\" | \"B4\" | \"B5\" | \"B6\" | \"B7\" | \"B8\" | \"B9\" | \"B10\" |\n\t\"C0\" | \"C1\" | \"C2\" | \"C3\" | \"C4\" | \"C5\" | \"C6\" | \"C7\" | \"C8\" | \"C9\" | \"C10\" |\n\t\"RA0\" | \"RA1\" | \"RA2\" | \"RA3\" | \"RA4\" |\n\t\"SRA0\" | \"SRA1\" | \"SRA2\" | \"SRA3\" | \"SRA4\" |\n\t\"EXECUTIVE\" | \"FOLIO\" | \"LEGAL\" | \"LETTER\" | \"TABLOID\";\n\nexport interface IExportingPDFOptions extends IExportingImageOptions {\n\n\t/**\n\t * Include data into PDF\n\t */\n\tincludeData?: boolean;\n\n\t/**\n\t * An image format to use for embedded images in PDF.\n\t *\n\t * See `imageFormats` in [[Export_module]].\n\t */\n\timageFormat?: \"png\" | \"jpg\";\n\n\t/**\n\t * Font size to use for all texts.\n\t */\n\tfontSize?: number;\n\n\t/**\n\t * Alignment of the chart image in PDF.\n\t *\n\t * Supported options: `\"left\"` (default), `\"center\"`, `\"right\"`.\n\t *\n\t * @default left\n\t */\n\talign?: \"left\" | \"center\" | \"middle\";\n\n\t/**\n\t * Whether to add a URL of the web page the chart has been exported from.\n\t *\n\t * @default true\n\t */\n\taddURL?: boolean;\n\n\t/**\n\t * Page size of the exported PDF.\n\t */\n\tpageSize?: pageSizes;\n\n\t/**\n\t * Page orientation.\n\t */\n\tpageOrientation?: \"landscape\" | \"portrait\";\n\n\t/**\n\t * Page margins.\n\t *\n\t * Can be one of the following:\n\t *\n\t * A single number, in which case it will act as margin setting\n\t * for all four edges of the page.\n\t *\n\t * An array of two numbers `[ horizontal, vertical ]`.\n\t *\n\t * An array of four numbers `[ left, top, right, bottom ]`.\n\t */\n\tpageMargins?: number | number[];\n\n\t/**\n\t * Font which should be used for the export.\n\t *\n\t * Default font used for PDF includes only Latin-based and Cyrilic\n\t * characters. If you are exporting text in other languages, you might need\n\t * to use some other export font.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/exporting-pdf/#Fonts} for more info\n\t */\n\tfont?: IFont;\n\n\t/**\n\t * Additional optional fonts which can be used on individual elements.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/exporting-pdf/#Fonts} for more info\n\t */\n\textraFonts?: Array<IFont>;\n\n}\n\nexport interface IExportingDataOptions extends IExportingFormatOptions {\n\n\t/**\n\t * Replace empty values with this string.\n\t */\n\temptyAs?: string;\n\n\t/**\n\t * Use timestamps instead of formatted dates.\n\t *\n\t * @default false\n\t */\n\tuseTimestamps?: boolean;\n\n\t/**\n\t * Use client's locale when formatting dates.\n\t *\n\t * @default false\n\t */\n\tuseLocale?: boolean;\n\n\t/**\n\t * If set to `true` will pivot the able so that columns are horizontal.\n\t */\n\tpivot?: boolean;\n\n\t/**\n\t * Will add a line with column names in CSV/HTML/PDF tables.\n\t */\n\taddColumnNames?: boolean;\n\n}\n\nexport interface IExportingJSONOptions extends IExportingDataOptions {\n\n\t/**\n\t * If set to a number, each line will be indented by X spaces, maintaining\n\t * hierarchy.\n\t *\n\t * If set to a string, will use that string to indent.\n\t *\n\t * @default 2\n\t */\n\tindent?: string | number;\n\n\t/**\n\t * If set to `true` and `dataFields` are set to `true`, will rename keys in\n\t * data.\n\t *\n\t * @default true\n\t */\n\trenameFields?: boolean;\n\n}\n\nexport interface IExportingCSVOptions extends IExportingDataOptions {\n\n\t/**\n\t * Column separator.\n\t *\n\t * @default \",\"\n\t */\n\tseparator?: string;\n\n\t/**\n\t * Force all values to be included in quotes, including numeric.\n\t *\n\t * @default false\n\t */\n\tforceQuotes?: boolean;\n\n\t/**\n\t * Reverse order of the records in data.\n\t *\n\t * @default false\n\t */\n\treverse?: boolean;\n\n\t/**\n\t * Add BOM character to output file, so that it can be used with UTF-8\n\t * characters properly in Excel.\n\t *\n\t * @default false\n\t * @since 5.1.0\n\t */\n\taddBOM?: boolean;\n\n}\n\nexport interface IExportingHTMLOptions extends IExportingDataOptions {\n\n\t/**\n\t * A `class` attribute for `<table>` tag.\n\t */\n\ttableClass?: string;\n\n\t/**\n\t * A `class` attribute for `<tr>` tags.\n\t */\n\trowClass?: string;\n\n\t/**\n\t * A `class` attribute for `<th>` tags.\n\t */\n\theaderClass?: string;\n\n\t/**\n\t * A `class` attribute for `<td>` tags.\n\t */\n\tcellClass?: string;\n\n}\n\nexport interface IExportingXLSXOptions extends IExportingDataOptions {\n\t// @todo\n\t//sheets?: string[];\n}\n\n/**\n * A plugin that can be used to export chart snapshots and data.\n *\n * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/} for more info\n */\nexport class Exporting extends Entity {\n\tpublic static className: string = \"Exporting\";\n\tpublic static classNames: Array<string> = Entity.classNames.concat([Exporting.className]);\n\n\tdeclare public _settings: IExportingSettings;\n\tdeclare public _privateSettings: IExportingPrivate;\n\tdeclare public _events: IExportingEvents;\n\n\t//public extraImages: Array<Root | IExportingImageSource> = [];\n\t//public dataSources: any[] = [];\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis._setRawDefault(\"filePrefix\", \"chart\");\n\t\tthis._setRawDefault(\"charset\", \"utf-8\");\n\t\tthis._setRawDefault(\"numericFields\", []);\n\t\tthis._setRawDefault(\"dateFields\", []);\n\t\tthis._setRawDefault(\"durationFields\", []);\n\t\tthis._setRawDefault(\"extraImages\", []);\n\t\tthis._setRawDefault(\"pngOptions\", { quality: 1, maintainPixelRatio: false });\n\t\tthis._setRawDefault(\"jpgOptions\", { quality: 0.8, maintainPixelRatio: false });\n\t\tthis._setRawDefault(\"printOptions\", { quality: 1, maintainPixelRatio: false, delay: 500, printMethod: \"iframe\", imageFormat: \"png\" });\n\t\tthis._setRawDefault(\"jsonOptions\", { indent: 2, renameFields: true });\n\t\tthis._setRawDefault(\"csvOptions\", { separator: \",\", addColumnNames: true, emptyAs: \"\", addBOM: true });\n\t\tthis._setRawDefault(\"htmlOptions\", { emptyAs: \"-\", addColumnNames: true });\n\t\tthis._setRawDefault(\"xlsxOptions\", { emptyAs: \"\", addColumnNames: true });\n\t\tthis._setRawDefault(\"pdfOptions\", { fontSize: 14, imageFormat: \"png\", align: \"left\", addURL: true });\n\t\tthis._setRawDefault(\"pdfdataOptions\", { emptyAs: \"\", addColumnNames: true });\n\n\t\tthis._root.addDisposer(this);\n\t}\n\n\tpublic _beforeChanged() {\n\t\tsuper._beforeChanged();\n\n\t\tif (this.isDirty(\"menu\")) {\n\t\t\tconst menu = this.get(\"menu\");\n\t\t\tif (menu) {\n\t\t\t\tmenu.set(\"exporting\", this);\n\t\t\t\tthis._disposers.push(menu);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _getFormatOptions(format: ExportingFormats, options?: IExportingFormatOptions): IExportingFormatOptions {\n\t\tconst newOptions: any = $object.copy((<any>this).get(format + \"Options\", {}));\n\t\tif (options) {\n\t\t\t$object.each(options, (key, value) => {\n\t\t\t\tnewOptions[key] = value;\n\t\t\t});\n\t\t}\n\t\treturn newOptions;\n\t}\n\n\t/**\n\t * Triggers a download of the chart/data in specific format.\n\t *\n\t * @param  format         Format\n\t * @param  customOptions  Format options\n\t */\n\tpublic async download(format: ExportingFormats, customOptions?: IExportingFormatOptions) {\n\t\tconst ext = format == \"pdfdata\" ? \"pdf\" : format;\n\t\tconst fileName = this.get(\"filePrefix\", \"chart\") + \".\" + ext;\n\t\tconst options = this._getFormatOptions(format, customOptions);\n\t\tthis.events.dispatch(\"downloadstarted\", {\n\t\t\ttype: \"downloadstarted\",\n\t\t\tformat: format,\n\t\t\toptions: options,\n\t\t\tfileName: fileName,\n\t\t\ttarget: this\n\t\t});\n\t\tconst uri = await this.export(format, options);\n\t\tthis.streamFile(uri, fileName, (options && (<IExportingCSVOptions>options).addBOM));\n\t}\n\n\t/**\n\t * Triggers print of the chart.\n\t *\n\t * @param  customOptions  Print options\n\t */\n\tpublic async print(customOptions?: IExportingPrintOptions) {\n\t\tconst options = <IExportingPrintOptions>this._getFormatOptions(\"print\", customOptions);\n\t\tthis.events.dispatch(\"printstarted\", {\n\t\t\ttype: \"printstarted\",\n\t\t\tformat: \"print\",\n\t\t\toptions: options,\n\t\t\ttarget: this\n\t\t});\n\t\tconst uri = await this.export(options.imageFormat || \"png\", options);\n\t\tthis.initiatePrint(uri, options, this.get(\"title\"));\n\t}\n\n\t/**\n\t * Returns data uri of the chart/data in specific format.\n\t *\n\t * @param          format  Format\n\t * @param   customOptions  Format options\n\t * @return                 Promise\n\t */\n\tpublic async export(format: ExportingFormats, customOptions?: IExportingFormatOptions): Promise<string> {\n\t\tconst options = this._getFormatOptions(format, customOptions);\n\t\tthis.events.dispatch(\"exportstarted\", {\n\t\t\ttype: \"exportstarted\",\n\t\t\tformat: format,\n\t\t\toptions: options,\n\t\t\ttarget: this\n\t\t});\n\n\t\tlet promise: Promise<string> | string = \"\";\n\t\tswitch (format) {\n\t\t\tcase \"png\":\n\t\t\tcase \"jpg\":\n\t\t\t\tthis._root._runTickerNow();\n\t\t\t\tpromise = this.exportImage(format, options);\n\t\t\t\tbreak;\n\t\t\tcase \"json\":\n\t\t\t\tpromise = this.exportJSON(options);\n\t\t\t\tbreak;\n\t\t\tcase \"csv\":\n\t\t\t\tpromise = this.exportCSV(options);\n\t\t\t\tbreak;\n\t\t\tcase \"html\":\n\t\t\t\tpromise = this.exportHTML(options);\n\t\t\t\tbreak;\n\t\t\tcase \"xlsx\":\n\t\t\t\tpromise = this.exportXLSX(options);\n\t\t\t\tbreak;\n\t\t\tcase \"pdf\":\n\t\t\t\tthis._root._runTickerNow();\n\t\t\t\tpromise = this.exportPDF(options);\n\t\t\t\tbreak;\n\t\t\tcase \"pdfdata\":\n\t\t\t\tpromise = this.exportPDFData(options);\n\t\t\t\tbreak;\n\t\t}\n\t\tthis.events.dispatch(\"exportfinished\", {\n\t\t\ttype: \"exportfinished\",\n\t\t\tformat: format,\n\t\t\toptions: options,\n\t\t\ttarget: this\n\t\t});\n\t\treturn promise;\n\t}\n\n\t/**\n\t * ==========================================================================\n\t * Images\n\t * ==========================================================================\n\t */\n\n\t/**\n\t * Returns chart image as a data:uri.\n\t *\n\t * @param   format         Image format\n\t * @param   customOptions  Format options\n\t * @return                 Promise\n\t */\n\tpublic async exportImage(format: ExportingImageFormats, customOptions?: IExportingImageOptions): Promise<string> {\n\t\tconst options: any = this._getFormatOptions(format, customOptions);\n\t\tconst canvas = await this.getCanvas(options);\n\t\tconst data = canvas.toDataURL(this.getContentType(format), options.quality || 1);\n\t\tthis.disposeCanvas(canvas);\n\t\treturn data;\n\t}\n\n\t/**\n\t * Returns canvas data.\n\t *\n\t * @param   customOptions  Image options\n\t * @return                 Promise\n\t */\n\tpublic async exportCanvas(customOptions?: IExportingImageOptions): Promise<string> {\n\t\tconst options: any = this._getFormatOptions(\"canvas\", customOptions);\n\t\tconst canvas = await this.getCanvas(options);\n\t\tconst data = canvas.toDataURL(this.getContentType(\"canvas\"), options.quality || 1);\n\t\tthis.disposeCanvas(canvas);\n\t\treturn data;\n\t}\n\n\t/**\n\t * Returns a `<canvas>` element with snapshot of the chart.\n\t *\n\t * @param   options  Image options\n\t * @return           Promise\n\t */\n\tpublic async getCanvas(options: IExportingImageOptions): Promise<HTMLCanvasElement> {\n\t\tconst mainCanvas = this._root._renderer.getCanvas(this._root._rootContainer._display, options);\n\t\tconst extraImages = this.get(\"extraImages\", []);\n\n\t\t// Add other canvases\n\t\tlet middleLeft = 0;\n\t\tlet middleTop = 0;\n\t\tlet middleWidth = mainCanvas.width;\n\t\tlet middleHeight = mainCanvas.height;\n\t\tlet extraRight = 0;\n\t\tlet extraBottom = 0;\n\n\t\tconst extras: any[] = [];\n\n\t\t$array.each(extraImages, (extraRoot) => {\n\n\t\t\t// Get that extra\n\t\t\tlet extra: IExportingImageSource;\n\n\t\t\tif (extraRoot instanceof Root) {\n\t\t\t\textra = {\n\t\t\t\t\tsource: extraRoot,\n\t\t\t\t\tposition: \"bottom\"\n\t\t\t\t};\n\n\t\t\t} else {\n\t\t\t\textra = <IExportingImageSource>extraRoot;\n\t\t\t}\n\n\t\t\t// Set defaults\n\t\t\textra.position = extra.position || \"bottom\";\n\t\t\textra.marginTop = extra.marginTop || 0;\n\t\t\textra.marginRight = extra.marginRight || 0;\n\t\t\textra.marginBottom = extra.marginBottom || 0;\n\t\t\textra.marginLeft = extra.marginLeft || 0;\n\n\t\t\tconst extraCanvas = extra.source._renderer.getCanvas(extra.source._rootContainer._display, options);\n\n\t\t\tconst extraWidth = extraCanvas.width + extra.marginLeft + extra.marginRight;\n\t\t\tconst extraHeight = extraCanvas.height + extra.marginTop + extra.marginBottom;\n\n\t\t\tif (extra.position == \"top\") {\n\t\t\t\tmiddleWidth = extra.crop ? middleHeight : Math.max(middleWidth, extraWidth);\n\t\t\t\tmiddleTop += extraHeight;\n\n\t\t\t} else if (extra.position == \"right\") {\n\t\t\t\tmiddleHeight = extra.crop ? middleHeight : Math.max(middleHeight, extraHeight);\n\t\t\t\textraRight += extraWidth;\n\n\t\t\t} else if (extra.position == \"left\") {\n\t\t\t\tmiddleHeight = extra.crop ? middleHeight : Math.max(middleHeight, extraHeight);\n\t\t\t\tmiddleLeft += extraWidth;\n\n\t\t\t} else if (extra.position === \"bottom\") {\n\t\t\t\tmiddleWidth = extra.crop ? middleHeight : Math.max(middleWidth, extraWidth);\n\t\t\t\textraBottom += extraHeight;\n\t\t\t}\n\n\t\t\textras.push({\n\t\t\t\tcanvas: extraCanvas,\n\t\t\t\tposition: extra.position,\n\t\t\t\tleft: extra.marginLeft,\n\t\t\t\ttop: extra.marginTop,\n\t\t\t\twidth: extraWidth,\n\t\t\t\theight: extraHeight\n\t\t\t});\n\n\t\t});\n\n\t\tconst newCanvas = this.getDisposableCanvas();\n\n\t\tnewCanvas.width = middleLeft + middleWidth + extraRight;\n\t\tnewCanvas.height = middleTop + middleHeight + extraBottom;\n\n\t\tconst ctx = newCanvas.getContext(\"2d\")!;\n\n\t\t// Get background\n\t\tconst background = this.get(\"backgroundColor\", this.findBackgroundColor(this._root.dom));\n\t\tconst backgroundOpacity = this.get(\"backgroundOpacity\", 1);\n\n\t\tif (background) {\n\t\t\tctx.fillStyle = background.toCSS(backgroundOpacity);\n\t\t\tctx.fillRect(0, 0, newCanvas.width, newCanvas.height);\n\t\t}\n\n\t\tlet left = middleLeft;\n\t\tlet top = middleTop;\n\t\tlet right = left + middleWidth;\n\t\tlet bottom = top + middleHeight;\n\n\t\t// Radiates outwards from center\n\t\t$array.each(extras, (extra) => {\n\t\t\tif (extra.position == \"top\") {\n\t\t\t\ttop -= extra.height;\n\t\t\t\tctx.drawImage(extra.canvas, middleLeft + extra.left, top + extra.top);\n\n\t\t\t} else if (extra.position == \"right\") {\n\t\t\t\tctx.drawImage(extra.canvas, right + extra.left, middleTop + extra.top);\n\t\t\t\tright += extra.width;\n\n\t\t\t} else if (extra.position == \"left\") {\n\t\t\t\tleft -= extra.width;\n\t\t\t\tctx.drawImage(extra.canvas, left + extra.left, middleTop + extra.top);\n\n\t\t\t} else if (extra.position === \"bottom\") {\n\t\t\t\tctx.drawImage(extra.canvas, middleLeft + extra.left, bottom + extra.top);\n\t\t\t\tbottom += extra.height;\n\t\t\t}\n\n\t\t\t//this.disposeCanvas(extra.canvas);\n\t\t});\n\n\t\tctx.drawImage(mainCanvas, middleLeft, middleTop);\n\n\t\treturn newCanvas;\n\t}\n\n\t/**\n\t * ==========================================================================\n\t * JSON\n\t * ==========================================================================\n\t */\n\n\t/**\n\t * Returns a data:uri representation of a JSON file with chart data.\n\t *\n\t * @param   customOptions  Format options\n\t * @return                 Promise\n\t */\n\tpublic async exportJSON(customOptions?: IExportingJSONOptions): Promise<string> {\n\t\treturn \"data:\" + this.getContentType(\"json\") + \";\" + this.get(\"charset\", \"utf-8\") + \",\" + encodeURIComponent(await this.getJSON(customOptions));\n\t}\n\n\t/**\n\t * Returns data in JSON format.\n\t *\n\t * @param   customOptions  Format options\n\t * @return                 Promise\n\t */\n\tpublic async getJSON(customOptions?: IExportingJSONOptions): Promise<string> {\n\t\tconst options: any = this._getFormatOptions(\"json\", customOptions);\n\t\treturn JSON.stringify(this.getData(\"json\", customOptions, options.renameFields), (_key, value) => {\n\t\t\tif ($type.isObject(value)) {\n\t\t\t\t$object.each(value, (field, item) => {\n\t\t\t\t\tvalue[field] = this.convertToSpecialFormat(field, item, options);\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn value;\n\t\t}, options.indent);\n\t}\n\n\t/**\n\t * ==========================================================================\n\t * CSV\n\t * ==========================================================================\n\t */\n\n\t/**\n\t * Returns a data:uri representation of a CSV file with chart data.\n\t *\n\t * @param   customOptions  Format options\n\t * @return                 Promise\n\t */\n\tpublic async exportCSV(customOptions?: IExportingCSVOptions): Promise<string> {\n\t\treturn \"data:\" + this.getContentType(\"csv\") + \";\" + this.get(\"charset\", \"utf-8\") + \",\" + encodeURIComponent(await this.getCSV(customOptions));\n\t}\n\n\t/**\n\t * Returns a CSV with export data.\n\t *\n\t * @param   customOptions  CSV options\n\t * @return                 Promise\n\t */\n\tpublic async getCSV(customOptions?: IExportingCSVOptions): Promise<string> {\n\t\tconst options: any = this._getFormatOptions(\"csv\", customOptions);\n\n\t\t// Init output\n\t\tlet csv = \"\";\n\n\t\t// Add rows\n\t\tlet br = \"\";\n\t\tconst data = this.getData(\"csv\", options);\n\n\t\t// Data fields\n\t\tconst dataFields = this.getDataFields(data);\n\n\t\t// Vertical or horizontal (default) layout\n\t\tif (options.pivot) {\n\n\t\t\t// Data fields order\n\t\t\tconst dataFieldsOrder: string[] = this.get(\"dataFieldsOrder\", []);\n\n\t\t\t$object.eachOrdered(dataFields, (key, val) => {\n\t\t\t\tlet dataRow = [];\n\t\t\t\tif (options.addColumnNames) {\n\t\t\t\t\tdataRow.push(val);\n\t\t\t\t}\n\t\t\t\tfor (let len = data.length, i = 0; i < len; i++) {\n\t\t\t\t\tlet dataValue = data[i][key];\n\t\t\t\t\tdataRow.push(this.convertToSpecialFormat(key, dataValue, options, true));\n\t\t\t\t}\n\t\t\t\tcsv += br + this.getCSVRow(dataRow, options, undefined, true);\n\t\t\t\tbr = \"\\n\";\n\t\t\t}, (a, b) => {\n\t\t\t\tlet ai = dataFieldsOrder.indexOf(a);\n\t\t\t\tlet bi = dataFieldsOrder.indexOf(b);\n\t\t\t\tif (ai > bi) {\n\t\t\t\t\treturn -1;\n\t\t\t\t}\n\t\t\t\telse if (ai < bi) {\n\t\t\t\t\treturn 1\n\t\t\t\t}\n\t\t\t\treturn 0;\n\t\t\t});\n\n\t\t}\n\n\t\telse {\n\t\t\tfor (let len = data.length, i = 0; i < len; i++) {\n\t\t\t\tlet row = this.getCSVRow(data[i], options, dataFields);\n\t\t\t\tif (options.reverse) {\n\t\t\t\t\tcsv = row + br + csv;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tcsv += br + row;\n\t\t\t\t}\n\t\t\t\tbr = \"\\n\";\n\t\t\t}\n\n\t\t\t// Add column names?\n\t\t\tif (options.addColumnNames) {\n\t\t\t\tcsv = this.getCSVRow(dataFields, options, undefined, true) + br + csv;\n\t\t\t}\n\t\t}\n\n\t\treturn csv;\n\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic getCSVRow(row: any, options: IExportingCSVOptions, dataFields: any, asIs: boolean = false): string {\n\n\t\t// Init\n\t\tlet separator = options.separator || \",\";\n\t\tlet items: any[] = [];\n\n\t\t// Data fields\n\t\tif (!dataFields) {\n\t\t\tdataFields = {};\n\t\t\t$object.each(row, (key, value) => {\n\t\t\t\tdataFields[key] = value;\n\t\t\t});\n\t\t}\n\n\t\t// Data fields order\n\t\tconst dataFieldsOrder: string[] = this.get(\"dataFieldsOrder\", []);\n\n\t\t// Process each row item\n\t\t$object.eachOrdered(dataFields, (key, _name) => {\n\n\t\t\t// Get value\n\t\t\tlet value = this.convertEmptyValue(key, row[key], options);\n\n\t\t\t// Check if we need to skip\n\t\t\t// This is no longer required because we are iterating via dataFields anyway\n\t\t\t/*if (this.dataFields != null && this.dataFields[key] == null) {\n\t\t\t\treturn;\n\t\t\t}*/\n\n\t\t\t// Convert dates\n\t\t\tlet item = asIs ? value : this.convertToSpecialFormat(key, value, options);\n\n\t\t\t// Cast and escape doublequotes\n\t\t\titem = \"\" + item;\n\t\t\titem = item.replace(/\"/g, '\"\"');\n\n\t\t\t// Enclose into double quotes\n\t\t\tif (options.forceQuotes || (item.search(new RegExp(\"\\\"|\\n|\" + separator, \"g\")) >= 0)) {\n\t\t\t\titem = \"\\\"\" + item + \"\\\"\";\n\t\t\t}\n\n\t\t\t// Add to item\n\t\t\titems.push(item);\n\t\t}, (a, b) => {\n\t\t\tlet ai = dataFieldsOrder.indexOf(a);\n\t\t\tlet bi = dataFieldsOrder.indexOf(b);\n\t\t\tif (ai > bi) {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t\telse if (ai < bi) {\n\t\t\t\treturn -1\n\t\t\t}\n\t\t\treturn 0;\n\t\t});\n\n\t\treturn items.join(separator);\n\t}\n\n\t/**\n\t * ==========================================================================\n\t * HTML\n\t * ==========================================================================\n\t */\n\n\t/**\n\t * Returns a data:uri representation of an HTML file with chart data.\n\t *\n\t * @param   customOptions  Format options\n\t * @return                 Promise\n\t */\n\tpublic async exportHTML(customOptions?: IExportingHTMLOptions): Promise<string> {\n\t\treturn \"data:\" + this.getContentType(\"html\") + \";\" + this.get(\"charset\", \"utf-8\") + \",\" + encodeURIComponent(await this.getHTML(customOptions));\n\t}\n\n\t/**\n\t * Returns an HTML with a table with export data.\n\t *\n\t * @param   customOptions  HTML options\n\t * @return                 Promise\n\t */\n\tpublic async getHTML(customOptions?: IExportingHTMLOptions): Promise<string> {\n\t\tconst options: any = this._getFormatOptions(\"html\", customOptions);\n\n\t\t// Init output\n\t\tlet html = \"<table>\";\n\t\tif (options.tableClass) {\n\t\t\thtml = \"<table class=\\\"\" + options.tableClass + \"\\\">\";\n\t\t}\n\n\t\t// Get data\n\t\tconst data = this.getData(\"html\", options);\n\t\tconst dataFields = this.getDataFields(data);\n\n\t\t// Vertical or horizontal (default) layout\n\t\tif (options.pivot) {\n\n\t\t\t// Data fields order\n\t\t\tconst dataFieldsOrder: string[] = this.get(\"dataFieldsOrder\", []);\n\n\t\t\thtml += \"\\n<tbody>\";\n\n\t\t\t$object.eachOrdered(dataFields, (key, val) => {\n\t\t\t\tlet dataRow = [];\n\t\t\t\tif (options.addColumnNames) {\n\t\t\t\t\tdataRow.push(val);\n\t\t\t\t}\n\t\t\t\tfor (let len = data.length, i = 0; i < len; i++) {\n\t\t\t\t\tlet dataValue = data[i][key];\n\t\t\t\t\tdataRow.push(this.convertToSpecialFormat(key, dataValue, options, true));\n\t\t\t\t}\n\t\t\t\thtml += \"\\n\" + this.getHTMLRow(dataRow, options, undefined, true);\n\t\t\t}, (a, b) => {\n\t\t\t\tlet ai = dataFieldsOrder.indexOf(a);\n\t\t\t\tlet bi = dataFieldsOrder.indexOf(b);\n\t\t\t\tif (ai > bi) {\n\t\t\t\t\treturn -1;\n\t\t\t\t}\n\t\t\t\telse if (ai < bi) {\n\t\t\t\t\treturn 1\n\t\t\t\t}\n\t\t\t\treturn 0;\n\t\t\t});\n\n\t\t\thtml += \"\\n</tbody>\";\n\n\t\t}\n\n\t\telse {\n\t\t\t// Add column names?\n\t\t\tif (options.addColumnNames) {\n\t\t\t\thtml += \"\\n<thead>\\n\" + this.getHTMLRow(dataFields, options, undefined, true, true) + \"\\n</thead>\";\n\t\t\t}\n\n\t\t\thtml += \"\\n<tbody>\";\n\n\t\t\tfor (let len = data.length, i = 0; i < len; i++) {\n\t\t\t\thtml += \"\\n\" + this.getHTMLRow(data[i], options, dataFields);\n\t\t\t}\n\n\t\t\thtml += \"\\n</tbody>\";\n\t\t}\n\n\t\thtml += \"\\n</table>\";\n\n\t\treturn html;\n\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic getHTMLRow(row: any, options: IExportingHTMLOptions, dataFields: any, asIs: boolean = false, headerRow: boolean = false): string {\n\n\t\t// Init output\n\t\tlet html = \"\\t<tr>\";\n\t\tif (options.rowClass) {\n\t\t\thtml = \"\\t<tr class=\\\"\" + options.rowClass + \"\\\">\";\n\t\t}\n\n\t\t// Data fields\n\t\tif (!dataFields) {\n\t\t\tdataFields = row;\n\t\t}\n\n\t\t// Data fields order\n\t\tconst dataFieldsOrder: string[] = this.get(\"dataFieldsOrder\", []);\n\n\t\t// th or dh?\n\t\tconst tag = headerRow ? \"th\" : \"td\";\n\n\t\t// Process each row item\n\t\tlet first = true;\n\t\t$object.eachOrdered(dataFields, (key, _name) => {\n\n\t\t\t// Get value\n\t\t\tlet value = this.convertEmptyValue(key, row[key], options);\n\n\t\t\t// Convert dates\n\t\t\tlet item = asIs ? value : this.convertToSpecialFormat(key, value, options);\n\n\t\t\t// Escape HTML entities\n\t\t\titem = \"\" + item;\n\t\t\titem = item.replace(/[\\u00A0-\\u9999<>\\&]/gim, function(i: string) {\n\t\t\t\treturn \"&#\" + i.charCodeAt(0) + \";\";\n\t\t\t});\n\n\t\t\t// Which tag to use\n\t\t\tlet useTag = tag;\n\t\t\tif (options.pivot && first) {\n\t\t\t\tuseTag = \"th\";\n\t\t\t}\n\n\t\t\t// Add cell\n\t\t\tif (options.cellClass) {\n\t\t\t\thtml += \"\\n\\t\\t<\" + useTag + \" class=\\\"\" + options.cellClass + \"\\\">\" + item + \"</\" + useTag + \">\";\n\t\t\t}\n\t\t\telse {\n\t\t\t\thtml += \"\\n\\t\\t<\" + useTag + \">\" + item + \"</\" + useTag + \">\";\n\t\t\t}\n\n\t\t\tfirst = false;\n\t\t}, (a, b) => {\n\t\t\tlet ai = dataFieldsOrder.indexOf(a);\n\t\t\tlet bi = dataFieldsOrder.indexOf(b);\n\t\t\tif (ai > bi) {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t\telse if (ai < bi) {\n\t\t\t\treturn -1\n\t\t\t}\n\t\t\treturn 0;\n\t\t});\n\n\t\thtml += \"\\n\\t</tr>\";\n\n\t\treturn html;\n\t}\n\n\t/**\n\t * ==========================================================================\n\t * XLSX\n\t * ==========================================================================\n\t */\n\n\t/**\n\t * Returns a data:uri representation of an XLSX file with chart data.\n\t *\n\t * @param   customOptions  Format options\n\t * @return                 Promise\n\t */\n\tpublic async exportXLSX(customOptions?: IExportingXLSXOptions): Promise<string> {\n\t\treturn \"data:\" + this.getContentType(\"xlsx\") + \";\" + this.get(\"charset\", \"utf-8\") + \",\" + encodeURIComponent(await this.getXLSX(customOptions));\n\t}\n\n\t/**\n\t * Returns a data:uri of XLSX data.\n\t *\n\t * @param  customOptions  Format options\n\t * @return                Promise\n\t */\n\tpublic async getXLSX(customOptions?: IExportingXLSXOptions): Promise<string> {\n\n\t\tconst options: any = this._getFormatOptions(\"xlsx\", customOptions);\n\n\t\t// Load xlsx\n\t\tlet XLSX = await this.getXLSXLib();\n\n\t\t// Create workbook options\n\t\tlet wbOptions = {\n\t\t\tbookType: \"xlsx\",\n\t\t\tbookSST: false,\n\t\t\ttype: \"base64\",\n\t\t\t//dateNF: 'yyyy-mm-dd'\n\t\t};\n\n\t\t// Get sheet name\n\t\tlet sheetName = this._normalizeExcelSheetName(this.get(\"title\", this._t(\"Data\")));\n\n\t\t// Create a workbook\n\t\tlet wb = {\n\t\t\tSheetNames: <any>[sheetName],\n\t\t\tSheets: <any>{}\n\t\t};\n\n\t\t// Init worksheet data\n\t\tlet wsData: Array<any> = [];\n\n\t\t// Get data\n\t\tconst data = this.getData(\"html\", options);\n\t\tconst dataFields = this.getDataFields(data);\n\n\t\t// Vertical or horizontal (default) layout\n\t\tif (options.pivot) {\n\n\t\t\t// Data fields order\n\t\t\tconst dataFieldsOrder: string[] = this.get(\"dataFieldsOrder\", []);\n\n\t\t\t$object.eachOrdered(dataFields, (key, val) => {\n\t\t\t\tlet dataRow = [];\n\t\t\t\tif (options.addColumnNames) {\n\t\t\t\t\tdataRow.push(val);\n\t\t\t\t}\n\t\t\t\tfor (let len = data.length, i = 0; i < len; i++) {\n\t\t\t\t\tlet dataValue = data[i][key];\n\t\t\t\t\tdataRow.push(this.convertToSpecialFormat(key, dataValue, options, true));\n\t\t\t\t}\n\t\t\t\twsData.push(this.getXLSXRow(dataRow, options, undefined, true));\n\t\t\t}, (a, b) => {\n\t\t\t\tlet ai = dataFieldsOrder.indexOf(a);\n\t\t\t\tlet bi = dataFieldsOrder.indexOf(b);\n\t\t\t\tif (ai > bi) {\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t\telse if (ai < bi) {\n\t\t\t\t\treturn -1\n\t\t\t\t}\n\t\t\t\treturn 0;\n\t\t\t});\n\n\t\t}\n\n\t\telse {\n\t\t\t// Add column names?\n\t\t\tif (options.addColumnNames) {\n\t\t\t\twsData.push(this.getXLSXRow(dataFields, options, undefined, true));\n\t\t\t}\n\n\t\t\t// Add lines\n\t\t\tfor (let len = data.length, i = 0; i < len; i++) {\n\t\t\t\twsData.push(this.getXLSXRow(data[i], options, dataFields));\n\t\t\t}\n\t\t}\n\n\t\t// Create sheet and add data\n\t\twb.Sheets[sheetName] = XLSX.utils.aoa_to_sheet(wsData);\n\n\t\tthis.events.dispatch(\"workbookready\", {\n\t\t\ttype: \"workbookready\",\n\t\t\tformat: \"xlsx\",\n\t\t\toptions: options,\n\t\t\tworkbook: wb,\n\t\t\tworkbookOptions: wbOptions,\n\t\t\txlsx: XLSX,\n\t\t\ttarget: this\n\t\t});\n\n\t\treturn XLSX.write(wb, wbOptions);\n\t}\n\n\tprivate _normalizeExcelSheetName(name: string): string {\n\t\tname = name.replace(/([:\\\\\\/?*\\[\\]]+)/g, \" \");\n\t\treturn name.length > 30 ? name.substr(0, 30) + \"...\" : name;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic getXLSXRow(row: any, options: IExportingXLSXOptions, dataFields: any, asIs: boolean = false): any[] {\n\n\t\t// Init\n\t\tlet items: any[] = [];\n\n\t\t// Data fields\n\t\tif (!dataFields) {\n\t\t\tdataFields = row;\n\t\t}\n\n\t\t// Data fields order\n\t\tconst dataFieldsOrder: string[] = this.get(\"dataFieldsOrder\", []);\n\n\t\t// Process each row item\n\t\t$object.eachOrdered(dataFields, (key, _name) => {\n\n\t\t\t// Get value\n\t\t\tlet value = this.convertEmptyValue(key, row[key], options);\n\n\t\t\t// Convert dates\n\t\t\tlet item = asIs ? value : this.convertToSpecialFormat(key, value, options, true);\n\n\t\t\titems.push(item);\n\t\t}, (a, b) => {\n\t\t\tlet ai = dataFieldsOrder.indexOf(a);\n\t\t\tlet bi = dataFieldsOrder.indexOf(b);\n\t\t\tif (ai > bi) {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t\telse if (ai < bi) {\n\t\t\t\treturn -1\n\t\t\t}\n\t\t\treturn 0;\n\t\t});\n\n\t\treturn items;\n\t}\n\n\n\t/**\n\t * @ignore\n\t */\n\tprivate async _xlsx(): Promise<any> {\n\t\treturn await import(/* webpackChunkName: \"xlsx\" */ \"../../bundled/xlsx\");\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic getXLSXLib(): Promise<any> {\n\t\treturn this._xlsx();\n\t}\n\n\t/**\n\t * ==========================================================================\n\t * PDF\n\t * ==========================================================================\n\t */\n\n\t/**\n\t * Returns a data:uri representation of a PDF file with chart image.\n\t *\n\t * @param   customOptions  Format options\n\t * @return                 Promise\n\t */\n\tpublic async exportPDF(customOptions?: IExportingPDFOptions): Promise<string> {\n\t\treturn \"data:\" + this.getContentType(\"pdf\") + \";\" + this.get(\"charset\", \"utf-8\") + \",\" + encodeURIComponent(await this.getPDF(customOptions, true));\n\t}\n\n\t/**\n\t * Returns a data:uri representation of a PDF file with chart data.\n\t *\n\t * @param   customOptions  Format options\n\t * @return                 Promise\n\t */\n\tpublic async exportPDFData(customOptions?: IExportingDataOptions): Promise<string> {\n\t\treturn \"data:\" + this.getContentType(\"pdf\") + \";\" + this.get(\"charset\", \"utf-8\") + \",\" + encodeURIComponent(await this.getPDF(customOptions, false, true));\n\t}\n\n\t/**\n\t * Returns Base64-encoded binary data for a PDF file.\n\t * @param   customOptions  PDF options\n\t * @param   includeImage   Include chart snapshot\n\t * @param   includeData    Include data\n\t * @return                 Promise\n\t */\n\tpublic async getPDF(customOptions?: IExportingPDFOptions, includeImage: boolean = true, includeData: boolean = false): Promise<string> {\n\n\t\tconst options: any = this._getFormatOptions(\"pdf\", customOptions);\n\t\tconst dataOptions: any = this._getFormatOptions(\"pdfdata\", customOptions);\n\t\tconst orientation: \"landscape\" | \"portrait\" = options.pageOrientation || \"portrait\";\n\n\t\t// Get image\n\t\tlet image: string;\n\t\tif (includeImage) {\n\t\t\timage = await this.exportImage(options.imageFormat || \"png\", options);\n\t\t}\n\t\t// Load pdfmake\n\n\t\tconst pdfmake = await this.getPdfmake();\n\n\t\t// Defaults\n\t\tconst defaultMargins = [30, 30, 30, 30];\n\n\t\t// Init content for PDF\n\t\tlet doc = {\n\t\t\tpageSize: options.pageSize || \"A4\",\n\t\t\tpageOrientation: orientation,\n\t\t\tpageMargins: options.pageMargins || defaultMargins,\n\t\t\tdefaultStyle: {\n\t\t\t\tfont: options.font ? options.font.name : undefined\n\t\t\t},\n\t\t\t//header: <any>[],\n\t\t\tcontent: <any>[]\n\t\t};\n\n\t\t// Should we add title?\n\t\tconst title = this.get(\"title\");\n\n\t\tlet extraMargin = 0;\n\n\t\tif (title) {\n\t\t\tdoc.content.push({\n\t\t\t\ttext: title,\n\t\t\t\tfontSize: options.fontSize || 14,\n\t\t\t\tbold: true,\n\t\t\t\tmargin: [0, 0, 0, 15]\n\t\t\t});\n\n\t\t\t// Add some leftover margin for title\n\t\t\textraMargin += 50;\n\t\t}\n\n\t\t// Add page URL?\n\t\tif (options.addURL) {\n\t\t\tdoc.content.push({\n\t\t\t\ttext: this._t(\"Saved from\") + \": \" + document.location.href,\n\t\t\t\tfontSize: options.fontSize,\n\t\t\t\tmargin: [0, 0, 0, 15]\n\t\t\t});\n\n\t\t\t// Add some leftover margin for URL\n\t\t\textraMargin += 50;\n\t\t}\n\n\t\t// Add image\n\t\tif (includeImage && image!) {\n\t\t\tdoc.content.push({\n\t\t\t\timage: image!,\n\t\t\t\talignment: options.align || \"left\",\n\t\t\t\tfit: this.getPageSizeFit(doc.pageSize, doc.pageMargins, extraMargin, orientation)\n\t\t\t});\n\t\t}\n\n\t\t// Add data\n\t\tif ((includeData || options.includeData) && this.hasData()) {\n\t\t\tdoc.content.push({\n\t\t\t\ttable: await this.getPDFData(dataOptions),\n\t\t\t\tfontSize: options.fontSize || 14\n\t\t\t});\n\t\t}\n\n\t\tlet fonts: { [name: string]: { [types: string]: string } } | null = null;\n\t\tlet vfs: { [path: string]: string } | null = null;\n\n\t\tfunction addFont(font: IFont) {\n\t\t\tconst paths: { [path: string]: string } = {};\n\n\t\t\tpaths.normal = font.normal.path;\n\t\t\tvfs![font.normal.path] = font.normal.bytes;\n\n\t\t\tif (font.bold) {\n\t\t\t\tpaths.bold = font.bold.path;\n\t\t\t\tvfs![font.bold.path] = font.bold.bytes;\n\n\t\t\t} else {\n\t\t\t\tpaths.bold = font.normal.path;\n\t\t\t}\n\n\t\t\tif (font.italics) {\n\t\t\t\tpaths.italics = font.italics.path;\n\t\t\t\tvfs![font.italics.path] = font.italics.bytes;\n\n\t\t\t} else {\n\t\t\t\tpaths.italics = font.normal.path;\n\t\t\t}\n\n\t\t\tif (font.bolditalics) {\n\t\t\t\tpaths.bolditalics = font.bolditalics.path;\n\t\t\t\tvfs![font.bolditalics.path] = font.bolditalics.bytes;\n\n\t\t\t} else {\n\t\t\t\tpaths.bolditalics = font.normal.path;\n\t\t\t}\n\n\t\t\tfonts![font.name] = paths;\n\t\t}\n\n\t\tif (options.font) {\n\t\t\tfonts = {};\n\t\t\tvfs = {};\n\t\t\taddFont(options.font);\n\n\t\t\tif (options.extraFonts) {\n\t\t\t\t$array.each(options.extraFonts, addFont);\n\t\t\t}\n\t\t}\n\n\t\tthis.events.dispatch(\"pdfdocready\", {\n\t\t\ttype: \"pdfdocready\",\n\t\t\tformat: \"pdf\",\n\t\t\toptions: options,\n\t\t\tdoc: doc,\n\t\t\ttarget: this\n\t\t});\n\n\t\t// Create PDF\n\t\treturn new Promise<string>((success, _error) => {\n\t\t\tpdfmake.createPdf(doc, null, fonts, vfs).getBase64((uri: string) => {\n\t\t\t\tsuccess(uri);\n\t\t\t});\n\t\t});\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic async getPDFData(customOptions?: IExportingDataOptions): Promise<any> {\n\n\t\tconst options: any = this._getFormatOptions(\"pdfdata\", customOptions);\n\n\t\t// Init output\n\t\tlet content = <any>{\n\t\t\t\"body\": <any>[]\n\t\t};\n\n\t\t// Get data\n\t\tconst data = this.getData(\"html\", options);\n\t\tconst dataFields = this.getDataFields(data);\n\n\t\t// Vertical or horizontal (default) layout\n\t\tif (options.pivot) {\n\n\t\t\t// Data fields order\n\t\t\tconst dataFieldsOrder: string[] = this.get(\"dataFieldsOrder\", []);\n\n\t\t\t$object.eachOrdered(dataFields, (key, val) => {\n\t\t\t\tlet dataRow = [];\n\t\t\t\tif (options.addColumnNames) {\n\t\t\t\t\tdataRow.push(val);\n\t\t\t\t}\n\t\t\t\tfor (let len = data.length, i = 0; i < len; i++) {\n\t\t\t\t\tlet dataValue = data[i][key];\n\t\t\t\t\tdataRow.push(this.convertToSpecialFormat(key, dataValue, options, true));\n\t\t\t\t}\n\t\t\t\tcontent.body.push(this.getPDFDataRow(dataRow, options, undefined, true));\n\t\t\t}, (a, b) => {\n\t\t\t\tlet ai = dataFieldsOrder.indexOf(a);\n\t\t\t\tlet bi = dataFieldsOrder.indexOf(b);\n\t\t\t\tif (ai > bi) {\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t\telse if (ai < bi) {\n\t\t\t\t\treturn -1\n\t\t\t\t}\n\t\t\t\treturn 0;\n\t\t\t});\n\n\t\t}\n\n\t\telse {\n\n\t\t\t// Add column names?\n\t\t\tif (options.addColumnNames) {\n\t\t\t\tcontent.body.push(this.getPDFDataRow(dataFields, options, undefined, true));\n\t\t\t\tcontent.headerRows = 1;\n\t\t\t}\n\n\t\t\tfor (let len = data.length, i = 0; i < len; i++) {\n\t\t\t\tcontent.body.push(this.getPDFDataRow(data[i], options, dataFields));\n\t\t\t}\n\n\t\t}\n\n\t\treturn content;\n\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic getPDFDataRow(row: any, options: IExportingDataOptions, dataFields?: any, asIs: boolean = false): Array<string> {\n\n\t\t// Init\n\t\tlet items: any[] = [];\n\n\t\t// Data fields\n\t\tif (!dataFields) {\n\t\t\tdataFields = row;\n\t\t}\n\n\t\t// Data fields order\n\t\tconst dataFieldsOrder: string[] = this.get(\"dataFieldsOrder\", []);\n\n\t\t// Process each row item\n\t\t$object.eachOrdered(dataFields, (key, _name) => {\n\n\t\t\t// Get value\n\t\t\tlet value = this.convertEmptyValue(key, row[key], options);\n\n\t\t\t// Convert dates\n\t\t\tlet item = asIs ? value : this.convertToSpecialFormat(key, value, options);\n\t\t\titem = \"\" + item;\n\n\t\t\t// Add to item\n\t\t\titems.push(item);\n\t\t}, (a, b) => {\n\t\t\tlet ai = dataFieldsOrder.indexOf(a);\n\t\t\tlet bi = dataFieldsOrder.indexOf(b);\n\t\t\tif (ai > bi) {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t\telse if (ai < bi) {\n\t\t\t\treturn -1\n\t\t\t}\n\t\t\treturn 0;\n\t\t});\n\n\t\treturn items;\n\t}\n\n\t/**\n\t * Returns pdfmake instance.\n\t *\n\t * @ignore\n\t * @return Instance of pdfmake\n\t */\n\tpublic getPdfmake(): Promise<any> {\n\t\tif (pdfmakePromise === undefined) {\n\t\t\tpdfmakePromise = _pdfmake();\n\t\t}\n\n\t\treturn pdfmakePromise;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic getPageSizeFit(pageSize: pageSizes, margins: number | number[], extraMargin: number = 0, orientation: \"landscape\" | \"portrait\" = \"portrait\"): number[] {\n\n\t\t// Check margins\n\t\tlet newMargins = [0, 0, 0, 0];\n\t\tif ($type.isNumber(margins)) {\n\t\t\tnewMargins = [margins, margins, margins, margins];\n\t\t}\n\t\telse if (margins.length == 2) {\n\t\t\tnewMargins = [margins[0], margins[1], margins[0], margins[1]];\n\t\t}\n\t\telse if (margins.length == 4) {\n\t\t\tnewMargins = margins;\n\t\t}\n\n\t\t// Define available page sizes\n\t\tlet sizes = {\n\t\t\t\"4A0\": [4767.87, 6740.79],\n\t\t\t\"2A0\": [3370.39, 4767.87],\n\t\t\tA0: [2383.94, 3370.39],\n\t\t\tA1: [1683.78, 2383.94],\n\t\t\tA2: [1190.55, 1683.78],\n\t\t\tA3: [841.89, 1190.55],\n\t\t\tA4: [595.28, 841.89],\n\t\t\tA5: [419.53, 595.28],\n\t\t\tA6: [297.64, 419.53],\n\t\t\tA7: [209.76, 297.64],\n\t\t\tA8: [147.40, 209.76],\n\t\t\tA9: [104.88, 147.40],\n\t\t\tA10: [73.70, 104.88],\n\t\t\tB0: [2834.65, 4008.19],\n\t\t\tB1: [2004.09, 2834.65],\n\t\t\tB2: [1417.32, 2004.09],\n\t\t\tB3: [1000.63, 1417.32],\n\t\t\tB4: [708.66, 1000.63],\n\t\t\tB5: [498.90, 708.66],\n\t\t\tB6: [354.33, 498.90],\n\t\t\tB7: [249.45, 354.33],\n\t\t\tB8: [175.75, 249.45],\n\t\t\tB9: [124.72, 175.75],\n\t\t\tB10: [87.87, 124.72],\n\t\t\tC0: [2599.37, 3676.54],\n\t\t\tC1: [1836.85, 2599.37],\n\t\t\tC2: [1298.27, 1836.85],\n\t\t\tC3: [918.43, 1298.27],\n\t\t\tC4: [649.13, 918.43],\n\t\t\tC5: [459.21, 649.13],\n\t\t\tC6: [323.15, 459.21],\n\t\t\tC7: [229.61, 323.15],\n\t\t\tC8: [161.57, 229.61],\n\t\t\tC9: [113.39, 161.57],\n\t\t\tC10: [79.37, 113.39],\n\t\t\tRA0: [2437.80, 3458.27],\n\t\t\tRA1: [1729.13, 2437.80],\n\t\t\tRA2: [1218.90, 1729.13],\n\t\t\tRA3: [864.57, 1218.90],\n\t\t\tRA4: [609.45, 864.57],\n\t\t\tSRA0: [2551.18, 3628.35],\n\t\t\tSRA1: [1814.17, 2551.18],\n\t\t\tSRA2: [1275.59, 1814.17],\n\t\t\tSRA3: [907.09, 1275.59],\n\t\t\tSRA4: [637.80, 907.09],\n\t\t\tEXECUTIVE: [521.86, 756.00],\n\t\t\tFOLIO: [612.00, 936.00],\n\t\t\tLEGAL: [612.00, 1008.00],\n\t\t\tLETTER: [612.00, 792.00],\n\t\t\tTABLOID: [792.00, 1224.00]\n\t\t};\n\n\t\t// Calculate size\n\t\tlet fitSize = sizes[pageSize];\n\t\tif (orientation == \"landscape\") {\n\t\t\tfitSize.reverse();\n\t\t}\n\t\tfitSize[0] -= newMargins[0] + newMargins[2];\n\t\tfitSize[1] -= newMargins[1] + newMargins[3] + extraMargin;\n\t\treturn fitSize;\n\t}\n\n\t/**\n\t * ==========================================================================\n\t * Data\n\t * ==========================================================================\n\t */\n\n\t/**\n\t\t* Returns `true` if `dataSource` is set, and the contents are proper\n\t\t* data (array).\n\t\t*\n\t\t* @return Has data?\n\t\t*/\n\tpublic hasData(): boolean {\n\t\tconst dataSource = this.get(\"dataSource\");\n\t\treturn $type.isArray(dataSource) && dataSource.length ? true : false;\n\t}\n\n\t/**\n\t * Returns processed data according to format options.\n\t *\n\t * @param   format         Format\n\t * @param   customOptions  Format options\n\t * @param   renameFields   Should fields be renamed?\n\t * @return                 Processed data\n\t */\n\tpublic getData(format: ExportingFormats, customOptions?: IExportingDataOptions, renameFields: boolean = false): any {\n\t\tconst options: any = this._getFormatOptions(format, customOptions);\n\t\tconst dataSource = this.get(\"dataSource\", []);\n\t\tlet data: any = dataSource;\n\n\t\t// Re-generate the data based on data fields if set\n\t\tconst dataFields = this.get(\"dataFields\");\n\t\tif (dataFields && $type.isArray(dataSource)) {\n\t\t\tdata = [];\n\t\t\t$array.each(dataSource, (row) => {\n\t\t\t\tif ($type.isObject(row)) {\n\t\t\t\t\tconst newRow: any = {}\n\t\t\t\t\t$object.each(dataFields, (field, value) => {\n\t\t\t\t\t\tif (dataFields![field] != null) {\n\t\t\t\t\t\t\tnewRow[renameFields ? value : field] = this.convertToSpecialFormat(field, row[field], options);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tdata.push(newRow);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tconst event: any = {\n\t\t\ttype: \"dataprocessed\",\n\t\t\tformat: format,\n\t\t\toptions: options,\n\t\t\tdata: data,\n\t\t\ttarget: this\n\t\t};\n\n\t\tthis.events.dispatch(\"dataprocessed\", event);\n\n\t\treturn event.data;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic getDataFields(data: any): { [index: string]: string } {\n\t\tlet dataFields = this.get(\"dataFields\");\n\t\tif (!dataFields) {\n\t\t\tdataFields = {};\n\t\t\tif ($type.isArray(data) && data.length) {\n\t\t\t\t$array.each(data, (row) => {\n\t\t\t\t\t$object.each(row, (key, _value) => {\n\t\t\t\t\t\tif (dataFields![key] == null) {\n\t\t\t\t\t\t\tdataFields![key] = key;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t\treturn dataFields!;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic convertEmptyValue(_field: string, value: any, options: IExportingDataOptions): any {\n\t\treturn value != null ? value : options.emptyAs;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic convertToSpecialFormat(field: any, value: any, options: IExportingDataOptions, keepOriginal?: boolean): any {\n\n\t\t// Is this a timestamp or duration?\n\t\tif (typeof value == \"number\") {\n\t\t\tif (this.isDateField(field)) {\n\t\t\t\tvalue = new Date(value);\n\t\t\t}\n\t\t\telse if (this.isNumericField(field) && this.get(\"numberFormat\")) {\n\t\t\t\treturn this._root.numberFormatter.format(value, this.get(\"numberFormat\"));\n\t\t\t}\n\t\t\telse if (this.isDurationField(field)) {\n\t\t\t\treturn this._root.durationFormatter.format(value, this.get(\"durationFormat\"), this.get(\"durationUnit\"));\n\t\t\t}\n\t\t}\n\n\t\tif (value instanceof Date) {\n\t\t\tif (options.useTimestamps) {\n\t\t\t\tvalue = value.getTime();\n\t\t\t}\n\t\t\telse if (options.useLocale) {\n\t\t\t\tif (!keepOriginal) {\n\t\t\t\t\tvalue = value.toLocaleString();\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tvalue = this._root.dateFormatter.format(value, this.get(\"dateFormat\"));\n\t\t\t}\n\t\t}\n\n\t\treturn value;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic isDateField(field: string): boolean {\n\t\treturn this.get(\"dateFields\")!.indexOf(field) !== -1;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic isNumericField(field: string): boolean {\n\t\treturn this.get(\"numericFields\")!.indexOf(field) !== -1;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic isDurationField(field: string): boolean {\n\t\treturn this.get(\"durationFields\")!.indexOf(field) !== -1;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic getContentType(type: ExportingFormats): string {\n\t\tlet contentType = \"\";\n\t\tswitch (type) {\n\t\t\tcase \"png\":\n\t\t\t\tcontentType = \"image/\" + type;\n\t\t\t\tbreak;\n\t\t\tcase \"jpg\":\n\t\t\t\tcontentType = \"image/jpeg\";\n\t\t\t\tbreak;\n\t\t\tcase \"csv\":\n\t\t\t\tcontentType = \"text/csv\";\n\t\t\t\tbreak;\n\t\t\tcase \"json\":\n\t\t\t\tcontentType = \"application/json\";\n\t\t\t\tbreak;\n\t\t\tcase \"html\":\n\t\t\t\tcontentType = \"text/html\";\n\t\t\t\tbreak;\n\t\t\tcase \"pdf\":\n\t\t\tcase \"pdfdata\":\n\t\t\t\tcontentType = \"application/pdf\";\n\t\t\t\tbreak;\n\t\t\tcase \"xlsx\":\n\t\t\t\tcontentType = \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\";\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontentType = \"application/octet-stream\";\n\t\t}\n\n\t\treturn contentType;\n\t}\n\n\tprotected getDisposableCanvas(): HTMLCanvasElement {\n\t\tlet canvas = document.createElement(\"canvas\");\n\t\tcanvas.style.position = \"fixed\";\n\t\tcanvas.style.top = \"-10000px\";\n\t\tthis._root.dom.appendChild(canvas);\n\t\treturn canvas;\n\t}\n\n\tprotected disposeCanvas(canvas: HTMLCanvasElement): void {\n\t\tthis._root.dom.removeChild(canvas);\n\t}\n\n\n\t/**\n\t * @ignore\n\t */\n\tpublic findBackgroundColor(element: Element): Color {\n\n\t\t// Check if element has styles set\n\t\tlet opacity = 1;\n\t\tlet currentColor = getComputedStyle(element, \"background-color\").getPropertyValue(\"background-color\");\n\n\t\t// Check opacity\n\t\tif (currentColor.match(/[^,]*,[^,]*,[^,]*,[ ]?0/) || currentColor == \"transparent\") {\n\t\t\topacity = 0;\n\t\t}\n\n\t\tif (opacity == 0) {\n\t\t\tlet parent = element.parentElement;// || <Element>element.parentNode;\n\n\t\t\t// Completely transparent. Look for a parent\n\t\t\tif (parent) {\n\t\t\t\treturn this.findBackgroundColor(parent);\n\t\t\t}\n\t\t\telse {\n\t\t\t\treturn Color.fromHex(0xffffff);\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\treturn Color.fromCSS(currentColor);\n\t\t}\n\n\t}\n\n\t/**\n\t * Triggers download of the file.\n\t *\n\t * @param   uri       data:uri with file content\n\t * @param   fileName  File name\n\t * @param   addBOM    Should download include byte order mark?\n\t * @return            Promise\n\t */\n\tpublic streamFile(uri: string, fileName: string, addBOM: boolean = false): boolean {\n\n\t\tif (this.blobDownloadSupport()) {\n\n\t\t\t/**\n\t\t\t * Supports Blob object.\n\t\t\t * Use it.\n\t\t\t */\n\t\t\tlet link = document.createElement(\"a\");\n\t\t\tlink.download = fileName;\n\t\t\tdocument.body.appendChild(link);\n\n\t\t\t// Extract content type and get pure data without headers\n\t\t\tlet parts = uri.split(\";\");\n\t\t\tlet contentType = parts!.shift()!.replace(/data:/, \"\");\n\n\t\t\turi = decodeURIComponent(parts.join(\";\").replace(/^[^,]*,/, \"\"));\n\n\t\t\tif ([\"image/svg+xml\", \"application/json\", \"text/csv\", \"text/html\"].indexOf(contentType) == -1) {\n\t\t\t\ttry {\n\t\t\t\t\tlet decoded = atob(uri);\n\t\t\t\t\turi = decoded;\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// Error occurred, meaning string was not Base64-encoded. Do nothing.\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif (addBOM) {\n\t\t\t\t\turi = \"\\ufeff\" + uri;\n\t\t\t\t}\n\t\t\t\tlet blob = new Blob([uri], { type: contentType });\n\t\t\t\tlet url = window.URL.createObjectURL(blob);\n\t\t\t\tlink.href = url;\n\t\t\t\tlink.download = fileName;\n\t\t\t\tlink.click();\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tdocument.body.removeChild(link);\n\t\t\t\t\twindow.URL.revokeObjectURL(url);\n\t\t\t\t}, 100);\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// Dissect uri into array\n\t\t\tlet chars = new Array(uri.length);\n\t\t\tfor (let i = 0; i < uri.length; ++i) {\n\t\t\t\tlet charCode = uri.charCodeAt(i);\n\t\t\t\tchars[i] = charCode;\n\t\t\t}\n\n\t\t\tif (addBOM) {\n\t\t\t\tchars = [0xEF, 0xBB, 0xBF].concat(chars);\n\t\t\t}\n\t\t\tlet blob = new Blob([new Uint8Array(chars)], { type: contentType });\n\t\t\tlet url = window.URL.createObjectURL(blob);\n\t\t\tlink.href = url;\n\t\t\tlink.download = fileName;\n\t\t\tdocument.body.appendChild(link);\n\t\t\tlink.click();\n\t\t\tdocument.body.removeChild(link);\n\t\t\tsetTimeout(() => {\n\t\t\t\twindow.URL.revokeObjectURL(url);\n\t\t\t}, 100);\n\n\t\t}\n\n\t\telse if (this.linkDownloadSupport()) {\n\n\t\t\t/**\n\t\t\t * For regular browsers, we create a link then simulate a click on it\n\t\t\t */\n\n\t\t\tlet link = document.createElement(\"a\");\n\t\t\tlink.download = fileName;\n\t\t\tlink.href = uri;\n\t\t\tdocument.body.appendChild(link);\n\t\t\tlink.click();\n\t\t\tdocument.body.removeChild(link);\n\n\t\t}\n\t\telse {\n\n\t\t\t/**\n\t\t\t * Something else - perhaps a mobile.\n\t\t\t * Let's just display it in the same page.\n\t\t\t * (hey we don't like it either)\n\t\t\t */\n\t\t\twindow.location.href = uri;\n\t\t}\n\n\t\treturn true;\n\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic downloadSupport(): boolean {\n\t\treturn this.linkDownloadSupport();\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic linkDownloadSupport(): boolean {\n\t\tlet a = document.createElement(\"a\");\n\t\tlet res = typeof a.download !== \"undefined\";\n\t\treturn res;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic blobDownloadSupport(): boolean {\n\t\treturn window.Blob != null;\n\t}\n\n\n\t/**\n\t * ==========================================================================\n\t * Print\n\t * ==========================================================================\n\t */\n\n\t/**\n\t * Initiates print of the chart.\n\t *\n\t * @param   data     data:uri for the image\n\t * @param   options  Options\n\t * @param   title    Optional title to use (uses window's title by default)\n\t * @return           Promise\n\t */\n\tpublic initiatePrint(data: string, customOptions?: IExportingPrintOptions, title?: string): void {\n\t\tconst options = <IExportingPrintOptions>this._getFormatOptions(\"print\", customOptions);\n\t\tif (options.printMethod == \"css\") {\n\t\t\tthis._printViaCSS(data, options, title);\n\t\t}\n\t\telse {\n\t\t\tthis._printViaIframe(data, options, title);\n\t\t}\n\n\t}\n\n\tprotected _printViaCSS(data: string, customOptions?: IExportingPrintOptions, title?: string): void {\n\t\tconst options = <IExportingPrintOptions>this._getFormatOptions(\"print\", customOptions);\n\t\tlet delay = options.delay || 500;\n\n\t\t//Save current scroll position\n\t\tlet scroll = document.documentElement.scrollTop || document.body.scrollTop;\n\n\t\t// Hide all document nodes by applying custom CSS\n\t\tlet rule = new StyleRule($utils.getShadowRoot(this._root.dom), \"body > *\", {\n\t\t\t\"display\": \"none\",\n\t\t\t\"position\": \"fixed\",\n\t\t\t\"visibility\": \"hidden\",\n\t\t\t\"opacity\": \"0\",\n\t\t\t\"clipPath\": \"polygon(0px 0px,0px 0px,0px 0px,0px 0px);\"\n\t\t}, this._root.nonce);\n\n\t\tlet rule2 = new StyleRule($utils.getShadowRoot(this._root.dom), \"body\", {\n\t\t\t\"padding\": \"0\",\n\t\t\t\"margin\": \"0\"\n\t\t}, this._root.nonce);\n\n\t\t// Replace title?\n\t\tlet originalTitle: string;\n\t\tif (title && document && document.title) {\n\t\t\toriginalTitle = document.title;\n\t\t\tdocument.title = title;\n\t\t}\n\n\t\t// Create and add exported image\n\t\tlet img = new Image();\n\t\timg.src = data;\n\t\timg.style.maxWidth = \"100%\";\n\t\timg.style.display = \"block\";\n\t\timg.style.position = \"relative\";\n\t\timg.style.visibility = \"visible\";\n\t\timg.style.opacity = \"1\";\n\t\timg.style.clipPath = \"none\";\n\t\tdocument.body.appendChild(img);\n\n\t\t// Print\n\t\tthis.setTimeout(() => {\n\t\t\t(<any>window).print();\n\t\t}, 50);\n\n\t\t// Delay needs to be at least a second for iOS devices\n\t\tlet isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(<any>window).MSStream;\n\t\tif (isIOS && (delay < 1000)) {\n\t\t\tdelay = 1000;\n\t\t}\n\t\telse if (delay < 100) {\n\t\t\tdelay = 100;\n\t\t}\n\n\t\t// Delay function that resets back the document the way ot was before\n\t\tthis.setTimeout(() => {\n\n\t\t\t// Remove image\n\t\t\tdocument.body.removeChild(img);\n\n\t\t\t// Reset back all elements\n\t\t\trule.dispose();\n\t\t\trule2.dispose();\n\n\t\t\t// Restore title\n\t\t\tif (originalTitle) {\n\t\t\t\tdocument.title = document.title;\n\t\t\t}\n\n\t\t\t// Scroll back the document the way it was before\n\t\t\tdocument.documentElement.scrollTop = document.body.scrollTop = scroll;\n\n\t\t}, delay || 500);\n\n\t}\n\n\tprotected _printViaIframe(data: string, customOptions?: IExportingPrintOptions, title?: string): void {\n\t\tconst options = <IExportingPrintOptions>this._getFormatOptions(\"print\", customOptions);\n\t\tlet delay = options.delay || 500;\n\n\t\t// Create an iframe\n\t\tconst iframe = document.createElement(\"iframe\");\n\t\tiframe.style.visibility = \"hidden\";\n\t\tdocument.body.appendChild(iframe);\n\n\t\t// This is needed for FireFox\n\t\tiframe.contentWindow!.document.open();\n\t\tiframe.contentWindow!.document.close();\n\n\t\t// Create and add exported image\n\t\tlet img = new Image();\n\t\timg.src = data;\n\t\timg.style.maxWidth = \"100%\";\n\t\timg.style.height = \"auto\";\n\t\tif (title) {\n\t\t\tiframe.contentWindow!.document.title = title;\n\t\t}\n\t\tiframe.contentWindow!.document.body.appendChild(img);\n\n\t\t(<any>iframe).load = function() {\n\t\t\tiframe.contentWindow!.document.body.appendChild(img);\n\t\t};\n\n\t\t// Print\n\t\tthis.setTimeout(() => {\n\t\t\ttry {\n\t\t\t\tif (!(<any>iframe).contentWindow.document.execCommand(\"print\", false, null)) {\n\t\t\t\t\t(<any>iframe).contentWindow.print();\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\t(<any>iframe).contentWindow.print();\n\t\t\t}\n\t\t}, delay || 50);\n\n\t\t// Delay needs to be at least a second for iOS devices\n\t\tlet isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(<any>window).MSStream;\n\t\tif (isIOS && (delay < 1000)) {\n\t\t\tdelay = 1000;\n\t\t}\n\t\telse if (delay < 100) {\n\t\t\tdelay = 100;\n\t\t}\n\n\t\t// Delay function that resets back the document the way ot was before\n\t\tthis.setTimeout(() => {\n\n\t\t\t// Remove image\n\t\t\tdocument.body.removeChild(iframe);\n\n\t\t}, delay + 50 || 100);\n\n\t}\n\n\t/**\n\t * Returns a list of formats that can be exported in current browser.\n\t *\n\t * @return Formats\n\t */\n\tpublic supportedFormats(): ExportingFormats[] {\n\t\tconst res: ExportingFormats[] = [];\n\t\tconst hasData = this.hasData();\n\t\tconst downloadSupport = this.downloadSupport();\n\t\t$array.each(<ExportingFormats[]>[\"png\", \"jpg\", \"canvas\", \"pdf\", \"xlsx\", \"csv\", \"json\", \"html\", \"pdfdata\", \"print\"], (format) => {\n\t\t\tconst options = this._getFormatOptions(format);\n\t\t\tif (options.disabled !== true) {\n\t\t\t\tif ([\"xlsx\", \"csv\", \"json\", \"html\", \"pdfdata\"].indexOf(format) == -1 || (hasData && downloadSupport)) {\n\t\t\t\t\tres.push(<ExportingFormats>format);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\treturn res;\n\t}\n\n\t/**\n\t * Returns a list of supported export types: image or print.\n\t *\n\t * @return Supported types\n\t */\n\tpublic supportedExportTypes(): ExportingTypes[] {\n\t\tconst res: ExportingTypes[] = [\"image\", \"print\"];\n\t\tif (this.downloadSupport() && this.hasData()) {\n\t\t\tres.push(\"data\");\n\t\t}\n\t\treturn res;\n\t}\n\n}\n", "import type { Root } from \"../../core/Root\"\n\nimport { StyleRule } from \"../../core/util/Utils\"\nimport { MultiDisposer, IDisposer, CounterDisposer } from \"../../core/util/Disposer\";\n\n/**\n * @ignore\n */\nlet rules: CounterDisposer | undefined;\n\n/**\n * @ignore\n */\nexport default function(element: ShadowRoot | null, root: Root, _prefix?: string): IDisposer {\n\t//const newPrefix = (prefix ? prefix : \"am5\");\n\n\t//let colorSet = new InterfaceColorSet();\n\tconst ic = root.interfaceColors;\n\n\tif (!rules) {\n\t\tconst disposer = new MultiDisposer([\n\n\t\t\t/*new StyleRule(\".${newPrefix}-menu\", {\n\t\t\t\t\"opacity\": \"0.3\",\n\t\t\t\t\"transition\": \"all 100ms ease-in-out\",\n\t\t\t}),\n\n\t\t\tnew StyleRule(\"div:hover .${newPrefix}-menu, .${newPrefix}-menu.active\", {\n\t\t\t\t\"opacity\": \"0.9\",\n\t\t\t}),*/\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu\", {\n\t\t\t\t\"color\": ic.get(\"secondaryButtonText\")!.toCSS(),\n\t\t\t\t\"font-size\": \"0.8em\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu *\", {\n\t\t\t\t\"box-sizing\": \"border-box\",\n\t\t\t\t\"transition\": \"all 100ms ease-in-out\",\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu a\", {\n\t\t\t\t\"display\": \"block\",\n\t\t\t\t\"cursor\": \"pointer\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-type-separator\", {\n\t\t\t\t\"color\": ic.get(\"disabled\")!.toCSS(),\n\t\t\t\t\"border-bottom\": \"1px solid \" + ic.get(\"secondaryButtonDown\")!.toCSS(),\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-label-alt\", {\n\t\t\t\t\"color\": ic.get(\"disabled\")!.toCSS(),\n\t\t\t\t\"font-size\": \"0.8em\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu .am5exporting-type-separator a\", {\n\t\t\t\t\"cursor\": \"default\"\n\t\t\t}),\n\t\t\tnew StyleRule(element, \".am5exporting-menu .am5exporting-type-separator a:hover\", {\n\t\t\t\t\"background\": \"initial\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu\", {\n\t\t\t\t\"position\": \"absolute\",\n\t\t\t\t\"z-index\": \"10\"\n\t\t\t}),\n\n\t\t\t// new StyleRule(element, \".am5exporting-list:before\", {\n\t\t\t// \t// \"display\": \"block\",\n\t\t\t// \t\"width\": \"20px\",\n\t\t\t// \t\"height\": \"20px\",\n\t\t\t// \t\"content\": \"url(\\\"data:image/svg+xml; utf8, <svg fill=\\\"#f00\\\" height=\\\"20\\\" width=\\\"20\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z\\\"/></svg>\\\")\"\n\t\t\t// }),\n\n\t\t\tnew StyleRule(element, \".am5exporting-icon\", {\n\t\t\t\t\"width\": \"30px\",\n\t\t\t\t\"height\": \"26px\",\n\t\t\t\t\"position\": \"absolute\",\n\t\t\t\t\"margin\": \"5px\",\n\t\t\t\t\"padding\": \"3px 5px\",\n\t\t\t\t\"border-radius\": \"3px\",\n\t\t\t\t\"opacity\": \"0.5\",\n\t\t\t\t\"background\": \"rgba(255, 255, 255, 0.001)\",\n\t\t\t\t\"background-opacity\": \"0\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-icon:focus, .am5exporting-icon:hover, .am5exporting-menu-open .am5exporting-icon\", {\n\t\t\t\t\"background\": ic.get(\"secondaryButtonHover\")!.toCSS(),\n\t\t\t\t\"opacity\": \"1\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu path\", {\n\t\t\t\t\"fill\": ic.get(\"secondaryButtonText\")!.toCSS()\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-list\", {\n\t\t\t\t\"display\": \"none\",\n\t\t\t\t\"list-style\": \"none\",\n\t\t\t\t\"list-style-type\": \"none\",\n\t\t\t\t\"margin\": \"5px\",\n\t\t\t\t\"background\": ic.get(\"secondaryButton\")!.toCSS(),\n\t\t\t\t\"padding\": \"5px 0\",\n\t\t\t\t\"border\": \"1px solid \" + ic.get(\"secondaryButtonStroke\")!.toCSS(),\n\t\t\t\t\"border-radius\": \"3px\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu-open .am5exporting-list\", {\n\t\t\t\t\"display\": \"block\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-item\", {\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-item a\", {\n\t\t\t\t\"padding\": \"3px 15px\",\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-item a:hover, .am5exporting-item a.am5exporting-item-active\", {\n\t\t\t\t\"background\": ic.get(\"secondaryButtonHover\")!.toCSS(),\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu.am5exporting-align-left, .am5exporting-icon.am5exporting-align-left, .am5exporting-list.am5exporting-align-left\", {\n\t\t\t\t\"left\": \"0\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu.am5exporting-align-right, .am5exporting-icon.am5exporting-align-right, .am5exporting-list.am5exporting-align-right\", {\n\t\t\t\t\"right\": \"0\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu.am5exporting-valign-top, .am5exporting-icon.am5exporting-valign-top, .am5exporting-list.am5exporting-align-top\", {\n\t\t\t\t\"top\": \"0\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-menu.am5exporting-valign-bottom, .am5exporting-icon.am5exporting-valign-bottom, .am5exporting-list.am5exporting-align-bottom\", {\n\t\t\t\t\"bottom\": \"0\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-list.am5exporting-align-left\", {\n\t\t\t\t\"margin-left\": \"40px\"\n\t\t\t}),\n\n\t\t\tnew StyleRule(element, \".am5exporting-list.am5exporting-align-right\", {\n\t\t\t\t\"margin-right\": \"40px\"\n\t\t\t}),\n\n\t\t\t// new StyleRule(element, \".${newPrefix}-menu-level-0\", {\n\t\t\t// \t\"position\": \"absolute\",\n\t\t\t// \t\"top\": \"5px\",\n\t\t\t// \t\"right\": \"5px\",\n\t\t\t// })\n\t\t]);\n\n\t\trules = new CounterDisposer(() => {\n\t\t\trules = undefined;\n\t\t\tdisposer.dispose();\n\t\t});\n\t}\n\n\treturn rules.increment();\n}", "import type { Exporting, ExportingFormats, ExportingTypes } from \"./Exporting\"\n\nimport { Entity, IEntitySettings, IEntityPrivate, IEntityEvents } from \"../../core/util/Entity\"\nimport { IDisposer, Disposer } from \"../../core/util/Disposer\";\nimport exportingCSS from \"./ExportingCSS\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $utils from \"../../core/util/Utils\";\n\nexport interface IExportingMenuItem {\n\n\t/**\n\t * Indicates type of the menu item:\n\t * * `\"format\"` - indicates export action\n\t * * `\"separator\"` - will show horizontal divider.\n\t * * `\"custom\"` - will invoke custom function when clicked.\n\t */\n\ttype: \"format\" | \"separator\" | \"custom\";\n\n\t/**\n\t * If `type` is set to `\"format\"`, clicking item will initiate export in\n\t * that format.\n\t */\n\tformat?: ExportingFormats;\n\n\t/**\n\t * Indicates export type: `\"image\"`, `\"data\"`, or `\"print\"`.\n\t */\n\texportType?: ExportingTypes;\n\n\t/**\n\t * Menu label.\n\t */\n\tlabel?: string;\n\n\t/**\n\t * Additional information.\n\t */\n\tsublabel?: string;\n\n\t/**\n\t * If `type` is set to `\"custom\"`, this needs to be set to a function.\n\t */\n\tcallback?: (menuItem?: any) => any;\n\n\t/**\n\t * A target for callback function.\n\t */\n\tcallbackTarget?: any;\n\n\t/**\n\t * A DOM element for the menu item.\n\t *\n\t * @readonly\n\t */\n\telement?: HTMLAnchorElement;\n\n}\n\nexport interface IExportingMenuSettings extends IEntitySettings {\n\n\t/**\n\t * Horizontal alignment of the menu.\n\t *\n\t * @default \"right\"\n\t */\n\talign?: \"left\" | \"right\";\n\n\t/**\n\t * Vertical alignment of the menu.\n\t *\n\t * @default \"top\"\n\t */\n\tvalign?: \"top\" | \"bottom\";\n\n\t/**\n\t * A reference to an element in the document to place export menu in.\n\t *\n\t * If not set, will use root element's container.\n\t */\n\tcontainer?: HTMLElement;\n\n\t/**\n\t * A list of menu items.\n\t */\n\titems?: IExportingMenuItem[];\n\n\t/**\n\t * A reference to related [[Exporting]] object.\n\t */\n\texporting?: Exporting;\n\n\t/**\n\t * If set to `false` the legend will not load default CSS.\n\t *\n\t * @default true\n\t */\n\tuseDefaultCSS?: boolean;\n\n\t/**\n\t * If set to `true` the menu will close automatically when export operation\n\t * is initiated.\n\t *\n\t * @default true\n\t */\n\tautoClose?: boolean;\n\n\t/**\n\t * Menu will disable all interactions for the underlying chart when browsing\n\t * the menu.\n\t *\n\t * @default true\n\t */\n\tdeactivateRoot?: boolean;\n\n}\n\nexport interface IExportingMenuPrivate extends IEntityPrivate {\n\n\t/**\n\t * A `<div>` element that acts as a container for other menu elements.\n\t */\n\tmenuElement?: HTMLDivElement;\n\n\t/**\n\t * A top-level `<ul>` element containing menu items.\n\t */\n\tlistElement?: HTMLUListElement;\n\n}\n\nexport interface IExportingMenuEvents extends IEntityEvents {\n\t\"menucreated\": {}\n\t\"menuopened\": {}\n\t\"menuclosed\": {}\n}\n\n/**\n * Displays a menu for [[Exporting]].\n *\n * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/} for more info\n */\nexport class ExportingMenu extends Entity {\n\tpublic static className: string = \"ExportingMenu\";\n\tpublic static classNames: Array<string> = Entity.classNames.concat([ExportingMenu.className]);\n\n\tdeclare public _settings: IExportingMenuSettings;\n\tdeclare public _privateSettings: IExportingMenuPrivate;\n\tdeclare public _events: IExportingMenuEvents;\n\n\tprivate _menuElement?: HTMLDivElement;\n\tprivate _iconElement?: HTMLElement;\n\tprivate _listElement?: HTMLUListElement;\n\tprivate _itemElements?: HTMLLIElement[] = [];\n\tprivate _itemDisposers: Array<IDisposer> = [];\n\n\tprivate _cssDisposer?: IDisposer;\n\tprivate _activeItem?: IExportingMenuItem;\n\n\tpublic isOpen: boolean = false;\n\n\tprivate _isOver: boolean = false;\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis._setRawDefault(\"container\", this._root._inner);\n\t\tthis._setRawDefault(\"align\", \"right\");\n\t\tthis._setRawDefault(\"valign\", \"top\");\n\t\tthis._setRawDefault(\"useDefaultCSS\", true);\n\t\tthis._setRawDefault(\"autoClose\", true);\n\t\tthis._setRawDefault(\"deactivateRoot\", true);\n\t\tthis._setRawDefault(\"items\", [{\n\t\t\ttype: \"separator\",\n\t\t\tlabel: this._t(\"Export\")\n\t\t}, {\n\t\t\ttype: \"format\",\n\t\t\tformat: \"png\",\n\t\t\texportType: \"image\",\n\t\t\tlabel: this._t(\"PNG\"),\n\t\t\tsublabel: this._t(\"Image\")\n\t\t}, {\n\t\t\ttype: \"format\",\n\t\t\tformat: \"jpg\",\n\t\t\texportType: \"image\",\n\t\t\tlabel: this._t(\"JPG\"),\n\t\t\tsublabel: this._t(\"Image\")\n\t\t}, {\n\t\t\ttype: \"format\",\n\t\t\tformat: \"pdf\",\n\t\t\texportType: \"image\",\n\t\t\tlabel: this._t(\"PDF\"),\n\t\t\tsublabel: this._t(\"Image\")\n\t\t}, {\n\t\t\ttype: \"separator\",\n\t\t\texportType: \"data\",\n\t\t\t//label: this._t(\"Data\")\n\t\t}, {\n\t\t\ttype: \"format\",\n\t\t\tformat: \"json\",\n\t\t\texportType: \"data\",\n\t\t\tlabel: this._t(\"JSON\"),\n\t\t\tsublabel: this._t(\"Data\")\n\t\t}, {\n\t\t\ttype: \"format\",\n\t\t\tformat: \"csv\",\n\t\t\texportType: \"data\",\n\t\t\tlabel: this._t(\"CSV\"),\n\t\t\tsublabel: this._t(\"Data\")\n\t\t}, {\n\t\t\ttype: \"format\",\n\t\t\tformat: \"xlsx\",\n\t\t\texportType: \"data\",\n\t\t\tlabel: this._t(\"XLSX\"),\n\t\t\tsublabel: this._t(\"Data\")\n\t\t}, {\n\t\t\ttype: \"format\",\n\t\t\tformat: \"pdfdata\",\n\t\t\texportType: \"data\",\n\t\t\tlabel: this._t(\"PDF\"),\n\t\t\tsublabel: this._t(\"Data\")\n\t\t}, {\n\t\t\ttype: \"format\",\n\t\t\tformat: \"html\",\n\t\t\texportType: \"data\",\n\t\t\tlabel: this._t(\"HTML\"),\n\t\t\tsublabel: this._t(\"Data\")\n\t\t}, {\n\t\t\ttype: \"separator\"\n\t\t}, {\n\t\t\ttype: \"format\",\n\t\t\tformat: \"print\",\n\t\t\texportType: \"print\",\n\t\t\tlabel: this._t(\"Print\")\n\t\t}]);\n\n\t\tconst menuElement = document.createElement(\"div\");\n\t\tthis._menuElement = menuElement;\n\t\tthis.setPrivate(\"menuElement\", this._menuElement);\n\n\t\tconst iconElement = document.createElement(\"a\");\n\t\tthis._iconElement = iconElement;\n\n\t\tthis._listElement = document.createElement(\"ul\");\n\t\tthis._listElement.setAttribute(\"role\", \"menu\");\n\t\tthis.setPrivate(\"listElement\", this._listElement);\n\t\tthis._applyClassNames();\n\n\t\ticonElement.innerHTML = '<svg fill=\"none\" height=\"20\" width=\"20\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z\"/></svg>';\n\t\ticonElement.setAttribute(\"tabindex\", this._root.tabindex.toString());\n\t\ticonElement.setAttribute(\"aria-label\", this._t(\"Export\") + \"; \" + this._t(\"Press ENTER to open\"));\n\t\ticonElement.setAttribute(\"role\", \"button\");\n\n\t\tif ($utils.supports(\"keyboardevents\")) {\n\t\t\tthis._disposers.push($utils.addEventListener(document, \"keydown\", (ev: KeyboardEvent) => {\n\t\t\t\tconst eventKey = $utils.getEventKey(ev);\n\t\t\t\tif (document.activeElement == this._iconElement || this.isOpen) {\n\t\t\t\t\tif (eventKey == \"Escape\") {\n\t\t\t\t\t\t// ESC\n\t\t\t\t\t\tthis.close();\n\t\t\t\t\t}\n\t\t\t\t\telse if (eventKey == \"Enter\") {\n\t\t\t\t\t\t// ENTER\n\t\t\t\t\t\tif (this._activeItem) {\n\t\t\t\t\t\t\tthis._handleClick(this._activeItem);\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthis.toggle();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse if (eventKey == \"ArrowUp\" || eventKey == \"ArrowDown\") {\n\t\t\t\t\t\tconst items = this.get(\"items\", []);\n\t\t\t\t\t\tlet currentIndex = (<any>items).indexOf(this._activeItem);\n\t\t\t\t\t\tif (this.get(\"valign\") == \"top\" && currentIndex == -1) {\n\t\t\t\t\t\t\tcurrentIndex = items.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst dir = eventKey == \"ArrowUp\" ? -1 : 1;\n\t\t\t\t\t\tlet newIndex = currentIndex + dir;\n\t\t\t\t\t\tlet newItem;\n\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\tif (newIndex < 0) {\n\t\t\t\t\t\t\t\tnewIndex = items.length - 1;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse if (newIndex > (items.length - 1)) {\n\t\t\t\t\t\t\t\tnewIndex = 0;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (items[newIndex].type == \"separator\") {\n\t\t\t\t\t\t\t\tnewIndex += dir;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tnewItem = items[newIndex];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} while (!newItem);\n\n\t\t\t\t\t\tif (newItem) {\n\t\t\t\t\t\t\tthis._handleItemFocus(newItem);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}));\n\t\t}\n\n\t\tthis._disposers.push($utils.addEventListener(iconElement, \"click\", (ev: MouseEvent) => {\n\t\t\tev.stopImmediatePropagation();\n\t\t\tthis.toggle();\n\t\t}));\n\n\t\tmenuElement.appendChild(this._iconElement);\n\t\tmenuElement.appendChild(this._listElement);\n\n\t\tconst container = this.get(\"container\", this._root._inner);\n\n\t\tcontainer.appendChild(this._menuElement);\n\n\t\tthis._disposers.push($utils.addEventListener(menuElement, $utils.getRendererEvent(\"pointerover\"), (_ev) => {\n\t\t\tthis._isOver = true;\n\t\t\tif (this.get(\"deactivateRoot\")) {\n\t\t\t\tthis._root._renderer.interactionsEnabled = false;\n\t\t\t}\n\t\t}));\n\n\t\tthis._disposers.push($utils.addEventListener(menuElement, $utils.getRendererEvent(\"pointerout\"), (_ev) => {\n\t\t\tif (this.get(\"deactivateRoot\") && (this.isOpen || this._isOver)) {\n\t\t\t\tthis._root._renderer.interactionsEnabled = true;\n\t\t\t}\n\t\t\tthis._isOver = false;\n\t\t}));\n\n\t\tthis._disposers.push(new Disposer(() => {\n\t\t\tif (this._menuElement) {\n\t\t\t\tcontainer.removeChild(this._menuElement);\n\t\t\t}\n\t\t}));\n\n\t\tthis._disposers.push($utils.addEventListener(document, \"click\", (_ev) => {\n\t\t\tif (this.isOpen && !this._isOver) {\n\t\t\t\tthis.close();\n\t\t\t}\n\t\t}));\n\n\t\tthis.loadDefaultCSS();\n\n\t\tthis._root.addDisposer(this);\n\n\t\tthis.events.dispatch(\"menucreated\", {\n\t\t\ttype: \"menucreated\",\n\t\t\ttarget: this\n\t\t});\n\t}\n\n\tpublic _afterChanged() {\n\t\tsuper._afterChanged();\n\n\t\tif (this._itemElements!.length == 0) {\n\t\t\tthis.createItems();\n\t\t}\n\n\t\tif (this.isDirty(\"useDefaultCSS\")) {\n\t\t\tif (this.get(\"useDefaultCSS\")) {\n\t\t\t\tthis.loadDefaultCSS();\n\t\t\t}\n\t\t\telse if (this._cssDisposer) {\n\t\t\t\tthis._cssDisposer.dispose();\n\t\t\t}\n\t\t}\n\n\t\tif (this.isDirty(\"exporting\")) {\n\t\t\tconst exporting = this.get(\"exporting\");\n\t\t\tif (exporting) {\n\t\t\t\tthis.createItems();\n\t\t\t}\n\t\t}\n\n\t\tif (this.isDirty(\"align\") || this.isDirty(\"valign\")) {\n\t\t\tthis._applyClassNames();\n\t\t}\n\n\t\tif (this.isDirty(\"container\")) {\n\t\t\tconst container = this.get(\"container\");\n\t\t\tif (container) {\n\t\t\t\tcontainer.appendChild(this._menuElement!);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _dispose(): void {\n\t\tsuper._dispose();\n\n\t\t$array.each(this._itemDisposers, (x) => {\n\t\t\tx.dispose();\n\t\t});\n\t}\n\n\tprivate _applyClassNames(): void {\n\t\tconst align = this.get(\"align\", \"right\");\n\t\tconst valign = this.get(\"valign\", \"top\");\n\t\tconst status = this.isOpen ? \"am5exporting-menu-open\" : \"am5exporting-menu-closed\";\n\n\t\tthis._menuElement!.className = \"am5exporting am5exporting-menu am5exporting-align-\" + align + \" am5exporting-valign-\" + valign + \" \" + status;\n\t\tthis._iconElement!.className = \"am5exporting am5exporting-icon am5exporting-align-\" + align + \" am5exporting-valign-\" + valign;\n\t\tthis._listElement!.className = \"am5exporting am5exporting-list am5exporting-align-\" + align + \" am5exporting-valign-\" + valign;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic createItems(): void {\n\t\tconst exporting = this.get(\"exporting\");\n\t\tif (!exporting) {\n\t\t\treturn;\n\t\t}\n\t\tthis._listElement!.innerHTML = \"\";\n\t\tthis._itemElements = [];\n\t\tconst items = this.get(\"items\", []);\n\t\tconst supportedFormats = exporting.supportedFormats();\n\t\tconst supportedExportTypes = exporting.supportedExportTypes();\n\n\t\t$array.each(this._itemDisposers, (x) => {\n\t\t\tx.dispose();\n\t\t});\n\n\t\tthis._itemDisposers.length = 0;\n\n\t\t$array.each(items, (item) => {\n\n\t\t\tif (item.format && supportedFormats.indexOf(item.format) == -1) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (item.exportType && supportedExportTypes.indexOf(item.exportType) == -1) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst li = document.createElement(\"li\");\n\t\t\tli.setAttribute(\"role\", \"menuitem\");\n\n\t\t\tli.className = \"am5exporting am5exporting-item am5exporting-type-\" + item.type;\n\t\t\tif (item.format) {\n\t\t\t\tli.className += \" am5exporting-format-\" + item.format;\n\t\t\t}\n\n\t\t\tconst a = document.createElement(\"a\");\n\n\t\t\tlet ariaLabel = this._t(\"Export\");\n\t\t\tif (item.label) {\n\t\t\t\ta.innerHTML = item.label;\n\t\t\t\tariaLabel += \" \" + item.label;\n\t\t\t}\n\n\t\t\tif (item.sublabel) {\n\t\t\t\ta.innerHTML += \" <span class=\\\"am5exporting-label-alt\\\">\" + item.sublabel + \"</span>\";\n\t\t\t\tariaLabel += \" (\" + item.sublabel + \")\";\n\t\t\t}\n\n\t\t\tif (item.callback) {\n\t\t\t\tthis._itemDisposers.push($utils.addEventListener(a, \"click\", (_ev) => {\n\t\t\t\t\titem.callback!.call(item.callbackTarget || this)\n\t\t\t\t}));\n\t\t\t\ta.setAttribute(\"tabindex\", this._root.tabindex.toString());\n\t\t\t}\n\t\t\telse if (item.format && exporting) {\n\t\t\t\tthis._itemDisposers.push($utils.addEventListener(a, \"click\", (_ev) => {\n\t\t\t\t\tthis._handleClick(item);\n\t\t\t\t}));\n\t\t\t\tthis._itemDisposers.push($utils.addEventListener(a, \"focus\", (_ev) => {\n\t\t\t\t\tthis._handleItemFocus(item);\n\t\t\t\t}));\n\t\t\t\tthis._itemDisposers.push($utils.addEventListener(a, \"blur\", (_ev) => {\n\t\t\t\t\tthis._handleItemBlur(item);\n\t\t\t\t}));\n\t\t\t\ta.setAttribute(\"tabindex\", this._root.tabindex.toString());\n\t\t\t\ta.setAttribute(\"aria-label\", ariaLabel);\n\t\t\t}\n\n\t\t\titem.element = a;\n\t\t\tli.appendChild(a);\n\t\t\tthis._listElement!.appendChild(li);\n\t\t\tthis._itemElements!.push(li);\n\t\t});\n\t}\n\n\tprivate _handleClick(item: IExportingMenuItem): void {\n\t\tconst exporting = this.get(\"exporting\")!;\n\t\tif (this.get(\"autoClose\")) {\n\t\t\tthis.close();\n\t\t}\n\t\tif (item.format == \"print\") {\n\t\t\texporting.print();\n\t\t}\n\t\telse {\n\t\t\texporting.download(item.format!);\n\t\t}\n\t}\n\n\tprivate _handleItemFocus(item: IExportingMenuItem): void {\n\t\tif (item != this._activeItem) {\n\t\t\tif (this._activeItem) {\n\t\t\t\tthis._activeItem.element!.className = \"\";\n\t\t\t}\n\t\t\tthis._activeItem = item;\n\t\t\titem.element!.className = \"am5exporting-item-active\";\n\t\t\titem.element!.focus();\n\t\t}\n\t}\n\n\tprivate _handleItemBlur(item: IExportingMenuItem): void {\n\t\titem.element!.className = \"\";\n\t\tif (item == this._activeItem) {\n\t\t\tthis._activeItem = undefined\n\t\t}\n\t\tthis.setTimeout(() => {\n\t\t\tif (!document.activeElement || !$utils.contains(this.get(\"container\")!, document.activeElement!)) {\n\t\t\t\tthis.close();\n\t\t\t}\n\t\t}, 10);\n\n\t}\n\n\t/**\n\t * Loads the default CSS.\n\t *\n\t * @ignore Exclude from docs\n\t */\n\tpublic loadDefaultCSS(): void {\n\t\tconst disposer = exportingCSS($utils.getShadowRoot(this._root.dom), this._root);\n\t\tthis._disposers.push(disposer);\n\t\tthis._cssDisposer = disposer;\n\n\t\t// if (this._element) {\n\t\t// \tthis._element.style.display = \"\";\n\t\t// }\n\t}\n\n\t/**\n\t * Opens menu.\n\t */\n\tpublic open(): void {\n\t\tthis.setTimeout(() => {\n\t\t\tthis.isOpen = true;\n\t\t\tif (this.get(\"deactivateRoot\")) {\n\t\t\t\tthis._root._renderer.interactionsEnabled = false;\n\t\t\t}\n\t\t\tthis._applyClassNames();\n\t\t\tthis.events.dispatch(\"menuopened\", {\n\t\t\t\ttype: \"menuopened\",\n\t\t\t\ttarget: this\n\t\t\t});\n\t\t}, 1);\n\t}\n\n\t/**\n\t * Closes menu.\n\t */\n\tpublic close(): void {\n\t\tthis.isOpen = false;\n\t\tif (this.get(\"deactivateRoot\")) {\n\t\t\tthis._root._renderer.interactionsEnabled = true;\n\t\t}\n\t\t$utils.blur();\n\t\tthis._applyClassNames();\n\t\tthis.events.dispatch(\"menuclosed\", {\n\t\t\ttype: \"menuclosed\",\n\t\t\ttarget: this\n\t\t});\n\t}\n\n\t/**\n\t * Toggles menu open and close.\n\t */\n\tpublic toggle(): void {\n\t\tif (this.isOpen) {\n\t\t\tthis.close();\n\t\t}\n\t\telse {\n\t\t\tthis.open();\n\t\t}\n\t}\n\n\n}\n", "import { Entity, IEntitySettings, IEntityPrivate, IEntityEvents } from \"../../core/util/Entity\"\nimport { Container } from \"../../core/render/Container\";\nimport { Picture } from \"../../core/render/Picture\";\nimport * as $utils from \"../../core/util/Utils\";\nimport * as $object from \"../../core/util/Object\";\n\nimport { p100 } from \"../../core/util/Percent\";\n\nexport interface IAnnotatorSettings extends IEntitySettings {\n\n\t/**\n\t * Layer number to use for annotations.\n\t *\n\t * @default 1000\n\t */\n\tlayer?: number;\n\n\t/**\n\t * Raw annotation info saved by MarkerJS.\n\t */\n\tmarkerState?: any;\n\n\t/**\n\t * MarkerArea settings in form of an object where keys are setting names and\n\t * value is a setting value. E.g.:\n\t *\n\t * ```TypeScript\n\t * let annotator = am5plugins_exporting.Annotator.new(root, {\n\t *  markerSettings: {\n\t *    defaultColorSet: [\"red\", \"green\", \"blue\"],\n\t *    wrapText: true\n\t *  }\n\t *});\n\t * ```\n\t * ```JavaScript\n\t * var annotator = am5plugins_exporting.Annotator.new(root, {\n\t *  markerSettings: {\n\t *    defaultColorSet: [\"red\", \"green\", \"blue\"],\n\t *    wrapText: true\n\t *  }\n\t *});\n\t * ```\n\t *\n\t * @see {@link https://markerjs.com/reference/classes/settings.html} for a full list of settings\n\t * @since 5.7.4\n\t */\n\tmarkerSettings?: {[index: string]: any};\n\n\t/**\n\t * MarkerArea style settings for user interface elements.E.g.:\n\t *\n\t * ```TypeScript\n\t * let annotator = am5plugins_exporting.Annotator.new(root, {\n\t *  markerStyleSettings: {\n\t *    toolboxColor: \"#F472B6\",\n\t *    toolboxAccentColor: \"#BE185D\"\n\t *  }\n\t *});\n\t * ```\n\t * ```JavaScript\n\t * var annotator = am5plugins_exporting.Annotator.new(root, {\n\t *  markerStyleSettings: {\n\t *    toolboxColor: \"#F472B6\",\n\t *    toolboxAccentColor: \"#BE185D\"\n\t *  }\n\t *});\n\t * ```\n\t *\n\t * @see {@link https://markerjs.com/reference/classes/settings.html} for a full list of settings\n\t * @since 5.7.5\n\t */\n\tmarkerStyleSettings?: {[index: string]: any};\n\n}\n\nexport interface IAnnotatorPrivate extends IEntityPrivate {\n}\n\nexport interface IAnnotatorEvents extends IEntityEvents {\n}\n\n\n\n/**\n * A plugin that can be used to annotate charts.\n *\n * @see {@link https://www.amcharts.com/docs/v5/concepts/exporting/annotator/} for more info\n */\nexport class Annotator extends Entity {\n\tpublic static className: string = \"Annotator\";\n\tpublic static classNames: Array<string> = Entity.classNames.concat([Annotator.className]);\n\n\tdeclare public _settings: IAnnotatorSettings;\n\tdeclare public _privateSettings: IAnnotatorPrivate;\n\tdeclare public _events: IAnnotatorEvents;\n\n\tprivate _container?: Container;\n\tprivate _picture?: Picture;\n\tprivate _markerArea?: any;\n\tprivate _skipRender?: boolean = false;\n\n\t//public extraImages: Array<Root | IAnnotatorImageSource> = [];\n\t//public dataSources: any[] = [];\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis._setRawDefault(\"layer\", 1000);\n\t\tthis._root.addDisposer(this);\n\t}\n\n\tpublic _beforeChanged() {\n\t\tsuper._beforeChanged();\n\n\t\tif (this.isDirty(\"markerState\")) {\n\t\t\tthis._renderState();\n\t\t}\n\t}\n\n\t/**\n\t * Triggers annotation mode on the chart. This will display UI toolbars and\n\t * disable all interactions on the chart itself.\n\t */\n\tpublic async open() {\n\n\t\t// Delay this so that it's not knocked off by closing of the ExportingMenu\n\t\tthis.setTimeout(() => {\n\t\t\tthis._root._renderer.interactionsEnabled = false;\n\t\t}, 100)\n\n\t\tconst markerArea = await this.getMarkerArea();\n\t\tmarkerArea.show();\n\t\tthis._picture!.hide(0);\n\t\tif (this.get(\"markerState\")) {\n\t\t\tmarkerArea.restoreState(this.get(\"markerState\"));\n\t\t}\n\t}\n\n\tpublic async _renderState() {\n\t\tconst markerArea = await this.getMarkerArea();\n\t\tif (this.get(\"markerState\")) {\n\t\t\tthis._skipRender = true;\n\t\t\tmarkerArea.renderState(this.get(\"markerState\"));\n\t\t}\n\t}\n\n\t/**\n\t * Exists from annotation mode. All annotations remain visible on the chart.\n\t */\n\tpublic async close() {\n\t\t//this._root._renderer.interactionsEnabled = true;\n\t\tconst markerArea = await this.getMarkerArea();\n\t\tmarkerArea!.close();\n\t\tthis._markerArea = undefined;\n\t}\n\n\t/**\n\t * Exits from annotation mode. Any changes made during last session of the\n\t * annotation editing are cancelled.\n\t */\n\tpublic async cancel() {\n\t\tthis._root._renderer.interactionsEnabled = true;\n\t\tconst markerArea = await this.getMarkerArea();\n\t\tthis._picture!.show(0);\n\t\tmarkerArea!.close();\n\t\tthis._markerArea = undefined;\n\t\t//markerArea!.cancel();\n\t}\n\n\t/**\n\t * All annotations are removed.\n\t */\n\tpublic clear() {\n\t\tthis.set(\"markerState\", undefined);\n\t\tif (this._picture) {\n\t\t\tthis._picture.set(\"src\", \"\");\n\t\t}\n\t}\n\n\tpublic async toggle() {\n\t\tconst markerArea = await this.getMarkerArea();\n\t\tif (markerArea.isOpen) {\n\t\t\tthis.close();\n\t\t}\n\t\telse {\n\t\t\tthis.open();\n\t\t}\n\t}\n\n\tpublic dispose(): void {\n\t\tsuper.dispose();\n\t\tif (this._markerArea && this._markerArea.isOpen) {\n\t\t\tthis._markerArea.close();\n\t\t}\n\t}\n\n\tprivate async _maybeInit() {\n\n\t\t// Create layer canvas\n\t\tif (!this._container) {\n\t\t\tthis._container = this._root.container.children.push(Container.new(this._root, {\n\t\t\t\twidth: p100,\n\t\t\t\theight: p100,\n\t\t\t\tlayer: this.get(\"layer\"),\n\t\t\t\tinteractiveChildren: false\n\t\t\t}));\n\n\t\t\tthis._picture = this._container.children.push(Picture.new(this._root, {\n\t\t\t\twidth: p100,\n\t\t\t\theight: p100\n\t\t\t}));\n\t\t}\n\n\t\t// Create MarkerArea\n\t\tif (!this._markerArea) {\n\t\t\tconst markerjs2 = await this._getMarkerJS();\n\t\t\tconst canvas = this._container._display.getCanvas();\n\t\t\tconst markerArea = new markerjs2.MarkerArea(canvas);\n\t\t\t//markerArea.renderTarget = canvas;\n\t\t\tmarkerArea.uiStyleSettings.logoPosition = \"right\";\n\t\t\tmarkerArea.uiStyleSettings.zIndex = 20;\n\t\t\tmarkerArea.targetRoot = canvas.parentElement!;\n\n\t\t\t// Apply custom settings\n\t\t\tconst markerSettings = this.get(\"markerSettings\", {});\n\t\t\t$object.each(markerSettings, (key, value) => {\n\t\t\t\tmarkerArea.settings[key] = value;\n\t\t\t});\n\n\t\t\tconst markerStyleSettings = this.get(\"markerStyleSettings\", {});\n\t\t\t$object.each(markerStyleSettings, (key, value) => {\n\t\t\t\tmarkerArea.uiStyleSettings[key] = value;\n\t\t\t});\n\n\t\t\tthis._disposers.push($utils.addEventListener(markerArea, \"close\", () => {\n\t\t\t\tthis._root._renderer.interactionsEnabled = true;\n\t\t\t\tthis._picture!.show(0);\n\t\t\t\tthis._markerArea = undefined;\n\t\t\t}));\n\n\t\t\tthis._disposers.push($utils.addEventListener(markerArea, \"render\", (event: any) => {\n\t\t\t\tconst picture = this._picture!;\n\t\t\t\tpicture.set(\"src\", event.dataUrl);\n\t\t\t\tif (!this._skipRender) {\n\t\t\t\t\tthis.set(\"markerState\", event.state);\n\t\t\t\t}\n\t\t\t\tthis._skipRender = false;\n\t\t\t}));\n\n\t\t\tthis._markerArea = markerArea;\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprivate async _getMarkerJS(): Promise<any> {\n\t\treturn await import(/* webpackChunkName: \"markerjs2\" */ \"markerjs2\");\n\t}\n\n\t/**\n\t * An instance of MarkerJS's [[MarkerArea]].\n\t *\n\t * @see {@link https://markerjs.com/docs/getting-started} for more info\n\t * @return MarkerArea\n\t */\n\tpublic async getMarkerArea(): Promise<any> {\n\t\tawait this._maybeInit();\n\t\treturn this._markerArea;\n\t}\n\n}\n", "import * as m from \"./../../../dist/es2015/plugins/exporting.js\";\nexport const am5plugins_exporting = m;"], "names": ["pdfmakePromise", "Exporting", "Entity", "_afterNew", "super", "this", "_setRawDefault", "quality", "maintainPixelRatio", "delay", "printMethod", "imageFormat", "indent", "renameFields", "separator", "addColumnNames", "emptyAs", "addBOM", "fontSize", "align", "addURL", "_root", "addDisposer", "_beforeChanged", "isDirty", "menu", "get", "set", "_disposers", "push", "_getFormatOptions", "format", "options", "newOptions", "key", "value", "download", "customOptions", "ext", "fileName", "events", "dispatch", "type", "target", "uri", "export", "streamFile", "print", "initiatePrint", "promise", "_runTickerNow", "exportImage", "exportJSON", "exportCSV", "exportHTML", "exportXLSX", "exportPDF", "exportPDFData", "canvas", "get<PERSON>anvas", "data", "toDataURL", "getContentType", "dispose<PERSON><PERSON><PERSON>", "exportCanvas", "mainCanvas", "_renderer", "_rootContainer", "_display", "extraImages", "middleLeft", "middleTop", "middleWidth", "width", "middleHeight", "height", "extraRight", "extraBottom", "extras", "extraRoot", "extra", "Root", "source", "position", "marginTop", "marginRight", "marginBottom", "marginLeft", "extraCanvas", "extraWidth", "extraHeight", "crop", "Math", "max", "left", "top", "newCanvas", "getDisposableCanvas", "ctx", "getContext", "background", "findBackgroundColor", "dom", "backgroundOpacity", "fillStyle", "toCSS", "fillRect", "right", "bottom", "drawImage", "encodeURIComponent", "getJSON", "JSON", "stringify", "getData", "_key", "field", "item", "convertToSpecialFormat", "getCSV", "csv", "br", "dataFields", "get<PERSON>ata<PERSON><PERSON>s", "pivot", "dataFieldsOrder", "val", "dataRow", "len", "length", "i", "dataValue", "getCSVRow", "undefined", "a", "b", "ai", "indexOf", "bi", "row", "reverse", "asIs", "items", "_name", "convertEmptyValue", "replace", "forceQuotes", "search", "RegExp", "join", "getHTML", "html", "tableClass", "getHTMLRow", "headerRow", "rowClass", "tag", "first", "charCodeAt", "useTag", "cellClass", "getXLSX", "XLSX", "getXLSXLib", "wbOptions", "bookType", "bookSST", "sheetName", "_normalizeExcelSheetName", "_t", "wb", "SheetNames", "Sheets", "wsData", "getXLSXRow", "utils", "aoa_to_sheet", "workbook", "workbookOptions", "xlsx", "write", "name", "substr", "_xlsx", "getPDF", "includeImage", "includeData", "dataOptions", "orientation", "pageOrientation", "image", "pdfmake", "getPdfmake", "doc", "pageSize", "<PERSON><PERSON><PERSON><PERSON>", "defaultStyle", "font", "content", "title", "extraMargin", "text", "bold", "margin", "document", "location", "href", "alignment", "fit", "getPageSizeFit", "hasData", "table", "getPDFData", "fonts", "vfs", "addFont", "paths", "normal", "path", "bytes", "italics", "bolditalics", "extraFonts", "Promise", "success", "_error", "createPdf", "getBase64", "body", "getPDFDataRow", "headerRows", "all", "default", "vfs_fonts", "global", "window", "pdfMake", "_pdfmake", "margins", "<PERSON><PERSON><PERSON><PERSON>", "fitSize", "A0", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "A10", "B0", "B1", "B2", "B3", "B4", "B5", "B6", "B7", "B8", "B9", "B10", "C0", "C1", "C2", "C3", "C4", "C5", "C6", "C7", "C8", "C9", "C10", "RA0", "RA1", "RA2", "RA3", "RA4", "SRA0", "SRA1", "SRA2", "SRA3", "SRA4", "EXECUTIVE", "FOLIO", "LEGAL", "LETTER", "TABLOID", "dataSource", "newRow", "event", "_value", "_field", "keepOriginal", "isDateField", "Date", "isNumericField", "numberF<PERSON>atter", "isDurationField", "durationFormatter", "useTimestamps", "getTime", "useLocale", "toLocaleString", "dateF<PERSON><PERSON><PERSON>", "contentType", "createElement", "style", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "element", "opacity", "currentColor", "getComputedStyle", "getPropertyValue", "match", "parent", "parentElement", "Color", "fromHex", "fromCSS", "blobDownloadSupport", "link", "parts", "split", "shift", "decodeURIComponent", "blob", "Blob", "url", "URL", "createObjectURL", "click", "setTimeout", "revokeObjectURL", "atob", "e", "chars", "Array", "charCode", "concat", "Uint8Array", "linkDownloadSupport", "downloadSupport", "_printViaCSS", "_printViaIframe", "originalTitle", "scroll", "documentElement", "scrollTop", "rule", "StyleRule", "nonce", "rule2", "img", "Image", "src", "max<PERSON><PERSON><PERSON>", "display", "visibility", "clipPath", "test", "navigator", "userAgent", "MSStream", "dispose", "iframe", "contentWindow", "open", "close", "load", "execCommand", "supportedFormats", "res", "disabled", "supportedExportTypes", "classNames", "className", "rules", "ExportingMenu", "_inner", "label", "exportType", "sublabel", "menuElement", "_menuElement", "setPrivate", "iconElement", "_iconElement", "_listElement", "setAttribute", "_applyClassNames", "innerHTML", "tabindex", "toString", "ev", "eventKey", "activeElement", "isOpen", "_activeItem", "_handleClick", "toggle", "currentIndex", "dir", "newItem", "newIndex", "_handleItemFocus", "stopImmediatePropagation", "container", "_ev", "_isOver", "interactionsEnabled", "Disposer", "loadDefaultCSS", "_afterChanged", "_itemElements", "createItems", "_cssDisposer", "_dispose", "_itemDisposers", "x", "valign", "status", "exporting", "li", "aria<PERSON><PERSON><PERSON>", "callback", "call", "callback<PERSON><PERSON><PERSON>", "_handleItemBlur", "focus", "disposer", "root", "_prefix", "ic", "interfaceColors", "increment", "Annotator", "_renderState", "markerArea", "getMarkerArea", "show", "_picture", "hide", "restoreState", "_skipRender", "renderState", "_<PERSON><PERSON><PERSON>", "cancel", "clear", "_maybeInit", "_container", "children", "Container", "new", "layer", "interactiveChildren", "Picture", "markerjs2", "_getMarkerJS", "<PERSON><PERSON><PERSON><PERSON>", "uiStyleSettings", "logoPosition", "zIndex", "targetRoot", "markerSettings", "settings", "markerStyleSettings", "dataUrl", "state", "am5plugins_exporting"], "sourceRoot": ""}