var root, chart, xAxis, yAxis, series;
var rootTwo, chartTwo, xAxisTwo, yAxisTwo, seriesTwo;
var seriesRangeDataItem, seriesRange;
var averageLine, aboveAverage, belowAverage;
var xAxisTitle, yAxisTitle;

/*
 * START TEAM CHART SECTION
 */

function getTeamRankingChart(data) {
    $(".message-div").addClass("d-none");

    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    root = am5.Root.new("chartdiv");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    root.numberFormatter.set("numberFormat", "#");

    // Create chart
    chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "panX",
        wheelY: "zoomX",
        paddingLeft: 0,
        layout: root.verticalLayout
    }));

    chart.set("cursor", am5xy.XYCursor.new(root, {
        behavior: "zoomX"
    }));

    var inversed = false;
    if (isOpposite(getPageEventType(), getPageTagType())) {
        inversed = true;
    }

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xRenderer = am5xy.AxisRendererX.new(root, {
        minGridDistance: 15,
        minorGridEnabled: true,
        inversed: inversed
    });
    xRenderer.grid.template.setAll({
        location: 1,
        strokeOpacity: 0.5
    });
    xRenderer.labels.template.setAll({
        rotation: -90,
        centerY: am5.p50,
        centerX: am5.p100,
        paddingRight: 35,
        fontWeight: "bold",
        fontSize: 13
    });

    xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        categoryField: "team",
        renderer: xRenderer/*,
         bullet: function (root, axis, dataItem) {
         return am5xy.AxisBullet.new(root, {
         location: 0.5,
         sprite: am5.Picture.new(root, {
         width: 48,
         height: 48,
         centerY: am5.p50,
         centerX: am5.p50,
         src: dataItem.dataContext.logo,
         shadowColor: am5.color(0x000000),
         shadowBlur: 4,
         shadowOffsetX: 4,
         shadowOffsetY: 4,
         shadowOpacity: 0.6
         })
         });
         }*/
    }));
    xAxis.data.setAll(data);

    yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        extraMax: 0.1,
        extraMin: 0.1,
        strictMinMax: true,
        renderer: am5xy.AxisRendererY.new(root, {
            strokeOpacity: 0.1
        })
    }));

//    // Add series
//    // https://www.amcharts.com/docs/v5/charts/xy-chart/series/
//    series = chart.series.push(am5xy.ColumnSeries.new(root, {
//        xAxis: xAxis,
//        yAxis: yAxis,
//        valueYField: "total",
//        categoryXField: "team"
//    }));
//
//    series.columns.template.setAll({
//        tooltipText: "{categoryX}: {valueY}",
//        tooltipY: 0,
//        strokeOpacity: 0,
//        templateField: "columnSettings"
//    });
//
//    // Set color for each bar
//    updateColors();
//
//    // Add Label bullet
//    series.bullets.push(function () {
//        return am5.Bullet.new(root, {
//            locationY: 1,
//            sprite: am5.Label.new(root, {
//                text: "{valueY}",
//                fill: root.interfaceColors.get("alternativeText"),
//                centerY: 0,
//                centerX: am5.p50,
//                populateText: true
//            })
//        });
//    });
//
//    // Rounded corners for columns
//    series.columns.template.setAll({
//        cornerRadiusTL: 5,
//        cornerRadiusTR: 5,
//        strokeOpacity: 0
//    });
//
//    series.data.setAll(data);

    // Average line
//    updateAverage();

    // Set up export and annotation
    var exporting = am5plugins_exporting.Exporting.new(root, {
        menu: am5plugins_exporting.ExportingMenu.new(root, {})
    });

    var annotator = am5plugins_exporting.Annotator.new(root, {});

    // Exporting + Annotate menu
    var menuitems = exporting.get("menu").get("items");
    menuitems.push({
        type: "separator"
    });
    menuitems.push({
        type: "custom",
        label: "Annotate",
        callback: function () {
            this.close();
            annotator.toggle();
        }
    });

    // animation on load
//    series.appear(1500, 100);
    chart.appear(1000, 100);
}

function createTeamRankingAxis(chart, data) {
    if (typeof xAxis !== "undefined") {
        removeXAxis(xAxis);
    }
    if (typeof yAxis !== "undefined") {
        removeYAxis(yAxis);
    }

    var inversed = false;
    if (isOpposite(getPageEventType(), getPageTagType())) {
        inversed = true;
    }

    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xRenderer = am5xy.AxisRendererX.new(root, {
        minGridDistance: 15,
        minorGridEnabled: true,
        inversed: inversed
    });
    xRenderer.grid.template.setAll({
        location: 1,
        strokeOpacity: 0.5
    });
    xRenderer.labels.template.setAll({
        rotation: -90,
        centerY: am5.p50,
        centerX: am5.p100,
        paddingRight: 35,
        fontWeight: "bold",
        fontSize: 13
    });

    xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        categoryField: "team",
        renderer: xRenderer/*,
         bullet: function (root, axis, dataItem) {
         return am5xy.AxisBullet.new(root, {
         location: 0.5,
         sprite: am5.Picture.new(root, {
         width: 48,
         height: 48,
         centerY: am5.p50,
         centerX: am5.p50,
         src: dataItem.dataContext.logo,
         shadowColor: am5.color(0x000000),
         shadowBlur: 4,
         shadowOffsetX: 4,
         shadowOffsetY: 4,
         shadowOpacity: 0.6
         })
         });
         }*/
    }));
    xAxis.data.setAll(data);

    yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        extraMax: 0.1,
        extraMin: 0.1,
        strictMinMax: true,
        renderer: am5xy.AxisRendererY.new(root, {
            strokeOpacity: 0.1
        })
    }));
    yAxis.data.setAll(data);
}

function addTeamRankingSeries(chart, eventTypeId, data, language) {
    // Add series
    // https://www.amcharts.com/docs/v5/charts/xy-chart/series/
    var tmpSeries = chart.series.push(am5xy.ColumnSeries.new(root, {
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "total" + eventTypeId,
        categoryXField: "team"
    }));

    tmpSeries.columns.template.setAll({
        tooltipText: "{categoryX}: {valueY}",
        tooltipY: 0,
        strokeOpacity: 0,
        templateField: "columnSettings",
        shadowColor: am5.color(0x000000),
        shadowBlur: 4,
        shadowOffsetX: 4,
        shadowOffsetY: 4,
        shadowOpacity: 0.6
    });

    // se singola serie allora metto i colori
    if (getPageExtraEventAmount() === 0) {
        series = tmpSeries;
    }

    // Add Label bullet
    tmpSeries.bullets.push(function () {
        return am5.Bullet.new(root, {
            locationY: 1,
            sprite: am5.Label.new(root, {
                text: "{valueY}",
                fill: root.interfaceColors.get("alternativeText"),
                centerY: 0,
                centerX: am5.p50,
                populateText: true
            })
        });
    });

    tmpSeries.bullets.push(function () {
        // Create the picture sprite
        let picture = am5.Picture.new(root, {
            templateField: "logo",
            centerX: am5.p50,
            centerY: am5.p100,
            shadowColor: am5.color(0x000000),
            shadowBlur: 4,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowOpacity: 0.6,
            cursorOverStyle: "pointer"
        });

        // Add click event listener
        picture.events.on("click", function (event) {
            let clickedColumn = event.target; // The clicked column
            let dataItem = clickedColumn.dataItem; // Access data item associated with the column

            // Perform actions based on the clicked column
            let teamId = dataItem.dataContext.teamId;
            let index = filtersIndex.indexOf("teamid");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);

                location.href = "/sicsdataanalytics/team/overview.htm";
            }
        });

        return am5.Bullet.new(root, {
            locationY: 1,
            sprite: picture
        });
    });

    // Rounded corners for columns
    tmpSeries.columns.template.setAll({
        cornerRadiusTL: 5,
        cornerRadiusTR: 5,
        strokeOpacity: 0
    });

    tmpSeries.data.setAll(data);
    // se singola serie allora calcolo la media
    if (getPageExtraEventAmount() === 0) {
        updateAverage();
        updateColors(tmpSeries); // mettendo questa fuori da questo if viene messa la gradazione dal rosso al verde
    }

    // Set cursor style for columns
    tmpSeries.columns.template.setAll({cursorOverStyle: "pointer"});

    // Gestione team preferito
    if (preferredTeamId !== null) {
        if (teamsMap.has(preferredTeamId)) {
            // Need to check if the team exists in the data
            let preferredTeamFound = false;
            data.forEach(function (element) {
                if (element.teamId === preferredTeamId) {
                    preferredTeamFound = true;
                }
            });

            if (preferredTeamFound) {
                var preferredTeam = teamsMap.get(preferredTeamId);
                var rangeDataItem = xAxis.makeDataItem({
                    category: getDescriptionInLanguage(preferredTeam, language),
                    endCategory: getDescriptionInLanguage(preferredTeam, language)
                });

                var range = xAxis.createAxisRange(rangeDataItem);

                rangeDataItem.get("grid").setAll({
                    stroke: am5.color(0xf5f500),
                    strokeOpacity: 0.5,
                    strokeDasharray: [3]
                });

                rangeDataItem.get("axisFill").setAll({
                    fill: am5.color(0xf5f500),
                    fillOpacity: 0.2,
                    visible: true
                });
            }
        }
    }

    // Add click event listener
    tmpSeries.columns.template.events.on("click", function (ev) {
        let clickedColumn = ev.target; // The clicked column
        let dataItem = clickedColumn.dataItem; // Access data item associated with the column

        // Perform actions based on the clicked column
        let teamId = dataItem.dataContext.teamId;
        let competitionId = dataItem.dataContext.competitionId;
        let indexTeamId = filtersIndex.indexOf("teamid");
        let indexCompetitionId = filtersIndex.indexOf("competitionid");
        if (indexTeamId >= 0) {
            let letter = ((indexTeamId + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);
            if (competitionId) {
                letter = ((indexCompetitionId + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-competitionid", competitionId);
            }

            location.href = "/sicsdataanalytics/team/trend.htm";
        }
    });
    tmpSeries.appear(1500, 100);
}

function getTeamTrendChartOne(data) {
    $(".message-div").addClass("d-none");

    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    root = am5.Root.new("chartdiv");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        paddingLeft: 0,
        layout: root.verticalLayout
    }));


    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xRenderer = am5xy.AxisRendererX.new(root, {
        minGridDistance: 30,
        minorGridEnabled: true
    });

    xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        categoryField: "matchDay",
        renderer: xRenderer,
        tooltip: am5.Tooltip.new(root, {})
    }));

    xRenderer.grid.template.setAll({
        location: 1
    });

    xRenderer.labels.template.setAll({
        visible: true
    });

    xAxis.data.setAll(data);

    var yRenderer = am5xy.AxisRendererY.new(root, {});
    yRenderer.labels.template.set('visible', false);

    yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        renderer: yRenderer,
        extraMin: 0.1,
        extraMax: 0.1
    }));

    // tooltips, per i tooltip serve il cursor nel grafico per essere visti
    chart.set("cursor", am5xy.XYCursor.new(root, {
        behavior: "none"
    }));

    // Average line
//    updateAverage();

    // Set up export and annotation
    var exporting = am5plugins_exporting.Exporting.new(root, {
        menu: am5plugins_exporting.ExportingMenu.new(root, {})
    });

    var annotator = am5plugins_exporting.Annotator.new(root, {});

    // Exporting + Annotate menu
    var menuitems = exporting.get("menu").get("items");
    menuitems.push({
        type: "separator"
    });
    menuitems.push({
        type: "custom",
        label: "Annotate",
        callback: function () {
            this.close();
            annotator.toggle();
        }
    });

    // animation on load
    chart.appear(1000, 100);
}

function addTeamTrendOneSeries(chart, data, language) {
    var index = chart.series.length;
    var valueNumber = chart.series.length + 1;

    let teamsData = new Map();
    data.teams.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        teamsData.set(key, JSON.parse(value));
    });

    var team = teamsData.get($("#filter-teamid").val()[index]);
    if (team) {
        // Add series
        var newSeries = chart.series.push(am5xy.LineSeries.new(root, {
            name: "Series " + valueNumber,
            minBulletDistance: 10,
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value" + team.id,
            categoryXField: "matchDay",
            valueXField: "matchDay",
            fill: team.htmlColor + "aa",
            stroke: team.htmlColor + "aa",
            tooltip: am5.Tooltip.new(root, {
                labelText: getDescriptionInLanguage(team, language) + ": {valueY}",
                dy: -40
            })
        }));

        newSeries.strokes.template.setAll({
            strokeWidth: 5,
            lineJoin: "round",
            shadowColor: am5.color(0x000000),
            shadowBlur: 4,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowOpacity: 0.6
        });

        // Add Label bullet
        newSeries.bullets.push(function () {
            return am5.Bullet.new(root, {
                locationY: 0.5,
                sprite: am5.Label.new(root, {
                    fill: "black",
                    stroke: root.interfaceColors.get("background"),
                    strokeWidth: 3,
                    text: "[fontSize: 15px][bold]{valueY}[/][/]",
                    centerY: 0,
                    centerX: am5.p50,
                    dy: -50,
                    populateText: true
                })
            });
        });

        newSeries.bullets.push(function () {
            // Create the picture sprite
            let picture = am5.Picture.new(root, {
                templateField: "logo" + team.id,
                radius: 30,
                centerX: am5.p50,
                centerY: am5.p50,
                shadowColor: am5.color(0x000000),
                shadowBlur: 4,
                shadowOffsetX: 4,
                shadowOffsetY: 4,
                shadowOpacity: 0.6,
                cursorOverStyle: "pointer"
            });

            // Add click event listener
            picture.events.on("click", function (event) {
                let clickedColumn = event.target; // The clicked column
                let imageSrc = clickedColumn.get("src");  // Get the `src` of the image
                let dataItem = clickedColumn.dataItem; // Access data item associated with the column

                // Find the key that contains the clicked image src
                console.log(dataItem.dataContext);
                let dataContext = dataItem.dataContext;
                let logoKey = null;
                for (const key in dataContext) {
                    if (dataContext.hasOwnProperty(key) && key.startsWith("logo")) {
                        if (dataContext[key].src === imageSrc) {
                            logoKey = key;
                            break;
                        }
                    }
                }

                if (logoKey) {
                    let teamId = parseInt(logoKey.replace("logo", ""));
                    let index = filtersIndex.indexOf("teamid");
                    if (index >= 0) {
                        let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                        localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);

                        location.href = "/sicsdataanalytics/team/overview.htm";
                    }
                }
            });

            return am5.Bullet.new(root, {
                locationY: 1,
                sprite: picture
            });
        });

//    pointSeries.bullets.push(function (root, series, dataItem) {
//        var container = am5.Container.new(root, {});
//
//        container.children.push(am5.Picture.new(root, {
//            templateField: "pictureSettings",
//            width: 90,
//            centerX: am5.p50,
//            centerY: am5.p50,
//            forceInactive: true
//        }));
//
//        var circle = container.children.push(am5.Circle.new(root, {
//            radius: 30,
//            stroke: am5.color(0xB4E1FF),
//            fill: am5.color(0xB4E1FF),
//            strokeWidth: 4,
//            tooltipText: "{name}"
//        }));
//
//        container.set("mask", circle);
//
//        return am5.Bullet.new(root, {
//            sprite: container
//        });
//    });

//    newSeries.bullets.push(function () {
//        var circle = am5.Circle.new(root, {
//            radius: 8,
//            fill: team.htmlColor,
//            stroke: root.interfaceColors.get("background"),
//            strokeWidth: 2
//        });
//
//        return am5.Bullet.new(root, {
//            sprite: circle
//        });
//    });

        newSeries.data.setAll(data.data);
        newSeries.appear(1500, 100);
    }
}

function getTeamTrendChartTwo(data) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    rootTwo = am5.Root.new("chartdiv-two");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    rootTwo.setThemes([
        am5themes_Animated.new(rootTwo)
    ]);

    // Create chart
    chartTwo = rootTwo.container.children.push(am5xy.XYChart.new(rootTwo, {
        panX: false,
        panY: false,
        paddingLeft: 0,
        layout: rootTwo.verticalLayout
    }));

//    chart.set("cursor", am5xy.XYCursor.new(rootTwo, {
//        behavior: "zoomXY"
//    }));

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xRenderer = am5xy.AxisRendererX.new(rootTwo, {
        cellStartLocation: 0.1,
        cellEndLocation: 0.9,
        minorGridEnabled: true
    });

    xAxisTwo = chartTwo.xAxes.push(am5xy.CategoryAxis.new(rootTwo, {
        categoryField: "matchDay",
        renderer: xRenderer
    }));

    xRenderer.grid.template.setAll({
        location: 1
    });

    xRenderer.labels.template.setAll({
        fontWeight: "bold",
        fontSize: 13
    });

    xAxisTwo.data.setAll(data);

    yAxisTwo = chartTwo.yAxes.push(am5xy.ValueAxis.new(rootTwo, {
        renderer: am5xy.AxisRendererY.new(rootTwo, {
            strokeOpacity: 0.1
        })
    }));

    // Set color for each bar
    // updateColors();

    // Set up export and annotation
    var exporting = am5plugins_exporting.Exporting.new(rootTwo, {
        menu: am5plugins_exporting.ExportingMenu.new(rootTwo, {})
    });

    var annotator = am5plugins_exporting.Annotator.new(rootTwo, {});

    // Exporting + Annotate menu
    var menuitems = exporting.get("menu").get("items");
    menuitems.push({
        type: "separator"
    });
    menuitems.push({
        type: "custom",
        label: "Annotate",
        callback: function () {
            this.close();
            annotator.toggle();
        }
    });

    // animation on load
    chartTwo.appear(1000, 100);
}

function addTeamTrendTwoSeries(chart, data, language) {
    var index = chart.series.length;
    var valueNumber = chart.series.length + 1;

    let teamsData = new Map();
    data.teams.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        teamsData.set(key, JSON.parse(value));
    });

    var team = teamsData.get($("#filter-teamid").val()[index]);
    if (team) {
        // Add series
        var newSeries = chart.series.push(am5xy.ColumnSeries.new(rootTwo, {
            name: "Series " + valueNumber,
            xAxis: xAxisTwo,
            yAxis: yAxisTwo,
            valueYField: "value" + team.id,
            categoryXField: "matchDay",
            fill: team.htmlColor + "aa",
            stroke: team.htmlColor + "aa"
        }));

        newSeries.columns.template.setAll({
            tooltipText: getDescriptionInLanguage(team, language) + ": {valueY}",
            tooltipY: 0,
            strokeOpacity: 0,
            templateField: "columnSettings",
            shadowColor: am5.color(0x000000),
            shadowBlur: 4,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowOpacity: 0.6
        });

        // Add Label bullet
        newSeries.bullets.push(function () {
            return am5.Bullet.new(rootTwo, {
                locationY: 1,
                sprite: am5.Label.new(rootTwo, {
                    text: "{valueY}",
                    fill: rootTwo.interfaceColors.get("alternativeText"),
                    centerY: 0,
                    centerX: am5.p50,
                    populateText: true
                })
            });
        });

        // Rounded corners for columns
        newSeries.columns.template.setAll({
            cornerRadiusTL: 5,
            cornerRadiusTR: 5,
            strokeOpacity: 0
        });

        newSeries.data.setAll(data.secondData);
        newSeries.appear(1500, 100);
    }
}

function getTeamScatterplotChart(data) {
    $(".message-div").addClass("d-none");

    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    root = am5.Root.new("chartdiv");

    root.numberFormatter.set("numberFormat", "#");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "none",
        wheelY: "none",
        paddingLeft: 0,
        layout: root.verticalLayout,
        maxTooltipDistance: 0
    }));

    chart.set("cursor", am5xy.XYCursor.new(root, {
        // behavior: "zoomXY"
    }));

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var inversedX = false, inversedY = false;
    if (isOpposite(eventFilter.currentFilterX[1].typeId, eventFilter.currentFilterX[1].tagTypes)) {
        inversedX = true;
    }
    if (isOpposite(eventFilter.currentFilterY[1].typeId, eventFilter.currentFilterY[1].tagTypes)) {
        inversedY = true;
    }

    let hideBestTeam = $("#filter-hidebest").is(":checked");
    let extraMax = 0.2, extraMin = 0.05;
    if (inversedX) {
        extraMax = 0.05;
        extraMin = 0.2;
    }
    if (hideBestTeam) {
        extraMax = 0.05;
        extraMin = 0.05;
    }
    xAxis = chart.xAxes.push(
        am5xy.ValueAxis.new(root, {
            extraMax: extraMax,
            extraMin: extraMin,
            renderer: am5xy.AxisRendererX.new(root, {minGridDistance: 50, inversed: inversedX}),
            tooltip: am5.Tooltip.new(root, {})
        })
    );

    yAxis = chart.yAxes.push(
        am5xy.ValueAxis.new(root, {
            extraMax: 0.1,
            extraMin: 0.1,
            renderer: am5xy.AxisRendererY.new(root, {inversed: inversedY}),
            tooltip: am5.Tooltip.new(root, {})
        })
    );

    // Add title to X-axis (at the bottom)
    let filterElement = eventFilter.currentFilterX[1];
    let xAxisTest = "";
    if (filterElement.type === "2") {
        xAxisTest = $("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    } else if (filterElement.type === "3") {
        xAxisTest = $("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    } else {
        xAxisTest = $("#filter-eventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-eventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    }
    if (typeof filterElement.zoneId !== "undefined") {
        xAxisTest += " (";
        xAxisTest += globalMessages.get(getZoneMessageTag(filterElement.zoneId) + ".abb");
        xAxisTest += ")";
    }
    xAxisTitle = am5.Label.new(root, {
        text: xAxisTest,
        fontWeight: "500",
        fontSize: 16, // Increase font size
        x: am5.p50,
        textAlign: "center"
    });
    xAxis.children.push(xAxisTitle);

    // Add title to Y-axis (rotated)
    filterElement = eventFilter.currentFilterY[1];
    let yAxisTest = "";
    if (filterElement.type === "2") {
        yAxisTest = $("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    } else if (filterElement.type === "3") {
        yAxisTest = $("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    } else {
        yAxisTest = $("#filter-eventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-eventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    }
    if (typeof filterElement.zoneId !== "undefined") {
        yAxisTest += " (";
        yAxisTest += globalMessages.get(getZoneMessageTag(filterElement.zoneId) + ".abb");
        yAxisTest += ")";
    }
    yAxisTitle = am5.Label.new(root, {
        text: yAxisTest,
        fontWeight: "500",
        fontSize: 16, // Increase font size
        y: am5.p50,
        textAlign: "center",
        rotation: -90 // Rotate by 90 degrees
    });
    yAxis.children.push(yAxisTitle);

    // Set up export and annotation
    var exporting = am5plugins_exporting.Exporting.new(root, {
        menu: am5plugins_exporting.ExportingMenu.new(root, {})
    });

    var annotator = am5plugins_exporting.Annotator.new(root, {});

    // Exporting + Annotate menu
    var menuitems = exporting.get("menu").get("items");
    menuitems.push({
        type: "separator"
    });
    menuitems.push({
        type: "custom",
        label: "Annotate",
        callback: function () {
            this.close();
            annotator.toggle();
        }
    });

    // animation on load
    chart.appear(1000, 100);
}

function createTeamScatterplotAxis(chart, data) {
    if (typeof xAxis !== "undefined") {
        removeXAxis(xAxis);
        xAxis.children.removeValue(xAxisTitle);
    }
    if (typeof yAxis !== "undefined") {
        removeYAxis(yAxis);
        yAxis.children.removeValue(yAxisTitle);
    }

    var inversedX = false, inversedY = false;
    if (isOpposite(eventFilter.currentFilterX[1].typeId, eventFilter.currentFilterX[1].tagTypes)) {
        inversedX = true;
    }
    if (isOpposite(eventFilter.currentFilterY[1].typeId, eventFilter.currentFilterY[1].tagTypes)) {
        inversedY = true;
    }

    let hideBestTeam = $("#filter-hidebest").is(":checked");
    let extraMax = 0.2, extraMin = 0.05;
    if (inversedX) {
        extraMax = 0.05;
        extraMin = 0.2;
    }
    if (hideBestTeam) {
        extraMax = 0.05;
        extraMin = 0.05;
    }
    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    xAxis = chart.xAxes.push(
        am5xy.ValueAxis.new(root, {
            extraMax: extraMax,
            extraMin: extraMin,
            renderer: am5xy.AxisRendererX.new(root, {minGridDistance: 50, inversed: inversedX}),
            tooltip: am5.Tooltip.new(root, {})
        })
    );

    yAxis = chart.yAxes.push(
        am5xy.ValueAxis.new(root, {
            extraMax: 0.1,
            extraMin: 0.1,
            renderer: am5xy.AxisRendererY.new(root, {inversed: inversedY}),
            tooltip: am5.Tooltip.new(root, {})
        })
    );

    // Add title to X-axis (at the bottom)
    let filterElement = eventFilter.currentFilterX[1];
    let xAxisTest = "";
    if (filterElement.type === "2") {
        xAxisTest = $("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    } else if (filterElement.type === "3") {
        xAxisTest = $("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    } else {
        xAxisTest = $("#filter-eventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-eventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    }
    if (typeof filterElement.zoneId !== "undefined") {
        xAxisTest += " (";
        xAxisTest += globalMessages.get(getZoneMessageTag(filterElement.zoneId) + ".abb");
        xAxisTest += ")";
    }
    xAxisTitle = am5.Label.new(root, {
        text: xAxisTest,
        fontWeight: "500",
        fontSize: 16, // Increase font size
        x: am5.p50,
        textAlign: "center"
    });
    xAxis.children.push(xAxisTitle);

    // Add title to Y-axis (rotated)
    filterElement = eventFilter.currentFilterY[1];
    let yAxisTest = "";
    if (filterElement.type === "2") {
        yAxisTest = $("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    } else if (filterElement.type === "3") {
        yAxisTest = $("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    } else {
        yAxisTest = $("#filter-eventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-eventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    }
    if (typeof filterElement.zoneId !== "undefined") {
        yAxisTest += " (";
        yAxisTest += globalMessages.get(getZoneMessageTag(filterElement.zoneId) + ".abb");
        yAxisTest += ")";
    }
    yAxisTitle = am5.Label.new(root, {
        text: yAxisTest,
        fontWeight: "500",
        fontSize: 16, // Increase font size
        y: am5.p50,
        textAlign: "center",
        rotation: -90 // Rotate by 90 degrees
    });
    yAxis.children.push(yAxisTitle);
}

function addTeamScatterplotSeries(chart, teamId, data) {
    let teamsData = new Map();
    data.teams.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        teamsData.set(key, JSON.parse(value));
    });

    var team = teamsData.get(teamId);
    if (team) {
        // Create series
        // https://www.amcharts.com/docs/v5/charts/xy-chart/series/
        var series = chart.series.push(
            am5xy.LineSeries.new(root, {
                calculateAggregates: true,
                xAxis: xAxis,
                yAxis: yAxis,
                valueYField: "y" + teamId,
                valueXField: "x",
                valueField: "value" + teamId,
                fill: team.htmlColor,
                stroke: team.htmlColor + "aa",
                tooltip: am5.Tooltip.new(root, {
                    labelText: "{name" + teamId + "} ({season" + teamId + "}): {valueX} - {valueY}"
                })
            })
        );

        series.seasonId = "season" + teamId;

        // Add bullet
        // https://www.amcharts.com/docs/v5/charts/xy-chart/series/#Bullets
//    var circleTemplate = am5.Template.new({});
//    series.bullets.push(function () {
//        var graphics = am5.Circle.new(
//                root,
//                {
//                    fill: series.get("fill")
//                },
//                circleTemplate
//                );
//        return am5.Bullet.new(root, {
//            sprite: graphics
//        });
//    });
//
//    // Add heat rule
//    // https://www.amcharts.com/docs/v5/concepts/settings/heat-rules/
//    series.set("heatRules", [{
//            target: circleTemplate,
//            min: 15,
//            max: 25,
//            dataField: "value",
//            key: "radius"
//        }]);

//    var bulletTemplate = am5.Template.new({});
        series.bullets.push(function () {
            // Create the picture sprite
            let picture = am5.Picture.new(root, {
                templateField: "logo" + teamId,
                shadowColor: am5.color(0x000000),
                shadowBlur: 4,
                shadowOffsetX: 4,
                shadowOffsetY: 4,
                shadowOpacity: 0.6,
                cursorOverStyle: "pointer",
                layer: 1
            });

            // Gestione team preferito
            if (preferredTeamId !== null) {
                if (parseInt(teamId) === preferredTeamId) {
                    let preferredPicture = am5.Picture.new(root, {
                        templateField: "logo" + teamId,
                        shadowColor: am5.color(0xf5f500),
                        shadowBlur: 10,
                        shadowOffsetX: 10,
                        shadowOffsetY: 10,
                        shadowOpacity: 1,
                        layer: 2
                    });

                    am5.Bullet.new(root, {
                        sprite: preferredPicture
                    });
                }
            }

            // Add click event listener
            picture.events.on("click", function (event) {
                let clickedColumn = event.target; // The clicked column
                let imageSrc = clickedColumn.get("src");  // Get the `src` of the image
                let dataItem = clickedColumn.dataItem; // Access data item associated with the column

                // Find the key that contains the clicked image src
                let dataContext = dataItem.dataContext;
                let logoKey = null;
                for (const key in dataContext) {
                    if (dataContext.hasOwnProperty(key) && key.startsWith("logo")) {
                        if (dataContext[key].src === imageSrc) {
                            logoKey = key;
                            break;
                        }
                    }
                }

                if (logoKey) {
                    let teamId = parseInt(logoKey.replace("logo", ""));
                    let index = filtersIndex.indexOf("teamid");
                    if (index >= 0) {
                        let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                        localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);

                        location.href = "/sicsdataanalytics/team/overview.htm";
                    }
                }
            });

            return am5.Bullet.new(root, {
                sprite: picture
            });
        });

        if (preferredTeamId !== null) {
            if (parseInt(teamId) === preferredTeamId) {
                series.bullets.push(function () {
                    // Create the picture sprite
                    let picture = am5.Picture.new(root, {
                        templateField: "logo" + teamId,
                        shadowColor: am5.color(0xffff00),
                        shadowBlur: 40,
                        shadowOffsetX: 0,
                        shadowOffsetY: 0,
                        shadowOpacity: 1,
                        layer: 2
                    });

                    return am5.Bullet.new(root, {
                        sprite: picture
                    });
                });
            }
        }

//    series.set("heatRules", [{
//            target: bulletTemplate,
//            min: 15,
//            max: 25,
//            dataField: "value",
//            key: "radius"
//        }]);

        series.strokes.template.setAll({
            strokeWidth: 10
        });

        series.data.setAll(data.data);
        series.appear(1000);
    }
}

function getTeamRadarChart(data) {
    $(".message-div").addClass("d-none");

    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    root = am5.Root.new("chartdiv");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    chart = root.container.children.push(am5radar.RadarChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "panX",
        wheelY: "zoomX"
    }));

    var cursor = chart.set("cursor", am5radar.RadarCursor.new(root, {
        behavior: "zoomX"
    }));
    cursor.lineY.set("visible", false);

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xRenderer = am5radar.AxisRendererCircular.new(root, {});
    xRenderer.labels.template.setAll({
        radius: 10
    });
    xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        maxDeviation: 0,
        categoryField: "event",
        renderer: xRenderer
    }));

    var yRenderer = am5radar.AxisRendererRadial.new(root, {});
    yRenderer.labels.template.setAll({
        visible: false
    });
    yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        min: 0, // Set the minimum value
        max: 100, // Set the maximum value
        extraMax: 0.025,
        strictMinMax: true,
        renderer: yRenderer
    }));

    // Set up export and annotation
    var exporting = am5plugins_exporting.Exporting.new(root, {
        menu: am5plugins_exporting.ExportingMenu.new(root, {})
    });

    var annotator = am5plugins_exporting.Annotator.new(root, {});

    // Exporting + Annotate menu
    var menuitems = exporting.get("menu").get("items");
    menuitems.push({
        type: "separator"
    });
    menuitems.push({
        type: "custom",
        label: "Annotate",
        callback: function () {
            this.close();
            annotator.toggle();
        }
    });

    // animation on load
    xAxis.data.setAll(data);
    chart.appear(1000, 100);
}

function addTeamRadarSeries(chart, teamCompetitionSeason, data, language) {
    var splitted = teamCompetitionSeason.split("-");
    var seasonId = splitted[splitted.length - 1];
    var competitionId = splitted[splitted.length - 2];
    var teamId = splitted[0];

    let seasonsData = new Map();
    data.seasons.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        seasonsData.set(key, JSON.parse(value));
    });
    let competitionsData = new Map();
    data.competitions.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        competitionsData.set(key, JSON.parse(value));
    });
    let teamsData = new Map();
    data.teams.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        teamsData.set(key, JSON.parse(value));
    });

    var season = seasonsData.get(seasonId);
    var competition = competitionsData.get(competitionId);
    var team = teamsData.get(teamId);
    if (season && competition && team) {
        // per cambiare la disposizione delle "categorie"
        // https://www.amcharts.com/docs/v5/charts/radar-chart/radar-axes/

        // Create series
        // https://www.amcharts.com/docs/v5/charts/xy-chart/series/
        var tmpSeries = chart.series.push(am5radar.RadarLineSeries.new(root, {
            name: getDescriptionInLanguage(team, language),
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "total" + teamCompetitionSeason,
            realValueY: "value" + teamCompetitionSeason,
            categoryXField: "event",
            fill: colorScale(colorIndex).hex(),
            stroke: colorScale(colorIndex).hex(),
            tooltip: am5.Tooltip.new(root, {
                labelText: getDescriptionInLanguage(competition, language) + ", {name} (" + season.name + "): "
            })
        }));

        tmpSeries.get("tooltip").adapters.add("labelText", function (text, target) {
            var targetDataItem = target.dataItem;
            if (targetDataItem) {
                text += targetDataItem.dataContext["value" + teamCompetitionSeason];
            }
            return text;
        });

        tmpSeries.seasonId = seasonId;

        tmpSeries.strokes.template.setAll({
            strokeWidth: 2,
            shadowColor: am5.color(0x000000),
            shadowBlur: 4,
            shadowOffsetX: 2,
            shadowOffsetY: 2,
            shadowOpacity: 0.3
        });

        tmpSeries.fills.template.setAll({
            visible: true,
            opacity: 0.2
        });

        tmpSeries.bullets.push(function () {
            return am5.Bullet.new(root, {
                sprite: am5.Circle.new(root, {
                    radius: 5,
                    fill: tmpSeries.get("fill"),
                    shadowColor: am5.color(0x000000),
                    shadowBlur: 4,
                    shadowOffsetX: 4,
                    shadowOffsetY: 4,
                    shadowOpacity: 0.3
                })
            });
        });

        tmpSeries.data.setAll(data.data);
        tmpSeries.appear(1000);
        colorIndex += 0.2;
    }
}

/*
 * END TEAM CHART SECTION
 */

/*
 * START PLAYER CHART SECTION
 */

function getPlayerTrendChartOne(data) {
    $(".message-div").addClass("d-none");

    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    root = am5.Root.new("chartdiv");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        paddingLeft: 0,
        layout: root.verticalLayout
    }));


    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xRenderer = am5xy.AxisRendererX.new(root, {
        minGridDistance: 30,
        minorGridEnabled: true
    });

    xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        categoryField: "matchDay",
        renderer: xRenderer,
        tooltip: am5.Tooltip.new(root, {})
    }));

    xRenderer.grid.template.setAll({
        location: 1
    });

    xRenderer.labels.template.setAll({
        visible: true
    });

    xAxis.data.setAll(data);

    var yRenderer = am5xy.AxisRendererY.new(root, {});
    yRenderer.labels.template.set('visible', false);

    yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        renderer: yRenderer,
        extraMin: 0.1,
        extraMax: 0.1
    }));

    // tooltips, per i tooltip serve il cursor nel grafico per essere visti
    chart.set("cursor", am5xy.XYCursor.new(root, {
        behavior: "none"
    }));

    // Average line
//    updateAverage();

    // Set up export and annotation
    var exporting = am5plugins_exporting.Exporting.new(root, {
        menu: am5plugins_exporting.ExportingMenu.new(root, {})
    });

    var annotator = am5plugins_exporting.Annotator.new(root, {});

    // Exporting + Annotate menu
    var menuitems = exporting.get("menu").get("items");
    menuitems.push({
        type: "separator"
    });
    menuitems.push({
        type: "custom",
        label: "Annotate",
        callback: function () {
            this.close();
            annotator.toggle();
        }
    });

    // animation on load
    chart.appear(1000, 100);
}

function addPlayerTrendOneSeries(chart, data, index) {
    // var index = chart.series.length;
    var valueNumber = chart.series.length + 1;

    let teamsData = new Map();
    data.teams.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        teamsData.set(key, JSON.parse(value));
    });
    let playersData = new Map();
    data.players.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        playersData.set(key, JSON.parse(value));
    });

    var team = teamsData.get($("#filter-teamid").val());
    var player = playersData.get($("#filter-playerid").val()[index]);
    if (team && player) {
        // Add series
        var newSeries = chart.series.push(am5xy.LineSeries.new(root, {
            name: "Series " + valueNumber,
            minBulletDistance: 10,
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value" + player.id,
            categoryXField: "matchDay",
            valueXField: "matchDay",
            fill: team.htmlColor + "aa",
            stroke: team.htmlColor + "aa",
            tooltip: am5.Tooltip.new(root, {
                labelText: player.knownName + ": {valueY}",
                dy: -40
            })
        }));

        newSeries.strokes.template.setAll({
            strokeWidth: 5,
            lineJoin: "round",
            shadowColor: am5.color(0x000000),
            shadowBlur: 4,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowOpacity: 0.6
        });

        // Add Label bullet
        newSeries.bullets.push(function () {
            return am5.Bullet.new(root, {
                locationY: 0.5,
                sprite: am5.Label.new(root, {
                    fill: "black",
                    stroke: root.interfaceColors.get("background"),
                    strokeWidth: 3,
                    text: "[fontSize: 15px][bold]{valueY}[/][/]",
                    centerY: 0,
                    centerX: am5.p50,
                    dy: -50,
                    populateText: true
                })
            });
        });

        newSeries.bullets.push(function () {
            // Create the picture sprite
            let picture = am5.Picture.new(root, {
                templateField: "logo" + player.id,
                centerX: am5.p50,
                centerY: am5.p50,
                shadowColor: am5.color(0x000000),
                shadowBlur: 4,
                shadowOffsetX: 4,
                shadowOffsetY: 4,
                shadowOpacity: 0.6,
                cursorOverStyle: "pointer"
            });

            // Add click event listener
            picture.events.on("click", function (event) {
                let clickedColumn = event.target; // The clicked column
                let imageSrc = clickedColumn.get("src");  // Get the `src` of the image
                let dataItem = clickedColumn.dataItem; // Access data item associated with the column

                // Find the key that contains the clicked image src
                console.log(dataItem.dataContext);
                let dataContext = dataItem.dataContext;
                let logoKey = null;
                for (const key in dataContext) {
                    if (dataContext.hasOwnProperty(key) && key.startsWith("logo")) {
                        if (dataContext[key].src === imageSrc) {
                            logoKey = key;
                            break;
                        }
                    }
                }

                if (logoKey) {
                    let index = filtersIndex.indexOf("teamid");
                    if (index >= 0) {
                        let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                        localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", $("#filter-teamid").val());
                    }

                    let playerId = parseInt(logoKey.replace("logo", ""));
                    index = filtersIndex.indexOf("playerid");
                    if (index >= 0) {
                        let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                        localStorage.setItem("dataanalytics/610/" + letter + "/filter-playerid", playerId);

                        location.href = "/sicsdataanalytics/player/overview.htm";
                    }
                }
            });

            return am5.Bullet.new(root, {
                locationY: 1,
                sprite: picture
            });
        });

//    newSeries.bullets.push(function (root, series, dataItem) {
//        var container = am5.Container.new(root, {});
//
//        var circle = container.children.push(am5.Circle.new(root, {
//            radius: 30,
//            stroke: am5.color(0xB4E1FF),
//            fill: am5.color(0xB4E1FF),
//            strokeWidth: 4
//        }));
//
//        container.set("mask", circle);
//
//        container.children.push(am5.Picture.new(root, {
//            templateField: "logo" + valueNumber,
//            width: 48,
//            height: 48,
//            centerX: am5.p50,
//            centerY: am5.p50,
//            shadowColor: am5.color(0x000000),
//            shadowBlur: 4,
//            shadowOffsetX: 4,
//            shadowOffsetY: 4,
//            shadowOpacity: 0.6
//        }));
//
//        return am5.Bullet.new(root, {
//            sprite: container
//        });
//    });

//    newSeries.bullets.push(function (root, series, dataItem) {
//        var container = am5.Container.new(root, {});
//
//        var circle = container.children.push(am5.RoundedRectangle.new(root, {
//            centerX: am5.p50,
//            centerY: am5.p50,
//            width: 40,
//            height: 40,
//            cornerRadius: 36, // Adjust this value for more or less rounding
//            fill: am5.color(0xffffff) // Set the fill color if needed
//        }));
//
//        container.set("mask", circle);
//
//        container.children.push(am5.Picture.new(root, {
//            templateField: "logo" + valueNumber,
//            width: 48,
//            height: 48,
//            centerX: am5.p50,
//            centerY: am5.p50,
//            shadowColor: am5.color(0x000000),
//            shadowBlur: 4,
//            shadowOffsetX: 4,
//            shadowOffsetY: 4,
//            shadowOpacity: 0.6
//        }));
//
//        return am5.Bullet.new(root, {
//            sprite: container
//        });
//    });

//    newSeries.bullets.push(function () {
//        // Create a bullet
//        let bullet = am5.Bullet.new(root, {
//            locationY: 1
//        });
//
//        // Create a rounded rectangle to serve as a mask
//        let mask = am5.RoundedRectangle.new(root, {
//            width: 48,
//            height: 48,
//            cornerRadius: 12, // Adjust this value for more or less rounding
//            fill: am5.color(0xffffff) // Set the fill color if needed
//        });
//
//        // Create the picture element
//        let picture = am5.Picture.new(root, {
//            templateField: "logo" + valueNumber,
//            width: 48,
//            height: 48,
//            centerX: am5.p50,
//            centerY: am5.p50,
//            shadowColor: am5.color(0x000000),
//            shadowBlur: 4,
//            shadowOffsetX: 4,
//            shadowOffsetY: 4,
//            shadowOpacity: 0.6
//        });
//
//        // Add the picture to the bullet and set the mask
//        bullet.children.push(mask);
//        bullet.children.push(picture);
//
//        return bullet;
//    });

        newSeries.data.setAll(data.data);
        newSeries.appear(1500, 100);
    }
}

function getPlayerTrendChartTwo(data) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    rootTwo = am5.Root.new("chartdiv-two");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    rootTwo.setThemes([
        am5themes_Animated.new(rootTwo)
    ]);

    // Create chart
    chartTwo = rootTwo.container.children.push(am5xy.XYChart.new(rootTwo, {
        panX: false,
        panY: false,
        paddingLeft: 0,
        layout: rootTwo.verticalLayout
    }));

//    chart.set("cursor", am5xy.XYCursor.new(rootTwo, {
//        behavior: "zoomXY"
//    }));

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xRenderer = am5xy.AxisRendererX.new(rootTwo, {
        cellStartLocation: 0.1,
        cellEndLocation: 0.9,
        minorGridEnabled: true
    });

    xAxisTwo = chartTwo.xAxes.push(am5xy.CategoryAxis.new(rootTwo, {
        categoryField: "matchDay",
        renderer: xRenderer
    }));

    xRenderer.grid.template.setAll({
        location: 1
    });

    xRenderer.labels.template.setAll({
        fontWeight: "bold",
        fontSize: 13
    });

    xAxisTwo.data.setAll(data);

    yAxisTwo = chartTwo.yAxes.push(am5xy.ValueAxis.new(rootTwo, {
        renderer: am5xy.AxisRendererY.new(rootTwo, {
            strokeOpacity: 0.1
        })
    }));

    // Set color for each bar
    // updateColors();

    // Set up export and annotation
    var exporting = am5plugins_exporting.Exporting.new(rootTwo, {
        menu: am5plugins_exporting.ExportingMenu.new(rootTwo, {})
    });

    var annotator = am5plugins_exporting.Annotator.new(rootTwo, {});

    // Exporting + Annotate menu
    var menuitems = exporting.get("menu").get("items");
    menuitems.push({
        type: "separator"
    });
    menuitems.push({
        type: "custom",
        label: "Annotate",
        callback: function () {
            this.close();
            annotator.toggle();
        }
    });

    // animation on load
    chartTwo.appear(1000, 100);
}

function addPlayerTrendTwoSeries(chart, data, index) {
    // var index = chart.series.length;
    var valueNumber = chart.series.length + 1;

    let teamsData = new Map();
    data.teams.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        teamsData.set(key, JSON.parse(value));
    });
    let playersData = new Map();
    data.players.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        playersData.set(key, JSON.parse(value));
    });

    var team = teamsData.get($("#filter-teamid").val());
    var player = playersData.get($("#filter-playerid").val()[index]);
    if (team && player) {
        // Add series
        var newSeries = chart.series.push(am5xy.ColumnSeries.new(rootTwo, {
            name: "Series " + valueNumber,
            xAxis: xAxisTwo,
            yAxis: yAxisTwo,
            valueYField: "value" + player.id,
            categoryXField: "matchDay",
            fill: team.htmlColor + "aa",
            stroke: team.htmlColor + "aa"
        }));

        newSeries.columns.template.setAll({
            tooltipText: player.knownName + ": {valueY}",
            tooltipY: 0,
            strokeOpacity: 0,
            templateField: "columnSettings",
            shadowColor: am5.color(0x000000),
            shadowBlur: 4,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowOpacity: 0.6
        });

        // Add Label bullet
        newSeries.bullets.push(function () {
            return am5.Bullet.new(rootTwo, {
                locationY: 1,
                sprite: am5.Label.new(rootTwo, {
                    text: "{valueY}",
                    fill: rootTwo.interfaceColors.get("alternativeText"),
                    centerY: 0,
                    centerX: am5.p50,
                    populateText: true
                })
            });
        });

        // Rounded corners for columns
        newSeries.columns.template.setAll({
            cornerRadiusTL: 5,
            cornerRadiusTR: 5,
            strokeOpacity: 0
        });

        newSeries.data.setAll(data.secondData);
        newSeries.appear(1500, 100);
    }
}

function getPlayerScatterplotChart(data) {
    $(".message-div").addClass("d-none");

    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    root = am5.Root.new("chartdiv");

    root.numberFormatter.set("numberFormat", "#");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "none",
        wheelY: "none",
        paddingLeft: 0,
        layout: root.verticalLayout,
        maxTooltipDistance: 0
    }));

    chart.set("cursor", am5xy.XYCursor.new(root, {
//        behavior: "zoomXY"
    }));

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var inversedX = false, inversedY = false;
    if (isOpposite(eventFilter.currentFilterX[1].typeId, eventFilter.currentFilterX[1].tagTypes)) {
        inversedX = true;
    }
    if (isOpposite(eventFilter.currentFilterY[1].typeId, eventFilter.currentFilterY[1].tagTypes)) {
        inversedY = true;
    }

    let hideBestTeam = $("#filter-hidebest").is(":checked");
    let extraMax = 0.2, extraMin = 0.05;
    if (inversedX) {
        extraMax = 0.05;
        extraMin = 0.2;
    }
    if (hideBestTeam) {
        extraMax = 0.05;
        extraMin = 0.05;
    }
    xAxis = chart.xAxes.push(
        am5xy.ValueAxis.new(root, {
            extraMax: extraMax,
            extraMin: extraMin,
            renderer: am5xy.AxisRendererX.new(root, {minGridDistance: 50, inversed: inversedX}),
            tooltip: am5.Tooltip.new(root, {})
        })
    );

    yAxis = chart.yAxes.push(
        am5xy.ValueAxis.new(root, {
            extraMax: 0.1,
            extraMin: 0.1,
            renderer: am5xy.AxisRendererY.new(root, {inversed: inversedY}),
            tooltip: am5.Tooltip.new(root, {})
        })
    );

    // Add title to X-axis (at the bottom)
    let filterElement = eventFilter.currentFilterX[1];
    let xAxisTest = "";
    if (filterElement.type === "2") {
        xAxisTest = $("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    } else if (filterElement.type === "3") {
        xAxisTest = $("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    } else {
        xAxisTest = $("#filter-eventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-eventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    }
    if (typeof filterElement.zoneId !== "undefined") {
        xAxisTest += " (";
        xAxisTest += globalMessages.get(getZoneMessageTag(filterElement.zoneId) + ".abb");
        xAxisTest += ")";
    }
    xAxisTitle = am5.Label.new(root, {
        text: xAxisTest,
        fontWeight: "500",
        fontSize: 16, // Increase font size
        x: am5.p50,
        textAlign: "center"
    });
    xAxis.children.push(xAxisTitle);

    // Add title to Y-axis (rotated)
    filterElement = eventFilter.currentFilterY[1];
    let yAxisTest = "";
    if (filterElement.type === "2") {
        yAxisTest = $("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    } else if (filterElement.type === "3") {
        yAxisTest = $("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    } else {
        yAxisTest = $("#filter-eventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-eventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    }
    if (typeof filterElement.zoneId !== "undefined") {
        yAxisTest += " (";
        yAxisTest += globalMessages.get(getZoneMessageTag(filterElement.zoneId) + ".abb");
        yAxisTest += ")";
    }
    yAxisTitle = am5.Label.new(root, {
        text: yAxisTest,
        fontWeight: "500",
        fontSize: 16, // Increase font size
        y: am5.p50,
        textAlign: "center",
        rotation: -90 // Rotate by 90 degrees
    });
    yAxis.children.push(yAxisTitle);

    // Set up export and annotation
    var exporting = am5plugins_exporting.Exporting.new(root, {
        menu: am5plugins_exporting.ExportingMenu.new(root, {})
    });

    var annotator = am5plugins_exporting.Annotator.new(root, {});

    // Exporting + Annotate menu
    var menuitems = exporting.get("menu").get("items");
    menuitems.push({
        type: "separator"
    });
    menuitems.push({
        type: "custom",
        label: "Annotate",
        callback: function () {
            this.close();
            annotator.toggle();
        }
    });

    // animation on load
    chart.appear(1000, 100);
}

function createPlayerScatterplotAxis(chart, data) {
    if (typeof xAxis !== "undefined") {
        removeXAxis(xAxis);
        xAxis.children.removeValue(xAxisTitle);
    }
    if (typeof yAxis !== "undefined") {
        removeYAxis(yAxis);
        yAxis.children.removeValue(yAxisTitle);
    }

    var inversedX = false, inversedY = false;
    if (isOpposite(eventFilter.currentFilterX[1].typeId, eventFilter.currentFilterX[1].tagTypes)) {
        inversedX = true;
    }
    if (isOpposite(eventFilter.currentFilterY[1].typeId, eventFilter.currentFilterY[1].tagTypes)) {
        inversedY = true;
    }

    let hideBestTeam = $("#filter-hidebest").is(":checked");
    let extraMax = 0.2, extraMin = 0.05;
    if (inversedX) {
        extraMax = 0.05;
        extraMin = 0.2;
    }
    if (hideBestTeam) {
        extraMax = 0.05;
        extraMin = 0.05;
    }
    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    xAxis = chart.xAxes.push(
        am5xy.ValueAxis.new(root, {
            extraMax: extraMax,
            extraMin: extraMin,
            renderer: am5xy.AxisRendererX.new(root, {minGridDistance: 50, inversed: inversedX}),
            tooltip: am5.Tooltip.new(root, {})
        })
    );

    yAxis = chart.yAxes.push(
        am5xy.ValueAxis.new(root, {
            extraMax: 0.1,
            extraMin: 0.1,
            renderer: am5xy.AxisRendererY.new(root, {inversed: inversedY}),
            tooltip: am5.Tooltip.new(root, {})
        })
    );

    // Add title to X-axis (at the bottom)
    let filterElement = eventFilter.currentFilterX[1];
    let xAxisTest = "";
    if (filterElement.type === "2") {
        xAxisTest = $("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    } else if (filterElement.type === "3") {
        xAxisTest = $("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    } else {
        xAxisTest = $("#filter-eventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            xAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-eventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            xAxisTest += tags.join(", ");
            xAxisTest += ")";
        }
    }
    if (typeof filterElement.zoneId !== "undefined") {
        xAxisTest += " (";
        xAxisTest += globalMessages.get(getZoneMessageTag(filterElement.zoneId) + ".abb");
        xAxisTest += ")";
    }
    xAxisTitle = am5.Label.new(root, {
        text: xAxisTest,
        fontWeight: "500",
        fontSize: 16, // Increase font size
        x: am5.p50,
        textAlign: "center"
    });
    xAxis.children.push(xAxisTitle);

    // Add title to Y-axis (rotated)
    filterElement = eventFilter.currentFilterY[1];
    let yAxisTest = "";
    if (filterElement.type === "2") {
        yAxisTest = $("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-advancedeventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    } else if (filterElement.type === "3") {
        yAxisTest = $("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-tacticaleventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    } else {
        yAxisTest = $("#filter-eventtypeid option[value='" + filterElement.typeId + "']").text().replace("\n", "");
        if (typeof filterElement.tagTypes !== "undefined" && filterElement.tagTypes.length > 0) {
            yAxisTest += " (";
            let tags = [];
            filterElement.tagTypes.forEach(function (value) {
                tags.push($("#filter-eventtypeid option[value='" + filterElement.typeId + "-" + value + "']").text().replace("\n", ""));
            });
            yAxisTest += tags.join(", ");
            yAxisTest += ")";
        }
    }
    if (typeof filterElement.zoneId !== "undefined") {
        yAxisTest += " (";
        yAxisTest += globalMessages.get(getZoneMessageTag(filterElement.zoneId) + ".abb");
        yAxisTest += ")";
    }
    yAxisTitle = am5.Label.new(root, {
        text: yAxisTest,
        fontWeight: "500",
        fontSize: 16, // Increase font size
        y: am5.p50,
        textAlign: "center",
        rotation: -90 // Rotate by 90 degrees
    });
    yAxis.children.push(yAxisTitle);
}

function addPlayerScatterplotSeries(chart, teamPlayerId, data, language) {
    let teamsData = new Map();
    data.teams.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        teamsData.set(key, JSON.parse(value));
    });

    var team = teamsData.get(teamPlayerId.split("-")[0]);
    if (team) {
        // Create series
        // https://www.amcharts.com/docs/v5/charts/xy-chart/series/
        var series = chart.series.push(
            am5xy.LineSeries.new(root, {
                calculateAggregates: true,
                xAxis: xAxis,
                yAxis: yAxis,
                valueYField: "y" + teamPlayerId,
                valueXField: "x",
                valueField: "value" + teamPlayerId,
                fill: team.htmlColor,
                stroke: team.htmlColor + "aa",
                tooltip: am5.Tooltip.new(root, {
                    labelText: getDescriptionInLanguage(team, language) + ", {name" + teamPlayerId + "} ({season" + teamPlayerId + "}): {valueX} - {valueY}"
                })
            })
        );

        series.seasonId = "season" + teamPlayerId;

        series.bullets.push(function (root, target, dataItem) {
            // Retrieve the 'logo' field or similar property
            let logoSrc = dataItem.dataContext["logo" + teamPlayerId]; // Adjust key name if needed

            // Create the picture sprite
            let picture = am5.Picture.new(root, {
                templateField: "logo" + teamPlayerId,
                shadowColor: am5.color(0x000000),
                shadowBlur: 4,
                shadowOffsetX: 4,
                shadowOffsetY: 4,
                shadowOpacity: 0.6,
                cursorOverStyle: "pointer",
                layer: logoSrc.src.includes("point.svg") ? 1 : 2
            });

            // Add click event listener
            picture.events.on("click", function (event) {
                let clickedColumn = event.target; // The clicked column
                let imageSrc = clickedColumn.get("src");  // Get the `src` of the image
                let dataItem = clickedColumn.dataItem; // Access data item associated with the column

                // Find the key that contains the clicked image src
                let dataContext = dataItem.dataContext;
                let logoKey = null;
                for (const key in dataContext) {
                    if (dataContext.hasOwnProperty(key) && key.startsWith("logo")) {
                        if (dataContext[key].src === imageSrc) {
                            logoKey = key;
                            break;
                        }
                    }
                }

                if (logoKey) {
                    let teamId = parseInt(logoKey.replace("logo", "").split("-")[0]);
                    let index = filtersIndex.indexOf("teamid");
                    if (index >= 0) {
                        let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                        localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);
                    }

                    let playerId = parseInt(logoKey.replace("logo", "").split("-")[1]);
                    index = filtersIndex.indexOf("playerid");
                    if (index >= 0) {
                        let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                        localStorage.setItem("dataanalytics/610/" + letter + "/filter-playerid", playerId);

                        location.href = "/sicsdataanalytics/player/overview.htm";
                    }
                }
            });

            return am5.Bullet.new(root, {
                sprite: picture
            });
        });

        series.strokes.template.setAll({
            strokeWidth: 10
        });

        series.data.setAll(data.data);
        series.appear(1000);
    }
}

function getPlayerDistributionChart(data, minValue, maxValue, isOpposite, firstBestPlayer, secondBestPlayer, thirdBestPlayer) {
    $(".message-div").addClass("d-none");

    // Create D3 simulation and collision force
    var simulation = d3.forceSimulation();
    var collisionForce = d3.forceCollide();

    // Store labels and arrows to manage their visibility
    var bulletLabels = new Map();
    var bulletArrows = new Map();

    // Update bullet positions on tick
    simulation.on("tick", function () {
        updatePositions();
    });

    setTimeout(function () {
        xAxis.on("start", function () {
            updatePositions();
        });
        xAxis.on("end", function () {
            updatePositions();
        });
    }, 2500);

    // Updated bullet positions
    function updatePositions() {
        if (am5.array && nodes) {
            am5.array.each(nodes, function (node) {
                var circle = node.circle;

                // Check if node and circle exist before accessing properties
                if (node && circle) {
                    // Instead of setting `y` we use `dy`, as `y` is set by the chart
                    // each time chart changes its size or something else changes
                    circle.setAll({
                        dy: node.y - circle.y()
                    });

                    node.fx = circle.x(); // `y` might change when div changes its size

                    // Update label and arrow positions if they exist and are visible
                    var label = bulletLabels.get(circle);
                    var arrow = bulletArrows.get(circle);

                    if (label && label.get("visible")) {
                        // Get the bullet's position in the root container coordinates
                        var bulletGlobalPos = circle.toGlobal({x: 0, y: 0});
                        var rootLocalPos = root.container.toLocal(bulletGlobalPos);

                        var labelY = rootLocalPos.y - circle.get("radius") - 15;

                        label.setAll({
                            x: rootLocalPos.x,
                            y: labelY
                        });

                        // Update arrow position and visibility
                        if (arrow) {
                            var arrowStartY = rootLocalPos.y - circle.get("radius");
                            var arrowEndY = labelY; // Point to bottom of label with some padding

                            // Clear previous path and draw new line
                            arrow.set("visible", true);

                            // Draw the line using the proper path method
                            arrow.set("svgPath", "M" + rootLocalPos.x + "," + arrowStartY + " L" + rootLocalPos.x + "," + arrowEndY);
                        }
                    } else if (arrow) {
                        arrow.set("visible", false);
                    }
                }
            });
        }
    }

    // Nodes array which will be used by simulation
    var nodes = [];

    // Adds nodes to the nodes array
    function addNode(dataItem) {
        var bullets = dataItem.bullets;
        if (bullets) {
            var bullet = bullets[0];
            if (bullet) {
                var circle = bullet.get("sprite");

                if (circle) {
                    // We use `fx` for horizontal position as we don't want `x` to change.
                    // For a vertical chart, set `fx` instead of `fy`
                    var node = {
                        fx: circle.x(),
                        y: circle.y(),
                        circle: circle
                    };
                    nodes.push(node);
                }
            }
        }
    }

    // Updates collision forces
    function updateForces() {
        simulation.force("collision", collisionForce);

        collisionForce.radius(function (node) {
            var circle = node.circle;
            return circle.get("radius", 1) + 1; // 1 add 1 for padding
        });
    }

    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    root = am5.Root.new("chartdiv");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    // https://www.amcharts.com/docs/v5/charts/xy-chart/
    chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        wheelY: "zoomXY",
        pinchZoomX: false,
        pinchZoomY: false
    }));

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    xAxis = chart.xAxes.push(am5xy.ValueAxis.new(root, {
        renderer: am5xy.AxisRendererX.new(root, {
            minGridDistance: 30
        }),
        extraMin: 0.01,
        extraMax: 0.01
    }));

    //xAxis.get("renderer").grid.template.set("forceHidden", true);
    yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        renderer: am5xy.AxisRendererY.new(root, {}),
        visible: false
    }));

    yAxis.get("renderer").grid.template.set("forceHidden", true);

    // Create series
    // https://www.amcharts.com/docs/v5/charts/xy-chart/series/
    series = chart.series.push(am5xy.LineSeries.new(root, {
        calculateAggregates: true,
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "y",
        valueXField: "x",
        valueField: "value"
    }));

    series.data.setAll(data);

    series.strokes.template.set("visible", false);

    // Add bullet
    // https://www.amcharts.com/docs/v5/charts/xy-chart/series/#Bullets
    var circleTemplate = am5.Template.new({});

    series.bullets.push(function (root, target, dataItem) {
        var bulletCircle = am5.Circle.new(root, {
            radius: 5,
            fill: dataItem.dataContext["strokeColor"] || dataItem.dataContext["bulletColor"] || series.get("fill"),
            stroke: dataItem.dataContext["strokeColor"] || dataItem.dataContext["bulletColor"] || series.get("fill"),
            strokeWidth: dataItem.dataContext["strokeColor"] ? 2 : 0,
            fillOpacity: 0.8,
            tooltipText: dataItem.dataContext["tooltip"] || "{x}",
            tooltipY: 0,
            shadowColor: am5.color(0x000000),
            shadowBlur: 4,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
            shadowOpacity: 0.3,
            cursorOverStyle: "pointer"
        }, circleTemplate);

        bulletCircle.states.create("hover", {
            fill: dataItem.dataContext["strokeColor"] || am5.color(dataItem.dataContext["bulletColor"] || series.get("fill"))
        });

        // Create label for this bullet (initially hidden)
        var label = root.container.children.push(am5.Label.new(root, {
            text: dataItem.dataContext["knownName"] || "Unknown",
            fontSize: 12,
            fontWeight: "bold",
            fill: am5.color(0x000000),
            centerX: am5.p50,
            centerY: am5.p100,
            visible: false,
            layer: 1000, // High z-index to appear on top
            background: am5.RoundedRectangle.new(root, {
                fill: am5.color(0xFFFFFF),
                fillOpacity: 0.9,
                cornerRadiusTL: 5,
                cornerRadiusTR: 5,
                cornerRadiusBR: 5,
                cornerRadiusBL: 5,
                stroke: am5.color(0x000000),
                strokeWidth: 1
            }),
            paddingTop: 5,
            paddingBottom: 5,
            paddingLeft: 8,
            paddingRight: 8
        }));

        // Create arrow line for this bullet (initially hidden)
        var arrow = root.container.children.push(am5.Graphics.new(root, {
            visible: false,
            layer: 999, // Just below the label
            stroke: am5.color(0x000000),
            strokeWidth: 2,
            strokeDasharray: [3, 3]
        }));

        // Store the label and arrow references
        bulletLabels.set(bulletCircle, label);
        bulletArrows.set(bulletCircle, arrow);

        // Add click event to toggle label and arrow visibility
        bulletCircle.events.on("click", function(e) {
            var currentLabel = bulletLabels.get(bulletCircle);
            var currentArrow = bulletArrows.get(bulletCircle);

            if (currentLabel) {
                var isVisible = currentLabel.get("visible");
                currentLabel.set("visible", !isVisible);

                // Update label and arrow positions when showing
                if (!isVisible) {
                    // Get the bullet's position in the root container coordinates
                    var bulletGlobalPos = bulletCircle.toGlobal({ x: 0, y: 0 });
                    var rootLocalPos = root.container.toLocal(bulletGlobalPos);

                    var labelY = rootLocalPos.y - bulletCircle.get("radius") - 15;

                    currentLabel.setAll({
                        x: rootLocalPos.x,
                        y: labelY
                    });

                    // Show and position the arrow
                    if (currentArrow) {
                        var arrowStartY = rootLocalPos.y - bulletCircle.get("radius");
                        var arrowEndY = labelY; // Point to bottom of label with some padding

                        // Clear previous path and draw new line
                        currentArrow.set("visible", true);

                        // Draw the line using the proper path method
                        currentArrow.set("svgPath", "M" + rootLocalPos.x + "," + arrowStartY + " L" + rootLocalPos.x + "," + arrowEndY);
                    }
                } else {
                    // Hide the arrow when hiding the label
                    if (currentArrow) {
                        currentArrow.set("visible", false);
                    }
                }
            }
        });

        return am5.Bullet.new(root, {
            sprite: bulletCircle
        });
    });

    // Add heat rule
    // https://www.amcharts.com/docs/v5/concepts/settings/heat-rules/
    // this makes radius different, depending on the value.
    // remove if you want all circles to be of the same size
    series.set("heatRules", [{
        target: circleTemplate,
        min: minValue,
        max: maxValue,
        dataField: "value",
        key: "radius"
    }]);

    // Set data
    // https://www.amcharts.com/docs/v5/charts/xy-chart/series/#Setting_data

    // Generate random data
//    var data = [];
//    for (var i = 0; i < 500; i++) {
//        data.push({
//            x: am5.math.round(Math.random() * 50 - Math.random() * 50, 2),
//            y: 0,
//            value: Math.round(Math.random() * 10)
//        });
//    }

    // Update forces whenever data is parsed
    series.events.on("datavalidated", function () {
        // Needs a timeout as bullets are created a bit later
        setTimeout(function () {
            am5.array.each(series.dataItems, function (dataItem) {
                addNode(dataItem);
            });
            simulation.nodes(nodes);
            updateForces();
        }, 500);
    });

    // Dispose of the existing container if it exists
    if (firstInfoContainer) {
        firstInfoContainer.dispose();
    }
    if (textContainer) {
        textContainer.dispose();
    }
    if (secondInfoContainer) {
        secondInfoContainer.dispose();
    }
    if (thirdInfoContainer) {
        thirdInfoContainer.dispose();
    }

    textContainer = chart.children.push(
        am5.Container.new(root, {
            x: am5.percent(60), // Align to the right
            y: am5.p10, // Align to the top
//                    centerX: am5.p50, // Center the container horizontally
            width: am5.percent(40), // Adjust as needed
            background: am5.RoundedRectangle.new(root, {
                fill: am5.color(0xFFFFFF),
                fillOpacity: 0.6,
                cornerRadiusTL: 20,
                cornerRadiusTR: 20,
                cornerRadiusBR: 20,
                cornerRadiusBL: 20
            }),
            layout: root.verticalLayout
        })
    );

    var hideBests = $("#filter-hidebest").is(":checked");
    if (!hideBests) {
        if (isOpposite) {
            textContainer.children.push(
                am5.Label.new(root, {
                    text: globalMessages.get("player.distribution.worst.three"),
                    fontWeight: "bold",
                    fontSize: 15, // Adjust font size
                    fill: am5.color(0x000000), // Adjust text color
                    textAlign: "center",
                    x: am5.percent(50),
                    centerX: am5.percent(50)
                })
            );
        } else {
            textContainer.children.push(
                am5.Label.new(root, {
                    text: globalMessages.get("player.distribution.best.three"),
                    fontWeight: "bold",
                    fontSize: 15, // Adjust font size
                    fill: am5.color(0x000000), // Adjust text color
                    textAlign: "center",
                    x: am5.percent(50),
                    centerX: am5.percent(50)
                })
            );
        }
        if (typeof firstBestPlayer !== "undefined" && firstBestPlayer) {
            firstInfoContainer = chart.children.push(
                am5.Container.new(root, {
                    x: am5.percent(88), // Align to the right
                    y: am5.percent(6), // Align to the top
//                    centerX: am5.p50, // Center the container horizontally
                    width: am5.percent(12), // Adjust as needed
                    background: am5.RoundedRectangle.new(root, {
                        fill: am5.color(0xFFC335),
                        fillOpacity: 0.6,
                        cornerRadiusTL: 20,
                        cornerRadiusTR: 20,
                        cornerRadiusBR: 20,
                        cornerRadiusBL: 20,
                        shadowColor: am5.color(0x000000),
                        shadowBlur: 7,
                        shadowOffsetX: 1,
                        shadowOffsetY: 1
                    }),
                    layout: root.verticalLayout
                })
            );

            // Add text below the image
            firstInfoContainer.children.push(
                am5.Label.new(root, {
                    text: globalMessages.get("player.distribution.first"),
                    fontWeight: "bold",
                    fontSize: 11, // Adjust font size
                    fill: am5.color(0xffffff), // Adjust text color
                    textAlign: "center",
                    x: am5.percent(50),
                    centerX: am5.percent(50)
                })
            );
            // Add an image
            firstInfoContainer.children.push(
                am5.Picture.new(root, {
                    width: 100, // Adjust as needed
                    height: 100, // Adjust as needed
                    src: firstBestPlayer.src, // URL to your image
                    shadowColor: am5.color(0xffffff),
                    shadowBlur: 7,
                    shadowOffsetX: 1,
                    shadowOffsetY: 1,
                    x: am5.percent(50),
                    centerX: am5.percent(50)
                })
            );
            let infoPlayerText = "[bold]" + firstBestPlayer.name + "[/]\n" + firstBestPlayer.teamName + "\n";
            if (firstBestPlayer.position) {
                infoPlayerText += firstBestPlayer.position;
                if (firstBestPlayer.positionDetail) {
                    infoPlayerText += " (" + firstBestPlayer.positionDetail + ")\n";
                } else {
                    infoPlayerText += "\n";
                }
            }
            infoPlayerText += globalMessages.get("menu.player.minutes.played") + ": [bold]" + firstBestPlayer.playtime + "[/] min.\n";
            infoPlayerText += globalMessages.get("menu.player.value") + ": [bold]" + firstBestPlayer.value + "[/]";
            // Add text below the image
            firstInfoContainer.children.push(
                am5.Label.new(root, {
                    text: infoPlayerText,
                    fontSize: 11, // Adjust font size
                    fill: am5.color(0xffffff), // Adjust text color
                    textAlign: "center",
                    x: am5.percent(50),
                    centerX: am5.percent(50),
                    wrap: true, // Wrap text if needed
                    maxWidth: am5.percent(90) // Prevent text overflow
                })
            );
        }
        if (typeof secondBestPlayer !== "undefined" && secondBestPlayer) {
            // Create a new container
            secondInfoContainer = chart.children.push(
                am5.Container.new(root, {
                    x: am5.percent(74), // Align to the right
                    y: am5.percent(6), // Align to the top
//                    centerX: am5.p50, // Center the container horizontally
                    width: am5.percent(12), // Adjust as needed
                    background: am5.RoundedRectangle.new(root, {
                        fill: am5.color(0xC4CAF3),
                        fillOpacity: 0.6,
                        cornerRadiusTL: 20,
                        cornerRadiusTR: 20,
                        cornerRadiusBR: 20,
                        cornerRadiusBL: 20,
                        shadowColor: am5.color(0x000000),
                        shadowBlur: 5,
                        shadowOffsetX: 1,
                        shadowOffsetY: 1
                    }),
                    layout: root.verticalLayout
                })
            );

            // Add text below the image
            secondInfoContainer.children.push(
                am5.Label.new(root, {
                    text: globalMessages.get("player.distribution.second"),
                    fontWeight: "bold",
                    fontSize: 11, // Adjust font size
                    fill: am5.color(0xffffff), // Adjust text color
                    textAlign: "center",
                    x: am5.percent(50),
                    centerX: am5.percent(50)
                })
            );
            // Add an image
            secondInfoContainer.children.push(
                am5.Picture.new(root, {
                    width: 100, // Adjust as needed
                    height: 100, // Adjust as needed
                    src: secondBestPlayer.src, // URL to your image
                    shadowColor: am5.color(0xffffff),
                    shadowBlur: 5,
                    shadowOffsetX: 1,
                    shadowOffsetY: 1,
                    x: am5.percent(50),
                    centerX: am5.percent(50)
                })
            );
            let infoPlayerText = "[bold]" + secondBestPlayer.name + "[/]\n" + secondBestPlayer.teamName + "\n";
            if (secondBestPlayer.position) {
                infoPlayerText += secondBestPlayer.position;
                if (secondBestPlayer.positionDetail) {
                    infoPlayerText += " (" + secondBestPlayer.positionDetail + ")\n";
                } else {
                    infoPlayerText += "\n";
                }
            }
            infoPlayerText += globalMessages.get("menu.player.minutes.played") + ": [bold]" + secondBestPlayer.playtime + "[/] min.\n";
            infoPlayerText += globalMessages.get("menu.player.value") + ": [bold]" + secondBestPlayer.value + "[/]";
            // Add text below the image
            secondInfoContainer.children.push(
                am5.Label.new(root, {
                    text: infoPlayerText,
                    fontSize: 11, // Adjust font size
                    fill: am5.color(0xffffff), // Adjust text color
                    textAlign: "center",
                    x: am5.percent(50),
                    centerX: am5.percent(50),
                    wrap: true, // Wrap text if needed
                    maxWidth: am5.percent(90) // Prevent text overflow
                })
            );
        }
        if (typeof thirdBestPlayer !== "undefined" && thirdBestPlayer) {
            // Create a new container
            thirdInfoContainer = chart.children.push(
                am5.Container.new(root, {
                    x: am5.percent(60), // Align to the right
                    y: am5.percent(6), // Align to the top
//                    centerX: am5.p50, // Center the container horizontally
                    width: am5.percent(12), // Adjust as needed
                    background: am5.RoundedRectangle.new(root, {
                        fill: am5.color(0xE4624C),
                        fillOpacity: 0.6,
                        cornerRadiusTL: 20,
                        cornerRadiusTR: 20,
                        cornerRadiusBR: 20,
                        cornerRadiusBL: 20,
                        shadowColor: am5.color(0x000000),
                        shadowBlur: 5,
                        shadowOffsetX: 1,
                        shadowOffsetY: 1
                    }),
                    layout: root.verticalLayout
                })
            );

            // Add text below the image
            thirdInfoContainer.children.push(
                am5.Label.new(root, {
                    text: globalMessages.get("player.distribution.third"),
                    fontWeight: "bold",
                    fontSize: 11, // Adjust font size
                    fill: am5.color(0xffffff), // Adjust text color
                    textAlign: "center",
                    x: am5.percent(50),
                    centerX: am5.percent(50)
                })
            );
            // Add an image
            thirdInfoContainer.children.push(
                am5.Picture.new(root, {
                    width: 100, // Adjust as needed
                    height: 100, // Adjust as needed
                    src: thirdBestPlayer.src, // URL to your image
                    shadowColor: am5.color(0xffffff),
                    shadowBlur: 7,
                    shadowOffsetX: 1,
                    shadowOffsetY: 1,
                    x: am5.percent(50),
                    centerX: am5.percent(50)
                })
            );
            let infoPlayerText = "[bold]" + thirdBestPlayer.name + "[/]\n" + thirdBestPlayer.teamName + "\n";
            if (thirdBestPlayer.position) {
                infoPlayerText += thirdBestPlayer.position;
                if (thirdBestPlayer.positionDetail) {
                    infoPlayerText += " (" + thirdBestPlayer.positionDetail + ")\n";
                } else {
                    infoPlayerText += "\n";
                }
            }
            infoPlayerText += globalMessages.get("menu.player.minutes.played") + ": [bold]" + thirdBestPlayer.playtime + "[/] min.\n";
            infoPlayerText += globalMessages.get("menu.player.value") + ": [bold]" + thirdBestPlayer.value + "[/]";
            // Add text below the image
            thirdInfoContainer.children.push(
                am5.Label.new(root, {
                    text: infoPlayerText,
                    fontSize: 11, // Adjust font size
                    fill: am5.color(0xffffff), // Adjust text color
                    textAlign: "center",
                    x: am5.percent(50),
                    centerX: am5.percent(50),
                    wrap: true, // Wrap text if needed
                    maxWidth: am5.percent(90) // Prevent text overflow
                })
            );
        }
    }

    // Update bullet positions when chart bounds change
    chart.plotContainer.events.on("boundschanged", function () {
        updateForces();
        simulation.restart();
    });

    // Make stuff animate on load
    // https://www.amcharts.com/docs/v5/concepts/animations/
    chart.appear(1000, 100);
}

function getPlayerRadarChart(data) {
    $(".message-div").addClass("d-none");

    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    root = am5.Root.new("chartdiv");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    chart = root.container.children.push(am5radar.RadarChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "panX",
        wheelY: "zoomX"
    }));

    var cursor = chart.set("cursor", am5radar.RadarCursor.new(root, {
        behavior: "zoomX"
    }));
    cursor.lineY.set("visible", false);

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xRenderer = am5radar.AxisRendererCircular.new(root, {});
    xRenderer.labels.template.setAll({
        radius: 10
    });
    xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        maxDeviation: 0,
        categoryField: "event",
        renderer: xRenderer
    }));

    var yRenderer = am5radar.AxisRendererRadial.new(root, {});
    yRenderer.labels.template.setAll({
        visible: true
    });
    yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        min: 0, // Set the minimum value
        max: 100, // Set the maximum value
        extraMax: 0.025,
        strictMinMax: true,
        renderer: yRenderer
    }));

    // Set up export and annotation
    var exporting = am5plugins_exporting.Exporting.new(root, {
        menu: am5plugins_exporting.ExportingMenu.new(root, {})
    });

    var annotator = am5plugins_exporting.Annotator.new(root, {});

    // Exporting + Annotate menu
    var menuitems = exporting.get("menu").get("items");
    menuitems.push({
        type: "separator"
    });
    menuitems.push({
        type: "custom",
        label: "Annotate",
        callback: function () {
            this.close();
            annotator.toggle();
        }
    });

    // animation on load
    xAxis.data.setAll(data);
    chart.appear(1000, 100);
}

function addPlayerRadarSeries(chart, playerTeamSeason, data, language) {
    var splitted = playerTeamSeason.split("-");
    var seasonId = splitted[splitted.length - 1];
    var competitionId = splitted[splitted.length - 2];
    var teamId = splitted[splitted.length - 3];
    var playerId = splitted[0];

    let seasonsData = new Map();
    data.seasons.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        seasonsData.set(key, JSON.parse(value));
    });
    let competitionsData = new Map();
    data.competitions.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        competitionsData.set(key, JSON.parse(value));
    });
    let teamsData = new Map();
    data.teams.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        teamsData.set(key, JSON.parse(value));
    });
    let playersData = new Map();
    data.players.forEach(function (element) {
        let [key, value] = Object.entries(element)[0];
        playersData.set(key, JSON.parse(value));
    });

    var season = seasonsData.get(seasonId);
    var competition = competitionsData.get(competitionId);
    var team = teamsData.get(teamId);
    var player = playersData.get(playerId);
    if (player) {
        // per cambiare la disposizione delle "categorie"
        // https://www.amcharts.com/docs/v5/charts/radar-chart/radar-axes/

        let labelText = getDescriptionInLanguage(competition, language) + " | " + getDescriptionInLanguage(team, language) + ", {name} (" + season.name + "): ";
        if (team.id === 0) {
            labelText = labelText.replace(getDescriptionInLanguage(team, language) + ", ", "");
        }

        // Create series
        // https://www.amcharts.com/docs/v5/charts/xy-chart/series/
        var tmpSeries = chart.series.push(am5radar.RadarLineSeries.new(root, {
            name: player.knownName,
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "total" + playerTeamSeason,
            categoryXField: "event",
            fill: colorScale(colorIndex).hex(),
            stroke: colorScale(colorIndex).hex(),
            tooltip: am5.Tooltip.new(root, {
                labelText: labelText
            })
        }));

        tmpSeries.get("tooltip").adapters.add("labelText", function (text, target) {
            var targetDataItem = target.dataItem;
            if (targetDataItem) {
                text += targetDataItem.dataContext["value" + playerTeamSeason];
            }
            return text;
        });

        tmpSeries.seasonId = seasonId;

        tmpSeries.strokes.template.setAll({
            strokeWidth: 2,
            shadowColor: am5.color(0x000000),
            shadowBlur: 4,
            shadowOffsetX: 2,
            shadowOffsetY: 2,
            shadowOpacity: 0.3
        });

        tmpSeries.fills.template.setAll({
            visible: true,
            opacity: 0.2
        });

        tmpSeries.bullets.push(function () {
            return am5.Bullet.new(root, {
                sprite: am5.Circle.new(root, {
                    radius: 5,
                    fill: tmpSeries.get("fill"),
                    shadowColor: am5.color(0x000000),
                    shadowBlur: 4,
                    shadowOffsetX: 4,
                    shadowOffsetY: 4,
                    shadowOpacity: 0.3
                })
            });
        });

        tmpSeries.data.setAll(data.data);
        tmpSeries.appear(1000);
        colorIndex += 0.2;
    }
}

function getTeamOverviewPolarChart(element, data) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    var root = am5.Root.new(element);

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    // https://www.amcharts.com/docs/v5/charts/radar-chart/
    var chart = root.container.children.push(am5radar.RadarChart.new(root, {
        panX: false,
        panY: false,
        innerRadius: am5.percent(5), // Set inner radius to 0 or a negative value for more space
        fill: am5.color(0x000000)
    }));

    // Create axes and their renderers
    // https://www.amcharts.com/docs/v5/charts/radar-chart/#Adding_axes
    var xRenderer = am5radar.AxisRendererCircular.new(root, {
        minGridDistance: 10,
        // Adjust these properties to fill space
        cellStartLocation: 0, // Start at the beginning of the cell
        cellEndLocation: 1    // End at the end of the cell
    });
    xRenderer.labels.template.setAll({
        radius: 10,
        textType: 'adjusted',
        fontSize: 12
    });
    var xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        maxDeviation: 0,
        categoryField: "category",
        renderer: xRenderer,
        tooltip: am5.Tooltip.new(root, {})
    }));
    var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        min: 0,
        max: 100,
        strictMinMax: true,
        renderer: am5radar.AxisRendererRadial.new(root, {})
    }));

    // Disable labels for both axes
    xAxis.get("renderer").labels.template.set("disabled", true); // Disable category axis labels
    yAxis.get("renderer").labels.template.set("disabled", true); // Disable value axis labels

    // Disable labels for the y-axis
    yAxis.get("renderer").labels.template.setAll({
        visible: false // You can also use "disabled: true" to completely disable them
    });

    // Create series
    // https://www.amcharts.com/docs/v5/charts/radar-chart/#Adding_series
    var series = chart.series.push(am5radar.RadarColumnSeries.new(root, {
        stacked: true,
        name: "Serie",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "value",
        categoryXField: "category"
    }));
//    series.columns.template.setAll({
//        tooltipHTML: "{tooltip}"
//    });
    // Use adapter to set fill color based on data's color property
    series.columns.template.adapters.add("fill", (fill, target) => {
        const dataItem = target.dataItem;
        return am5.color(dataItem.dataContext.color); // Use the color from data context
    });
    // set stroke color
    series.columns.template.adapters.add("stroke", (stroke, target) => {
        const dataItem = target.dataItem;
        return am5.color(dataItem.dataContext.color); // Optionally use same color for stroke
    });
    series.columns.template.setAll({
        strokeOpacity: 0,
        shadowColor: am5.color(0x000000),
        shadowBlur: 7,
        shadowOffsetX: 1,
        shadowOffsetY: 1
    });
    series.data.setAll(data);
    series.appear(1500);

    xAxis.data.setAll(data);

    // Animate chart
    // https://www.amcharts.com/docs/v5/concepts/animations/#Initial_animation
    chart.appear(1000, 100);
}

function getTeamOverviewGoalChart(data, difference) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    var root = am5.Root.new("goalChartContainer");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    // https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/
    // start and end angle must be set both for chart and series
    var chart = root.container.children.push(am5percent.PieChart.new(root, {
        startAngle: 180,
        endAngle: 360,
        layout: root.verticalLayout,
        innerRadius: am5.percent(75)
    }));

    // Create series
    // https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Series
    // start and end angle must be set both for chart and series
    var series = chart.series.push(am5percent.PieSeries.new(root, {
        startAngle: 180,
        endAngle: 360,
        valueField: "value",
        categoryField: "category",
        alignLabels: false
    }));

    series.set("colors", am5.ColorSet.new(root, {
        colors: [
            am5.color("#059669"),
            am5.color("#ef4444")
        ]
    }));

    series.states.create("hidden", {
        startAngle: 180,
        endAngle: 180
    });

    series.slices.template.setAll({
        cornerRadius: 5,
        tooltipText: "{category}",
        strokeOpacity: 0,
        shadowColor: am5.color(0x000000),
        shadowBlur: 7,
        shadowOffsetX: 1,
        shadowOffsetY: 1
    });

    series.ticks.template.setAll({
        forceHidden: true
    });

    series.labels.template.setAll({
        radius: -35,
        text: "{value}",
        fontSize: "1.25em",
        fill: am5.color("#ffffff"),
        centerX: am5.percent(100),
        centerY: am5.percent(50),
        populateText: true
    });

    let labelColor = "#059669";
    if (difference === 0) {
        labelColor = "#313638";
    } else if (difference < 0) {
        labelColor = "#ef4444";
    }
    if (difference > 0) {
        difference = "+" + difference;
    } else if (difference === 0) {
        difference = "" + difference;
    }
    series.children.push(am5.Label.new(root, {
        centerX: am5.percent(50),
        centerY: am5.percent(100),
        text: difference,
        fill: am5.color(labelColor),
        populateText: true,
        fontSize: "2em"
    }));

    // Set data
    // https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Setting_data
    series.data.setAll(data);

    series.appear(1000, 100);
}

function getTeamOverviewResultChart(data, fixtureAmount) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    var root = am5.Root.new("resultChartContainer");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    // https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/
    var chart = root.container.children.push(am5percent.PieChart.new(root, {
        layout: root.verticalLayout,
        innerRadius: am5.percent(60)
    }));

    // Create series
    // https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Series
    var series = chart.series.push(am5percent.PieSeries.new(root, {
        valueField: "value",
        categoryField: "category",
        alignLabels: false,
        startAngle: -180, // Set start angle
        endAngle: 180    // Set end angle for full rotation (360 degrees total)
    }));

    series.set("colors", am5.ColorSet.new(root, {
        colors: [
            am5.color("#059669"),
            am5.color("#f3a000"),
            am5.color("#ef4444")
        ]
    }));

    series.slices.template.setAll({
        strokeOpacity: 0,
        shadowColor: am5.color(0x000000),
        shadowBlur: 7,
        shadowOffsetX: 1,
        shadowOffsetY: 1
    });

    series.labels.template.setAll({
        radius: -35,
        text: "{value}",
        fontSize: "1.5em",
        fill: am5.color("#ffffff"),
        centerX: am5.percent(100)
    });

    series.ticks.template.setAll({
        forceHidden: true
    });

    series.children.push(am5.Label.new(root, {
        centerX: am5.percent(50),
        centerY: am5.percent(50),
        text: fixtureAmount,
        populateText: true,
        fontSize: "1.5em"
    }));

    // Set data
    // https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Setting_data
    series.data.setAll(data);

    // Play initial series animation
    // https://www.amcharts.com/docs/v5/concepts/animations/#Animation_of_series
    series.appear(1000, 100);
}

function getTeamOverviewPlayerDataChart(data) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    var root = am5.Root.new("playerChartContainer");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    // https://www.amcharts.com/docs/v5/charts/xy-chart/
    var chart = root.container.children.push(
        am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            layout: root.horizontalLayout,
            arrangeTooltips: false
        })
    );

    // Use only absolute numbers
    root.numberFormatter.set("numberFormat", "#.#s'%");

    // Add legend
    // https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
    var legend = chart.children.push(
        am5.Legend.new(root, {
            centerY: am5.p50,
            y: am5.p50,
            //useDefaultMarker: true,
            layout: root.verticalLayout
        })
    );

    legend.markers.template.setAll({
        width: 50,
        height: 50
    });

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var yAxis = chart.yAxes.push(
        am5xy.CategoryAxis.new(root, {
            categoryField: "category",
            renderer: am5xy.AxisRendererY.new(root, {
                inversed: true,
                cellStartLocation: 0.1,
                cellEndLocation: 0.9
            })
        })
    );

    var yRenderer = yAxis.get("renderer");
    yRenderer.grid.template.setAll({
        visible: false
    });

    yAxis.data.setAll(data);

    var xAxis = chart.xAxes.push(
        am5xy.ValueAxis.new(root, {
            calculateTotals: true,
            min: 0,
            max: 100,
            renderer: am5xy.AxisRendererX.new(root, {
                minGridDistance: 50
            })
        })
    );

    var xRenderer = xAxis.get("renderer");
    xRenderer.grid.template.setAll({
        visible: false
    });

    var rangeDataItem = xAxis.makeDataItem({
        value: 0
    });

    var range = xAxis.createAxisRange(rangeDataItem);

    range.get("grid").setAll({
        stroke: am5.color(0xeeeeee),
        strokeOpacity: 1,
        location: 1,
        visible: true
    });

    // Add series
    // https://www.amcharts.com/docs/v5/charts/xy-chart/series/
    function createSeries(field, name, color, icon, inlegend) {
        var series = chart.series.push(
            am5xy.ColumnSeries.new(root, {
                xAxis: xAxis,
                yAxis: yAxis,
                name: name,
                valueXField: field,
                categoryYField: "category",
                sequencedInterpolation: true,
                fill: color,
                stroke: color,
                clustered: false
            })
        );

        series.columns.template.setAll({
            height: 50,
            fillOpacity: 0,
            strokeOpacity: 0
        });

        if (icon) {
            series.columns.template.set("fillPattern", am5.PathPattern.new(root, {
                color: color,
                repetition: "repeat-x",
                width: 28,
                height: 50,
                fillOpacity: 0,
                svgPath: icon
            }));
        }

        series.data.setAll(data);
        series.appear();

        if (inlegend) {
            legend.data.push(series);
        }

        return series;
    }

    // var femaleColor = am5.color(0xf25f5c);
    var maleColor = am5.color(0x247ba0);
    var placeholderColor = am5.color(0xdbdbdb);

    var maleIcon = "m8.4 7.4c2.1 0 3.7-1.7 3.7-3.7 0-2.1-1.7-3.7-3.7-3.7-2.1 0-3.7 1.7-3.7 3.7s1.6 3.7 3.7 3.7zm3.7 0.8h-7.4c-2.8 0-4.7 2.5-4.7 4.8v11.4c0 2.2 3.1 2.2 3.1 0v-10.5h0.6v28.6c0 3 4.2 2.9 4.3 0v-16.5h0.8v16.5c0.2 3.1 4.3 2.8 4.3 0v-28.6h0.5v10.5c0 2.2 3.2 2.2 3.2 0v-11.4c0-2.3-1.9-4.8-4.7-4.8z";
    // var femaleIcon = "M 18.4 15.1 L 15.5 25.5 c -0.6 2.3 2.1 3.2 2.7 1 l 2.6 -9.6 h 0.7 l -4.5 16.9 H 21.3 v 12.7 c 0 2.3 3.2 2.3 3.2 0 V 33.9 h 1 v 12.7 c 0 2.3 3.1 2.3 3.1 0 V 33.9 h 4.3 l -4.6 -16.9 h 0.8 l 2.6 9.6 c 0.7 2.2 3.3 1.3 2.7 -1 l -2.9 -10.4 c -0.4 -1.2 -1.8 -3.3 -4.2 -3.4 h -4.7 C 20.1 11.9 18.7 13.9 18.4 15.1 z M 28.6 7.2 c 0 -2.1 -1.6 -3.7 -3.7 -3.7 c -2 0 -3.7 1.7 -3.7 3.7 c 0 2.1 1.6 3.7 3.7 3.7 C 27 10.9 28.6 9.2 28.6 7.2 z";

    createSeries("maleMax", "", placeholderColor, maleIcon, false);
    createSeries("male", "", maleColor, maleIcon, false);

    // Make stuff animate on load
    // https://www.amcharts.com/docs/v5/concepts/animations/
    chart.appear(1000, 100);
}

function getTeamOverviewAgeAverageChart(barIndex, value, min, max) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    var root = am5.Root.new("ageAverageChartContainer");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    // https://www.amcharts.com/docs/v5/charts/xy-chart/
    var chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "none",
        wheelY: "none",
        layout: root.verticalLayout,
        paddingRight: 30
    }));

    // Add legend
    // https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
    var legend = chart.children.push(
        am5.Legend.new(root, {
            centerX: am5.p50,
            x: am5.p50
        })
    );

    var data = [
        {
            category: "9",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "8",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "7",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "6",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "5",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "4",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "3",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "2",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "1",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "0",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }];

    data[barIndex].columnSettings.fill = am5.color(0x247ba0);

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        categoryField: "category",
        renderer: am5xy.AxisRendererX.new(root, {

        }),
        tooltip: am5.Tooltip.new(root, {})
    }));

    var xRenderer = xAxis.get("renderer");

    xRenderer.grid.template.set("forceHidden", true);
    xRenderer.labels.template.set("forceHidden", true);

    xAxis.data.setAll(data);

    var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        min: 0,
        max: 400,
        strictMinMax: true,
        renderer: am5xy.AxisRendererY.new(root, {})
    }));

    var yRenderer = yAxis.get("renderer");

    yRenderer.grid.template.set("forceHidden", true);
    yRenderer.labels.template.set("forceHidden", true);

    // Add series
    // https://www.amcharts.com/docs/v5/charts/xy-chart/series/

    var series = chart.series.push(am5xy.ColumnSeries.new(root, {
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "value",
        categoryXField: "category",
        maskBullets: false
    }));

    series.columns.template.setAll({
        //tooltipText: "{categoryX}: {valueY}",
        width: am5.p100,
        tooltipY: 0,
        strokeOpacity: 1,
        strokeWidth: 2,
        stroke: am5.color(0xffffff),
        templateField: "columnSettings"
    });

    series.bullets.push(function (root, target, dataItem) {
        if (9 - parseInt(dataItem.dataContext.category) === barIndex) {
            var container = am5.Container.new(root, {});

            var pin = container.children.push(am5.Graphics.new(root, {
                fill: am5.color(0x247ba0),
                dy: -5,
                centerY: am5.p100,
                centerX: am5.p50,
                svgPath: "m35.779 17.889c0-9.9093-7.9801-17.889-17.889-17.889-9.9093 0-17.889 7.9801-17.889 17.889 0 9.9093 17.889 28.413 17.889 28.413s17.889-18.503 17.889-28.413zm-26.22-0.35077c0-4.5601 3.7708-8.3309 8.3309-8.3309 4.5601 0 8.3309 3.6831 8.3309 8.3309 0 4.5601-3.6831 8.3309-8.3309 8.3309-4.5601 0-8.3309-3.7708-8.3309-8.3309z"
            }));

            var label = container.children.push(am5.Label.new(root, {
                text: "" + value,
                dy: -34,
                centerY: am5.p50,
                centerX: am5.p50,
                populateText: true,
                paddingTop: 4,
                paddingRight: 4,
                paddingBottom: 4,
                paddingLeft: 4,
                background: am5.RoundedRectangle.new(root, {
                    fill: am5.color(0xffffff),
                    cornerRadiusTL: 20,
                    cornerRadiusTR: 20,
                    cornerRadiusBR: 20,
                    cornerRadiusBL: 20
                })
            }));

            return am5.Bullet.new(root, {
                locationY: 1,
                sprite: container
            });
        }
        return false;
    });

    series.data.setAll(data);

    // Add labels
    function addAxisLabel(category, text) {
        var rangeDataItem = xAxis.makeDataItem({
            category: category
        });

        var range = xAxis.createAxisRange(rangeDataItem);

        range.get("label").setAll({
            //fill: am5.color(0xffffff),
            text: text,
            forceHidden: false
        });

        range.get("grid").setAll({
            //stroke: color,
            strokeOpacity: 1,
            location: 1
        });
    }

    addAxisLabel("9", "" + max);
    addAxisLabel("0", "" + min);

    // Make stuff animate on load
    // https://www.amcharts.com/docs/v5/concepts/animations/
    series.appear(1000, 100);
    chart.appear(1000, 100);
}

function getPlayerOverviewPolarChart(element, data) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    var root = am5.Root.new(element);

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    // https://www.amcharts.com/docs/v5/charts/radar-chart/
    var chart = root.container.children.push(am5radar.RadarChart.new(root, {
        panX: false,
        panY: false,
        innerRadius: am5.percent(5), // Set inner radius to 0 or a negative value for more space
        fill: am5.color(0x000000)
    }));

    // Create axes and their renderers
    // https://www.amcharts.com/docs/v5/charts/radar-chart/#Adding_axes
    var xRenderer = am5radar.AxisRendererCircular.new(root, {
        minGridDistance: 10,
        // Adjust these properties to fill space
        cellStartLocation: 0, // Start at the beginning of the cell
        cellEndLocation: 1    // End at the end of the cell
    });
    xRenderer.labels.template.setAll({
        radius: 10,
        textType: 'adjusted',
        fontSize: 12
    });
    var xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        maxDeviation: 0,
        categoryField: "category",
        renderer: xRenderer,
        tooltip: am5.Tooltip.new(root, {})
    }));
    var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        min: 0,
        max: 100,
        strictMinMax: true,
        renderer: am5radar.AxisRendererRadial.new(root, {})
    }));

    // Disable labels for both axes
    xAxis.get("renderer").labels.template.set("disabled", true); // Disable category axis labels
    yAxis.get("renderer").labels.template.set("disabled", true); // Disable value axis labels

    // Disable labels for the y-axis
    yAxis.get("renderer").labels.template.setAll({
        visible: false // You can also use "disabled: true" to completely disable them
    });

    // Create series
    // https://www.amcharts.com/docs/v5/charts/radar-chart/#Adding_series
    var series = chart.series.push(am5radar.RadarColumnSeries.new(root, {
        stacked: true,
        name: "Serie",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "value",
        categoryXField: "category"
    }));
//    series.columns.template.setAll({
//        tooltipHTML: "{tooltip}"
//    });
    // Use adapter to set fill color based on data's color property
    series.columns.template.adapters.add("fill", (fill, target) => {
        const dataItem = target.dataItem;
        return am5.color(dataItem.dataContext.color); // Use the color from data context
    });
    // set stroke color
    series.columns.template.adapters.add("stroke", (stroke, target) => {
        const dataItem = target.dataItem;
        return am5.color(dataItem.dataContext.color); // Optionally use same color for stroke
    });
    series.columns.template.setAll({
        strokeOpacity: 0,
        shadowColor: am5.color(0x000000),
        shadowBlur: 7,
        shadowOffsetX: 1,
        shadowOffsetY: 1
    });
    series.data.setAll(data);
    series.appear(1500);

    xAxis.data.setAll(data);

    // Animate chart
    // https://www.amcharts.com/docs/v5/concepts/animations/#Initial_animation
    chart.appear(1000, 100);
}

function getPlayerOverviewPlaytimeAverageChart(barIndex, value, min, max) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    var root = am5.Root.new("ageAverageChartContainer");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    // https://www.amcharts.com/docs/v5/charts/xy-chart/
    var chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "none",
        wheelY: "none",
        layout: root.verticalLayout,
        paddingRight: 30
    }));

    // Add legend
    // https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
    var legend = chart.children.push(
        am5.Legend.new(root, {
            centerX: am5.p50,
            x: am5.p50
        })
    );

    var data = [
        {
            category: "9",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "8",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "7",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "6",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "5",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "4",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "3",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "2",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "1",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }, {
            category: "0",
            value: 100,
            columnSettings: {
                fill: am5.color(0xdbdbdb)
            }
        }];

    data[barIndex].columnSettings.fill = am5.color(0x247ba0);

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        categoryField: "category",
        renderer: am5xy.AxisRendererX.new(root, {

        }),
        tooltip: am5.Tooltip.new(root, {})
    }));

    var xRenderer = xAxis.get("renderer");

    xRenderer.grid.template.set("forceHidden", true);
    xRenderer.labels.template.set("forceHidden", true);

    xAxis.data.setAll(data);

    var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        min: 0,
        max: 400,
        strictMinMax: true,
        renderer: am5xy.AxisRendererY.new(root, {})
    }));

    var yRenderer = yAxis.get("renderer");

    yRenderer.grid.template.set("forceHidden", true);
    yRenderer.labels.template.set("forceHidden", true);

    // Add series
    // https://www.amcharts.com/docs/v5/charts/xy-chart/series/

    var series = chart.series.push(am5xy.ColumnSeries.new(root, {
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "value",
        categoryXField: "category",
        maskBullets: false
    }));

    series.columns.template.setAll({
        //tooltipText: "{categoryX}: {valueY}",
        width: am5.p100,
        tooltipY: 0,
        strokeOpacity: 1,
        strokeWidth: 2,
        stroke: am5.color(0xffffff),
        templateField: "columnSettings"
    });

    series.bullets.push(function (root, target, dataItem) {
        if (9 - parseInt(dataItem.dataContext.category) === barIndex) {
            var container = am5.Container.new(root, {});

            var pin = container.children.push(am5.Graphics.new(root, {
                fill: am5.color(0x247ba0),
                dy: -5,
                centerY: am5.p100,
                centerX: am5.p50,
                svgPath: "m35.779 17.889c0-9.9093-7.9801-17.889-17.889-17.889-9.9093 0-17.889 7.9801-17.889 17.889 0 9.9093 17.889 28.413 17.889 28.413s17.889-18.503 17.889-28.413zm-26.22-0.35077c0-4.5601 3.7708-8.3309 8.3309-8.3309 4.5601 0 8.3309 3.6831 8.3309 8.3309 0 4.5601-3.6831 8.3309-8.3309 8.3309-4.5601 0-8.3309-3.7708-8.3309-8.3309z"
            }));

            var label = container.children.push(am5.Label.new(root, {
                text: "" + value,
                dy: -34,
                centerY: am5.p50,
                centerX: am5.p50,
                populateText: true,
                paddingTop: 4,
                paddingRight: 4,
                paddingBottom: 4,
                paddingLeft: 4,
                background: am5.RoundedRectangle.new(root, {
                    fill: am5.color(0xffffff),
                    cornerRadiusTL: 20,
                    cornerRadiusTR: 20,
                    cornerRadiusBR: 20,
                    cornerRadiusBL: 20
                })
            }));

            return am5.Bullet.new(root, {
                locationY: 1,
                sprite: container
            });
        }
        return false;
    });

    series.data.setAll(data);

    // Add labels
    function addAxisLabel(category, text) {
        var rangeDataItem = xAxis.makeDataItem({
            category: category
        });

        var range = xAxis.createAxisRange(rangeDataItem);

        range.get("label").setAll({
            //fill: am5.color(0xffffff),
            text: text,
            forceHidden: false
        });

        range.get("grid").setAll({
            //stroke: color,
            strokeOpacity: 1,
            location: 1
        });
    }

    addAxisLabel("9", "" + max);
    addAxisLabel("0", "" + min);

    // Make stuff animate on load
    // https://www.amcharts.com/docs/v5/concepts/animations/
    series.appear(1000, 100);
    chart.appear(1000, 100);
}

function getPlayerOverviewMatchResumeChart(data, max) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    var root = am5.Root.new("matchResumeMatchContainer");

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    // Create chart
    // https://www.amcharts.com/docs/v5/charts/xy-chart/
    var chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        layout: root.verticalLayout
    }));

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var yAxis = chart.yAxes.push(am5xy.CategoryAxis.new(root, {
        categoryField: "category",
        renderer: am5xy.AxisRendererY.new(root, {})
    }));
    yAxis.data.setAll([{category: ""}]);

    var xAxis = chart.xAxes.push(am5xy.ValueAxis.new(root, {
        min: 0,
        max: max,
        strictMinMax: true,
        numberFormat: "#",
        renderer: am5xy.AxisRendererX.new(root, {})
    }));

    xAxis.get("renderer").labels.template.set("forceHidden", true);

    // Add series
    // https://www.amcharts.com/docs/v5/charts/xy-chart/series/
    var series = chart.series.push(am5xy.ColumnSeries.new(root, {
        xAxis: xAxis,
        yAxis: yAxis,
        valueXField: "to",
        openValueXField: "from",
        categoryYField: "category",
        categoryXField: "name"
    }));

    series.columns.template.setAll({
        tooltipText: "{name}: {total}",
        tooltipY: 0,
        strokeWidth: 0,
        strokeOpacity: 0,
        height: am5.percent(100),
        templateField: "columnSettings"
    });

    series.data.setAll(data);

    // Create axis ranges for each column
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/axis-ranges/
    for (var i = 0; i < data.length; i++) {
        var rangeDataItem = xAxis.makeDataItem({
            value: data[i].from
        });

        var range = xAxis.createAxisRange(rangeDataItem);
        rangeDataItem.get("grid").set("forceHidden", true);
        rangeDataItem.get("tick").setAll({
            visible: true,
            length: 10,
            strokeOpacity: 0.2
        });
        rangeDataItem.get("label").setAll({
            centerX: am5.p0,
            forceHidden: false,
            fontSize: 10,
            text: (data[i].total > 0 ? data[i].total : "")
        });
    }

    // Add legend
    // https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
    var legend = chart.children.push(am5.Legend.new(root, {
        nameField: "categoryX",
        centerX: am5.percent(50),
        x: am5.percent(50),
        clickTarget: "none",
        layout: root.horizontalLayout
    }));

    // Set font size and spacing
    legend.labels.template.setAll({
        fontSize: "11px", // Adjust the font size
        paddingTop: 1, // Reduce the top padding
        paddingBottom: 1  // Reduce the bottom padding
    });

    legend.itemContainers.template.setAll({
        paddingTop: 0,
        paddingBottom: 0,
        paddingLeft: 1, // Reduce left padding
        paddingRight: 1, // Reduce right padding
        marginTop: 0,
        marginBottom: 0
    });

    // Reduce marker spacing
    legend.markerRectangles.template.setAll({
        strokeOpacity: 0,
        marginRight: 5, // Reduce space between marker and label
        cornerRadiusTL: 10,
        cornerRadiusTR: 10,
        cornerRadiusBL: 10,
        cornerRadiusBR: 10
    });

    legend.valueLabels.template.set("forceHidden", true);
    legend.data.setAll(series.dataItems);

    // Make stuff animate on load
    // https://www.amcharts.com/docs/v5/concepts/animations/
    series.appear();
    chart.appear(1000, 100);
}

function getPlayerSimilarityRadarChart(containerId, data) {
    // Create root element
    // https://www.amcharts.com/docs/v5/getting-started/#Root_element
    var newRoot = am5.Root.new(containerId);

    // Set themes
    // https://www.amcharts.com/docs/v5/concepts/themes/
    newRoot.setThemes([
        am5themes_Animated.new(newRoot)
    ]);

    // Create chart
    var newChart = newRoot.container.children.push(am5radar.RadarChart.new(newRoot, {
        panX: false,
        panY: false,
        wheelX: "none",
        wheelY: "none"
    }));

    var cursor = newChart.set("cursor", am5radar.RadarCursor.new(newRoot, {
        behavior: "none"
    }));
    cursor.lineY.set("visible", false);

    // Create axes
    // https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
    var xRenderer = am5radar.AxisRendererCircular.new(newRoot, {
        minGridDistance: 0,
        minorGridEnabled: true
    });
    xRenderer.labels.template.setAll({
        visible: false
    });
    var newXAxis = newChart.xAxes.push(am5xy.CategoryAxis.new(newRoot, {
        maxDeviation: 0,
        categoryField: "event",
        renderer: xRenderer
    }));

    var yRenderer = am5radar.AxisRendererRadial.new(newRoot, {});
    yRenderer.labels.template.setAll({
        visible: false
    });
    var newYAxis = newChart.yAxes.push(am5xy.ValueAxis.new(newRoot, {
        min: 0, // Set the minimum value
        max: 1, // Set the maximum value
        strictMinMax: false,
        renderer: yRenderer
    }));

    // animation on load
    newXAxis.data.setAll(data);
    newChart.appear(1000, 100);

    return [newRoot, newChart, newXAxis, newYAxis];
}

function addPlayerSimilarityRadarSeries(root, chart, xAxis, yAxis, data, index, language) {
    // per cambiare la disposizione delle "categorie"
    // https://www.amcharts.com/docs/v5/charts/radar-chart/radar-axes/

    // Create series
    // https://www.amcharts.com/docs/v5/charts/xy-chart/series/
    if (index === 0) {
        var tmpSeries = chart.series.push(am5radar.RadarLineSeries.new(root, {
            name: "nome",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "total_" + index,
            categoryXField: "event",
            fill: colorScale(index).hex(),
            stroke: colorScale(index).hex(),
            tooltip: am5.Tooltip.new(root, {
                labelText: ""
            })
        }));
    } else {
        var tmpSeries = chart.series.push(am5radar.RadarLineSeries.new(root, {
            name: "nome",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "total_" + index,
            categoryXField: "event",
            fill: colorScale(index).hex(),
            stroke: colorScale(index).hex()
        }));
    }

    if (index === 0) {
        tmpSeries.get("tooltip").adapters.add("labelText", function (text, target) {
            var targetDataItem = target.dataItem;
            if (targetDataItem) {
                text += targetDataItem.dataContext["event"];
            }
            return text;
        });
    }

    tmpSeries.strokes.template.setAll({
        strokeWidth: 2,
        shadowColor: am5.color(0x000000),
        shadowBlur: 4,
        shadowOffsetX: 2,
        shadowOffsetY: 2,
        shadowOpacity: 0.3
    });

    tmpSeries.fills.template.setAll({
        visible: true,
        opacity: 0.2
    });

    tmpSeries.bullets.push(function () {
        return am5.Bullet.new(root, {
            sprite: am5.Circle.new(root, {
                radius: 4,
                fill: tmpSeries.get("fill"),
                shadowColor: am5.color(0x000000),
                shadowBlur: 4,
                shadowOffsetX: 4,
                shadowOffsetY: 4,
                shadowOpacity: 0.3
            })
        });
    });

    tmpSeries.data.setAll(data);
    tmpSeries.appear(1000);
}

/*
 * END PLAYER CHART SECTION
 */

function updateColors(customSeries) {
    if (typeof customSeries === "undefined") {
        customSeries = series;
    }

    customSeries.columns.template.adapters.remove("fill");

    var colors = [am5.color("#c26b51"), am5.color("#f4bc4e"), am5.color("#79a163"), am5.color("#529985")];
    if (xAxis._settings.renderer._settings.inversed) {
        colors = [am5.color("#529985"), am5.color("#79a163"), am5.color("#f4bc4e"), am5.color("#c26b51")];
    }
    customSeries.columns.template.adapters.add("fill", function (fill, target) {
        if (typeof seriesRangeDataItem !== "undefined" && !seriesRangeDataItem.isDisposed() && typeof seriesRangeDataItem._settings.value !== "undefined") {
            var maxValue = customSeries.getPrivate("maxY");
            var minValue = customSeries.getPrivate("minY");
            var average = seriesRangeDataItem._settings.value;
            var value = target.dataItem.get("valueY");
//            var step = (maxValue - minValue) / 4;

            if (value >= average) {
                var halfMax = (maxValue + average) / 2;
                if (value > halfMax) {
                    return colors[3];
                } else {
                    return colors[2];
                }
            } else {
                var halfMin = (minValue + average) / 2;
                if (value > halfMin) {
                    return colors[1];
                } else {
                    return colors[0];
                }
            }
//            if (value < (minValue + step)) {
//                return colors[0];
//            } else if (value < (minValue + step * 2)) {
//                return colors[1];
//            } else if (value < (minValue + step * 3)) {
//                return colors[2];
//            } else {
//                return colors[3];
//            }
        }
    });
}

function updateAverage(average, customSeries) {
    var tmpSeries = series;
    if (typeof customSeries !== "undefined") {
        tmpSeries = customSeries;
    }

    if (typeof average === "undefined") {
        var data = tmpSeries.data._values;
        let total = data.reduce((sum, item) => sum + eval("item." + tmpSeries._settings.valueYField), 0);
        if ($("#filter-totaltype").val() !== "p90" && $("#filter-totaltype").val() !== "average") {
            average = Math.round(total / data.length);
        } else {
            average = Math.round(total / data.length * 100) / 100;
        }
    }
    if (typeof seriesRangeDataItem !== "undefined" && !seriesRangeDataItem.isDisposed()) {
//        seriesRangeDataItem.get("grid").setAll({
//            visible: false
//        });
//        seriesRangeDataItem.get("label").setAll({
//            visible: false
//        });
        seriesRangeDataItem.dispose();
    }

    seriesRangeDataItem = yAxis.makeDataItem({value: average, endValue: 0});
    if (typeof seriesRange !== "undefined") {
        tmpSeries.axisRanges.removeValue(seriesRange);
    }

    seriesRange = tmpSeries.createAxisRange(seriesRangeDataItem);
    seriesRange.columns.template.setAll({
        visible: true,
        opacity: 0.75,
        shadowOpacity: 0
    });

    seriesRangeDataItem.get("grid").setAll({
        strokeOpacity: 1,
        visible: true,
        stroke: am5.color(0x000000),
        strokeDasharray: [10]
    });

    seriesRangeDataItem.get("label").setAll({
        location: 0,
        visible: true,
        text: globalMessages.get("messages.average") + " " + average,
        inside: true,
        centerX: 0,
        centerY: am5.p100,
        fontWeight: "bold",
        layer: 100
    });
}

function removeXAxis(axis) {
    am5.array.each(axis.series, function (series) {
        chart.series.removeValue(series);
    });
    chart.xAxes.removeValue(axis);
    axis.dispose();
}

function removeYAxis(axis) {
    am5.array.each(axis.series, function (series) {
        chart.series.removeValue(series);
    });
    chart.yAxes.removeValue(axis);
    axis.dispose();
}

function showChartMessage(type) {
    // query per trovare div container del grafico
    $("#chartdiv").children("div:not(.message-div)").addClass("d-none");
    $("#chartdiv").removeClass("d-none");
    $(".message-div").addClass("d-none");
    $("#page-title").addClass("d-none");
    $("#chart-message-" + type).removeClass("d-none");
    // radar
    $("#table-container").addClass("d-none");
    // trend
    $("#extra-content").addClass("d-none");
    $("#chartdiv-two-content").addClass("d-none");
    $(".content > .fixture-table").remove();

    let chartDivClasses = $("#chartdiv").prop("className").split(" ");
    let updatedChartDivClasses = [...chartDivClasses];
    if (chartDivClasses) {
        chartDivClasses.forEach(function (elementClass) {
            if (elementClass.startsWith("col-")) {
                const index = chartDivClasses.indexOf(elementClass);
                if (index > -1) {
                    updatedChartDivClasses.splice(index, 1);
                }
            }
        });
    }

    $("#chartdiv").prop("className", updatedChartDivClasses.join(" "));
}

imageToBase64 = (URL) => {
    let image;
    image = new Image();
    image.crossOrigin = 'Anonymous';
    image.src = URL;
    image.addEventListener('load', function () {
        let canvas = document.createElement('canvas');
        let context = canvas.getContext('2d');
        canvas.width = image.width;
        canvas.height = image.height;
        context.drawImage(image, 0, 0);
        try {
            return canvas.toDataURL('image/png');
        } catch (err) {
            console.error(err);
        }
    });
};

const imageUrlToBase64 = async (url) => {
    const data = await fetch(url);
    const blob = await data.blob();
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onloadend = () => {
            const base64data = reader.result;
            resolve(base64data);
        };
        reader.onerror = reject;
    });
};

function test(url) {
    let baseImage = new Image;
    baseImage.setAttribute('crossOrigin', 'anonymous');
    baseImage.src = url;

    var canvas = document.createElement("canvas");
    canvas.width = baseImage.width;
    canvas.height = baseImage.height;
    var ctx = canvas.getContext("2d");
    ctx.drawImage(baseImage, 0, 0);
    return canvas.toDataURL("image/png");
}

function imageExists(imageUrl) {
    const img = new Image();
    img.src = imageUrl;
    return img.complete; // Returns true if the image is loaded
}