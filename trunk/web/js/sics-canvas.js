var svgFasce, pitch, scale, scaleSvg, multiplier;
var tip;
var currentContainerId;
var raggio = 5.0;
var basePitchWidth = 138.6, basePitchLength = 89.76;
var realWidth = 105, realLength = 68;
var minDistance;
var zoneEventAmount = new Map(), angleEventAmount = new Map();
var eventMap = new Map();

var posizionaleOptions = {
    startPos: "in", // in = le azioni INIZIANO nel quadrante selezionato, out = le azioni FINISCONO nel quadrante selezionato
    orientation: "half"     // half, vertical, horizontal, grid
};

var lineColors = {
    black: "#2a2d2a",
    blue: "#3672ff",
    red: "#ff4545"
};

var clickColors = {
    base: "#adc274",
    from: "#0040ff44",
    to: "#00ddff44",
    fromTo: "#ea00ff44",
    zoneClickable: "#ffffff00",

    fromAngle: "#5e6fa2",
    toAngle: "#5e99a2",
    fromToAngle: "#9c5ea2"
};

pitch = {
    width: basePitchWidth,
    length: basePitchLength,
    padding: {
        top: 1,
        right: 1,
        bottom: 1,
        left: 1
    },
    paintColor: "#FFF",
    grassColor: "#40b36d",
    isDefault: true,
    id: "basePitch"
};

pitchFasce = {...pitch};
pitchFasce.width = basePitchWidth / 2;
pitchFasce.length = basePitchLength / 2;
pitchFasce.padding = {top: 1, right: 1, bottom: 1, left: 1};
pitchFasce.baseOpacity = 0.6;
pitchFasce.highlightOpacity = 1;
pitchFasce.isDefault = false;
pitchFasce.grassColor = "gray";
pitchFasce.id = "pitchFasce";

pitchDiagram = {...pitch};
pitchDiagram.width = basePitchWidth / 2;
pitchDiagram.length = basePitchLength / 2;
pitchDiagram.id = "pitchDiagram";

pitchTouch = {...pitch};
pitchTouch.id = "pitchTouch";

function drawField(containerId, pitchElement, modality) {
    currentContainerId = containerId;

    if (typeof pitchElement === 'undefined' || !pitchElement) {
        pitchElement = pitch;
    }

    var tmpMultiplier = pitchElement.length / realLength;
    var tmpScale = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500 * tmpMultiplier]);

    if (pitchElement.isDefault) {
        multiplier = tmpMultiplier;
        scale = tmpScale;

        minDistance = scale(2.3); // distanza dipendente dalla dimensione del field
    }

    scaleSvg = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500]);

    var baseBezier = 50 * tmpMultiplier;

    // rimuovo precedente se esistente
    d3.select("#" + pitchElement.id).remove();
    var svg = d3.select("#" + currentContainerId).append("svg")
            .attr("width", scaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right))
            .attr("height", scaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom))
            .attr("style", "background:" + pitchElement.grassColor)
            .attr("id", pitchElement.id);

    var baseLayer = svg.append("g")
            .attr("data-index", "0")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

    // fasce alternate verde chiaro e verde scuro
    var stripeHeight = pitchElement.width / 8; // Altezza delle fasce
    var numStripes = Math.floor(scaleSvg(pitchElement.width) / scaleSvg(stripeHeight));

    if (pitchElement.isDefault) {
        for (var i = 0; i < numStripes; i++) {
            // var stripeColor = i % 2 === 0 ? "#4059df" : "#475ee0"; // Alterna tra verde chiaro e scuro
            var stripeColor = i % 2 === 0 ? "#46ac6d" : "#40b36d"; // Alterna tra verde chiaro e scuro

            baseLayer.append("rect")
                    .attr("x", scaleSvg(i * stripeHeight))
                    .attr("y", 0)
                    .attr("width", scaleSvg(stripeHeight))
                    .attr("height", scaleSvg(pitchElement.length))
                    .attr("fill", stripeColor);
        }
    }

    // linee dei campi
    var pitchElementOutline = baseLayer.append("rect")
            .attr("x", 0)
            .attr("y", 0)
            .attr("width", scaleSvg(pitchElement.width))
            .attr("height", scaleSvg(pitchElement.length))
            .attr("stroke", pitchElement.paintColor)
            .attr("fill", "none");

    var centerSpot = baseLayer.append("circle")
            .attr("cx", scaleSvg(pitchElement.width / 2))
            .attr("cy", scaleSvg(pitchElement.length / 2))
            .attr("r", 2)
            .attr("fill", pitchElement.paintColor);

    var centerCircle = baseLayer.append("circle")
            .attr("cx", scaleSvg(pitchElement.width / 2))
            .attr("cy", scaleSvg(pitchElement.length / 2))
            .attr("r", baseBezier)
            .attr("fill", 'none')
            .attr("stroke", pitchElement.paintColor);

    var halfwayLine = baseLayer.append("line")
            .attr("y1", scaleSvg(pitchElement.length))
            .attr("y2", 0)
            .attr("x1", scaleSvg(pitchElement.width / 2))
            .attr("x2", scaleSvg(pitchElement.width / 2))
            .attr("stroke", pitchElement.paintColor);

    // corners
    function addPath(pathData, parentElement, size) {
        var path = parentElement.append("path")
                .attr("d", pathData)
                .attr("stroke", pitchElement.paintColor)
                .attr("fill", "none");

        if (typeof size !== 'undefined') {
            path.attr("stroke-width", size);
        }

        return path;
    }

    // top left
    var pathData = "M0," + scaleSvg(1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(1) + ",0";
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + ",0 A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(pitchElement.width) + "," + scaleSvg(1);
    addPath(pathData, baseLayer);

    // bottom left
    pathData = "M0," + scaleSvg(pitchElement.length - 1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(1) + "," + scaleSvg(pitchElement.length);
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + "," + scaleSvg(pitchElement.length) + " A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(pitchElement.width) + "," + scaleSvg(pitchElement.length - 1);
    addPath(pathData, baseLayer);

    // Top Penalty Area
    var penaltyAreaTop = baseLayer.append("g");
    pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54);
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Area
    pathData = "M0," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(43.16) + "L0," + tmpScale(43.16);
    addPath(pathData, penaltyAreaTop);

    // Top D
    pathData = "M" + tmpScale(16.5) + "," + tmpScale(42) + "A " + baseBezier + " " + baseBezier + " 5 0 0 " + tmpScale(16.5) + "," + tmpScale(26);
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Spot
    var penaltySpotTop = penaltyAreaTop.append("circle")
            .attr("cx", tmpScale(11))
            .attr("cy", tmpScale(34))
            .attr("r", 1)
            .attr("fill", pitchElement.paintColor)
            .attr("stroke", pitchElement.paintColor);

    var penaltyAreaBottom = baseLayer.append("g");
    penaltyAreaBottom.html(penaltyAreaTop.html());
    penaltyAreaBottom.attr("transform", "rotate(180) translate(-" + scaleSvg(pitchElement.width) + ",-" + scaleSvg(pitchElement.length) + ")");

    pitchElement.baseLayer = baseLayer;
    if (pitchElement.isDefault) {
        var pointLayer = svg.append("g")
                .attr("data-index", "1")
                .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");
        pitchElement.pointLayer = pointLayer;

        tip = d3.tip()
                .attr('class', 'd3-tip')
                .offset([-10, 0])
                .html(function (d) {
                    return d;
                });

        //drawMainPitchText("Please choose a filter");
    } else {
        drawBackgroundArrow(baseLayer, pitchFasce);

        if (typeof modality === 'undefined' || !modality || modality === 0) { // divisione orizzontale 2 fasce
            // Fasce
            for (var i = 1; i < 2; i++) {
                var layerFasce = baseLayer.append("g");
                pathData = "M" + scaleSvg(pitchFasce.width / 2 * i) + ",0L" + scaleSvg(pitchFasce.width / 2 * i) + "," + scaleSvg(pitchFasce.length);
                addPath(pathData, layerFasce, 2);
            }

            for (var i = 0; i < 2; i++) {
                baseLayer.append("rect")
                        .attr("x", scaleSvg(pitchFasce.width / 2) * i)
                        .attr("y", 0)
                        .attr("width", scaleSvg(pitchFasce.width / 2))
                        .attr("height", scaleSvg(pitchFasce.length))
                        .attr("fill", "transparent")
                        .attr("data-event", (i + 1))
                        .attr("data-index", i);
            }
        } else if (modality === 1) { // divisione verticale 5 fasce
            // Fasce
            for (var i = 1; i < 3; i++) {
                var layerFasce = baseLayer.append("g");
                pathData = "M" + scaleSvg(pitchFasce.width / 3 * i) + ",0L" + scaleSvg(pitchFasce.width / 3 * i) + "," + scaleSvg(pitchFasce.length);
                addPath(pathData, layerFasce, 2);
            }

            for (var i = 0; i < 3; i++) {
                baseLayer.append("rect")
                        .attr("x", scaleSvg(pitchFasce.width / 3) * i)
                        .attr("y", 0)
                        .attr("width", scaleSvg(pitchFasce.width / 3))
                        .attr("height", scaleSvg(pitchFasce.length))
                        .attr("fill", "transparent")
                        .attr("data-event", (i + 1))
                        .attr("data-index", i);
            }
        } else if (modality === 2) { // divisione verticale 5 fasce
            // Fasce
            for (var i = 1; i < 5; i++) {
                var layerFasce = baseLayer.append("g");
                pathData = "M0," + scaleSvg(pitchFasce.length / 5 * i) + "L" + scaleSvg(pitchFasce.width) + "," + scaleSvg(pitchFasce.length / 5 * i);
                addPath(pathData, layerFasce, 2);
            }

            var baseValue = 3;
            for (var i = 0; i < 5; i++) {
                baseLayer.append("rect")
                        .attr("x", 0)
                        .attr("y", scaleSvg(pitchFasce.length / 5) * i)
                        .attr("width", scaleSvg(pitchFasce.width))
                        .attr("height", scaleSvg(pitchFasce.length / 5))
                        .attr("fill", "transparent")
                        .attr("data-event", (baseValue + i + 1))
                        .attr("data-index", i);
            }
        } else if (modality === 3) { // divisione orizzontale e verticale
            // Fasce
            for (var i = 1; i < 3; i++) {
                var layerFasce = baseLayer.append("g");
                pathData = "M" + scaleSvg(pitchFasce.width / 3 * i) + ",0L" + scaleSvg(pitchFasce.width / 3 * i) + "," + scaleSvg(pitchFasce.length);
                addPath(pathData, layerFasce, 2);
            }
            for (var i = 1; i < 5; i++) {
                var layerFasce = baseLayer.append("g");
                pathData = "M0," + scaleSvg(pitchFasce.length / 5 * i) + "L" + scaleSvg(pitchFasce.width) + "," + scaleSvg(pitchFasce.length / 5 * i);
                addPath(pathData, layerFasce, 2);
            }

            var baseValue = 8, counter = 1;
            for (var i = 0; i < 5; i++) {
                for (var j = 0; j < 3; j++) {
                    baseLayer.append("rect")
                            .attr("x", scaleSvg(pitchFasce.width / 3) * j)
                            .attr("y", scaleSvg(pitchFasce.length / 5) * i)
                            .attr("width", scaleSvg(pitchFasce.width / 3))
                            .attr("height", scaleSvg(pitchFasce.length / 5))
                            .attr("fill", "transparent")
                            .attr("data-event", (baseValue + counter))
                            .attr("data-index", i)
                            .attr("data-index-j", j);

                    counter++;
                }
            }
        } else if (modality === 4) { // disivione aree (fuori, area, area piccola)
            // PRIMA META' OFFENSIVA
            pathData = "M0,0L" + scaleSvg(pitchFasce.width / 2) + ",0L" + scaleSvg(pitchFasce.width / 2) + "," + scaleSvg(pitchFasce.length) + "L0," + scaleSvg(pitchFasce.length) + "L0," + tmpScale(54) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L0," + tmpScale(14) + "L0,0";
            var fuoriArea = addPath(pathData, baseLayer, 2);
            fuoriArea.attr("fill", "transparent") // altrimenti non è cliccabile tutta
                    .attr("x", realWidth / 4)
                    .attr("y", 0)
                    .attr("width", scaleSvg(pitchFasce.width / 2))
                    .attr("height", scaleSvg(pitchFasce.length))
                    .attr("data-event", 24)
                    .attr("data-index", 0);

            //pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54) + "L0," + tmpScale(43.16) + "L" + tmpScale(5.5) + "," + tmpScale(43.16) + "L" + tmpScale(5.5) + "," + tmpScale(24.84) + "L0," + tmpScale(24.84) + "L0," + tmpScale(14);
            pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54) + "L0," + tmpScale(14);
            var areaGrande = addPath(pathData, baseLayer, 2);
            areaGrande.attr("fill", clickColors.zoneClickable) // altrimenti non è cliccabile tutta
                    .attr("x", 0)
                    .attr("y", 0)
                    .attr("width", tmpScale(16.5))
                    .attr("height", tmpScale(40.3))
                    .attr("data-event", 25)
                    .attr("data-index", 1);

            // SECONDA META' OFFENSIVA
            pathData = "M" + scaleSvg(pitchFasce.width) + ",0L" + scaleSvg(pitchFasce.width / 2) + ",0L" + scaleSvg(pitchFasce.width / 2) + "," + scaleSvg(pitchFasce.length) + "L" + scaleSvg(pitchFasce.width) + "," + scaleSvg(pitchFasce.length) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(54) + "L" + tmpScale(88.5) + "," + tmpScale(54) + "L" + tmpScale(88.5) + "," + tmpScale(14) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(14) + "L" + scaleSvg(pitchFasce.width) + ",0";
            fuoriArea = addPath(pathData, baseLayer, 2);
            fuoriArea.attr("fill", "transparent") // altrimenti non è cliccabile tutta
                    .attr("x", realWidth * 1.5)
                    .attr("y", 0)
                    .attr("width", scaleSvg(pitchFasce.width / 2))
                    .attr("height", scaleSvg(pitchFasce.length))
                    .attr("data-event", 26)
                    .attr("data-index", 2);

            pathData = "M" + scaleSvg(pitchFasce.width) + "," + tmpScale(14) + "L" + tmpScale(88.5) + "," + tmpScale(14) + "L" + tmpScale(88.5) + "," + tmpScale(54) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(54) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(14);
            areaGrande = addPath(pathData, baseLayer, 2);
            areaGrande.attr("fill", clickColors.zoneClickable) // altrimenti non è cliccabile tutta
                    .attr("x", tmpScale(88.5))
                    .attr("y", 0)
                    .attr("width", tmpScale(16.5))
                    .attr("height", tmpScale(40.3))
                    .attr("data-event", 27)
                    .attr("data-index", 3);
        }

        $("#pitchDiagram").insertAfter($("#pitchFasce"));   // sposto la torta (filtro angolo) dopo filtro fasce
    }
}

function drawDiagram() {
    // il totale dev'essere 360 gradi
    var datiFette = [
        {color: "gray", clickColor: clickColors.toAngle, radius: 30, zone: 1},
        {color: "gray", clickColor: clickColors.fromAngle, radius: 45, zone: 2},
        {color: "gray", clickColor: clickColors.fromToAngle, radius: 60, zone: 3},
        {color: "gray", clickColor: clickColors.fromAngle, radius: 45, zone: 4},
        {color: "gray", clickColor: clickColors.toAngle, radius: 30, zone: 5},
        {color: "gray", clickColor: clickColors.base, radius: 150, zone: 6}
    ];

    // rimuovo precedente se esistente
    d3.select("#" + pitchDiagram.id).remove();
    var svg = d3.select("#" + currentContainerId)
            .append("svg")
            .attr("width", scaleSvg(pitchDiagram.width + 2))
            .attr("height", scaleSvg(pitchDiagram.length + 2))
            .attr("id", pitchDiagram.id)
            .append("g")
            .attr("transform", "translate(" + scaleSvg(pitchDiagram.width / 2 + 1) + "," + scaleSvg(pitchDiagram.length / 2 + 1) + ")");

    var range = Math.min(pitchDiagram.width, pitchDiagram.length) / 2;
    var arc = d3.arc()
            .outerRadius(range)
            .innerRadius(0);

    var offsetAngle = -15 * Math.PI / 180;
    var pie = d3.pie()
            .sort(null)
            .startAngle(offsetAngle)
            .value(function (d) {
                return d.radius;
            });

    var angles = svg.selectAll("g.slice")
            .data(pie(datiFette))
            .enter()
            .append("g")
            .attr("class", "slice")
            .attr("transform", "scale(4.75)");

    angles.append("path")
            .attr("d", arc)
            .attr("fill", function (d) {
                return d.data.color;
            })
            .attr("base-color", function (d) {
                return d.data.color;
            })
            .attr("click-color", function (d) {
                return d.data.clickColor;
            })
            .attr("zone", function (d) {
                return d.data.zone;
            })
            .attr("stroke", "white")
            .attr("stroke-width", 1)
            .style("cursor", "pointer")
            .on("click", function (d) {
                var element = d3.select(this);
                var baseColor = element.attr("base-color");
                if (element.attr("fill") === baseColor) {
                    element.attr("fill", element.attr("click-color"));
                } else {
                    element.attr("fill", baseColor);
                }

                if ($("#filter-anglezone").val() === element.attr("zone")) {
                    $("#filter-anglezone").val(null).trigger("change");
                } else {
                    $("#filter-anglezone").val(element.attr("zone")).trigger("change");
                }
            });

    svg.append("circle")
            .attr("cx", 0)
            .attr("cy", 0)
            .attr("r", scale(6))
            .attr("fill", pitchDiagram.paintColor);

    if (d3.select("#arrow" + pitchDiagram.id).empty()) {
        // Calcola le dimensioni della freccia in base alle proporzioni specificate
        var arrowWidth = scale(12) / 2;
        var arrowHeight = scale(12) / 5;
        var arrowHeadWidth = arrowWidth / 4;
        var arrowHeadHeight = arrowHeight / 3 * 1.5;

        // Calcola le coordinate x e y per posizionare la freccia al centro del SVG
        var centerX = -arrowWidth / 2;
        var centerY = -arrowHeight / 2;

        // Costruisci il path per la freccia
        var arrowPath = "M" + (centerX) + "," + (centerY) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY - arrowHeadHeight) +
                " L" + (centerX + arrowHeadWidth * 3 + arrowHeadWidth) + "," + (centerY + arrowHeight / 2) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY + arrowHeadHeight + arrowHeight) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY + arrowHeight) +
                " L" + (centerX) + "," + (centerY + arrowHeight) +
                " L" + (centerX) + "," + (centerY);

        svg.append("path")
                .attr("d", arrowPath)
                .attr("stroke", "#00000033")
                .attr("stroke-width", 1)
                .attr("fill", "#00000025")
                .attr("id", "arrow" + pitchDiagram.id);
    }

    // TEXTS
    var zoneText = svg.append("text")
            .text(globalMessages.get("filters.angle.zone.lateral"))
            .attr("text-anchor", "middle") // Centro orizzontalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("x", 0) // Posizione x al centro
            .attr("y", -scale(13.4)) // Posizione y al centro
            .attr("font-size", calculateTextSize(6.5))
            .attr("fill", "#ffffffbb")
            .style("text-transform", "uppercase")
            .style("cursor", "pointer")
            .on("click", function () {
                clickRelativeElement(d3.event, "path");
            });
    zoneText.append("tspan")
            .attr("text-anchor", "middle")
            .attr("alignment-baseline", "middle")
            .attr("x", 0)
            .attr("y", -scale(13.4))
            .attr("font-size", calculateTextSize(6.5))
            .attr("id", "textAmountAngle1")
            .attr("dy", "1.2em")
            .attr("eventAmountSpanAngle", "1")
            .style("cursor", "pointer")
            .text(null);

    zoneText = svg.append("text")
            .text(globalMessages.get("filters.angle.zone.lateral"))
            .attr("text-anchor", "middle") // Centro orizzontalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("x", 0) // Posizione x al centro
            .attr("y", scale(13.4)) // Posizione y al centro
            .attr("font-size", calculateTextSize(6.5))
            .attr("fill", "#ffffffbb")
            .style("text-transform", "uppercase")
            .style("cursor", "pointer")
            .on("click", function () {
                clickRelativeElement(d3.event, "path");
            });
    zoneText.append("tspan")
            .attr("text-anchor", "middle")
            .attr("alignment-baseline", "middle")
            .attr("x", 0)
            .attr("y", scale(13.4))
            .attr("font-size", calculateTextSize(6.5))
            .attr("id", "textAmountAngle5")
            .attr("dy", "-1.2em")
            .attr("eventAmountSpanAngle", "1")
            .style("cursor", "pointer")
            .text(null);

    zoneText = svg.append("text")
            .text(globalMessages.get("filters.angle.zone.diagonal"))
            .attr("text-anchor", "middle") // Centro orizzontalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("x", scale(7.5)) // Posizione x al centro
            .attr("y", -scale(8.7)) // Posizione y al centro
            .attr("font-size", calculateTextSize(6.5))
            .attr("fill", "#ffffffbb")
            .style("text-transform", "uppercase")
            .style("cursor", "pointer")
            .on("click", function () {
                clickRelativeElement(d3.event, "path");
            });
    zoneText.append("tspan")
            .attr("text-anchor", "middle")
            .attr("alignment-baseline", "middle")
            .attr("x", scale(7.5))
            .attr("y", -scale(8.7))
            .attr("font-size", calculateTextSize(6.5))
            .attr("id", "textAmountAngle2")
            .attr("dy", "-1.2em")
            .attr("eventAmountSpanAngle", "1")
            .style("cursor", "pointer")
            .text(null);

    zoneText = svg.append("text")
            .text(globalMessages.get("filters.angle.zone.diagonal"))
            .attr("text-anchor", "middle") // Centro orizzontalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("x", scale(7.5)) // Posizione x al centro
            .attr("y", scale(8.7)) // Posizione y al centro
            .attr("font-size", calculateTextSize(6.5))
            .attr("fill", "#ffffffbb")
            .style("text-transform", "uppercase")
            .style("cursor", "pointer")
            .on("click", function () {
                clickRelativeElement(d3.event, "path");
            });
    zoneText.append("tspan")
            .attr("text-anchor", "middle")
            .attr("alignment-baseline", "middle")
            .attr("x", scale(7.5))
            .attr("y", scale(8.7))
            .attr("font-size", calculateTextSize(6.5))
            .attr("id", "textAmountAngle4")
            .attr("dy", "1.2em")
            .attr("eventAmountSpanAngle", "1")
            .style("cursor", "pointer")
            .text(null);

    zoneText = svg.append("text")
            .text(globalMessages.get("filters.angle.zone.vertical"))
            .attr("text-anchor", "middle") // Centro orizzontalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("x", scale(11)) // Posizione x al centro
            .attr("y", 0) // Posizione y al centro
            .attr("font-size", calculateTextSize(6.5))
            .attr("fill", "#ffffffbb")
            .style("text-transform", "uppercase")
            .style("cursor", "pointer")
            .on("click", function () {
                clickRelativeElement(d3.event, "path");
            });
    zoneText.append("tspan")
            .attr("text-anchor", "middle")
            .attr("alignment-baseline", "middle")
            .attr("x", scale(11))
            .attr("y", 0)
            .attr("font-size", calculateTextSize(6.5))
            .attr("id", "textAmountAngle3")
            .attr("dy", "1.2em")
            .attr("eventAmountSpanAngle", "1")
            .style("cursor", "pointer")
            .text(null);

    zoneText = svg.append("text")
            .text(globalMessages.get("filters.angle.zone.back"))
            .attr("text-anchor", "middle") // Centro orizzontalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("x", -scale(10)) // Posizione x al centro
            .attr("y", 0) // Posizione y al centro
            .attr("font-size", calculateTextSize(6.5))
            .attr("fill", "#ffffffbb")
            .style("text-transform", "uppercase")
            .style("cursor", "pointer")
            .on("click", function () {
                clickRelativeElement(d3.event, "path");
            });
    zoneText.append("tspan")
            .attr("text-anchor", "middle")
            .attr("alignment-baseline", "middle")
            .attr("x", -scale(10))
            .attr("y", 0)
            .attr("font-size", calculateTextSize(6.5))
            .attr("id", "textAmountAngle6")
            .attr("dy", "1.2em")
            .attr("eventAmountSpanAngle", "1")
            .style("cursor", "pointer")
            .text(null);
}

function drawEvent(event, normalized, pitchElement) {
    var startPos, endPos, isTouchPoint = false;

    if (typeof pitchElement === "undefined") {
        pitchElement = pitch;
    } else {
        if (pitchElement === pitchTouch) {
            isTouchPoint = true;
        }
    }

    if (typeof normalized === 'undefined' || normalized) {
        startPos = event.startPointNormalized;
        endPos = event.endPointNormalized;
    } else {
        startPos = event.startPoint;
        endPos = event.endPoint;
    }

    var scaledRaggio = raggio * multiplier;

    if (startPos && !startPos.isDefault) {
        var firstPoint = {
            cx: scale(startPos.x),
            cy: scale(startPos.y),
            r: scaledRaggio
        };
        var originalFirstPoint = {...firstPoint};
        var secondPoint, originalSecondPoint;

        var color = "white";

        if (event.mType === 'DLL' || event.mType === 'DLS' ||
                event.mType === 'DRF' || event.mType === 'DRS' ||
                event.mType === 'INT') {
            pitchElement.pointLayer.append("rect")
                    .attr("x", firstPoint.cx - scaledRaggio - (scaledRaggio / 2))
                    .attr("y", firstPoint.cy - scaledRaggio - (scaledRaggio / 2))
                    .attr("width", scaledRaggio * 2)
                    .attr("height", scaledRaggio * 2)
                    .attr("fill", color)
                    .attr("data-event", "event-" + event.id);

            firstPoint.cx = firstPoint.cx - scaledRaggio / 2;
            firstPoint.cy = firstPoint.cy - scaledRaggio / 4;
        } else if (event.mType === 'FAF' || event.mType === 'AMM' || event.mType === 'ESP') {
            var firstCoord = (firstPoint.cx - scaledRaggio * 2 - scaledRaggio / 2) + "," + (firstPoint.cy + scaledRaggio);
            var secondCoord = firstPoint.cx - scaledRaggio / 2 + "," + (firstPoint.cy - scaledRaggio * 2);
            var thirdCoord = (firstPoint.cx + scaledRaggio * 2 - scaledRaggio / 2) + "," + (firstPoint.cy + scaledRaggio);

            pitchElement.pointLayer.append("polygon")
                    .attr("points", firstCoord + " " + secondCoord + " " + thirdCoord)
                    .attr("fill", color)
                    .attr("data-event", "event-" + event.id);

            firstPoint.cx = firstPoint.cx - scaledRaggio / 2;
            firstPoint.cy = firstPoint.cy + scaledRaggio / 4;
        } else {
            if (isTouchPoint) {
                pitchElement.pointLayer.append("circle")
                        .attr("cx", firstPoint.cx - scaledRaggio / 2)
                        .attr("cy", firstPoint.cy - scaledRaggio / 2)
                        .attr("r", scaledRaggio)
                        .attr("fill", color)
                        .attr("stroke", "#000000")
                        .attr("stroke-width", 2)
                        .attr("data-event", "event-" + event.id);
            }

            originalFirstPoint.cx = firstPoint.cx - scaledRaggio / 2;
            originalFirstPoint.cy = firstPoint.cy - scaledRaggio / 2;
            firstPoint.cx = firstPoint.cx - scaledRaggio / 2;
            firstPoint.cy = firstPoint.cy - scaledRaggio / 4;
        }

        if (endPos && !endPos.isDefault && !isTouchPoint) {
            secondPoint = {
                cx: scale(endPos.x),
                cy: scale(endPos.y),
                r: scaledRaggio
            };
            originalSecondPoint = {...secondPoint};

            originalSecondPoint.cx = secondPoint.cx - scaledRaggio / 2;
            originalSecondPoint.cy = secondPoint.cy - scaledRaggio / 2;

            if (firstPoint && secondPoint) {
                var drawArrow = !(scale(event.mActionDistance) <= scale(4));
                connectPoints(originalFirstPoint, originalSecondPoint, drawArrow, event);
            }

            pitchElement.pointLayer.append("circle")
                    .attr("cx", secondPoint.cx - scaledRaggio / 2)
                    .attr("cy", secondPoint.cy - scaledRaggio / 2)
                    .attr("r", scaledRaggio)
                    .attr("fill", color)
                    .attr("stroke", "#000000")
                    .attr("stroke-width", 2)
                    .attr("data-event", "event-" + event.id);

            secondPoint.cx = secondPoint.cx - scaledRaggio / 2;
            secondPoint.cy = secondPoint.cy + scaledRaggio / 4;
        }
    } else {
        if (!event.mType === 'SUB' && !event.mType === 'SOS') {
            console.warn("drawEvent()", "can't draw event id " + event.id + ". Coords are empty");
        }
    }
}

function drawEvents(events) {
    d3.select("#infoText" + pitch.id).remove(); // tolgo testi se ci sono
    d3.select("#infoEventText" + pitch.id).remove();

    if (typeof actionTable !== 'undefined' && typeof eventMap !== 'undefined') {
        pitch.pointLayer.selectAll("*").remove();
        tip.hide();

        var index = 0;
        if (typeof events === 'undefined' || !events) {
            raggio = 5.0;

            var currentTableRows = actionTable.rows({filter: 'applied'})[0].length;
            var tableRows = actionTable.rows()[0].length;
            var hasFilter = $("#bottomNavContent").find("button.uk-button-success:not(.teamAllButton)").length > 0 || $("#sourcePersonal").hasClass("uk-button-success");
            var hasPositionalFilter = zoneEventAmount.size > 0 || angleEventAmount.size > 0;
            if (currentTableRows === 0) {
                drawMainPitchText("No data meet criteria");
            } else if (hasFilter || hasPositionalFilter) {
                $("#closeClipViewer2").removeClass("uk-hidden");
                actionTable.rows({filter: 'applied'}).every(function () {
                    var id = $(this.node()).attr("id").replace("rowEventId", "");

                    if (id) {
                        drawEvent(eventMap.get(id));
                    } else {
                        console.warn("drawEvents()", "Event id not found at index " + index);
                    }
                    index++;
                });
            } else {
                // non disegno mai tutte le righe tranne nel caso in cui filtro per avere tutto
                // ma torno alla visualizzazione video
                drawMainPitchText("Please choose a filter");
                // backToVideoMode();
                d3.select("#arrowbasePitch").remove();  // tolgo freccia normalizzazione
                $("#closeClipViewer2").addClass("uk-hidden");
            }
        } else {
            d3.select("#arrowbasePitch").remove();  // tolgo freccia normalizzazione

            raggio = 5.0;
            var scaledRaggio = raggio * multiplier;

            var closestEvent;
            events.forEach((event) => {
                if (typeof closestEvent === 'undefined') {
                    closestEvent = event;
                } else {
                    if (typeof videoJsPlayer !== 'undefined' && videoJsPlayer) {
                        var closestTime = ((closestEvent.mStart + closestEvent.mEnd) / 2);
                        var currentEventTime = ((event.mStart + event.mEnd) / 2);
                        var currentTime = videoJsPlayer.currentTime() * 1000;

                        var closestDifference = Math.abs(currentTime - closestTime);
                        var currentEventDifference = Math.abs(currentTime - currentEventTime);
                        if (currentEventDifference < closestDifference) {
                            closestEvent = event;
                        }
                    } else {
                        console.warn("drawEvents()", "videoJsPlayer not found");
                    }
                }

                index++;
            });
            if (typeof closestEvent !== 'undefined') {
                // tolgo gli eventi prima di quello selezionato, ma tengo quelli che servono per
                // fare la conduzione nel caso in cui sia minimamente visibile
                var eventsToRemove = events.filter(function (event) {
                    var firstCheck = event.mStart < closestEvent.mStart;
                    var secondCheck = true;

                    var player = closestEvent._player;
                    var playerTo = event._playerTo;
                    if (player && Object.keys(player).length === 1) {
                        if (playerTo) {
                            if (player[Object.keys(player)[0]].id === playerTo.id) {
                                var scaledRaggio = raggio * multiplier;
                                var startPos = closestEvent.startPointNormalized;
                                var endPos = event.endPointNormalized;

                                var firstPoint = {
                                    cx: scale(startPos.x),
                                    cy: scale(startPos.y),
                                    r: scaledRaggio
                                };
                                var secondPoint = {
                                    cx: scale(endPos.x),
                                    cy: scale(endPos.y),
                                    r: scaledRaggio
                                };

                                var distance = getPointsDistance(firstPoint, secondPoint);
                                if (distance > minDistance) {
                                    secondCheck = false;
                                }
                            }
                        }
                    }

                    return firstCheck && secondCheck;
                });
                var futureEventsToRemove = events.filter(event => event.mStart > closestEvent.mStart);
                if (futureEventsToRemove && futureEventsToRemove.length > 1) {
                    var tmpEvents = futureEventsToRemove.slice(1);
                    tmpEvents.forEach((event) => {
                        eventsToRemove.push(event);
                    });
                }
                eventsToRemove.forEach((event) => {
                    var index = events.indexOf(event);
                    if (index >= 0) {
                        events.splice(index, 1);
                    }
                });
            }

            for (var i = 0; i < events.length; i++) {
                var currentEvent = events[i];
                drawEvent(currentEvent, false);
                var nextEvent, nextFound = false;
                for (var j = 0; j < events.length; j++) {
                    nextEvent = events[j];

                    if (typeof nextEvent !== 'undefined' && nextEvent && nextEvent.mStart >= currentEvent.mStart && !nextFound) {
                        if (currentEvent.id !== nextEvent.id) {
                            nextFound = true;
                            var player = nextEvent._player;
                            var playerTo = currentEvent._playerTo;
                            var isSinglePoint = false;
                            if ((typeof playerTo === 'undefined' || !playerTo)
                                    && (currentEvent.mType === 'DLL' || currentEvent.mType === 'FAF' || currentEvent.mType === 'INT')) {
                                // nel caso di duelli, dribbling o tutti gli eventi che non hanno a chi arriva
                                // verifico per il giocatore che inizia l'azione
                                // quindi tipo se passo da duello a passaggio dello stesso giocatore faccio la conduzione
                                playerTo = currentEvent._player;
                                if (playerTo && Object.keys(playerTo).length === 1) {
                                    playerTo = playerTo[Object.keys(playerTo)[0]];
                                    isSinglePoint = true;
                                }
                            }
                            if (playerTo && Object.keys(player).length === 1) {
                                var playerObject = player[Object.keys(player)[0]];
                                if (playerObject.id === playerTo.id) {
                                    var startPos = isSinglePoint ? nextEvent.startPoint : currentEvent.endPoint;
                                    var endPos = isSinglePoint ? currentEvent.startPoint : nextEvent.startPoint;

                                    var firstPoint = {
                                        cx: scale(startPos.x) - scaledRaggio / 2,
                                        cy: scale(startPos.y) - scaledRaggio / 2,
                                        r: scaledRaggio
                                    };
                                    var secondPoint = {
                                        cx: scale(endPos.x) - scaledRaggio / 2,
                                        cy: scale(endPos.y) - scaledRaggio / 2,
                                        r: scaledRaggio
                                    };

                                    // disegno conduzione tratteggiata
                                    connectPoints(firstPoint, secondPoint, false, nextEvent, true);
                                }
                            }
                        }
                    }
                }
            }
        }
    } else {
        console.warn("drawEvents()", "actionTable or eventMap are undefined");
    }

    // caricamento posizionale tiri
    if (typeof drawShotEvents === "function") {
        drawShotEvents();
    }
}

function drawEventsIfNeeded() {
    if (typeof actionTable !== 'undefined' && typeof eventMap !== 'undefined') {
        if (pitch.pointLayer) {
            var elements = pitch.pointLayer.selectAll("*")._groups[0].length;
            var hasLiveText = pitch.pointLayer.select("#infoEventText" + pitch.id)._groups[0].length;
            if (elements === 0 || hasLiveText) {
                drawEvents();
            }
        } else {
            console.warn("drawEventsIfNeeded()", "pitch.pointLayer is not initialized");
        }
    } else {
        console.warn("drawEventsIfNeeded()", "actionTable or eventMap are undefined");
    }

    // caricamento posizionale tiri
    if (typeof drawShotEventsIfNeeded === "function") {
        drawShotEventsIfNeeded();
    }
}

function connectPoints(firstPoint, secondPoint, drawArrow, event, isConduzione) {
    // console.log(pointLayer, firstPoint, secondPoint, drawArrow);
    var distance = getPointsDistance(firstPoint, secondPoint);

    if (distance > minDistance) {
        // colore freccia
        var lineColor = getEventColor(event);
        if (typeof isConduzione !== 'undefined' && isConduzione) {
            // conduzione sempre nera
            lineColor = lineColors.black;
        }

        // controllo se c'è bisogno di creare la freccia
        if (typeof drawArrow !== 'undefined' && drawArrow) {
            var isArrowheadPresent = !pitch.pointLayer.select("#arrowhead" + lineColor.replace("#", "")).empty();
            if (!isArrowheadPresent) {
                pitch.pointLayer.append("defs").append("marker")
                        .attr("id", "arrowhead" + lineColor.replace("#", ""))
                        .attr("refX", 2)
                        .attr("refY", 2)
                        .attr("markerWidth", 6)
                        .attr("markerHeight", 4)
                        .attr("orient", "auto")
                        .append("path")
                        .attr("d", "M0,0 L6,2 L0,4")
                        .style("fill", lineColor);
            }

            isArrowheadPresent = !pitch.pointLayer.select("#arrowheadHighlighted").empty();
            if (!isArrowheadPresent) {
                pitch.pointLayer.append("defs").append("marker")
                        .attr("id", "arrowheadHighlighted")
                        .attr("refX", 2)
                        .attr("refY", 2)
                        .attr("markerWidth", 6)
                        .attr("markerHeight", 4)
                        .attr("orient", "auto")
                        .append("path")
                        .attr("d", "M0,0 L6,2 L0,4")
                        .style("fill", "lime");
            }
        }

        // dati primo punto
        var x1 = parseFloat(firstPoint.cx);
        var y1 = parseFloat(firstPoint.cy);
        var r1 = parseFloat(firstPoint.r);
        // dati secondo punto
        var x2 = parseFloat(secondPoint.cx);
        var y2 = parseFloat(secondPoint.cy);
        var r2 = parseFloat(secondPoint.r);

        var angle = Math.atan2(y2 - y1, x2 - x1);
        var startX = x1 + r1 * Math.cos(angle);
        var startY = y1 + r1 * Math.sin(angle);
        var endX = x2 - (r2) * Math.cos(angle);
        var endY = y2 - (r2) * Math.sin(angle);
//        if (typeof event._playerTo === 'undefined' || !event._playerTo) {
//            if (typeof event.angle !== 'undefined' && event.angle) {
//                if ((event.angle > 90 && event.angle < 270) || (event.angle < -90 && event.angle > -270)) {
//                    endX = x2 + (r2) * Math.cos(angle);
//                    endY = y2 + (r2) * Math.sin(angle);
//                }
//            }
//        }
//        endX = x2 - (r2 * 2) * Math.cos(angle);
//        endY = y2 - (r2 * 2) * Math.sin(angle);

        var points = findPerpendicularPoints(parseFloat(firstPoint.cx), parseFloat(firstPoint.cy), parseFloat(secondPoint.cx), parseFloat(secondPoint.cy), 3);

        var trianglePath = "M" + startX + "," + startY +
                " L" + points[0].x + "," + points[0].y +
                " L" + points[1].x + "," + points[1].y +
                " L" + startX + "," + startY;
        var path = pitch.pointLayer.append("path")
                .attr("d", trianglePath)
                .attr("stroke", "#000000")
                .attr("stroke-width", 1)
                .attr("fill", "#00000099");
        path.lower();

        // creo ora la linea
//        var line = pitch.pointLayer.append("line")
//                .attr("x1", startX)
//                .attr("y1", startY)
//                .attr("x2", endX) // 6 è la lunghezza della freccia
//                .attr("y2", endY) // 6 è la lunghezza della freccia
//                .attr("stroke", lineColor)
//                .attr("stroke-width", 2);
//        if (typeof drawArrow !== 'undefined' && drawArrow) {
//            line.attr("marker-end", "url(#arrowhead" + lineColor.replace("#", "") + ")");
//        }
//        if (typeof event !== 'undefined' && event) {
//            line.attr("data-event", "event-" + event.id);
//        }
//        if (typeof isConduzione !== 'undefined' && isConduzione) {
//            line.attr("stroke-dasharray", "2,2"); // Imposta un modello di tratteggio (2 pixel di tratto, 2 pixel di spazio)
//        }
    }
}

function findPerpendicularPoints(startX, startY, endX, endY, distance) {
    var newM = 0;
    var m;
    if (startX !== endX && startY !== endY) {
        m = (endY - startY) / (endX - startX);
        newM = -1 / m;
    }

    var magnitude = Math.sqrt(1 + (newM * newM));

    var deltaX = distance / magnitude;
    var deltaY = (distance * newM) / magnitude;

    var x1 = endX + deltaX;
    var y1 = endY + deltaY;

    var x2 = endX - deltaX;
    var y2 = endY - deltaY;

    // Calcola i nuovi punti a distanza specificata
    const point1 = {
        x: x1,
        y: y1
    };

    const point2 = {
        x: x2,
        y: y2
    };

    return [point1, point2];
}

function drawBackgroundArrow(layer, pitch) {
    if (d3.select("#arrow" + pitch.id).empty()) {
        // Calcola le dimensioni della freccia in base alle proporzioni specificate
        var arrowWidth = scaleSvg(pitch.width / 2);
        var arrowHeight = scaleSvg(pitch.length / 5);
        var arrowHeadWidth = arrowWidth / 4;
        var arrowHeadHeight = arrowHeight / 3 * 1.5;

        // Calcola le coordinate x e y per posizionare la freccia al centro del SVG
        var centerX = (scaleSvg(pitch.width) - arrowWidth) / 2;
        var centerY = (scaleSvg(pitch.length) / 2) - (arrowHeight / 2);

        // Costruisci il path per la freccia
        var arrowPath = "M" + (centerX) + "," + (centerY) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY - arrowHeadHeight) +
                " L" + (centerX + arrowHeadWidth * 3 + arrowHeadWidth) + "," + (centerY + arrowHeight / 2) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY + arrowHeadHeight + arrowHeight) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY + arrowHeight) +
                " L" + (centerX) + "," + (centerY + arrowHeight) +
                " L" + (centerX) + "," + (centerY);

        layer.append("path")
                .attr("d", arrowPath)
                .attr("stroke", "#ffffff33")
                .attr("stroke-width", 1)
                .attr("fill", "#ffffff25")
                .attr("id", "arrow" + pitch.id);
    }
}

function drawMainPitchText(text) {
    var width = scaleSvg(pitch.width);
    var height = scaleSvg(pitch.length);

    d3.select("#infoText" + pitch.id).remove();
    // Aggiungi un elemento text al g
    var testoElement = pitch.baseLayer.append("text")
            .text(text)
            .attr("text-anchor", "middle") // Centro orizzontalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("x", width / 2) // Posizione x al centro
            .attr("y", height / 10 * 2) // Posizione y al centro
            .attr("font-size", calculateTextSize(30))
            .attr("id", "infoText" + pitch.id)
            .attr("stroke", "#ffffff")
            .attr("stroke-width", "0.25")
            .attr("fill", "#ffffff55")
            .style("text-transform", "uppercase");

    // Se il testo supera la larghezza dell'elemento g, riduci la dimensione del testo
    var textWidth = testoElement.node().getComputedTextLength();
    if (textWidth > width) {
        var newSize = Math.floor((width / textWidth) * calculateTextSize(30)); // calculateTextSize(30) è la dimensione del font iniziale
        testoElement.style("font-size", newSize + "px");
    }
}

function drawMainPitchEventInfoText(event) {
    var width = scaleSvg(pitch.width);

    var player = event._player;
    var playerTo = event._playerTo;
    var descrAzione = event.descrAzione.split("#");

    d3.select("#infoEventText" + pitch.id).remove();
    // Aggiungi un elemento text al g
    var textElement = pitch.baseLayer.append("text")
            .attr("text-anchor", "middle") // Centro orizzontalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("x", width / 2) // Posizione x al centro
            .attr("y", scale(2)) // Posizione y al centro
            .attr("font-size", calculateTextSize(10))
            .attr("id", "infoEventText" + pitch.id)
            .attr("fill", "#ffffffbb");

    textElement.append("tspan")
            .text("(" + event.mTeam + ") ");
    if (player && Object.keys(player).length === 1) {
        textElement.append("tspan")
                .text(player[Object.keys(player)[0]].known_name)
                .style("font-weight", "bold");
    }
    textElement.append("tspan")
            .text(" - " + descrAzione[0] + "," + descrAzione[1]);
    if (playerTo) {
        textElement.append("tspan")
                .text(" - " + playerTo.known_name)
                .style("font-weight", "bold");
    }

    // Se il testo supera la larghezza dell'elemento g, riduci la dimensione del testo
    var textWidth = textElement.node().getComputedTextLength();
    if (textWidth > width) {
        var newSize = Math.floor((width / textWidth) * calculateTextSize(10)); // calculateTextSize(10) è la dimensione del font iniziale
        textElement.style("font-size", newSize);
    }
}

function clickEvent(element, type, eventType) {
    if (eventType === 2) {  // tasto destro
        d3.event.preventDefault();
    }

    var d3Element = d3.select(element);
    var text;
    if (d3Element.attr("fill") === 'transparent') {
        if (eventType === 1) {
            d3Element.attr("fill", clickColors.from);
            text = "FROM";
        } else {
            d3Element.attr("fill", clickColors.to);
            text = "TO";
        }
    } else {
        if (eventType === 1) {
            if (d3Element.attr("fill") === clickColors.fromTo) {
                d3Element.attr("fill", clickColors.to);
                text = "TO";
            } else if (d3Element.attr("fill") === clickColors.from) {
                d3Element.attr("fill", "transparent");
            } else if (d3Element.attr("fill") === clickColors.to) {
                d3Element.attr("fill", clickColors.fromTo);
                text = "FROM - TO";
            } else if (d3Element.attr("fill") === clickColors.zoneClickable) {
                d3Element.attr("fill", clickColors.from);
                text = "FROM";
            }
        } else {
            if (d3Element.attr("fill") === clickColors.fromTo) {
                d3Element.attr("fill", clickColors.from);
                text = "FROM";
            } else if (d3Element.attr("fill") === clickColors.to) {
                d3Element.attr("fill", "transparent");
            } else if (d3Element.attr("fill") === clickColors.from) {
                d3Element.attr("fill", clickColors.fromTo);
                text = "FROM - TO";
            } else if (d3Element.attr("fill") === clickColors.zoneClickable) {
                d3Element.attr("fill", clickColors.to);
                text = "TO";
            }
        }
    }
//    var parentNode = d3.select(element.parentNode);
//    parentNode.selectAll("*:not(rect)").raise();

    var zoneId = parseInt(d3Element.attr("data-event"));
    var index = parseInt(d3Element.attr("data-index"));
    var indexJ = "";
    var coords, additionalX = 0, additionalY = 0, additionalTextSize = 0, additionalNumberSize = 0; // usati per il testo
    if (type === 0) {
        coords = eventType + "|" + (realWidth / 2 * index) + ";0;" + (realWidth / 2) + ";" + realLength;
    } else if (type === 1) {
        coords = eventType + "|" + (realWidth / 3 * index) + ";0;" + (realWidth / 3) + ";" + realLength;
    } else if (type === 2) {
        coords = eventType + "|" + "0;" + (realLength / 5 * index) + ";" + realWidth + ";" + (realLength / 5);
    } else if (type === 3) {
        indexJ = parseInt(d3Element.attr("data-index-j"));
        coords = eventType + "|" + (realWidth / 3 * indexJ) + ";" + (realLength / 5 * index) + ";" + (realWidth / 3) + ";" + (realLength / 5);
    } else if (type === 4) {
        var tmpScale = d3.scaleLinear()
                .domain([0, 100])
                .range([0, 500 * multiplier]);
        var tmpCoords;

        if (index === 0) {
            // fuori area
            coords = eventType + "|";
            // parte sopra
            tmpCoords = "0;0;" + (realWidth / 2) + ";" + 13.85;
            coords += tmpCoords + "||";
            // parte destra
            tmpCoords = 16.5 + ";0;" + (realWidth / 2) + ";" + realLength;
            coords += tmpCoords + "||";
            // parte sotto
            tmpCoords = "0;" + 54.15 + ";" + (realWidth / 2) + ";" + realLength;
            coords += tmpCoords;
        } else if (index === 1) {
            // area grande (comprende anche area piccola, deciso da Michele)
            coords = eventType + "|" + "0;" + 13.85 + ";16.5;40.3";
//            additionalY = -(parseFloat(d3Element.attr("width")) / 1.25);
            additionalTextSize = -(calculateTextSize(12) / 2);
        } else if (index === 2) {
            // fuori area offensiva
            coords = eventType + "|";
            // parte sopra
            tmpCoords = "52.5;0;" + realWidth + ";" + 13.85;
            coords += tmpCoords + "||";
            // parte sinistra
            tmpCoords = 52.5 + ";0;88.5;" + realLength;
            coords += tmpCoords + "||";
            // parte sotto
            tmpCoords = "52.5;" + 54.15 + ";" + realWidth + ";" + realLength;
            coords += tmpCoords;
        } else if (index === 3) {
            // area grande offensiva (comprende anche area piccola, deciso da Michele)
            coords = eventType + "|" + "88.5;" + 13.85 + ";16.5;40.3";
            additionalTextSize = -(calculateTextSize(12) / 2);
        }
    }
    if (typeof jsSetButtonSelected === 'function') {
        jsShowBlockUI();
        setTimeout(function () {
            // se non ci sono filtri è stato deciso da Michele di mostrare i passaggi
            var hasFilter = $("#bottomNavContent").find("button.uk-button-success:not(.teamAllButton)").length > 0 || $("#sourcePersonal").hasClass("uk-button-success");
            if (!hasFilter) {
                jsSetButtonSelected('SICS2015_PASS', 0);
            }

            jsSetButtonSelected("" + 0, 7, 0, zoneId + "-_-" + coords); // uso campo comment
            checkModality();
            $.unblockUI();
        }, 150);
    }

    d3.select("#text" + index + indexJ).remove();
    if (typeof text !== 'undefined' && text) {
        // devo sempre mettere le 2 linee altrimenti quando vado ad aggiornare
        // filtrando per giocatore o evento e la linea non è presente non aggiorna
        // il contatore
        if (zoneEventAmount.has(zoneId)) {
            text += "\n" + zoneEventAmount.get(zoneId).length;
        } else {
            text += "\n" + -1;
        }
        var lines = text.split("\n");
        var dy = (lines.length > 1 ? ((lines.length - 1) * scale(2)) : 0);

        pitchFasce.baseLayer.append("text")
                .attr("x", parseFloat(d3Element.attr("width")) / 2 + parseFloat(d3Element.attr("x")) + additionalX)
                .attr("y", parseFloat(d3Element.attr("height")) / 2 + parseFloat(d3Element.attr("y")) + (calculateTextSize(12) / 2) - dy + additionalY)
                .attr("text-anchor", "middle")
                .attr("fill", "white")
                .attr("font-size", calculateTextSize(12) + additionalTextSize)
                .attr("id", "text" + index + indexJ)
                .style("font-weight", "bold")
                .text(null)
                .each(function () {
                    var lines = text.split("\n");
                    for (var i = 0; i < lines.length; i++) {
                        var dy = i * 1.2;
                        var textContent = lines[i];
                        if (textContent === '-1') {
                            textContent = null;
                        }
                        var tspan = d3.select(this).append("tspan")
                                .attr("x", parseFloat(d3Element.attr("width")) / 2 + parseFloat(d3Element.attr("x")) + additionalX)
                                .attr("dy", dy + "em")
                                .text(textContent);

                        if (i === 1) { // nella seconda linea metto l'id
                            tspan.attr("id", "textAmount" + zoneId);
                            tspan.attr("font-size", calculateTextSize(10) + additionalNumberSize);
                            tspan.attr("eventAmountSpan", "1");
                        }
                    }
                });

        d3Element.raise();
    }
}

function updateZoneCounters(excludedZoneId) {
    // prima di tutto tolgo tutti i contatori così nel caso in cui passo da avere un contatore
    // a non averlo e non è la stessa zona che ho cliccato, devo togliere il suo valore
    d3.selectAll("tspan[eventAmountSpan]").text(null);

    // aggiorno tutti gli altri contatori
    zoneEventAmount.forEach(function (value, key) {
        if (typeof excludedZoneId === 'undefined' || !excludedZoneId || key !== excludedZoneId) {
            if (value > 0) {
                var textAmountElement = d3.select("#textAmountAngle" + key);
                if (typeof textAmountElement !== 'undefined' && textAmountElement) {
                    textAmountElement.text(value);
                } else {
                    console.warn("Can't find element by id", ("#textAmountAngle" + key));
                }
            }
        }
    });
}

function updateAngleCounters() {
    // prima di tutto tolgo tutti i contatori così nel caso in cui passo da avere un contatore
    // a non averlo e non è la stessa zona che ho cliccato, devo togliere il suo valore
    d3.selectAll("tspan[eventAmountSpanAngle]").text(null);

    // aggiorno tutti gli altri contatori
    angleEventAmount.forEach(function (value, key) {
        var textAmountElement = d3.select("#textAmountAngle" + key);
        if (typeof textAmountElement !== 'undefined' && textAmountElement) {
            textAmountElement.text(value.length);
        } else {
            console.warn("Can't find element by id", ("#textAmount" + key));
        }
    });
}

var playerListenerIndex;
var validEventTypes = ['PASS', 'TIF', 'DLL', 'FAF', 'INT'];
function initializePlayerListener() {
    if (typeof videoJsPlayer !== 'undefined' && videoJsPlayer) {
        if (typeof playerListenerIndex === 'undefined') {
            playerListenerIndex = 0;
            videoJsPlayer.on("timeupdate", function () {
                if (typeof isClipStopped !== 'undefined' && isClipStopped) {
                    if (playerListenerIndex === 4) { // evento richiamato ogni 250ms
                        checkModality();
                        playerListenerIndex = 0;

                        var currentTime = parseInt(videoJsPlayer.currentTime() * 1000); // in ms
                        var validEvents = [];
                        if (typeof eventMap !== 'undefined' && eventMap) {
                            eventMap.forEach((value, key) => {
                                var checkEvent = false;
                                if (value.mType) {
                                    if (validEventTypes.includes(value.mType)) {
                                        checkEvent = true;
//                                        if (value.mType === 'FAF') {
//                                            value.mTags.forEach((tag) => {
//                                                if (tag.code && tag.code === 'FAF-0') {
//                                                    checkEvent = false;
//                                                }
//                                            });
//                                        }
                                        if (value.mType === 'DLL') {
                                            value.mTags.forEach((tag) => {
                                                if (tag.code && tag.code === 'DLL-4') { // escludo duello con fallo
                                                    checkEvent = false;
                                                }
                                            });
                                        }
                                    }

                                    if (checkEvent) {
                                        if (currentTime >= value.mStart && currentTime <= value.mEnd) {
                                            validEvents.push(value);
                                        }
                                    }
                                }
                            });
                        }

                        if (validEvents && validEvents.length > 0) {
//                        console.log("going to draw", validEvents.length, "events...");
                            drawEvents(validEvents);
                        } else {
                            // tolgo eventuali punti ancora visibili e testo dell'ultima azione vista
                            pitch.pointLayer.selectAll("*").remove();
                            d3.select("#infoEventText" + pitch.id).remove();
                        }
                    }

                    playerListenerIndex++;
                }
            });
        }
    } else {
        console.warn("initializePlayerListener()", "videoJsPlayer is not initialized.");
    }
}

/*
 * UTILS
 */
function calculateTextNumberSize(number) {
    var baseSize = 10;

    if (number > 9) {
        baseSize = 8;
    }

    return baseSize * multiplier;
}

function calculateTextSize(number) {
    return number * multiplier;
}

function getOffsetY(number) {
    if (raggio <= 5) {
        if (number > 9) {
            return -2 * multiplier;
        } else {
            return -1 * multiplier;
        }
    } else {
        return -4 * multiplier; // per raggio = 7 va bene -4
    }
}

function highlightElements() {
    var eventData = d3.select(this).attr("data-event");
    highlightElementById(eventData);

    var needToStartAction = false;
    if (!isClipStopped) {
        needToStartAction = true;
    } else if (indexActionToPlay >= 0) {
        var actionsArray;
        if (isRunningOnChecked) {
            actionsArray = checkedActions;
        } else {
            actionsArray = selectedActions;
        }

        // se l'evento che sto cliccando non è quello che sto visualizzando
        // allora lancio startAction
        var currentEventId = actionsArray[indexActionToPlay];
        if (currentEventId !== parseInt(eventData.replace("event-", ""))) {
            needToStartAction = true;
        }
    } else if (indexActionToPlay === -1) {
        // se non sto guardando nessuna clip allora lancio startAction
        needToStartAction = true;
    }

    if (needToStartAction) {
        startAction("" + eventData.replace("event-", "") + "", 0, false);
    } else {
        if (typeof videoJsPlayer !== 'undefined' && videoJsPlayer) {
            var event = eventMap.get(eventData.replace("event-", ""));
            if (event) {
                videoJsPlayer.currentTime(event.mStart / 1000);
            } else {
                console.warn("highlightElements()", "Unable to find event in map. Looking for", eventData.replace("event-", ""));
            }
        } else {
            console.warn("highlightElements()", "videoJsPlayer is not initialized");
        }
    }
}

function highlightElementById(eventId) {
    if (eventId) {
        var event = eventMap.get(eventId.replace("event-", ""));

        // pagina video manda solo eventId
        if (!eventId.includes("event-")) {
            eventId = "event-" + eventId;
        }

        d3.selectAll(".highlighted").classed("highlighted", false).style("stroke", null).style("stroke-width", null);
        var lineColor = getEventColor(event);
        d3.selectAll("line[marker-end='url(#arrowheadHighlighted)']").each(function () {
            // per ogni freccia devo prendere il SUO evento e prendere il colore corretto
            var dataEvent = d3.select(this).attr("data-event");
            if (dataEvent && dataEvent.includes("event-")) {
                var previousEvent = eventMap.get(dataEvent.replace("event-", ""));
                var arrowColor = getEventColor(previousEvent);
                d3.select(this).attr("marker-end", "url(#arrowhead" + arrowColor.replace("#", "") + ")");
            } else {
                console.warn("Unexpected value for highlighted arrow. data-event attribute is", dataEvent);
                // per evitare problemi metto la freccia nera
                d3.select(this).attr("marker-end", "url(#arrowhead" + lineColors.black.replace("#", "") + ")");
            }
        });
        // per posizionale shots
        d3.selectAll("line[marker-end='url(#arrowheadverticalHighlighted)']").each(function () {
            // per ogni freccia devo prendere il SUO evento e prendere il colore corretto
            var dataEvent = d3.select(this).attr("data-event");
            if (dataEvent && dataEvent.includes("event-")) {
                var previousEvent = eventMap.get(dataEvent.replace("event-", ""));
                var arrowColor = getEventColor(previousEvent);
                d3.select(this).attr("marker-end", "url(#arrowheadvertical" + arrowColor.replace("#", "") + ")");
            } else {
                console.warn("Unexpected value for highlighted arrow. data-event attribute is", dataEvent);
                // per evitare problemi metto la freccia nera
                d3.select(this).attr("marker-end", "url(#arrowheadvertical" + lineColors.black.replace("#", "") + ")");
            }
        });

        // per posizionale shots
        d3.selectAll("[data-event='" + eventId + "']:not(text):not(line[stroke-dasharray])")
                .classed("highlighted", true)
                .style("stroke", "black")
                .style("stroke-width", 3)
                .each(function () {
                    // Cambia l'attributo "marker-end" delle linee
                    if (this.tagName === "line") {
                        if (d3.select(this).attr("marker-end")) {
                            d3.select(this).style("stroke", "lime");
                            d3.select(this).style("stroke-width", 2);
                            d3.select(this).attr("marker-end", "url(#arrowheadverticalHighlighted)");
                        }
                    }
                });
        d3.selectAll("[data-event='" + eventId + "']:not(text):not(line[stroke-dasharray]):not([isShot])")
                .classed("highlighted", true)
                .style("stroke", "lime")
                .style("stroke-width", 2)
                .each(function () {
                    // Cambia l'attributo "marker-end" delle linee
                    if (this.tagName === "line") {
                        if (d3.select(this).attr("marker-end")) {
                            d3.select(this).attr("marker-end", "url(#arrowheadHighlighted)");
                        }
                    }
                });

        // porto in primo piano se ci sono oggetti sovrapposti
        d3.selectAll("[data-event='" + eventId + "']").raise();
        if (typeof isClipStopped !== 'undefined' && isClipStopped && !filteredRequest) {
            drawMainPitchEventInfoText(event);
        }
    } else {
        console.warn("highlightElementById()", "Can't highlight element because eventId is null or empty");
    }
}

function showTooltip() {
    var tooltipText = d3.select(this).attr("data-tooltip");
    var bbox = this.getBBox(); // Ottieni il bounding box dell'elemento <text>

    tooltip.transition()
            .duration(200)
            .style("opacity", .9)
            .style("left", (bbox.x + bbox.width / 2) + "px")
            .style("top", (bbox.y - 10) + "px");

    tooltip.html(tooltipText)
            .style("left", (bbox.x + bbox.width / 2) + "px")
            .style("top", (bbox.y - 10) + "px");
}

function hideTooltip() {
    tooltip.transition()
            .duration(500)
            .style("opacity", 0);
}

function changeOptions(element, option) {
    if (option === 'orientation') {
        var type;
        if (posizionaleOptions.orientation === 'half') {
            type = 1;
            posizionaleOptions.orientation = 'vertical';
            $(element).find("img").attr("src", "/sicstv/images/vertical-pos.png");
        } else if (posizionaleOptions.orientation === 'vertical') {
            type = 2;
            posizionaleOptions.orientation = 'horizontal';
            $(element).find("img").attr("src", "/sicstv/images/horizontal-pos.png");
        } else if (posizionaleOptions.orientation === 'horizontal') {
            type = 3;
            posizionaleOptions.orientation = 'grid';
            $(element).find("img").attr("src", "/sicstv/images/grid-pos.png");
        } else if (posizionaleOptions.orientation === 'grid') {
            type = 4;
            posizionaleOptions.orientation = 'aree';
            $(element).find("img").attr("src", "/sicstv/images/aree-pos.png");
        } else {
            type = 0;
            posizionaleOptions.orientation = 'half';
            $(element).find("img").attr("src", "/sicstv/images/half-pos.png");
        }
        drawField(currentContainerId, pitchFasce, type);

        // tolgo filtro posizione
        posizioniSelected = [];
    }
}

function resetPositionalFilters() {
    var type;
    if (posizionaleOptions.orientation === 'half') {
        type = 0;
    } else if (posizionaleOptions.orientation === 'vertical') {
        type = 1;
    } else if (posizionaleOptions.orientation === 'horizontal') {
        type = 2;
    } else if (posizionaleOptions.orientation === 'grid') {
        type = 3;
    } else {
        type = 4;
    }

    drawField(currentContainerId, pitchFasce, type);
    posizioniSelected = [];
    applyFilterEvents(false, true);
    drawEvents();
}

function resetAngleFilters() {
    angoliSelected = [];
    applyFilterEvents(false, true);
    drawEvents();
}

function resetAllFilters(onlyFilters) {
    var type;
    if (posizionaleOptions.orientation === 'half') {
        type = 0;
    } else if (posizionaleOptions.orientation === 'vertical') {
        type = 1;
    } else if (posizionaleOptions.orientation === 'horizontal') {
        type = 2;
    } else if (posizionaleOptions.orientation === 'grid') {
        type = 3;
    } else {
        type = 4;
    }

    drawField(currentContainerId, pitchFasce, type);
    angoliSelected = [];
    posizioniSelected = [];

    if (typeof onlyFilters === 'undefined' || !onlyFilters) {
        applyFilterEvents(false, true);
        drawEvents();
        startAction('', 0, false);  // faccio ripartire la prima clip
    }
}

function clickRelativeElement(event, elementType) {
    var x = event.clientX;
    var y = event.clientY;
    // Ottieni gli elementi nelle stesse coordinate del click
    var elements = document.elementsFromPoint(x, y);
    elements.forEach(function (element) {
        if (element.tagName === elementType && !d3.select(element).empty()) {
            d3.select(element).dispatch("click");
        }
    });
}

function backToVideoMode() {
    closeClipViewer();
    resetAllFilters(true);
    resetFilter(true);  // tolgo tutti i filtri eventi, tempi, giocatori...
    $('#closeClipViewer2').addClass('uk-hidden');
}

function getEventColor(event) {
    var lineColor = lineColors.black;
    if (typeof event !== 'undefined' && event) {
        if (event.mType === 'TIF') { // tiri rossi
            lineColor = lineColors.red;
        } else if (event.mType === 'PASS') {
            event.mTags.forEach((tag) => {
                if (tag.code && tag.code === 'PASS-0') {
                    lineColor = lineColors.blue;
                    return false;
                }
            });
        }
    }

    return lineColor;
}

function getPointsDistance(firstPoint, secondPoint) {
    if (firstPoint && !firstPoint.isDefault && secondPoint && !secondPoint.isDefault) {
        var x = firstPoint.cx - secondPoint.cx;
        var y = firstPoint.cy - secondPoint.cy;

        return Math.sqrt(x * x + y * y);
    } else {
        return 0;
    }
}

function takeBasePitchScreen(svgElement, name) {
    // Serializza l'SVG
    var serializer = new XMLSerializer();
    var svgString = serializer.serializeToString(svgElement);

    // Crea un oggetto Image
    var img = new Image();
    img.src = 'data:image/svg+xml;base64,' + window.btoa(unescape(encodeURIComponent(svgString)));

    // Quando l'immagine è caricata, disegnala su un canvas
    img.onload = function () {
        var canvas = document.createElement('canvas');
        canvas.width = svgElement.clientWidth;
        canvas.height = svgElement.clientHeight;
        var ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);

        // Salva il canvas come PNG
        var a = document.createElement('a');
        a.href = canvas.toDataURL('image/png');
        a.download = name || 'image.png';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    };
}

function checkModality() {
    var continueCheck = true;
    // se arrivo filtrato non mostro la modalità dato che sarà sempre la filter mode
    if (typeof filteredRequest !== 'undefined' && filteredRequest) {
        if (!$("#posizionaleModalityFilter").hasClass("uk-hidden") || !$("#posizionaleModalityVideo").hasClass("uk-hidden")) {
            $("#posizionaleModalityFilter").addClass("uk-hidden");
            $("#posizionaleModalityVideo").addClass("uk-hidden");
            continueCheck = false;
        }
    }

    if (continueCheck) {
        if (typeof isClipStopped !== 'undefined' && isClipStopped) {
            if (!$("#posizionaleModalityFilter").hasClass("uk-hidden")) {
                $("#posizionaleModalityFilter").addClass("uk-hidden");
                $("#posizionaleModalityVideo").removeClass("uk-hidden");
            }
        } else {
            if (!$("#posizionaleModalityVideo").hasClass("uk-hidden")) {
                $("#posizionaleModalityFilter").removeClass("uk-hidden");
                $("#posizionaleModalityVideo").addClass("uk-hidden");
            }
        }
    }
}

function handleResize() {
    var baseWidth = 1920;

    // Aggiorna le dimensioni del tuo SVG in base alle nuove dimensioni della finestra
    var newWidth = window.innerWidth;
    var newPitchWidth = basePitchWidth / baseWidth * newWidth;
    var newPitchLength = basePitchLength / basePitchWidth * newPitchWidth;

    console.log("handleResize()", "old pitch:", pitch.width, "x", pitch.length, "new pitch:", newPitchWidth, "x", newPitchLength);
    pitch.width = newPitchWidth;
    pitch.length = newPitchLength;

    pitchFasce.width = pitch.width / 2;
    pitchFasce.length = pitch.length / 2;

    pitchDiagram.width = pitch.width / 2;
    pitchDiagram.length = pitch.length / 2;

    drawField(currentContainerId);
    drawEvents();

    // resize campo tiri
    if (typeof handleShotsResize === "function") {
        handleShotsResize();
    }
}

// Aggiungi un ascoltatore per l'evento di ridimensionamento della finestra
window.addEventListener("resize", handleResize);