@charset "UTF-8";
/* ------------------------------------------------------------------------------
 *
 *  # Components
 *
 *  Components import. Ordering matters. See _config.scss for more options
 *
 * ---------------------------------------------------------------------------- */
/* ------------------------------------------------------------------------------
 *
 *  # Global configuration
 *
 *  Here you can change main theme, enable or disable certain components and
 *  optional styles. This allows you to include only components that you need.
 *
 *  'true'  - enables component and includes it to main CSS file.
 *  'false' - disables component and excludes it from main CSS file.
 *
 * ---------------------------------------------------------------------------- */
/* ------------------------------------------------------------------------------
 *
 *  # Custom Limitless functions
 *
 *  Utility mixins and functions for evalutating source code across our variables, maps, and mixins.
 *
 * ---------------------------------------------------------------------------- */
/* ------------------------------------------------------------------------------
 *
 *  # Custom template mixins
 *
 *  All custom mixins are prefixed with "ll-" to avoid conflicts
 *
 * ---------------------------------------------------------------------------- */
/* ------------------------------------------------------------------------------
 *
 *  # Additional variables
 *
 *  Mainly 3rd party libraries and additional variables for default
 *  Bootstrap components.
 *
 * ---------------------------------------------------------------------------- */
/* ------------------------------------------------------------------------------
*
*  # Select2 selects
*
*  Styles for select2.js - custom select plugin
*
* ---------------------------------------------------------------------------- */
.select2-container {
  --s2-bg: var(--white);
  --s2-color: var(--body-color);
  --s2-padding-y: 0.5rem;
  --s2-padding-x: 0.875rem;
  --s2-placeholder-color: var(--gray-600);
  --s2-border-width: var(--border-width);
  --s2-border-color: var(--gray-400);
  --s2-border-radius: var(--border-radius);
  --s2-focus-border-color: var(--component-active-bg);
  --s2-focus-box-shadow: var(--focus-ring-box-shadow);
  --s2-disabled-color: var(--body-color);
  --s2-disabled-bg: var(--gray-100);
  --s2-disabled-border-color: var(--gray-400);
  --s2-disabled-opacity: 0.6;
  --s2-font-size: var(--body-font-size);
  --s2-line-height: var(--body-line-height);
  --s2-indicator-padding: 2.625rem;
  --s2-menu-padding-y: 0.625rem;
  --s2-menu-max-height: 17rem;
  --s2-menu-bg: var(--white);
  --s2-menu-border-width: var(--border-width);
  --s2-menu-border-color: var(--border-color-translucent);
  --s2-menu-border-radius: var(--border-radius);
  --s2-menu-box-shadow: var(--box-shadow-lg);
  --s2-menu-item-spacer-y: 1px;
  --s2-menu-item-padding-x: var(--spacer);
  --s2-menu-item-padding-y: calc(var(--spacer) * 0.4);
  --s2-menu-link-color: var(--body-color);
  --s2-menu-link-disabled-color: var(--gray-500);
  --s2-menu-link-hover-color: var(--body-color);
  --s2-menu-link-hover-bg: var(--gray-200);
  --s2-menu-link-active-color: var(--gray-900);
  --s2-menu-link-active-bg: var(--gray-300);
  --s2-search-bg: var(--white);
  --s2-search-padding-y: 0.5rem;
  --s2-search-padding-x: 0.875rem;
  --s2-search-color: var(--body-color);
  --s2-search-border-width: var(--border-width);
  --s2-search-border-color: var(--gray-400);
  --s2-search-border-radius: var(--border-radius);
  --s2-search-focus-bg: var(--white);
  --s2-search-focus-border-color: var(--component-active-bg);
  --s2-search-focus-box-shadow: var(--focus-ring-box-shadow);
  --s2-pills-bg: var(--gray-300);
  --s2-pills-color: var(--body-color);
  --s2-pills-hover-bg: var(--component-active-bg);
  --s2-pills-hover-color: var(--component-active-color);
  --s2-pills-border-radius: calc(var(--border-radius) - (var(--border-width)));
  --s2-pills-spacer: 0.25rem;
  outline: 0;
  position: relative;
  display: inline-block;
  vertical-align: middle;
  text-align: left;
}
.select2-container[data-color-theme=dark], [data-color-theme=dark] .select2-container:not([data-color-theme]), html[data-color-theme=dark] .select2-container {
  color-scheme: dark;
  --s2-bg: #2c2d33;
  --s2-menu-bg: #32333a;
  --s2-menu-border-color: rgba(var(--black-rgb), 0.25);
  --s2-search-bg: #2c2d33;
  --s2-search-focus-bg: #2c2d33;
}

.select2-selection--single {
  outline: 0;
  display: block;
  position: relative;
  padding: var(--s2-padding-y) 0;
  font-size: var(--s2-font-size);
  line-height: var(--s2-line-height);
  color: var(--s2-color);
  background-color: var(--s2-bg);
  border: var(--s2-border-width) solid var(--s2-border-color);
  white-space: nowrap;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
  border-radius: var(--s2-border-radius);
}
@media (prefers-reduced-motion: reduce) {
  .select2-selection--single {
    transition: none;
  }
}
.select2-container--focus:not(.select2-container--disabled) .select2-selection--single, .select2-container--open .select2-selection--single {
  border-color: var(--s2-focus-border-color);
  box-shadow: var(--s2-focus-box-shadow);
}
.select2-selection--single .select2-selection__rendered {
  display: block;
  padding-left: var(--s2-padding-x);
  padding-right: var(--s2-indicator-padding);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.select2-selection--single .select2-selection__rendered > i {
  margin-right: var(--spacer-2);
}
.select2-selection--single .select2-selection__clear {
  position: relative;
  cursor: pointer;
  float: right;
  font-size: 0;
  line-height: 1;
  margin-top: calc((var(--body-line-height-computed) - var(--icon-font-size)) * 0.5);
  margin-left: var(--spacer-2);
  opacity: 0.75;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .select2-selection--single .select2-selection__clear {
    transition: none;
  }
}
.select2-selection--single .select2-selection__clear:hover {
  opacity: 1;
}
.select2-selection--single .select2-selection__clear:after {
  content: "\f642";
  font-family: var(--icon-font-family);
  display: inline-block;
  font-size: var(--icon-font-size);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.select2-selection--single .select2-selection__placeholder {
  color: var(--s2-placeholder-color);
}
.select2-selection--single .select2-selection__arrow {
  display: inline-block;
  position: absolute;
  right: var(--s2-padding-x);
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 1em;
  height: 1em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23374151' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-size: 16px 12px;
}
.select2-selection--single .select2-selection__arrow[data-color-theme=dark], [data-color-theme=dark] .select2-selection--single .select2-selection__arrow:not([data-color-theme]), html[data-color-theme=dark] .select2-selection--single .select2-selection__arrow {
  color-scheme: dark;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
.select2-container--disabled .select2-selection--single {
  color: var(--s2-disabled-color);
  background-color: var(--s2-disabled-bg);
  border-color: var(--s2-border-color);
}
.select2-container--disabled .select2-selection--single .select2-selection__clear {
  display: none;
}

.select2-selection--multiple {
  outline: 0;
  display: block;
  border: var(--s2-border-width) solid var(--s2-border-color);
  cursor: text;
  background-color: var(--s2-bg);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
  border-radius: var(--s2-border-radius);
}
@media (prefers-reduced-motion: reduce) {
  .select2-selection--multiple {
    transition: none;
  }
}
.select2-container--focus:not(.select2-container--disabled) .select2-selection--multiple, .select2-container--open .select2-selection--multiple {
  border-color: var(--s2-focus-border-color);
  box-shadow: var(--s2-focus-box-shadow);
}
.select2-selection--multiple .select2-selection__rendered {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  list-style: none;
  margin: 0;
  padding: 0 var(--s2-pills-spacer) var(--s2-pills-spacer) var(--s2-pills-spacer);
  width: 100%;
}
.select2-selection--multiple .select2-selection__placeholder {
  color: var(--s2-placeholder-color);
}
.select2-container--disabled .select2-selection--multiple {
  background-color: var(--s2-disabled-bg);
  border-color: var(--s2-border-color);
}
.select2-selection--multiple .select2-selection__choice {
  background-color: var(--s2-pills-bg);
  color: var(--s2-pills-color);
  cursor: default;
  float: left;
  margin-right: var(--s2-pills-spacer);
  margin-top: var(--s2-pills-spacer);
  padding: calc(var(--s2-padding-y) - var(--s2-pills-spacer)) var(--s2-padding-x);
  white-space: normal;
  word-break: break-all;
  border-radius: var(--s2-pills-border-radius);
  transition: color ease-in-out var(--transition-base-timer), background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .select2-selection--multiple .select2-selection__choice {
    transition: none;
  }
}
.select2-selection--multiple .select2-selection__choice:hover, .select2-selection--multiple .select2-selection__choice:focus {
  background-color: var(--s2-pills-hover-bg);
  color: var(--s2-pills-hover-color);
}
.select2-selection--multiple .select2-selection__choice > i {
  margin-right: var(--spacer-2);
}
.select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
  cursor: pointer;
  float: right;
  font-size: var(--icon-font-size);
  line-height: 1;
  margin-left: var(--spacer-2);
  opacity: 0.75;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
    transition: none;
  }
}
.select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove:hover {
  opacity: 1;
}
.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
  opacity: var(--s2-disabled-opacity);
}
.select2-container--disabled .select2-selection--multiple .select2-selection__choice:hover, .select2-container--disabled .select2-selection--multiple .select2-selection__choice:focus {
  background-color: var(--s2-pills-bg);
  color: var(--s2-pills-color);
}
.select2-container--disabled .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
  display: none;
}
.select2-selection--multiple .select2-search--inline {
  float: left;
}
.select2-selection--multiple .select2-search--inline .select2-search__field {
  font-size: 100%;
  margin-top: var(--s2-pills-spacer);
  padding: calc(var(--s2-search-padding-y) - var(--s2-pills-spacer)) 0;
  background-color: transparent;
  border-width: 0;
  outline: 0;
  color: inherit;
  margin-left: calc(var(--s2-pills-spacer) * 2);
  -webkit-appearance: textfield;
}
.select2-selection--multiple .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
.select2-selection--multiple .select2-search--inline .select2-search__field::-webkit-input-placeholder {
  color: var(--s2-placeholder-color);
}
.select2-selection--multiple .select2-search--inline .select2-search__field::-moz-placeholder {
  color: var(--s2-placeholder-color);
}
.select2-selection--multiple .select2-search--inline .select2-search__field:-ms-input-placeholder {
  color: var(--s2-placeholder-color);
}
.select2-selection--multiple .select2-search--inline .select2-search__field::-ms-input-placeholder {
  color: var(--s2-placeholder-color);
}
.select2-selection--multiple .select2-search--inline .select2-search__field::placeholder {
  color: var(--s2-placeholder-color);
}
.select2-selection--multiple .select2-search--inline:first-child .select2-search__field {
  margin-left: 0;
  padding-left: calc(var(--s2-search-padding-x) - var(--s2-pills-spacer));
}

.select2-dropdown {
  background-color: var(--s2-menu-bg);
  color: var(--s2-menu-link-color);
  border: var(--s2-menu-border-width) solid var(--s2-menu-border-color);
  display: block;
  position: absolute;
  /*rtl:ignore*/
  left: -100000px;
  width: 100%;
  z-index: 1080;
  border-radius: var(--s2-menu-border-radius);
  box-shadow: var(--s2-menu-box-shadow);
}

.select2-results {
  display: block;
}

.select2-results__options {
  list-style: none;
  margin: 0;
  padding: 0;
}
.select2-results > .select2-results__options {
  padding-bottom: var(--s2-menu-padding-y);
  max-height: var(--s2-menu-max-height);
  overflow-y: auto;
}
.select2-search--hide + .select2-results > .select2-results__options {
  padding-top: var(--s2-menu-padding-y);
}
.select2-results:first-child > .select2-results__options {
  padding-top: var(--s2-menu-padding-y);
}

.select2-results__option {
  padding: var(--s2-menu-item-padding-y) var(--s2-menu-item-padding-x);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .select2-results__option {
    transition: none;
  }
}
.select2-results__option + .select2-results__option {
  margin-top: var(--s2-menu-item-spacer-y);
}
.select2-results__option i {
  margin-right: var(--spacer-2);
}
.select2-results__option i.icon-undefined {
  display: none;
}
.select2-results__option[role=group] {
  padding: 0;
}
.select2-results__option.select2-results__option--highlighted {
  background-color: var(--s2-menu-link-hover-bg);
  color: var(--s2-menu-link-hover-color);
}
.select2-results__option[aria-disabled=true] {
  color: var(--s2-menu-link-disabled-color);
}
.select2-results__option.select2-results__option--highlighted:active, .select2-results__option[aria-selected=true] {
  color: var(--s2-menu-link-active-color);
  background-color: var(--s2-menu-link-active-bg);
}
.select2-results__options--nested > .select2-results__option {
  padding-left: calc(var(--s2-menu-item-padding-x) * 2);
  padding-right: calc(var(--s2-menu-item-padding-x) * 2);
}

.select2-results__group {
  display: block;
  padding: var(--s2-menu-item-padding-y) var(--s2-menu-item-padding-x);
  cursor: default;
  font-weight: 600;
  margin-top: var(--s2-menu-padding-y);
  margin-bottom: var(--s2-menu-padding-y);
}
.select2-results__option:first-child > .select2-results__group {
  margin-top: 0;
}

.select2-results__message {
  color: rgba(var(--body-color-rgb), 0.75);
  cursor: default;
}

.select2-results__option.loading-results {
  padding-top: 0;
}
.select2-results__option.loading-results + .select2-results__option {
  margin-top: var(--s2-menu-padding-y);
}

.select2-results__option--load-more {
  text-align: center;
  margin-top: var(--s2-menu-padding-y);
  cursor: default;
}

.select2-container--open .select2-dropdown {
  /*rtl:ignore*/
  left: 0;
}
.select2-container--open .select2-dropdown--above {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.select2-container--open.select2-container--above .select2-selection--single,
.select2-container--open.select2-container--above .select2-selection--multiple {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.select2-container--open .select2-dropdown--below {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.select2-container--open.select2-container--below .select2-selection--single,
.select2-container--open.select2-container--below .select2-selection--multiple {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.select2-search--dropdown {
  display: block;
  position: relative;
  padding: var(--s2-menu-item-padding-x);
}
.select2-search--dropdown:after {
  content: "\f4a8";
  font-family: var(--icon-font-family);
  position: absolute;
  top: 50%;
  left: calc(var(--s2-menu-item-padding-x) + var(--s2-search-padding-x));
  color: var(--gray-600);
  display: block;
  font-size: var(--icon-font-size);
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.select2-search--dropdown + .select2-results .select2-results__message:first-child {
  padding-top: 0;
}
.select2-search--dropdown .select2-search__field {
  padding: var(--s2-search-padding-y) var(--s2-search-padding-x);
  padding-left: calc(var(--s2-search-padding-x) * 2 + var(--icon-font-size));
  background-color: var(--s2-search-bg);
  color: var(--s2-search-color);
  border: var(--s2-search-border-width) solid var(--s2-search-border-color);
  outline: 0;
  width: 100%;
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
  border-radius: var(--s2-search-border-radius);
}
@media (prefers-reduced-motion: reduce) {
  .select2-search--dropdown .select2-search__field {
    transition: none;
  }
}
.select2-search--dropdown .select2-search__field:focus {
  border-color: var(--s2-search-focus-border-color);
  box-shadow: var(--s2-search-focus-box-shadow);
}
.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
.select2-search--dropdown.select2-search--hide {
  display: none;
}

.select-lg {
  --s2-padding-y: 0.625rem;
  --s2-padding-x: 1rem;
  --s2-font-size: var(--body-font-size-lg);
  --s2-line-height: var(--body-line-height-lg);
  --s2-search-padding-y: 0.625rem;
  --s2-search-padding-x: 1rem;
}

.select-sm {
  --s2-padding-y: 0.375rem;
  --s2-padding-x: 0.75rem;
  --s2-font-size: var(--body-font-size-sm);
  --s2-line-height: var(--body-line-height-sm);
  --s2-search-padding-y: 0.375rem;
  --s2-search-padding-x: 0.75rem;
}

.select2-close-mask {
  border: 0;
  margin: 0;
  padding: 0;
  display: block;
  position: fixed;
  left: 0;
  top: 0;
  min-height: 100%;
  min-width: 100%;
  height: auto;
  width: auto;
  z-index: 99;
  background-color: var(--white);
  opacity: 0;
}

.select2-hidden-accessible {
  border: 0 !important;
  clip: rect(0 0 0 0) !important;
  height: 1px !important;
  margin: -1px !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: fixed !important;
  width: 1px !important;
}

.input-group > :not(:nth-child(2)):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) .select2-selection {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3) .select2-selection,
.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu) .select2-selection {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .select2-container {
  -ms-flex: 1 1 auto;
      flex: 1 1 auto;
  min-width: 0;
}

.input-group > .select2-container--focus {
  z-index: 3;
}

.input-group > .select2-hidden-accessible.flex-grow-0 + .select2-container {
  -ms-flex-positive: 0;
      flex-grow: 0;
}

.select2-result-repository {
  padding-top: var(--s2-search-padding-y);
  padding-bottom: var(--s2-search-padding-y);
}

.select2-result-repository__avatar {
  float: left;
  width: 60px;
  margin-right: 0.9375rem;
}
.select2-result-repository__avatar img {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-pill);
}

.select2-result-repository__meta {
  margin-left: 70px;
}

.select2-result-repository__title {
  font-weight: 500;
  word-wrap: break-word;
  margin-bottom: 2px;
}

.select2-result-repository__forks,
.select2-result-repository__stargazers,
.select2-result-repository__watchers {
  display: inline-block;
  font-size: var(--body-font-size-sm);
}

.select2-result-repository__description {
  font-size: var(--body-font-size-sm);
}

.select2-result-repository__forks,
.select2-result-repository__stargazers {
  margin-right: 0.9375rem;
}

/* ------------------------------------------------------------------------------
*
*  # Bootstrap multiselect
*
*  Styles for multiselect.js - custom multiple select plugin
*
* ---------------------------------------------------------------------------- */
.multiselect-native-select {
  --multiselect-padding-x: 0.875rem;
  --multiselect-max-height: 280px;
  --multiselect-bg: var(--white);
  --multiselect-color: var(--body-color);
  --multiselect-border-color: var(--gray-400);
  --multiselect-border-radius: var(--border-radius);
  --multiselect-focus-border-color: var(--component-active-bg);
  --multiselect-focus-box-shadow: var(--focus-ring-box-shadow);
  --multiselect-disabled-color: var(--body-color);
  --multiselect-disabled-bg: var(--gray-100);
  --multiselect-disabled-border-color: var(--gray-400);
  position: relative;
  display: block;
}
.multiselect-native-select[data-color-theme=dark], [data-color-theme=dark] .multiselect-native-select:not([data-color-theme]), html[data-color-theme=dark] .multiselect-native-select {
  color-scheme: dark;
  --multiselect-bg: #2c2d33;
}
.multiselect-native-select select {
  border: 0 !important;
  clip: rect(0 0 0 0) !important;
  height: 0 !important;
  margin: 0 !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  width: 0 !important;
  left: 0;
  top: 0;
  display: none;
}

.multiselect {
  display: -ms-flexbox;
  display: flex;
  text-align: left;
  -ms-flex-pack: start;
      justify-content: flex-start;
  padding-left: var(--multiselect-padding-x);
  padding-right: var(--multiselect-padding-x);
  color: var(--multiselect-color);
  background-color: var(--multiselect-bg);
  border-color: var(--multiselect-border-color);
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
  border-radius: var(--multiselect-border-radius);
}
@media (prefers-reduced-motion: reduce) {
  .multiselect {
    transition: none;
  }
}
.multiselect.btn {
  cursor: default;
}
.multiselect .multiselect-selected-text {
  -ms-flex: 1;
      flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.multiselect:hover {
  background-color: var(--multiselect-bg);
  border-color: var(--multiselect-border-color);
}
.multiselect:active, .multiselect:active:focus, .multiselect:focus, .multiselect.show, .multiselect.show:focus {
  background-color: var(--multiselect-bg);
  border-color: var(--multiselect-focus-border-color);
  box-shadow: var(--multiselect-focus-box-shadow);
}
.multiselect.disabled {
  color: var(--multiselect-disabled-color);
  background-color: var(--multiselect-disabled-bg);
  border-color: var(--multiselect-disabled-border-color);
}
.multiselect.btn-lg {
  --multiselect-padding-x: 1rem;
}
.multiselect.btn-sm {
  --multiselect-padding-x: 0.75rem;
}

.multiselect-container {
  max-height: var(--multiselect-max-height);
  overflow-y: auto;
  width: 100%;
}

.multiselect-group {
  font-weight: 600;
}

.multiselect-all {
  font-weight: 600;
}

.multiselect-filter {
  position: relative;
  padding: var(--dropdown-padding-y) var(--dropdown-item-padding-x);
  margin-bottom: var(--dropdown-padding-y);
}

.multiselect-group-option-indented {
  padding-left: calc(var(--dropdown-item-padding-x) * 2);
}

.input-group .multiselect-native-select {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex: 1 1 auto;
      flex: 1 1 auto;
}
.input-group .multiselect-native-select:not(:first-child) .multiselect {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group .multiselect-native-select:not(:last-child) .multiselect {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group .multiselect:focus,
.input-group .show > .multiselect {
  z-index: 3;
}

/* ------------------------------------------------------------------------------
*
*  # Autocomplete
*
*  Styles for autocomplete.min.js - input suggestion engine
*
* ---------------------------------------------------------------------------- */
.autoComplete_wrapper {
  --ac-bg: var(--white);
  --ac-border-width: var(--border-width);
  --ac-border-color: var(--border-color-translucent);
  --ac-border-radius: var(--border-radius);
  --ac-box-shadow: var(--box-shadow-lg);
  --ac-scrollable-max-height: 17rem;
  --ac-min-width: 13.75rem;
  --ac-spacer: 0.125rem;
  --ac-padding-y: 0.625rem;
  --ac-item-padding-y: calc(var(--spacer) * 0.4);
  --ac-item-padding-x: var(--spacer);
  --ac-item-color: var(--body-color);
  --ac-item-hover-color: var(--body-color);
  --ac-item-hover-bg: var(--gray-200);
  --ac-item-active-color: var(--gray-900);
  --ac-item-active-bg: var(--gray-300);
}
.autoComplete_wrapper[data-color-theme=dark], [data-color-theme=dark] .autoComplete_wrapper:not([data-color-theme]), html[data-color-theme=dark] .autoComplete_wrapper {
  color-scheme: dark;
  --ac-bg: #32333a;
  --ac-border-color: rgba(var(--black-rgb), 0.25);
}

.autoComplete_wrapper {
  position: relative;
}
.autoComplete_wrapper > ul {
  position: absolute;
  max-height: var(--ac-scrollable-max-height);
  overflow-y: auto;
  left: 0;
  right: 0;
  margin-top: var(--ac-spacer);
  min-width: var(--ac-min-width);
  padding: var(--ac-padding-y) 0;
  z-index: 1;
  list-style: none;
  background-color: var(--ac-bg);
  border: var(--ac-border-width) solid var(--ac-border-color);
  outline: none;
  border-radius: var(--ac-border-radius);
  box-shadow: var(--ac-box-shadow);
  transition: color ease-in-out var(--transition-base-timer), background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .autoComplete_wrapper > ul {
    transition: none;
  }
}
.autoComplete_wrapper > ul[hidden], .autoComplete_wrapper > ul:empty {
  display: block;
  opacity: 0;
  -webkit-transform: scale(0);
          transform: scale(0);
}
.autoComplete_wrapper > ul > li {
  padding: var(--ac-item-padding-y) var(--ac-item-padding-x);
  white-space: nowrap;
  cursor: default;
  color: var(--ac-item-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color ease-in-out var(--transition-base-timer), background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .autoComplete_wrapper > ul > li {
    transition: none;
  }
}
.autoComplete_wrapper > ul > li:hover {
  color: var(--ac-item-hover-color);
  background-color: var(--ac-item-hover-bg);
}
.autoComplete_wrapper > ul > li:active, .autoComplete_wrapper > ul > li[aria-selected=true] {
  color: var(--ac-item-active-color);
  background-color: var(--ac-item-active-bg);
}

/* ------------------------------------------------------------------------------
*
*  # Form Validation
*
*  Styles for validate.min.js - jQuery plugin for simple clientside form validation
*
* ---------------------------------------------------------------------------- */
.validation-invalid-label,
.validation-valid-label {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--danger);
  position: relative;
  padding-left: calc(var(--spacer-2) + var(--icon-font-size));
}

.validation-valid-label {
  color: var(--success);
}

.validation-invalid-label:before,
.validation-valid-label:before {
  font-family: var(--icon-font-family);
  font-size: var(--icon-font-size);
  position: absolute;
  top: calc((var(--body-line-height-computed) - var(--icon-font-size)) * 0.5);
  left: 0;
  display: inline-block;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.validation-invalid-label:empty,
.validation-valid-label:empty {
  display: none;
}

.validation-invalid-label:before {
  content: "\f62d";
}

.validation-valid-label:before {
  content: "\f33f";
}

/* ------------------------------------------------------------------------------
*
*  # Tokenfield component
*
*  Styles for tokenfield.min.js - Input field with tagging/token/chip capabilities
*
* ---------------------------------------------------------------------------- */
.tokenfield {
  --tf-bg: var(--white);
  --tf-disabled-bg: var(--gray-100);
  --tf-disabled-border-color: var(--gray-400);
  --tf-color: var(--body-color);
  --tf-border-width: var(--border-width);
  --tf-border-color: var(--gray-400);
  --tf-border-radius: var(--border-radius);
  --tf-placeholder-color: var(--gray-600);
  --tf-focus-border-color: var(--component-active-bg);
  --tf-focus-box-shadow: var(--focus-ring-box-shadow);
  --tf-input-padding-y: 0.5rem;
  --tf-input-padding-x: 0.875rem;
  --tf-input-font-size: var(--body-font-size);
  --tf-input-line-height: var(--body-line-height);
  --tf-menu-bg: var(--white);
  --tf-menu-max-height: 17rem;
  --tf-menu-spacer: 0.125rem;
  --tf-menu-padding-y: 0.625rem;
  --tf-menu-padding-x: 0;
  --tf-menu-border-width: var(--border-width);
  --tf-menu-border-color: var(--border-color-translucent);
  --tf-menu-border-radius: var(--border-radius);
  --tf-menu-box-shadow: var(--box-shadow-lg);
  --tf-menu-item-padding-y: calc(var(--spacer) * 0.4);
  --tf-menu-item-padding-x: var(--spacer);
  --tf-menu-link-color: var(--body-color);
  --tf-menu-link-hover-color: var(--body-color);
  --tf-menu-link-hover-bg: var(--gray-200);
  --tf-menu-link-active-color: var(--gray-900);
  --tf-menu-link-active-bg: var(--gray-300);
  --tf-tag-color: var(--body-color);
  --tf-tag-bg: var(--gray-300);
  --tf-tag-hover-color: var(--component-active-color);
  --tf-tag-hover-bg: var(--component-active-bg);
}
.tokenfield[data-color-theme=dark], [data-color-theme=dark] .tokenfield:not([data-color-theme]), html[data-color-theme=dark] .tokenfield {
  color-scheme: dark;
  --tf-bg: #2c2d33;
  --tf-focus-bg: #2c2d33;
  --tf-menu-bg: #32333a;
  --tf-menu-border-color: rgba(var(--black-rgb), 0.25);
}

.tokenfield {
  position: relative;
}
.tokenfield:before, .tokenfield:after {
  content: " ";
  display: table;
}
.tokenfield:after {
  clear: both;
}

.tokenfield-mode-tokens {
  display: block;
  width: 100%;
  padding: calc(var(--tf-input-padding-y) * 0.5);
  padding-bottom: 0;
  font-size: var(--tf-input-font-size);
  line-height: var(--tf-input-line-height);
  color: var(--tf-color);
  background-color: var(--tf-bg);
  background-clip: padding-box;
  border: var(--tf-border-width) solid var(--tf-border-color);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: var(--tf-border-radius);
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .tokenfield-mode-tokens {
    transition: none;
  }
}
.tokenfield-mode-tokens.focused {
  border-color: var(--tf-focus-border-color);
  outline: 0;
  box-shadow: var(--tf-focus-box-shadow);
}
.tokenfield-mode-tokens::-webkit-input-placeholder {
  color: var(--tf-placeholder-color);
  opacity: 1;
}
.tokenfield-mode-tokens::-moz-placeholder {
  color: var(--tf-placeholder-color);
  opacity: 1;
}
.tokenfield-mode-tokens:-ms-input-placeholder {
  color: var(--tf-placeholder-color);
  opacity: 1;
}
.tokenfield-mode-tokens::-ms-input-placeholder {
  color: var(--tf-placeholder-color);
  opacity: 1;
}
.tokenfield-mode-tokens::placeholder {
  color: var(--tf-placeholder-color);
  opacity: 1;
}
.tokenfield-mode-tokens[disabled], .tokenfield-mode-tokens[readonly], fieldset[disabled] .tokenfield-mode-tokens {
  background-color: var(--tf-disabled-bg);
  border-color: var(--tf-disabled-border-color);
  opacity: 1;
}
.input-group > .tokenfield-mode-tokens {
  -ms-flex: 1 1 auto;
      flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .tokenfield-mode-tokens.focused {
  z-index: 3;
}

.tokenfield-set > ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.tokenfield-set-item {
  float: left;
  margin-right: calc(var(--tf-input-padding-y) * 0.5);
  margin-bottom: calc(var(--tf-input-padding-y) * 0.5);
  padding: calc(var(--tf-input-padding-y) * 0.5) calc(var(--tf-input-padding-x) - var(--tf-input-padding-y) * 0.5);
  color: var(--tf-tag-color);
  background-color: var(--tf-tag-bg);
  border-radius: calc(var(--tf-border-radius) - var(--tf-border-width));
  transition: color ease-in-out var(--transition-base-timer), background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .tokenfield-set-item {
    transition: none;
  }
}
.tokenfield-set-item:hover {
  color: var(--tf-tag-hover-color);
  background-color: var(--tf-tag-hover-bg);
}
.tokenfield-set-item .item-remove {
  display: inline-block;
  margin-left: calc(var(--tf-input-padding-y) * 0.5);
  color: inherit;
  cursor: pointer;
  opacity: 0.75;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .tokenfield-set-item .item-remove {
    transition: none;
  }
}
.tokenfield-set-item .item-remove:hover {
  opacity: 1;
}

.tokenfield-input {
  padding: calc(var(--tf-input-padding-y) * 0.5) calc(var(--tf-input-padding-x) - var(--tf-input-padding-y) * 0.5);
  margin-bottom: calc(var(--tf-input-padding-y) * 0.5);
  color: var(--tf-color);
  background-color: transparent;
  border: 0;
  outline: 0;
  float: left;
}
.tokenfield-input::-webkit-input-placeholder {
  color: var(--tf-placeholder-color);
  opacity: 1;
}
.tokenfield-input::-moz-placeholder {
  color: var(--tf-placeholder-color);
  opacity: 1;
}
.tokenfield-input:-ms-input-placeholder {
  color: var(--tf-placeholder-color);
  opacity: 1;
}
.tokenfield-input::-ms-input-placeholder {
  color: var(--tf-placeholder-color);
  opacity: 1;
}
.tokenfield-input::placeholder {
  color: var(--tf-placeholder-color);
  opacity: 1;
}

.tokenfield-suggest {
  position: absolute;
  left: calc(var(--tf-menu-border-width) * -1);
  top: 100%;
  margin-top: var(--tf-menu-spacer);
  width: 100%;
  z-index: 10;
  overflow: auto;
  background-color: var(--tf-menu-bg);
  border: var(--tf-menu-border-width) solid var(--tf-menu-border-color);
  box-sizing: content-box;
  border-radius: var(--tf-menu-border-radius);
  box-shadow: var(--tf-menu-box-shadow);
}

.tokenfield-suggest-list {
  margin: 0;
  padding: var(--tf-menu-padding-y) var(--tf-menu-padding-x);
  list-style: none;
  background-clip: padding-box;
  z-index: 1000;
  max-height: var(--tf-menu-max-height);
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.tokenfield-suggest-item {
  padding: var(--tf-menu-item-padding-y) var(--tf-menu-item-padding-x);
  color: var(--tf-menu-link-color);
  cursor: pointer;
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .tokenfield-suggest-item {
    transition: none;
  }
}
.tokenfield-suggest-item.selected {
  color: var(--tf-menu-link-hover-color);
  background-color: var(--tf-menu-link-hover-bg);
}
.tokenfield-suggest-item:active {
  color: var(--tf-menu-link-active-color);
  background-color: var(--tf-menu-link-active-bg);
}

/* ------------------------------------------------------------------------------
*
*  # Dual listbox
*
*  Styles for dual-listbox.min.js - A responsive dual listbox widget
*
* ---------------------------------------------------------------------------- */
.dual-listbox {
  --dlb-height: 18.75rem;
  --dlb-input-padding-y: 0.5rem;
  --dlb-input-padding-x: 0.875rem;
  --dlb-input-bg: var(--white);
  --dlb-input-color: var(--body-color);
  --dlb-input-placeholder-color: var(--gray-600);
  --dlb-input-border-width: var(--border-width);
  --dlb-input-border-color: var(--gray-400);
  --dlb-input-border-radius: var(--border-radius);
  --dlb-input-box-shadow: 0 0 0 0 transparent;
  --dlb-input-focus-bg: var(--white);
  --dlb-input-focus-border-color: var(--component-active-bg);
  --dlb-input-focus-box-shadow: var(--focus-ring-box-shadow);
  --dlb-btn-padding-x: 0.875rem;
  --dlb-btn-padding-y: 0.5rem;
  --dlb-btn-color: var(--body-color);
  --dlb-btn-bg: var(--gray-200);
  --dlb-btn-border-width: var(--border-width);
  --dlb-btn-border-color: var(--gray-400);
  --dlb-btn-hover-color: var(--body-color);
  --dlb-btn-hover-bg: var(--gray-300);
  --dlb-btn-hover-border-color: var(--gray-500);
  --dlb-btn-active-color: var(--body-color);
  --dlb-btn-active-bg: var(--gray-400);
  --dlb-btn-active-border-color: var(--gray-600);
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
}
.dual-listbox[data-color-theme=dark], [data-color-theme=dark] .dual-listbox:not([data-color-theme]), html[data-color-theme=dark] .dual-listbox {
  color-scheme: dark;
  --dlb-input-bg: #2c2d33;
  --dlb-input-focus-bg: #2c2d33;
}
.dual-listbox .dual-listbox__container {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.dual-listbox .dual-listbox__container > div:not(.dual-listbox__buttons) {
  -ms-flex: 1;
      flex: 1;
}
.dual-listbox .dual-listbox__container .dual-listbox__buttons,
.dual-listbox .dual-listbox__container .dual-listbox__available {
  margin-bottom: var(--spacer);
}
.dual-listbox .dual-listbox__container > * {
  width: 100%;
}
@media (min-width: 768px) {
  .dual-listbox .dual-listbox__container {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .dual-listbox .dual-listbox__container > * {
    width: auto;
  }
  .dual-listbox .dual-listbox__container > *:not(:first-child) {
    margin-left: var(--spacer);
  }
  .dual-listbox .dual-listbox__container .dual-listbox__buttons,
.dual-listbox .dual-listbox__container .dual-listbox__available {
    margin-bottom: 0;
  }
}
.sidebar .dual-listbox .dual-listbox__container {
  -ms-flex-direction: column;
      flex-direction: column;
}
.sidebar .dual-listbox .dual-listbox__container > * {
  margin-left: 0;
  width: 100%;
}
.sidebar .dual-listbox .dual-listbox__container .dual-listbox__buttons,
.sidebar .dual-listbox .dual-listbox__container .dual-listbox__available {
  margin-bottom: var(--spacer);
}
.dual-listbox .dual-listbox__search {
  display: block;
  width: 100%;
  padding: var(--dlb-input-padding-y) var(--dlb-input-padding-x);
  color: var(--dlb-input-color);
  background-color: var(--dlb-input-bg);
  background-clip: padding-box;
  border: var(--dlb-input-border-width) solid var(--dlb-input-border-color);
  margin-bottom: var(--spacer);
  border-radius: var(--dlb-input-border-radius);
  box-shadow: var(--dlb-input-box-shadow);
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .dual-listbox .dual-listbox__search {
    transition: none;
  }
}
.dual-listbox .dual-listbox__search:focus {
  background-color: var(--dlb-input-focus-bg);
  border-color: var(--dlb-input-focus-border-color);
  outline: 0;
  box-shadow: 0 0 0 0 transparent, var(--dlb-input-focus-box-shadow);
}
.dual-listbox .dual-listbox__search::-webkit-input-placeholder {
  color: var(--dlb-input-placeholder-color);
  opacity: 1;
}
.dual-listbox .dual-listbox__search::-moz-placeholder {
  color: var(--dlb-input-placeholder-color);
  opacity: 1;
}
.dual-listbox .dual-listbox__search:-ms-input-placeholder {
  color: var(--dlb-input-placeholder-color);
  opacity: 1;
}
.dual-listbox .dual-listbox__search::-ms-input-placeholder {
  color: var(--dlb-input-placeholder-color);
  opacity: 1;
}
.dual-listbox .dual-listbox__search::placeholder {
  color: var(--dlb-input-placeholder-color);
  opacity: 1;
}
.dual-listbox .dual-listbox__available,
.dual-listbox .dual-listbox__selected {
  border: var(--border-width) solid var(--border-color);
  height: var(--dlb-height);
  overflow-y: auto;
  padding: var(--spacer-2) 0;
  margin-top: 0;
  margin-bottom: 0;
  -webkit-margin-before: 0;
  border-bottom-right-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}
.dual-listbox .dual-listbox__buttons {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
}
.dual-listbox .dual-listbox__buttons:empty {
  display: none;
}
.dual-listbox .dual-listbox__button {
  display: inline-block;
  padding: var(--dlb-btn-padding-y) var(--dlb-btn-padding-x);
  color: var(--dlb-btn-color);
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border: var(--dlb-btn-border-width) solid var(--dlb-btn-border-color);
  border-radius: var(--border-radius);
  background-color: var(--dlb-btn-bg);
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .dual-listbox .dual-listbox__button {
    transition: none;
  }
}
.dual-listbox .dual-listbox__button:not(:last-child) {
  margin-bottom: var(--spacer-2);
}
.dual-listbox .dual-listbox__button:hover {
  color: var(--dlb-btn-hover-color);
  background-color: var(--dlb-btn-hover-bg);
  border-color: var(--dlb-btn-hover-border-color);
}
.dual-listbox .dual-listbox__button:focus {
  color: var(--dlb-btn-hover-color);
  background-color: var(--dlb-btn-hover-bg);
  border-color: var(--dlb-btn-hover-border-color);
  outline: 0;
}
.dual-listbox .dual-listbox__button:active {
  color: var(--dlb-btn-active-color);
  background-color: var(--dlb-btn-active-bg);
  border-color: var(--dlb-btn-active-border-color);
  box-shadow: var(--dlb-btn-active-shadow);
}
.dual-listbox .dual-listbox__title {
  font-weight: 600;
  background-color: var(--light);
  padding: var(--spacer-2) var(--spacer);
  border-left: var(--border-width) solid var(--border-color);
  border-right: var(--border-width) solid var(--border-color);
  border-top: var(--border-width) solid var(--border-color);
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}
.dual-listbox .dual-listbox__item {
  display: block;
  padding: var(--spacer-1) var(--spacer);
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  transition: background-color ease-in-out var(--transition-base-timer), color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .dual-listbox .dual-listbox__item {
    transition: none;
  }
}
.dual-listbox .dual-listbox__item:hover, .dual-listbox .dual-listbox__item:focus {
  background-color: var(--gray-200);
}
.dual-listbox .dual-listbox__item:active {
  background-color: var(--gray-300);
}
.dual-listbox .dual-listbox__item.dual-listbox__item--selected {
  color: var(--component-active-color);
  background-color: var(--component-active-bg);
}

/* ------------------------------------------------------------------------------
*
*  # Steps wizard
*
*  Styles for steps.min.js - An all-in-one wizard plugin that is extremely flexible, compact and feature-rich
*
* ---------------------------------------------------------------------------- */
.wizard {
  --wizard-step-line-size: 2px;
  --wizard-step-line-color: var(--gray-400);
  --wizard-step-inactive-color: var(--gray-600);
  --wizard-step-number-size: 2.375rem;
  --wizard-step-number-bg: var(--white);
  --wizard-step-number-color: var(--gray-600);
  --wizard-step-number-active-bg: var(--component-active-bg);
  --wizard-step-number-active-color: var(--white);
  --wizard-step-number-error-bg: var(--danger);
  --wizard-step-number-error-color: var(--white);
}
.wizard[data-color-theme=dark], [data-color-theme=dark] .wizard:not([data-color-theme]), html[data-color-theme=dark] .wizard {
  color-scheme: dark;
  --wizard-step-number-bg: #2c2d33;
}
.wizard > .steps .current-info,
.wizard > .content > .title {
  display: none;
}
.wizard > .content {
  position: relative;
  width: auto;
  padding: 0;
}
.wizard > .content > .body {
  padding: 0 var(--spacer);
}
.wizard > .content > iframe {
  border: 0;
  width: 100%;
  height: 100%;
}
.wizard > .steps {
  position: relative;
  display: block;
  width: 100%;
}
.wizard > .steps > ul {
  display: table;
  width: 100%;
  table-layout: fixed;
  margin: 0;
  padding: 0;
  list-style: none;
}
.wizard > .steps > ul > li {
  display: table-cell;
  width: auto;
  vertical-align: top;
  text-align: center;
  position: relative;
}
.wizard > .steps > ul > li a {
  position: relative;
  padding-top: calc(var(--wizard-step-number-size) + var(--spacer) * 0.5);
  margin-top: var(--spacer);
  margin-bottom: var(--spacer);
  display: block;
  outline: 0;
  color: var(--wizard-step-inactive-color);
}
.wizard > .steps > ul > li:before, .wizard > .steps > ul > li:after {
  content: "";
  display: block;
  position: absolute;
  top: calc(var(--wizard-step-number-size) + var(--spacer) - var(--wizard-step-number-size) * 0.5 - var(--wizard-step-line-size) * 0.5);
  width: 50%;
  height: var(--wizard-step-line-size);
  background-color: var(--wizard-step-number-active-bg);
  z-index: 9;
  transition: background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .wizard > .steps > ul > li:before, .wizard > .steps > ul > li:after {
    transition: none;
  }
}
.wizard > .steps > ul > li:before {
  left: 0;
}
.wizard > .steps > ul > li:after {
  right: 0;
}
.wizard > .steps > ul > li:first-child:before, .wizard > .steps > ul > li:last-child:after {
  content: none;
}
.wizard > .steps > ul > li .number {
  background-color: var(--wizard-step-number-bg);
  color: var(--wizard-step-number-color);
  display: inline-block;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: calc(var(--wizard-step-number-size) * 0.5 * -1);
  border: var(--wizard-step-line-size) solid var(--wizard-step-line-color);
  z-index: 10;
  line-height: calc(var(--wizard-step-number-size) - var(--wizard-step-line-size) * 2);
  text-align: center;
  width: var(--wizard-step-number-size);
  height: var(--wizard-step-number-size);
  border-radius: var(--border-radius-pill);
}
.wizard > .steps > ul > li .number:after {
  font-family: var(--icon-font-family);
  display: inline-block;
  font-size: var(--icon-font-size);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: color ease-in-out var(--transition-base-timer), background-color ease-in-out var(--transition-base-timer), border-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .wizard > .steps > ul > li .number:after {
    transition: none;
  }
}
.wizard > .steps > ul > li.current:after,
.wizard > .steps > ul > li.current ~ li:before,
.wizard > .steps > ul > li.current ~ li:after {
  background-color: var(--wizard-step-line-color);
}
.wizard > .steps > ul > li.current > a {
  color: var(--body-color);
  cursor: default;
}
.wizard > .steps > ul > li.current .number {
  border-color: var(--wizard-step-number-active-bg);
  color: var(--wizard-step-number-active-bg);
  font-size: 0;
}
.wizard > .steps > ul > li.current .number:after {
  content: "\f510";
}
.wizard > .steps > ul > li.done a, .wizard > .steps > ul > li.done a:hover, .wizard > .steps > ul > li.done a:focus {
  color: var(--body-color);
}
.wizard > .steps > ul > li.done .number {
  background-color: var(--wizard-step-number-active-bg);
  color: var(--wizard-step-number-active-color);
  border-color: var(--wizard-step-number-active-bg);
  font-size: 0;
}
.wizard > .steps > ul > li.done .number:after {
  content: "\f33e";
}
.wizard > .steps > ul > li.error .number {
  background-color: var(--wizard-step-number-error-bg);
  color: var(--wizard-step-number-error-color);
  border-color: var(--wizard-step-number-error-bg);
}
.wizard > .steps > ul > li.error .number:after {
  content: "\f642";
}
@media (max-width: 767.98px) {
  .wizard > .steps > ul {
    margin-bottom: var(--spacer);
  }
  .wizard > .steps > ul > li {
    display: block;
    float: left;
    width: 50%;
  }
  .wizard > .steps > ul > li > a {
    margin-bottom: 0;
  }
  .wizard > .steps > ul > li:first-child:before, .wizard > .steps > ul > li:last-child:after {
    content: "";
  }
  .wizard > .steps > ul > li:last-child:after {
    background-color: var(--wizard-step-number-active-bg);
  }
}
@media (max-width: 575.98px) {
  .wizard > .steps > ul > li {
    width: 100%;
  }
  .wizard > .steps > ul > li.current:after {
    background-color: var(--wizard-step-number-active-bg);
  }
}
.wizard > .actions {
  position: relative;
  text-align: right;
  padding: var(--spacer);
  padding-top: 0;
}
.wizard > .actions > ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.wizard > .actions > ul::after {
  display: block;
  clear: both;
  content: "";
}
.wizard > .actions > ul > li {
  display: inline-block;
}
.wizard > .actions > ul > li + li {
  margin-left: var(--spacer);
}

/* ------------------------------------------------------------------------------
*
*  # Quill editor
*
*  Styles for quill.min.js - super simple WYSIWYG Editor
*
* ---------------------------------------------------------------------------- */
.ql-container {
  height: 100%;
  margin: 0;
  position: relative;
}
.ql-container.ql-disabled .ql-tooltip {
  visibility: hidden;
}
.ql-container.ql-disabled .ql-editor ul[data-checked] > li::before {
  pointer-events: none;
}

.ql-clipboard {
  left: -100000px;
  height: 1px;
  overflow-y: hidden;
  position: absolute;
  top: 50%;
}
.ql-clipboard p {
  margin: 0;
  padding: 0;
}

.ql-editor {
  height: 100%;
  min-height: calc(var(--spacer) * 2 + var(--body-line-height-computed) * 3);
  outline: none;
  overflow-y: auto;
  padding: var(--spacer);
  -moz-tab-size: 4;
    -o-tab-size: 4;
       tab-size: 4;
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.ql-editor > * {
  cursor: text;
}
.quill-scrollable-container .ql-editor {
  overflow: auto;
  max-height: 20rem;
}
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor p,
.ql-editor ol,
.ql-editor ul,
.ql-editor pre,
.ql-editor blockquote {
  margin: 0;
  padding: 0;
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol,
.ql-editor ul {
  padding-left: 1.5em;
}
.ql-editor ol > li,
.ql-editor ul > li {
  list-style-type: none;
}
.ql-editor ol > li:before,
.ql-editor ul > li:before {
  content: "•";
}
.ql-editor ul[data-checked=true], .ql-editor ul[data-checked=false] {
  pointer-events: none;
}
.ql-editor ul[data-checked=true] > li:before, .ql-editor ul[data-checked=false] > li:before {
  color: rgba(var(--body-color-rgb), 0.75);
  cursor: pointer;
  pointer-events: all;
}
.ql-editor ul[data-checked=true] > li *, .ql-editor ul[data-checked=false] > li * {
  pointer-events: all;
}
.ql-editor ul[data-checked=true] > li:before {
  content: "☑";
}
.ql-editor ul[data-checked=false] > li:before {
  content: "☐";
}
.ql-editor li:before {
  display: inline-block;
  white-space: nowrap;
  width: 1.2em;
}
.ql-editor li:not(.ql-direction-rtl)::before {
  margin-left: -1.5em;
  margin-right: 0.5em;
  text-align: right;
}
.ql-editor li.ql-direction-rtl::before {
  margin-left: 0.5em;
  margin-right: -1.5em;
}
.ql-editor ol li:not(.ql-direction-rtl),
.ql-editor ul li:not(.ql-direction-rtl) {
  padding-left: 1.5em;
}
.ql-editor ol li.ql-direction-rtl,
.ql-editor ul li.ql-direction-rtl {
  padding-right: 1.5em;
}
.ql-editor ol li {
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
  counter-increment: list-0;
}
.ql-editor ol li:before {
  content: counter(list-0, decimal) ". ";
}
.ql-editor ol li.ql-indent-1 {
  counter-increment: list-1;
  counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-1:before {
  content: counter(list-1, lower-alpha) ". ";
}
.ql-editor ol li.ql-indent-2 {
  counter-increment: list-2;
  counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-2:before {
  content: counter(list-2, lower-roman) ". ";
}
.ql-editor ol li.ql-indent-3 {
  counter-increment: list-3;
  counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-3:before {
  content: counter(list-3, decimal) ". ";
}
.ql-editor ol li.ql-indent-4 {
  counter-increment: list-4;
  counter-reset: list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-4:before {
  content: counter(list-4, lower-alpha) ". ";
}
.ql-editor ol li.ql-indent-5 {
  counter-increment: list-5;
  counter-reset: list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-5:before {
  content: counter(list-5, lower-roman) ". ";
}
.ql-editor ol li.ql-indent-6 {
  counter-increment: list-6;
  counter-reset: list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-6:before {
  content: counter(list-6, decimal) ". ";
}
.ql-editor ol li.ql-indent-7 {
  counter-increment: list-7;
  counter-reset: list-8 list-9;
}
.ql-editor ol li.ql-indent-7:before {
  content: counter(list-7, lower-alpha) ". ";
}
.ql-editor ol li.ql-indent-8 {
  counter-increment: list-8;
  counter-reset: list-9;
}
.ql-editor ol li.ql-indent-8:before {
  content: counter(list-8, lower-roman) ". ";
}
.ql-editor ol li.ql-indent-9 {
  counter-increment: list-9;
}
.ql-editor ol li.ql-indent-9:before {
  content: counter(list-9, decimal) ". ";
}
.ql-editor .ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 3em;
}
.ql-editor .ql-indent-1.ql-direction-rtl .ql-align-right {
  padding-right: 3em;
}
.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 4.5em;
}
.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 4.5em;
}
.ql-editor .ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 6em;
}
.ql-editor .ql-indent-2.ql-direction-rtl .ql-align-right {
  padding-right: 6em;
}
.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 7.5em;
}
.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 7.5em;
}
.ql-editor .ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 9em;
}
.ql-editor .ql-indent-3.ql-direction-rtl .ql-align-right {
  padding-right: 9em;
}
.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 10.5em;
}
.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 10.5em;
}
.ql-editor .ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 12em;
}
.ql-editor .ql-indent-4.ql-direction-rtl .ql-align-right {
  padding-right: 12em;
}
.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 13.5em;
}
.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 13.5em;
}
.ql-editor .ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 15em;
}
.ql-editor .ql-indent-5.ql-direction-rtl .ql-align-right {
  padding-right: 15em;
}
.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 16.5em;
}
.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 16.5em;
}
.ql-editor .ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 18em;
}
.ql-editor .ql-indent-6.ql-direction-rtl .ql-align-right {
  padding-right: 18em;
}
.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 19.5em;
}
.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 19.5em;
}
.ql-editor .ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 21em;
}
.ql-editor .ql-indent-7.ql-direction-rtl .ql-align-right {
  padding-right: 21em;
}
.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 22.5em;
}
.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 22.5em;
}
.ql-editor .ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 24em;
}
.ql-editor .ql-indent-8.ql-direction-rtl .ql-align-right {
  padding-right: 24em;
}
.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 25.5em;
}
.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 25.5em;
}
.ql-editor .ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 27em;
}
.ql-editor .ql-indent-9.ql-direction-rtl .ql-align-right {
  padding-right: 27em;
}
.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 28.5em;
}
.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 28.5em;
}
.ql-editor .ql-video {
  display: block;
  max-width: 100%;
}
.ql-editor .ql-video.ql-align-center {
  margin: 0 auto;
}
.ql-editor .ql-video.ql-align-right {
  margin: 0 0 0 auto;
}
.ql-editor img {
  max-width: 100%;
}
.ql-editor .ql-bg-black {
  background-color: var(--black);
}
.ql-editor .ql-bg-red {
  background-color: var(--danger);
}
.ql-editor .ql-bg-orange {
  background-color: var(--warning);
}
.ql-editor .ql-bg-yellow {
  background-color: var(--yellow);
}
.ql-editor .ql-bg-green {
  background-color: var(--success);
}
.ql-editor .ql-bg-blue {
  background-color: var(--primary);
}
.ql-editor .ql-bg-purple {
  background-color: var(--purple);
}
.ql-editor .ql-color-black {
  color: var(--black);
}
.ql-editor .ql-color-red {
  color: var(--danger);
}
.ql-editor .ql-color-orange {
  color: var(--warning);
}
.ql-editor .ql-color-yellow {
  color: var(--yellow);
}
.ql-editor .ql-color-green {
  color: var(--success);
}
.ql-editor .ql-color-blue {
  color: var(--primary);
}
.ql-editor .ql-color-purple {
  color: var(--purple);
}
.ql-editor .ql-font-serif {
  font-family: var(--font-sans-serif);
}
.ql-editor .ql-font-monospace {
  font-family: var(--font-monospace);
}
.ql-editor .ql-size-small {
  font-size: 0.75em;
}
.ql-editor .ql-size-large {
  font-size: 1.5em;
}
.ql-editor .ql-size-huge {
  font-size: 2.5em;
}
.ql-editor .ql-direction-rtl {
  direction: rtl;
  text-align: inherit;
}
.ql-editor .ql-align-center {
  text-align: center;
}
.ql-editor .ql-align-justify {
  text-align: justify;
}
.ql-editor .ql-align-right {
  text-align: right;
}
.ql-editor.ql-blank:before {
  color: rgba(var(--body-color-rgb), 0.75);
  content: attr(data-placeholder);
  left: var(--spacer);
  pointer-events: none;
  position: absolute;
  right: var(--spacer);
}

.ql-toolbar {
  padding: var(--spacer-2);
  border-bottom: var(--border-width) solid var(--border-color);
}
.ql-toolbar:after {
  clear: both;
  content: "";
  display: table;
}
.ql-toolbar + .ql-container {
  border-top: 0;
}
.ql-toolbar .ql-formats {
  margin-right: var(--spacer-2);
  padding-right: var(--spacer-2);
  border-right: var(--border-width) solid var(--border-color);
}
.ql-toolbar button {
  background: none;
  border: none;
  cursor: pointer;
  display: inline-block;
  float: left;
  height: 1.5rem;
  padding: 0.1875rem 0.3125rem;
  width: 1.75rem;
}
.ql-toolbar button:active:hover {
  outline: 0;
}
.ql-toolbar button svg {
  float: left;
  height: 100%;
}
.ql-toolbar input.ql-image[type=file] {
  display: none;
}
.ql-toolbar .ql-fill {
  transition: fill ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .ql-toolbar .ql-fill {
    transition: none;
  }
}
.ql-toolbar .ql-stroke {
  transition: stroke ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .ql-toolbar .ql-stroke {
    transition: none;
  }
}
.ql-toolbar button:hover,
.ql-toolbar button:focus,
.ql-toolbar button.ql-active,
.ql-toolbar .ql-picker-label:hover,
.ql-toolbar .ql-picker-label.ql-active,
.ql-toolbar .ql-picker-item:hover,
.ql-toolbar .ql-picker-item.ql-selected {
  color: var(--link-color);
}
.ql-toolbar button:hover .ql-fill,
.ql-toolbar button:focus .ql-fill,
.ql-toolbar button.ql-active .ql-fill,
.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: var(--link-color);
}
.ql-toolbar button:hover .ql-stroke,
.ql-toolbar button:focus .ql-stroke,
.ql-toolbar button.ql-active .ql-stroke,
.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-toolbar button:hover .ql-stroke-miter,
.ql-toolbar button:focus .ql-stroke-miter,
.ql-toolbar button.ql-active .ql-stroke-miter,
.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
  stroke: var(--link-color);
}
@media (pointer: coarse) {
  .ql-toolbar button:hover:not(.ql-active) {
    color: var(--body-color);
  }
  .ql-toolbar button:hover:not(.ql-active) .ql-fill,
.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill {
    fill: var(--body-color);
  }
  .ql-toolbar button:hover:not(.ql-active) .ql-stroke,
.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter {
    stroke: var(--body-color);
  }
}

.ql-tooltip {
  --ql-tooltip-bg: var(--white);
  --ql-tooltip-color: var(--body-color);
  --ql-tooltip-input-focus-border-color: var(--component-active-bg);
  --ql-tooltip-input-focus-box-shadow: var(--focus-ring-box-shadow);
  position: absolute;
  background-color: var(--ql-tooltip-bg);
  border: var(--border-width) solid var(--border-color-translucent);
  color: var(--ql-tooltip-color);
  padding: var(--spacer-2) calc(var(--spacer-2) * 1.5);
  white-space: nowrap;
  z-index: 1080;
  -webkit-transform: translateY(var(--spacer-2));
          transform: translateY(var(--spacer-2));
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}
.ql-tooltip[data-color-theme=dark], [data-color-theme=dark] .ql-tooltip:not([data-color-theme]), html[data-color-theme=dark] .ql-tooltip {
  color-scheme: dark;
  --ql-tooltip-bg: #32333a;
}
.ql-tooltip:before {
  content: "Visit URL:";
  margin-right: var(--spacer-2);
}
.ql-tooltip a {
  cursor: pointer;
}
.ql-tooltip a.ql-preview {
  display: inline-block;
  max-width: 12.5rem;
  overflow-x: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
}
.ql-tooltip a.ql-action::after {
  border-right: var(--border-width) solid var(--border-color);
  content: "Edit";
  margin-left: var(--spacer);
  padding-right: var(--spacer-2);
}
.ql-tooltip a.ql-remove::before {
  content: "Remove";
  margin-left: var(--spacer-2);
}
.ql-tooltip.ql-flip {
  -webkit-transform: translateY(calc(var(--spacer-2) * -1));
          transform: translateY(calc(var(--spacer-2) * -1));
}
.ql-tooltip input[type=text] {
  display: none;
  border: var(--border-width) solid var(--border-color);
  margin: 0;
  padding: var(--spacer-1) var(--spacer-2);
  width: 10.625rem;
  border-radius: var(--border-radius);
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .ql-tooltip input[type=text] {
    transition: none;
  }
}
.ql-tooltip input[type=text]:focus {
  border-color: var(--ql-tooltip-input-focus-border-color);
  outline: 0;
  box-shadow: 0 0 0 0 transparent, var(--ql-tooltip-input-focus-box-shadow);
}
.ql-tooltip.ql-editing a.ql-preview,
.ql-tooltip.ql-editing a.ql-remove {
  display: none;
}
.ql-tooltip.ql-editing input[type=text] {
  display: inline-block;
}
.ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0;
  content: "Save";
  padding-right: 0;
}
.ql-tooltip[data-mode=link]::before {
  content: "Enter link:";
}
.ql-tooltip[data-mode=formula]::before {
  content: "Enter formula:";
}
.ql-tooltip[data-mode=video]::before {
  content: "Enter video:";
}

.ql-formats {
  display: inline-block;
  vertical-align: middle;
}
.ql-formats:after {
  clear: both;
  content: "";
  display: table;
}

.ql-stroke {
  fill: none;
  stroke: var(--body-color);
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 1.5;
}

.ql-stroke-miter {
  fill: none;
  stroke: var(--body-color);
  stroke-miterlimit: 10;
  stroke-width: 1.5;
}

.ql-fill,
.ql-stroke.ql-fill {
  fill: var(--body-color);
}

.ql-empty {
  fill: none;
}

.ql-even {
  fill-rule: evenodd;
}

.ql-thin,
.ql-stroke.ql-thin {
  stroke-width: 1;
}

.ql-transparent {
  opacity: 0.4;
}

.ql-direction svg:last-child {
  display: none;
}
.ql-direction.ql-active svg:last-child {
  display: inline;
}
.ql-direction.ql-active svg:first-child {
  display: none;
}

.ql-editor blockquote {
  border-left: calc(var(--border-width) * 4) solid var(--border-color);
  margin-bottom: var(--spacer-1);
  margin-top: var(--spacer-1);
  padding-left: var(--spacer);
}
.ql-editor code,
.ql-editor pre {
  background-color: var(--gray-200);
  border-radius: var(--border-radius);
}
.ql-editor pre {
  white-space: pre-wrap;
  padding: var(--spacer);
}
.ql-editor pre.ql-syntax {
  background-color: var(--gray-200);
  color: var(--body-color);
  overflow: visible;
}
.ql-editor code {
  font-size: 0.875em;
}

.ql-picker {
  color: var(--body-color);
  display: inline-block;
  float: left;
  height: 1.5rem;
  position: relative;
  vertical-align: middle;
}

.ql-picker-label {
  cursor: pointer;
  display: inline-block;
  height: 100%;
  border: var(--border-color) solid transparent;
  padding-left: var(--spacer-2);
  padding-right: var(--spacer-1);
  position: relative;
  width: 100%;
  line-height: 1.5rem;
  transition: color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .ql-picker-label {
    transition: none;
  }
}
.ql-picker-label:before {
  display: inline-block;
}

.ql-picker-options {
  --ql-dropdown-bg: var(--white);
  --ql-dropdown-border-color: var(--border-color-translucent);
  --ql-dropdown-box-shadow: var(--box-shadow-lg);
  background-color: var(--ql-dropdown-bg);
  display: none;
  min-width: 100%;
  padding: var(--spacer-2);
  border: var(--border-width) solid var(--ql-dropdown-border-color);
  position: absolute;
  white-space: nowrap;
  border-radius: var(--border-radius);
  box-shadow: var(--ql-dropdown-box-shadow);
}
.ql-picker-options[data-color-theme=dark], [data-color-theme=dark] .ql-picker-options:not([data-color-theme]), html[data-color-theme=dark] .ql-picker-options {
  color-scheme: dark;
  --ql-dropdown-bg: #32333a;
  --ql-dropdown-border-color: rgba(var(--black-rgb), 0.25);
}
.ql-picker-options .ql-picker-item {
  cursor: pointer;
  display: block;
  padding: var(--spacer-1) var(--spacer-2);
  transition: color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .ql-picker-options .ql-picker-item {
    transition: none;
  }
}

.ql-picker.ql-expanded .ql-picker-label {
  color: var(--link-color);
  z-index: 2;
}
.ql-picker.ql-expanded .ql-picker-label .ql-fill {
  fill: var(--link-color);
}
.ql-picker.ql-expanded .ql-picker-label .ql-stroke {
  stroke: var(--link-color);
}
.ql-picker.ql-expanded .ql-picker-options {
  display: block;
  margin-top: calc(var(--spacer-2) - var(--border-width));
  top: 100%;
  z-index: 1;
}

.ql-color-picker,
.ql-icon-picker {
  width: 1.75rem;
}
.ql-color-picker .ql-picker-label,
.ql-icon-picker .ql-picker-label {
  padding: 0.125rem 0.25rem;
}
.ql-color-picker .ql-picker-label svg,
.ql-icon-picker .ql-picker-label svg {
  right: 0.25rem;
  display: block;
}

.ql-icon-picker .ql-picker-options {
  padding: 0.25rem 0;
}
.ql-icon-picker .ql-picker-item {
  padding: 0.25rem;
}
.ql-icon-picker .ql-picker-item svg {
  display: block;
}

.ql-color-picker .ql-picker-options {
  padding: 0.1875rem 0.3125rem;
  width: 9.5rem;
}
.ql-color-picker .ql-picker-item {
  border: var(--border-width) solid transparent;
  float: left;
  height: 1rem;
  margin: 0.125rem;
  padding: 0;
  width: 1rem;
}
.ql-color-picker.ql-background .ql-picker-item {
  background-color: #fff;
}
.ql-color-picker.ql-color .ql-picker-item {
  background-color: #000;
}

.ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  position: absolute;
  right: 0;
  top: 50%;
  width: 1.125rem;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""])::before, .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""])::before, .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""])::before, .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""])::before, .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""])::before, .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""])::before {
  content: attr(data-label);
}
.ql-picker.ql-header {
  width: 6.125rem;
}
.ql-picker.ql-header .ql-picker-label::before,
.ql-picker.ql-header .ql-picker-item::before {
  content: "Normal";
}
.ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "Heading 1";
}
.ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "Heading 2";
}
.ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "Heading 3";
}
.ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "Heading 4";
}
.ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "Heading 5";
}
.ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "Heading 6";
}
.ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  font-size: 1.625rem;
}
.ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  font-size: 1.5rem;
}
.ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  font-size: 1.375rem;
}
.ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  font-size: 1.25rem;
}
.ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  font-size: 1.125rem;
}
.ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  font-size: 1rem;
}
.ql-picker.ql-font {
  width: 6.75rem;
}
.ql-picker.ql-font .ql-picker-label::before,
.ql-picker.ql-font .ql-picker-item::before {
  content: "Sans Serif";
}
.ql-picker.ql-font .ql-picker-label[data-value=serif]::before,
.ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  content: "Serif";
}
.ql-picker.ql-font .ql-picker-label[data-value=monospace]::before,
.ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  content: "Monospace";
}
.ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  font-family: Georgia, Times New Roman, serif;
}
.ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.ql-picker.ql-size {
  width: 6.125rem;
}
.ql-picker.ql-size .ql-picker-label::before,
.ql-picker.ql-size .ql-picker-item::before {
  content: "Normal";
}
.ql-picker.ql-size .ql-picker-label[data-value=small]::before,
.ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  content: "Small";
}
.ql-picker.ql-size .ql-picker-label[data-value=large]::before,
.ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  content: "Large";
}
.ql-picker.ql-size .ql-picker-label[data-value=huge]::before,
.ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  content: "Huge";
}
.ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  font-size: 0.75rem;
}
.ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  font-size: 1.125rem;
}
.ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  font-size: 2rem;
}

.ql-hidden {
  display: none;
}

.ql-out-bottom,
.ql-out-top {
  visibility: hidden;
}

/* ------------------------------------------------------------------------------
*
*  # CKEditor editor
*
*  Styles for CKEditor Classic, Document and Balloon
*
* ---------------------------------------------------------------------------- */
.ck {
  --ck-font-size-base: var(--body-font-size);
  --ck-font-face: var(--body-font-family);
  --ck-color-text: var(--body-color);
  --ck-line-height-base: var(--body-line-height);
  --ck-border-radius: var(--border-radius);
  --ck-color-link-default: var(--link-color);
  --ck-color-base-background: var(--white);
  --ck-color-toolbar-background: var(--white);
  --ck-color-toolbar-border: var(--border-color);
  --ck-color-base-border: var(--border-color);
  --ck-focus-ring: var(--border-width) solid transparent;
  --ck-icon-size: 1.25rem;
  --ck-color-button-default-hover-background: var(--gray-300);
  --ck-color-button-default-active-background: var(--gray-400);
  --ck-color-button-default-active-shadow: (0,0,0,0);
  --ck-color-button-save: var(--success);
  --ck-color-button-cancel: var(--danger);
  --ck-color-button-on-background: var(--gray-400);
  --ck-color-button-on-hover-background: var(--gray-400);
  --ck-color-button-on-active-background: var(--gray-400);
  --ck-color-button-on-active-shadow: (0,0,0,0);
  --ck-color-button-on-disabled-background: var(--gray-200);
  --ck-color-list-background: var(--white);
  --ck-color-list-button-hover-background: var(--gray-200);
  --ck-color-list-button-on-background: var(--gray-300);
  --ck-color-list-button-on-background-focus: var(--gray-300);
  --ck-color-list-button-on-text: var(--gray-900);
  --ck-color-dropdown-panel-background: var(--white);
  --ck-color-dropdown-panel-border: var(--border-color-translucent);
  --ck-color-panel-background: var(--white);
  --ck-color-panel-border: var(--border-color-translucent);
  --ck-drop-shadow: var(--box-shadow-lg);
  --ck-color-input-background: var(--white);
  --ck-color-input-border: var(--gray-400);
  --ck-color-input-error-border: var(--danger);
  --ck-color-input-text: var(--body-color);
  --ck-color-input-disabled-background: var(--gray-100);
  --ck-color-input-disabled-border: var(--gray-400);
  --ck-color-input-disabled-text: var(--gray-500);
  --ck-color-labeled-field-label-background: transparent;
  --ck-color-widget-editable-focus-background: var(--gray-300);
  --ck-color-engine-placeholder-text: var(--gray-600);
  --ck-color-image-caption-background: var(--gray-200);
  --ck-color-image-caption-text: var(--body-color);
  --ck-color-table-focused-cell-background: var(--gray-100);
  --ck-inner-shadow: var(--focus-ring-box-shadow);
  --ck-color-widget-blurred-border: var(--border-color);
}
.ck[data-color-theme=dark], [data-color-theme=dark] .ck:not([data-color-theme]), html[data-color-theme=dark] .ck {
  color-scheme: dark;
  --ck-color-base-background: #2c2d33;
  --ck-color-toolbar-background: #2c2d33;
  --ck-color-list-background: #32333a;
  --ck-color-dropdown-panel-background: #32333a;
  --ck-color-tooltip-text: var(--black);
  --ck-color-tooltip-background: var(--white);
  --ck-color-panel-background: #32333a;
  --ck-color-panel-border: rgba(var(--black-rgb), 0.25);
  --ck-color-input-background: #2c2d33;
}

.ck.ck-editor__editable {
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .ck.ck-editor__editable {
    transition: none;
  }
}

.ck-editor .ck-editor__editable {
  max-height: 450px;
}

.ck.ck-editor__editable_inline {
  --ck-spacing-standard: var(--spacer);
  --ck-spacing-large: var(--spacer);
}
.ck.ck-editor__editable_inline:not(.ck-focused) {
  border-color: var(--border-color);
}

.ck.ck-editor__editable:not(.ck-editor__nested-editable).ck-focused {
  --ck-focus-ring: var(--border-width) solid var(--component-active-bg);
  --ck-focus-inner-shadow: var(--focus-ring-box-shadow);
}

.ck.ck-text-alternative-form,
.ck.ck-link-form {
  -ms-flex-align: center;
      align-items: center;
}

.ck.ck-button,
a.ck.ck-button {
  --ck-spacing-tiny: 0.5rem;
  --ck-focus-outer-shadow: none;
  transition: all ease-in-out var(--transition-base-timer) !important;
}
@media (prefers-reduced-motion: reduce) {
  .ck.ck-button,
a.ck.ck-button {
    transition: none;
  }
}
.ck.ck-button:not(.ck-disabled),
a.ck.ck-button:not(.ck-disabled) {
  cursor: pointer;
}

.ck.ck-dropdown .ck-button.ck-dropdown__button:not(.ck-button_with-text) {
  --ck-spacing-small: 0.5rem;
}

body .ck.ck-dropdown__panel {
  background-clip: padding-box;
}
body .ck.ck-dropdown .ck-dropdown__panel .ck-list {
  padding: 0.625rem 0;
}
body .ck.ck-list__item .ck-button {
  --ck-border-radius: 0;
  padding: calc(var(--spacer) * 0.4) var(--spacer);
  border: 0;
}
body .ck.ck-list__item .ck-button .ck-button__label {
  line-height: var(--body-line-height);
}
body .ck.ck-dropdown__panel.ck-dropdown__panel_se .ck-color-table__remove-color {
  border-top-left-radius: 0;
}
body .ck.ck-dropdown__panel.ck-dropdown__panel_sw .ck-color-table__remove-color {
  border-top-right-radius: 0;
}
body .ck-sticky-panel__placeholder {
  display: none !important;
}
body .ck.ck-sticky-panel .ck-sticky-panel__content_sticky {
  width: 100% !important;
  position: static;
  box-shadow: none;
}
body .ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content_sticky .ck-toolbar {
  border-bottom-width: 0;
}
body .ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content_sticky .ck-toolbar,
body .ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content_sticky .ck-toolbar.ck-rounded-corners {
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}
body .ck.ck-splitbutton.ck-splitbutton_open > .ck-button:not(.ck-on):not(.ck-disabled):not(:hover),
body .ck.ck-splitbutton:hover > .ck-button:not(.ck-on):not(.ck-disabled):not(:hover) {
  --ck-color-split-button-hover-background: var(--ck-color-button-default-hover-background);
}
body .ck.ck-splitbutton.ck-splitbutton_open > .ck-splitbutton__arrow:not(.ck-disabled):after,
body .ck.ck-splitbutton:hover > .ck-splitbutton__arrow:not(.ck-disabled):after {
  --ck-color-split-button-hover-border: var(--border-color);
}
body [dir=ltr] .ck.ck-splitbutton.ck-splitbutton_open > .ck-splitbutton__action,
body [dir=ltr] .ck.ck-splitbutton:hover > .ck-splitbutton__action {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
body .ck-content figure, body .ck-content ol, body .ck-content p, body .ck-content ul {
  margin-top: var(--spacer);
  margin-bottom: var(--spacer);
}
body .ck-content .table table,
body .ck-content .table table th,
body .ck-content .table table td {
  border-color: var(--border-color);
}
body .ck-content .table table th,
body .ck-content .table table td {
  padding: 0.75rem 1.25rem;
}
body .ck.ck-balloon-panel {
  box-shadow: var(--box-shadow);
  border: var(--border-width) solid var(--border-color-translucent);
}
body .ck.ck-balloon-panel.ck-toolbar-container:after {
  --ck-color-base-foreground: var(--ck-color-toolbar-background);
}
body .ck.ck-balloon-panel.ck-toolbar-container:before {
  border-bottom-color: var(--border-color-translucent);
}
body .ck.ck-input-text {
  --ck-spacing-extra-tiny: 0.5rem;
  --ck-spacing-medium: 0.875rem;
  border-width: var(--border-width);
  padding-top: calc(0.5rem + var(--body-font-size) * 1.1);
}
body .ck.ck-input-text.ck-input_focused {
  --ck-focus-ring: var(--border-width) solid var(--component-active-bg);
  --ck-focus-outer-shadow: var(--focus-ring-box-shadow);
}
body .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label {
  padding: 0;
  -webkit-transform: translate(calc(0.875rem + var(--border-width)), calc(0.625rem + var(--border-width))) scale(0.75);
          transform: translate(calc(0.875rem + var(--border-width)), calc(0.625rem + var(--border-width))) scale(0.75);
}
body [dir=ltr] .ck.ck-labeled-field-view.ck-disabled.ck-labeled-field-view_empty > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
body [dir=ltr] .ck.ck-labeled-field-view.ck-labeled-field-view_empty:not(.ck-labeled-field-view_focused):not(.ck-labeled-field-view_placeholder) > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label {
  -webkit-transform: translate(0.875rem, calc(var(--body-font-size) * 1.35)) scale(1);
          transform: translate(0.875rem, calc(var(--body-font-size) * 1.35)) scale(1);
}
body [dir=rtl] .ck.ck-labeled-field-view.ck-disabled.ck-labeled-field-view_empty > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
body [dir=rtl] .ck.ck-labeled-field-view.ck-labeled-field-view_empty:not(.ck-labeled-field-view_focused):not(.ck-labeled-field-view_placeholder) > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label {
  -webkit-transform: translate(-0.875rem, calc(var(--body-font-size) * 1.35)) scale(1);
          transform: translate(-0.875rem, calc(var(--body-font-size) * 1.35)) scale(1);
}

.document-editor {
  --ck-doc-container-bg: rgba(var(--black-rgb), .05);
  --ck-doc-content-bg: var(--white);
  --ck-doc-content-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.075), var(--box-shadow);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  max-height: 700px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-flow: column nowrap;
      flex-flow: column nowrap;
}
[data-color-theme=dark] .document-editor {
  --ck-doc-container-bg: rgba(var(--black-rgb), .25);
  --ck-doc-content-bg: #2c2d33;
}

.document-editor-container {
  padding: 1.25rem;
  background-color: var(--ck-doc-container-bg);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  border-bottom-right-radius: calc(var(--border-radius) - 1px);
  border-bottom-left-radius: calc(var(--border-radius) - 1px);
}

.document-editor-toolbar {
  z-index: 1;
  border-bottom: 1px solid var(--border-color);
}
.document-editor-toolbar .ck.ck-toolbar {
  border: 0;
}
.document-editor-toolbar .ck.ck-toolbar.ck-rounded-corners {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.document-editor-container .document-editor-editable.ck-editor__editable {
  width: 15.8cm;
  padding: var(--spacer-4);
  margin: 0 auto;
  background-color: var(--ck-doc-content-bg);
  box-shadow: var(--ck-doc-content-box-shadow);
}
@media (min-width: 768px) {
  .document-editor-container .document-editor-editable.ck-editor__editable {
    width: 90%;
  }
}
@media (min-width: 1200px) {
  .document-editor-container .document-editor-editable.ck-editor__editable {
    width: 60%;
  }
}

/* ------------------------------------------------------------------------------
*
*  # Trumbowyg editor
*
*  Styles for trumbowyg.min.js - a lightweight WYSIWYG editor
*
* ---------------------------------------------------------------------------- */
.trumbowyg-box {
  --tw-min-height: 350px;
  --tw-bg: var(--white);
  --tw-dropdown-bg: var(--white);
  --tw-dropdown-border-width: var(--border-width);
  --tw-dropdown-border-color: var(--border-color-translucent);
  --tw-dropdown-border-radius: var(--border-radius);
  --tw-dropdown-box-shadow: var(--box-shadow-lg);
  --tw-dropdown-link-color: var(--body-color);
  --tw-dropdown-link-hover-color: var(--body-color);
  --tw-dropdown-link-hover-bg: var(--gray-200);
  --tw-dropdown-link-active-color: var(--gray-900);
  --tw-dropdown-link-active-bg: var(--gray-300);
}
.trumbowyg-box[data-color-theme=dark], [data-color-theme=dark] .trumbowyg-box:not([data-color-theme]), html[data-color-theme=dark] .trumbowyg-box {
  color-scheme: dark;
  --tw-bg: #2c2d33;
  --tw-dropdown-bg: #32333a;
  --tw-dropdown-border-color: rgba(var(--black-rgb), 0.25);
}

.trumbowyg-box {
  position: relative;
}
.trumbowyg-box svg {
  fill: var(--body-color);
  display: block;
  width: var(--icon-font-size);
  height: var(--icon-font-size);
}
.trumbowyg-box.trumbowyg-fullscreen {
  background: var(--tw-bg);
  border-width: 0;
}

#trumbowyg-icons {
  overflow: hidden;
  visibility: hidden;
  height: 0;
  width: 0;
}
#trumbowyg-icons svg {
  height: 0;
  width: 0;
}

.trumbowyg-editor {
  position: relative;
}
.trumbowyg-editor[contenteditable=true]:empty:not(:focus)::before {
  content: attr(placeholder);
  color: rgba(var(--body-color-rgb), 0.75);
  pointer-events: none;
}

.trumbowyg-editor,
.trumbowyg-textarea {
  position: relative;
  padding: var(--spacer);
  min-height: var(--tw-min-height);
  width: 100%;
  border: 0;
  color: var(--body-color);
  resize: none;
  background-color: transparent;
  outline: 0;
  overflow: auto;
}

.trumbowyg-box-blur .trumbowyg-editor img,
.trumbowyg-box-blur .trumbowyg-editor hr {
  opacity: 0.2;
}

.trumbowyg-textarea {
  position: relative;
  display: block;
  overflow: auto;
  border: 0;
  white-space: normal;
}

.trumbowyg-box.trumbowyg-editor-visible .trumbowyg-textarea {
  height: 1px !important;
  width: 25%;
  min-height: 0 !important;
  padding: 0 !important;
  background: none;
  opacity: 0 !important;
}
.trumbowyg-box.trumbowyg-editor-hidden .trumbowyg-textarea {
  display: block;
}
.trumbowyg-box.trumbowyg-editor-hidden .trumbowyg-editor {
  display: none;
}
.trumbowyg-box.trumbowyg-disabled .trumbowyg-textarea {
  opacity: 0.65;
  background-color: transparent;
}

.trumbowyg-button-pane {
  width: 100%;
  border-bottom: var(--border-width) solid var(--border-color);
  margin: 0;
  padding-top: var(--spacer-2);
  padding-left: var(--spacer-2);
  padding-right: var(--spacer-2);
  position: relative;
  list-style: none;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.trumbowyg-button-pane .trumbowyg-button-group {
  display: inline-block;
  position: relative;
  margin-bottom: var(--spacer-2);
}
.trumbowyg-button-pane .trumbowyg-button-group + .trumbowyg-button-group:not(.trumbowyg-right) {
  margin-left: var(--spacer-2);
  padding-left: var(--spacer-2);
  border-left: var(--border-width) solid var(--border-color);
}
.trumbowyg-button-pane .trumbowyg-button-group button + button {
  margin-left: var(--spacer-1);
}
.trumbowyg-button-pane .trumbowyg-button-group .trumbowyg-fullscreen-button svg {
  color: transparent;
}
.trumbowyg-button-pane button {
  display: inline-block;
  position: relative;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  background-color: transparent;
  padding: var(--spacer-1);
  border: var(--border-width) solid transparent;
  border-radius: var(--border-radius);
  transition: background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .trumbowyg-button-pane button {
    transition: none;
  }
}
.trumbowyg-button-pane button:hover, .trumbowyg-button-pane button:focus {
  background-color: var(--gray-200);
}
.trumbowyg-button-pane button:active, .trumbowyg-button-pane button.trumbowyg-active {
  background-color: var(--gray-300);
}
.trumbowyg-button-pane.trumbowyg-disable button:not(.trumbowyg-not-disable):not(.trumbowyg-active), .trumbowyg-button-pane.trumbowyg-disable button:not(.trumbowyg-not-disable):not(.trumbowyg-viewHTML-button), .trumbowyg-disabled .trumbowyg-button-pane button:not(.trumbowyg-not-disable):not(.trumbowyg-active), .trumbowyg-disabled .trumbowyg-button-pane button:not(.trumbowyg-not-disable):not(.trumbowyg-viewHTML-button) {
  opacity: 0.65;
  cursor: default;
  pointer-events: none;
}
.trumbowyg-button-pane .trumbowyg-open-dropdown {
  padding-right: calc(var(--spacer-2) + var(--spacer-1) + 0.3em);
}
.trumbowyg-button-pane .trumbowyg-open-dropdown:after {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
  right: calc(var(--spacer-1) + 0.3em);
  height: 0;
  width: 0;
  border: 0.3em solid transparent;
  border-top-color: currentcolor;
  -webkit-transform: translateY(-25%);
          transform: translateY(-25%);
}
.trumbowyg-button-pane .trumbowyg-right {
  float: right;
}

.trumbowyg-dropdown {
  min-width: 13.75rem;
  padding: 0.625rem 0;
  background-color: var(--tw-dropdown-bg);
  border: var(--tw-dropdown-border-width) solid var(--tw-dropdown-border-color);
  margin-top: 0.125rem;
  z-index: 1000;
  box-shadow: var(--tw-dropdown-box-shadow);
  border-radius: var(--tw-dropdown-border-radius);
}
.trumbowyg-dropdown button {
  display: block;
  width: 100%;
  padding: calc(var(--spacer) * 0.4) var(--spacer);
  color: var(--tw-dropdown-link-color);
  background-color: transparent;
  white-space: nowrap;
  border: 0;
  text-align: inherit;
  cursor: pointer;
  transition: background-color ease-in-out var(--transition-base-timer), color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .trumbowyg-dropdown button {
    transition: none;
  }
}
.trumbowyg-dropdown button:hover, .trumbowyg-dropdown button:focus {
  color: var(--tw-dropdown-link-hover-color);
  background-color: var(--tw-dropdown-link-hover-bg);
}
.trumbowyg-dropdown button:active {
  color: var(--tw-dropdown-link-active-color);
  background-color: var(--tw-dropdown-link-active-bg);
}
.trumbowyg-dropdown button svg {
  display: inline-block;
  vertical-align: middle;
  margin-right: var(--spacer);
  margin-top: calc((var(--body-font-size) - var(--icon-font-sise)) * 0.5);
}

.trumbowyg-modal {
  position: absolute;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  max-width: 33rem;
  width: 100%;
  z-index: 11;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

.trumbowyg-modal-box {
  --tw-modal-bg: var(--white);
  --tw-modal-border-width: var(--border-width);
  --tw-modal-border-color: var(--border-color-translucent);
  --tw-modal-box-shadow: var(--box-shadow);
  position: absolute;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  max-width: 30rem;
  width: calc(100% - 3rem);
  background-color: var(--tw-modal-bg);
  border: var(--tw-modal-border-width) solid var(--tw-modal-border-color);
  z-index: 1;
  background-clip: padding-box;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--tw-modal-box-shadow);
}
.trumbowyg-modal-box[data-color-theme=dark], [data-color-theme=dark] .trumbowyg-modal-box:not([data-color-theme]), html[data-color-theme=dark] .trumbowyg-modal-box {
  color-scheme: dark;
  --tw-modal-bg: #2c2d33;
}
.trumbowyg-modal-box .trumbowyg-modal-title {
  font-size: 1rem;
  font-weight: 600;
  padding: var(--spacer) var(--spacer);
  padding-bottom: 0;
  display: block;
}
.trumbowyg-modal-box .trumbowyg-progress {
  width: 100%;
  height: 0.1875rem;
  position: absolute;
  top: calc(var(--spacer-1) * 2 + var(--spacer) * 2 + var(--icon-font-size) + var(--border-width));
}
.trumbowyg-modal-box .trumbowyg-progress .trumbowyg-progress-bar {
  background-color: var(--success);
  width: 0;
  height: 100%;
  transition: width linear var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .trumbowyg-modal-box .trumbowyg-progress .trumbowyg-progress-bar {
    transition: none;
  }
}
.trumbowyg-modal-box form {
  padding: var(--spacer);
  text-align: right;
}
.trumbowyg-modal-box form .trumbowyg-input-infos {
  display: block;
  text-align: left;
  transition: all ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .trumbowyg-modal-box form .trumbowyg-input-infos {
    transition: none;
  }
}
.trumbowyg-modal-box form .trumbowyg-input-infos label {
  display: block;
  position: relative;
  margin-bottom: var(--spacer-2);
}
@media (min-width: 768px) {
  .trumbowyg-modal-box form .trumbowyg-input-infos label {
    margin-bottom: 0;
  }
}
.trumbowyg-modal-box form .trumbowyg-input-infos span {
  display: block;
  line-height: var(--body-line-height);
}
@media (min-width: 768px) {
  .trumbowyg-modal-box form .trumbowyg-input-infos span {
    padding-top: calc(0.5rem + var(--border-width));
    padding-bottom: calc(0.5rem + var(--border-width));
  }
}
.trumbowyg-modal-box form .trumbowyg-input-infos span.trumbowyg-msg-error {
  color: var(--danger);
  margin-left: 30%;
  padding-bottom: 0;
}
.trumbowyg-modal-box form .trumbowyg-input-infos .trumbowyg-input-error input,
.trumbowyg-modal-box form .trumbowyg-input-infos .trumbowyg-input-error textarea {
  border-color: var(--danger);
}
.trumbowyg-modal-box form .trumbowyg-input-html input {
  --tw-input-bg: var(--white);
  --tw-input-color: var(--body-color);
  --tw-input-border-width: var(--border-width);
  --tw-input-border-color: var(--gray-400);
  --tw-input-focus-bg: var(--white);
  --tw-input-focus-border-color: var(--component-active-bg);
}
.trumbowyg-modal-box form .trumbowyg-input-html input[data-color-theme=dark], [data-color-theme=dark] .trumbowyg-modal-box form .trumbowyg-input-html input:not([data-color-theme]), html[data-color-theme=dark] .trumbowyg-modal-box form .trumbowyg-input-html input {
  color-scheme: dark;
  --tw-input-bg: #2c2d33;
  --tw-input-focus-bg: #2c2d33;
}
.trumbowyg-modal-box form .trumbowyg-input-html input:focus {
  outline: 0;
}
.trumbowyg-modal-box form .trumbowyg-input-html input:not([type=checkbox]):not([type=radio]) {
  padding: 0.5rem 0.875rem;
  font-size: var(--body-font-size);
  line-height: var(--body-line-height);
  color: var(--tw-input-color);
  background-color: var(--tw-input-bg);
  background-clip: padding-box;
  border: var(--tw-input-border-width) solid var(--tw-input-border-color);
  width: 100%;
  border-radius: var(--border-radius);
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .trumbowyg-modal-box form .trumbowyg-input-html input:not([type=checkbox]):not([type=radio]) {
    transition: none;
  }
}
.trumbowyg-modal-box form .trumbowyg-input-html input:not([type=checkbox]):not([type=radio]):focus {
  background-color: var(--tw-input-focus-bg);
  border-color: var(--tw-input-focus-border-color);
  box-shadow: var(--focus-ring-box-shadow);
}
.trumbowyg-modal-box form .trumbowyg-input-html input[type=checkbox] {
  position: absolute;
  left: 30%;
  top: 50%;
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
}
.trumbowyg-modal-box .trumbowyg-input-row {
  margin-bottom: var(--spacer);
}
@media (min-width: 768px) {
  .trumbowyg-modal-box .trumbowyg-input-row {
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-gap: var(--spacer);
  }
}
.trumbowyg-modal-box .error {
  display: block;
  color: var(--danger);
}

.trumbowyg-modal-button {
  --btn-padding-x: 0.875rem;
  --btn-padding-y: 0.5rem;
  --btn-color: #1F2937;
  --btn-bg: transparent;
  --btn-border-width: var(--border-width);
  --btn-border-color: transparent;
  --btn-border-radius: var(--border-radius);
  --btn-disabled-opacity: 0.65;
  --btn-focus-box-shadow: 0 0 0 0.125rem rgba(var(--btn-focus-shadow-rgb), .25);
  display: inline-block;
  text-align: center;
  color: var(--btn-color);
  padding: var(--btn-padding-y) var(--btn-padding-x);
  background-color: var(--btn-bg);
  margin-left: var(--spacer-2);
  vertical-align: middle;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border: var(--btn-border-width) solid var(--btn-border-color);
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
  border-radius: var(--btn-border-radius);
  --btn-color: var(--body-color);
  --btn-bg: var(--gray-200);
  --btn-border-color: var(--gray-400);
  --btn-hover-color: var(--body-color);
  --btn-hover-bg: var(--gray-300);
  --btn-hover-border-color: var(--gray-500);
  --btn-focus-shadow-rgb: 207, 207, 209;
  --btn-active-color: var(--body-color);
  --btn-active-bg: var(--gray-400);
  --btn-active-border-color: var(--gray-600);
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: var(--body-color);
  --btn-disabled-bg: var(--gray-200);
  --btn-disabled-border-color: var(--gray-400);
}
@media (prefers-reduced-motion: reduce) {
  .trumbowyg-modal-button {
    transition: none;
  }
}
.trumbowyg-modal-button:hover {
  color: var(--btn-hover-color);
  background-color: var(--btn-hover-bg);
  border-color: var(--btn-hover-border-color);
}
.trumbowyg-modal-button:focus {
  color: var(--btn-hover-color);
  background-color: var(--btn-hover-bg);
  border-color: var(--btn-hover-border-color);
  outline: 0;
  box-shadow: var(--btn-box-shadow), var(--btn-focus-box-shadow);
}
.trumbowyg-modal-button:active {
  color: var(--btn-active-color);
  background-color: var(--btn-active-bg);
  border-color: var(--btn-active-border-color);
}
.trumbowyg-modal-button:active:focus {
  box-shadow: var(--btn-active-shadow), var(--btn-focus-box-shadow);
}
.trumbowyg-modal-button:disabled {
  pointer-events: none;
  opacity: var(--btn-disabled-opacity);
  box-shadow: none;
}
.trumbowyg-modal-button.trumbowyg-modal-submit {
  --btn-color: #fff;
  --btn-bg: #0c83ff;
  --btn-border-color: #0c83ff;
  --btn-hover-color: #fff;
  --btn-hover-bg: #0b76e6;
  --btn-hover-border-color: #0b76e6;
  --btn-focus-shadow-rgb: 48, 150, 255;
  --btn-active-color: #fff;
  --btn-active-bg: #0a6fd9;
  --btn-active-border-color: #0a6fd9;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #0c83ff;
  --btn-disabled-border-color: #0c83ff;
}

.trumbowyg-overlay {
  position: absolute;
  background-color: var(--black);
  opacity: 0.35;
  height: 100%;
  width: 100%;
  left: 0;
  display: none;
  top: 0;
  z-index: 10;
}

body.trumbowyg-body-fullscreen {
  overflow: hidden;
}

.trumbowyg-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  z-index: 99999;
}
.trumbowyg-fullscreen.trumbowyg-box,
.trumbowyg-fullscreen .trumbowyg-editor {
  border: 0;
  border-radius: 0;
}
.trumbowyg-fullscreen .trumbowyg-editor,
.trumbowyg-fullscreen .trumbowyg-textarea {
  height: calc(100% - calc(var(--spacer-1) * 2 + var(--spacer) * 2 + var(--icon-font-size) + var(--border-width))) !important;
  overflow: auto;
}
.trumbowyg-fullscreen .trumbowyg-overlay {
  height: 100% !important;
}
.trumbowyg-fullscreen .trumbowyg-button-group .trumbowyg-fullscreen-button svg {
  color: var(--body-color);
  fill: transparent;
}

.trumbowyg-dropdown-foreColor,
.trumbowyg-dropdown-backColor {
  padding: 0.625rem;
  width: 15rem;
}
.trumbowyg-dropdown-foreColor svg,
.trumbowyg-dropdown-backColor svg {
  display: none !important;
}
.trumbowyg-dropdown-foreColor button,
.trumbowyg-dropdown-backColor button {
  display: block;
  position: relative;
  float: left;
  text-indent: -9999px;
  border: var(--border-width) solid var(--border-color);
  padding: 0;
  width: 1.25rem;
  height: 1.25rem;
  transition: -webkit-transform ease-in-out var(--transition-base-timer);
  transition: transform ease-in-out var(--transition-base-timer);
  transition: transform ease-in-out var(--transition-base-timer), -webkit-transform ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .trumbowyg-dropdown-foreColor button,
.trumbowyg-dropdown-backColor button {
    transition: none;
  }
}
.trumbowyg-dropdown-foreColor button:hover, .trumbowyg-dropdown-foreColor button:focus,
.trumbowyg-dropdown-backColor button:hover,
.trumbowyg-dropdown-backColor button:focus {
  -webkit-transform: scale(1.25);
          transform: scale(1.25);
  z-index: 10;
}

/* ------------------------------------------------------------------------------
*
*  # Ace code editor
*
*  Styles Ace - an embeddable code editor written in JavaScript
*
* ---------------------------------------------------------------------------- */
.ace_editor {
  height: 450px;
  position: relative;
}

/* ------------------------------------------------------------------------------
*
*  # Daterange picker
*
*  Date range picker component for Bootstrap
*
* ---------------------------------------------------------------------------- */
.daterangepicker {
  --drp-bg: var(--white);
  --drp-padding: 0.9375rem;
  --drp-border-width: var(--border-width);
  --drp-border-color: var(--border-color-translucent);
  --drp-box-shadow: var(--box-shadow-lg);
  --drp-border-radius: var(--border-radius);
  --drp-item-padding: 0.5rem;
  --drp-item-border-radius: var(--border-radius);
  --drp-item-hover-bg: var(--gray-300);
  --drp-item-hover-color: var(--body-color);
  --drp-item-active-bg: var(--primary);
  --drp-item-active-color: var(--white);
  --drp-item-weekday-color: var(--gray-600);
  --drp-item-focusout-color: var(--gray-500);
}
.daterangepicker[data-color-theme=dark], [data-color-theme=dark] .daterangepicker:not([data-color-theme]), html[data-color-theme=dark] .daterangepicker {
  color-scheme: dark;
  --drp-bg: #32333a;
  --drp-border-color: rgba(var(--black-rgb), 0.25);
}

.daterangepicker {
  position: absolute;
  color: inherit;
  background-color: var(--drp-bg);
  background-clip: padding-box;
  border: var(--drp-border-width) solid var(--drp-border-color);
  width: auto;
  max-width: none;
  padding: 0;
  margin-top: 0.125rem;
  top: 0;
  left: 0;
  z-index: 1080;
  display: none;
  border-radius: var(--drp-border-radius);
  box-shadow: var(--drp-box-shadow);
}
.daterangepicker.drop-up {
  margin-top: -0.625rem;
}
.daterangepicker.single .daterangepicker .ranges, .daterangepicker.single .drp-calendar {
  float: none;
}
.daterangepicker.single .drp-selected, .daterangepicker.auto-apply .drp-buttons {
  display: none;
}
.daterangepicker.show-calendar .drp-calendar {
  display: block;
}
.daterangepicker.show-calendar .drp-buttons {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: end;
      justify-content: flex-end;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.daterangepicker .drp-calendar {
  display: none;
}
.daterangepicker .drp-calendar.left, .daterangepicker .drp-calendar.right {
  padding: var(--drp-padding);
}
.daterangepicker .calendar-table .prev,
.daterangepicker .calendar-table .next {
  cursor: pointer;
}
.daterangepicker .calendar-table .prev span,
.daterangepicker .calendar-table .next span {
  border: solid var(--body-color);
  border-width: 0 0.125rem 0.125rem 0;
  border-radius: 0;
  display: inline-block;
  padding: 0.1875rem;
}
.daterangepicker .calendar-table .next span {
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}
.daterangepicker .calendar-table .prev span {
  -webkit-transform: rotate(135deg);
          transform: rotate(135deg);
}
.daterangepicker .calendar-table table {
  width: 100%;
  margin: 0;
  border-spacing: 0;
  border-collapse: collapse;
}
.daterangepicker th {
  font-weight: 400;
}
.daterangepicker th.month {
  width: auto;
  padding-top: 0;
  padding-bottom: 0;
  font-size: 1rem;
}
.daterangepicker td,
.daterangepicker th {
  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
  padding: var(--drp-item-padding);
  min-width: calc(var(--drp-item-padding) * 2 + var(--body-line-height-computed));
  cursor: default;
  border-radius: var(--drp-item-border-radius);
  transition: color ease-in-out var(--transition-base-timer), background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .daterangepicker td,
.daterangepicker th {
    transition: none;
  }
}
.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
  background-color: var(--drp-item-hover-bg);
  color: var(--drp-item-hover-color);
}
.daterangepicker td.week,
.daterangepicker th.week {
  font-size: 80%;
  color: var(--drp-item-weekday-color);
}
.daterangepicker td.off, .daterangepicker td.off.in-range, .daterangepicker td.off.start-date, .daterangepicker td.off.end-date {
  background-color: transparent;
  color: var(--drp-item-focusout-color);
}
.daterangepicker td.in-range {
  background-color: var(--drp-item-hover-bg);
  color: var(--drp-item-hover-color);
  border-radius: 0;
}
.daterangepicker td.start-date {
  border-radius: var(--drp-item-border-radius) 0 0 var(--drp-item-border-radius);
}
.daterangepicker td.end-date {
  border-radius: 0 var(--drp-item-border-radius) var(--drp-item-border-radius) 0;
}
.daterangepicker td.start-date.end-date {
  border-radius: var(--drp-item-border-radius);
}
.daterangepicker td.active:not(.off), .daterangepicker td.active:not(.off):hover {
  background-color: var(--drp-item-active-bg);
  color: var(--drp-item-active-color);
}
.daterangepicker td.disabled,
.daterangepicker option.disabled {
  opacity: 0.25;
}
.daterangepicker select {
  display: inline-block;
}
.daterangepicker select.monthselect, .daterangepicker select.yearselect {
  width: 49%;
}
.daterangepicker select.monthselect {
  margin-right: 2%;
}
.daterangepicker .calendar-time {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  margin: var(--drp-padding) auto 0 auto;
  position: relative;
}
.daterangepicker .calendar-time .form-select {
  display: inline-block;
  width: auto;
}
.daterangepicker .calendar-time .form-select ~ .form-select {
  margin-left: var(--spacer-1);
}
.daterangepicker .calendar-time .form-select:first-child {
  margin-right: var(--spacer-1);
}
.daterangepicker .drp-buttons {
  clear: both;
  padding: var(--drp-padding);
  border-top: var(--border-width) solid var(--border-color);
  display: none;
}
.daterangepicker .drp-buttons .btn {
  margin-left: var(--spacer-2);
}
.daterangepicker .drp-selected {
  display: inline-block;
  margin-right: auto;
}
.daterangepicker .ranges {
  float: none;
  text-align: left;
  margin: 0;
  padding: 0.625rem 0;
}
.daterangepicker .ranges:empty {
  display: none;
}
.daterangepicker .ranges ul {
  list-style: none;
  margin: 0 auto;
  padding: 0;
  width: 100%;
}
.daterangepicker .ranges li {
  cursor: pointer;
}
@media (max-width: 575.98px) {
  .daterangepicker {
    margin-left: var(--spacer);
    margin-right: var(--spacer);
  }
  .daterangepicker.opensleft, .daterangepicker.opensright {
    left: 0 !important;
    right: 0 !important;
  }
  .daterangepicker.opensleft .ranges,
.daterangepicker.opensleft .calendar,
.daterangepicker.opensleft .calendars, .daterangepicker.opensright .ranges,
.daterangepicker.opensright .calendar,
.daterangepicker.opensright .calendars {
    float: none;
  }
  .daterangepicker .calendar {
    margin-left: 0;
    margin-right: 0;
  }
  .daterangepicker .ranges {
    width: 100%;
  }
  .daterangepicker .drp-selected {
    width: 100%;
    margin-bottom: var(--spacer-2);
  }
  .daterangepicker.show-calendar.show-ranges .ranges {
    border-bottom: var(--border-width) solid var(--border-color);
  }
  .daterangepicker.show-calendar .drp-buttons {
    -ms-flex-pack: center;
        justify-content: center;
    text-align: center;
  }
}
@media (min-width: 576px) {
  .daterangepicker.show-ranges.single.rtl .drp-calendar.left, .daterangepicker.show-ranges.rtl .drp-calendar.right {
    border-right: var(--border-width) solid var(--border-color);
  }
  .daterangepicker.show-ranges.single.ltr .drp-calendar.left, .daterangepicker.show-ranges.ltr .drp-calendar.left {
    border-left: var(--border-width) solid var(--border-color);
  }
  .daterangepicker .ranges,
.daterangepicker .drp-calendar {
    float: left;
  }
  .daterangepicker .ranges {
    min-width: 13.75rem;
  }
}

/* ------------------------------------------------------------------------------
*
*  # Date picker
*
*  Vanilla JS date picker with decade, year, month and day selection
*
* ---------------------------------------------------------------------------- */
.datepicker {
  --dp-bg: var(--white);
  --dp-border-width: var(--border-width);
  --dp-border-color: var(--border-color-translucent);
  --dp-box-shadow: var(--box-shadow-lg);
  --dp-border-radius: var(--border-radius);
  --dp-item-border-radius: var(--border-radius);
  --dp-item-hover-bg: var(--gray-300);
  --dp-item-hover-color: var(--body-color);
  --dp-item-active-bg: var(--primary);
  --dp-item-active-color: var(--white);
  --dp-item-weekday-color: var(--gray-600);
  --dp-item-focusout-color: var(--gray-500);
}
.datepicker[data-color-theme=dark], [data-color-theme=dark] .datepicker:not([data-color-theme]), html[data-color-theme=dark] .datepicker {
  color-scheme: dark;
  --dp-bg: #32333a;
  --dp-border-color: rgba(var(--black-rgb), 0.25);
}

.datepicker {
  display: none;
}
.datepicker.active {
  display: block;
}

.datepicker-dropdown {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
}

.datepicker-picker {
  display: inline-block;
  background-color: var(--dp-bg);
  background-clip: padding-box;
  border: var(--dp-border-width) solid var(--dp-border-color);
  border-radius: var(--dp-border-radius);
}
.datepicker-dropdown .datepicker-picker {
  box-shadow: var(--dp-box-shadow);
}
.datepicker-inline .datepicker-picker {
  border: 0;
}
.datepicker-picker span {
  display: block;
  -ms-flex: 1;
      flex: 1;
  border: 0;
  cursor: default;
  text-align: center;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border-radius: var(--dp-item-border-radius);
}

.datepicker-controls,
.datepicker-view,
.datepicker-view .days-of-week,
.datepicker-grid {
  display: -ms-flexbox;
  display: flex;
}

.datepicker-grid {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.datepicker-inline .datepicker-grid {
  width: 100%;
}
.datepicker-inline .datepicker-picker {
  display: block;
}

.datepicker-main {
  padding: var(--spacer-2);
}

.datepicker-header {
  border-bottom: var(--border-width) solid var(--border-color);
}

.datepicker-footer {
  box-shadow: inset 0 var(--border-width) 0 var(--border-color);
}

.datepicker-controls .btn {
  --btn-color: var(--body-color);
  --btn-bg: var(--gray-200);
  --btn-border-color: var(--gray-400);
  --btn-hover-color: var(--body-color);
  --btn-hover-bg: var(--gray-300);
  --btn-hover-border-color: var(--gray-500);
  --btn-focus-shadow-rgb: 207, 207, 209;
  --btn-active-color: var(--body-color);
  --btn-active-bg: var(--gray-400);
  --btn-active-border-color: var(--gray-600);
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: var(--body-color);
  --btn-disabled-bg: var(--gray-200);
  --btn-disabled-border-color: var(--gray-400);
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
}
.datepicker-controls .btn, .datepicker-controls .btn:hover, .datepicker-controls .btn:focus, .datepicker-controls .btn:active {
  border-color: var(--border-color);
}
.datepicker-controls .btn:hover, .datepicker-controls .btn:focus {
  --btn-hover-color: var(--link-color);
}
.datepicker-controls .btn:active {
  --btn-active-color: var(--link-hover-color);
}
.datepicker-controls .btn:focus, .datepicker-controls .btn:active:focus {
  box-shadow: none;
}
.datepicker-footer .datepicker-controls .btn {
  margin-top: var(--border-width);
  width: 100%;
}
.datepicker-controls .view-switch {
  -ms-flex: auto;
      flex: auto;
  font-weight: 600;
}
.datepicker-controls .prev-btn.disabled,
.datepicker-controls .next-btn.disabled {
  opacity: 0;
  pointer-events: none;
}
.datepicker-controls .prev-btn {
  border-right-width: var(--border-width);
  border-top-left-radius: calc(var(--dp-border-radius) - var(--dp-border-width));
}
.datepicker-controls .next-btn {
  border-left-width: var(--border-width);
  border-top-right-radius: calc(var(--dp-border-radius) - var(--dp-border-width));
}
.datepicker-controls .today-btn {
  border-bottom-left-radius: calc(var(--dp-border-radius) - var(--dp-border-width));
}
.datepicker-controls.clear-btn-hidden .today-btn {
  border-bottom-right-radius: calc(var(--dp-border-radius) - var(--dp-border-width));
}
.datepicker-controls .clear-btn {
  border-left-width: var(--border-width);
  border-bottom-right-radius: calc(var(--dp-border-radius) - var(--dp-border-width));
}
.datepicker-controls.today-btn-hidden .clear-btn {
  border-left: 0;
  border-bottom-left-radius: calc(var(--dp-border-radius) - var(--dp-border-width));
}

.datepicker-title {
  box-shadow: inset 0 calc(var(--border-width) * -1) 0 var(--border-color);
  padding: var(--spacer-2);
  text-align: center;
  font-weight: 600;
}

.datepicker-view .dow,
.datepicker-view .days .datepicker-cell {
  -ms-flex-preferred-size: 14.28571%;
      flex-basis: 14.28571%;
}
.datepicker-view.datepicker-grid .datepicker-cell {
  -ms-flex-preferred-size: 25%;
      flex-basis: 25%;
}
.datepicker-view .days-of-week {
  margin-bottom: var(--spacer-2);
}
.datepicker-view .dow {
  color: var(--dp-item-weekday-color);
  min-height: var(--body-line-height-computed);
}
.datepicker-view .week {
  margin-right: var(--spacer-2);
  width: 2.25rem;
  color: var(--dp-item-weekday-color);
}

.datepicker-view .week,
.datepicker-cell {
  height: calc((0.5rem * 2) + var(--body-line-height-computed));
  line-height: calc((0.5rem * 2) + var(--body-line-height-computed));
}

.datepicker-grid {
  width: calc(((0.5rem * 2) + var(--body-line-height-computed)) * 7);
}

.datepicker-cell {
  transition: background-color ease-in-out var(--transition-base-timer), color ease-in-out var(--transition-base-timer), border-radius ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .datepicker-cell {
    transition: none;
  }
}
.datepicker-cell:not(.disabled):hover {
  background-color: var(--dp-item-hover-bg);
  color: var(--dp-item-hover-color);
  cursor: pointer;
}
.datepicker-cell.focused:not(.selected) {
  background-color: var(--dp-item-hover-bg);
  color: var(--dp-item-hover-color);
}
.datepicker-cell.selected, .datepicker-cell.selected:hover {
  background-color: var(--dp-item-active-bg);
  color: var(--dp-item-active-color);
}
.datepicker-cell.disabled {
  opacity: 0.25;
}
.datepicker-cell.prev:not(.disabled), .datepicker-cell.next:not(.disabled) {
  color: var(--dp-item-focusout-color);
}
.datepicker-cell.highlighted:not(.selected):not(.range):not(.today) {
  color: var(--dp-item-active-bg);
}
.datepicker-cell.highlighted:not(.selected):not(.range):not(.today):not(.disabled):hover {
  color: var(--dp-item-hover-color);
}
.datepicker-cell.today:not(.selected):not(.disabled) {
  background-color: var(--dp-item-hover-bg);
  color: var(--dp-item-hover-color);
}
.datepicker-cell.range-start:not(.selected), .datepicker-cell.range-end:not(.selected) {
  background-color: var(--dp-item-active-bg);
  color: var(--dp-item-active-color);
}
.datepicker-cell.range-start:not(.range-end) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.datepicker-cell.range-end:not(.range-start) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.datepicker-cell.range {
  border-radius: 0;
  background-color: var(--dp-item-hover-bg);
  color: var(--dp-item-hover-color);
}
.datepicker-cell.range.disabled {
  opacity: 0.65;
}
.datepicker-view.datepicker-grid .datepicker-cell {
  height: 4rem;
  line-height: 4rem;
}

/* ------------------------------------------------------------------------------
*
*  # Spectrum color picker
*
*  Flexible and powerful jQuery colorpicker library
*
* ---------------------------------------------------------------------------- */
.sp-container {
  --spectrum-bg: var(--white);
  --spectrum-border-width: var(--border-width);
  --spectrum-border-color: var(--border-color-translucent);
  --spectrum-box-shadow: var(--box-shadow-lg);
  --spectrum-border-radius: var(--border-radius);
}
.sp-container[data-color-theme=dark], [data-color-theme=dark] .sp-container:not([data-color-theme]), html[data-color-theme=dark] .sp-container {
  color-scheme: dark;
  --spectrum-bg: #32333a;
  --spectrum-border-color: rgba(var(--black-rgb), 0.25);
}

.sp-sat,
.sp-val,
.sp-top-inner,
.sp-color,
.sp-hue,
.sp-clear-enabled .sp-clear,
.sp-preview-inner,
.sp-alpha-inner,
.sp-thumb-inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.sp-container.sp-input-disabled .sp-input-container,
.sp-container.sp-buttons-disabled .sp-button-container,
.sp-container.sp-palette-buttons-disabled .sp-palette-button-container,
.sp-palette-only .sp-picker-container,
.sp-palette-disabled .sp-palette-container,
.sp-initial-disabled .sp-initial {
  display: none;
}

.sp-hidden {
  display: none !important;
}

.sp-cf::after {
  display: block;
  clear: both;
  content: "";
}

.sp-preview,
.sp-alpha,
.sp-thumb-el {
  position: relative;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
}

.sp-preview-inner,
.sp-alpha-inner,
.sp-thumb-inner {
  display: block;
}

.sp-container {
  position: absolute;
  top: 0;
  /*rtl:ignore*/
  left: 0;
  display: inline-block;
  z-index: 1000;
  background-color: var(--spectrum-bg);
  background-clip: padding-box;
  border: var(--spectrum-border-width) solid var(--spectrum-border-color);
  overflow: hidden;
  box-sizing: content-box;
  border-radius: var(--spectrum-border-radius);
  box-shadow: var(--spectrum-box-shadow);
}
.sp-container.sp-flat {
  position: relative;
  overflow-x: auto;
  max-width: 100%;
  white-space: nowrap;
  box-shadow: none;
}

.sp-picker-container,
.sp-palette-container {
  display: block;
  white-space: nowrap;
  vertical-align: top;
  position: relative;
  padding: 0.625rem;
}
@media (min-width: 576px) {
  .sp-picker-container,
.sp-palette-container {
    display: inline-block;
  }
}

.sp-picker-container {
  width: 13.75rem;
}

.sp-container,
.sp-replacer,
.sp-preview,
.sp-dragger,
.sp-slider,
.sp-alpha,
.sp-clear,
.sp-alpha-handle,
.sp-container.sp-dragging .sp-input,
.sp-container button {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.sp-top {
  position: relative;
  width: 100%;
  display: block;
}

.sp-color {
  right: 20%;
  border-radius: var(--border-radius);
}

.sp-hue {
  left: 85%;
  height: 100%;
}

.sp-clear-enabled .sp-hue {
  top: 2.5rem;
  height: 75%;
}

.sp-fill {
  padding-top: 80%;
}

.sp-alpha-enabled .sp-top {
  margin-bottom: 1.625rem;
}
.sp-alpha-enabled .sp-alpha {
  display: block;
}

.sp-alpha-handle {
  position: absolute;
  top: 50%;
  width: 0.375rem;
  height: 1rem;
  /*rtl:ignore*/
  left: 100%;
  -webkit-transform: translate(-25%, -50%);
          transform: translate(-25%, -50%);
  cursor: ew-resize;
  border: var(--border-width) solid var(--border-color-translucent);
  background-color: var(--white);
  background-clip: content-box;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.sp-alpha {
  display: none;
  position: absolute;
  bottom: -1rem;
  right: 0;
  left: 0;
  height: 0.375rem;
}

.sp-clear {
  display: none;
}

.sp-clear-display {
  cursor: pointer;
}
.sp-clear-display:after {
  content: "\f267";
  display: block;
  font-family: var(--icon-font-family);
  font-size: var(--icon-font-size);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sp-preview .sp-clear-display:after, .sp-initial .sp-clear-display:after {
  content: none;
}

.sp-clear-enabled .sp-clear {
  display: block;
  left: 85%;
  height: calc(var(--icon-font-size) + var(--spacer-1) * 2);
  text-align: center;
  box-shadow: 0 0 0 var(--border-width) var(--border-color) inset;
  border-radius: var(--border-radius);
}

.sp-input-container {
  margin-top: var(--spacer-2);
}

.sp-initial {
  margin-top: var(--spacer-2);
}
.sp-initial span {
  width: 50%;
  height: var(--body-line-height-computed);
  display: block;
  float: left;
}
.sp-initial span .sp-thumb-inner {
  height: 100%;
  width: 100%;
  display: block;
}
.sp-initial .sp-thumb-el:first-child, .sp-initial .sp-thumb-el:first-child .sp-thumb-inner {
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}
.sp-initial .sp-thumb-el:last-child, .sp-initial .sp-thumb-el:last-child .sp-thumb-inner {
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
}

.sp-dragger {
  border: var(--border-width) solid var(--white);
  background-color: var(--black);
  cursor: pointer;
  position: absolute;
  top: 0;
  /*rtl:ignore*/
  left: 0;
  width: 0.5rem;
  height: 0.5rem;
  -webkit-transform: translate(0.125rem, 0.125rem);
          transform: translate(0.125rem, 0.125rem);
  border-radius: var(--border-radius-pill);
}

.sp-slider {
  position: absolute;
  top: 0;
  cursor: ns-resize;
  height: 0.375rem;
  left: -0.1875rem;
  right: -0.1875rem;
  -webkit-transform: translateY(0.0625rem);
          transform: translateY(0.0625rem);
  border: var(--border-width) solid var(--border-color-translucent);
  background-color: var(--white);
  background-clip: content-box;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.sp-replacer {
  padding: 0.375rem 0.5rem;
}
.sp-replacer.sp-active {
  color: var(--btn-active-color);
  background-color: var(--btn-active-bg);
  border-color: var(--btn-active-border-color);
}
.sp-replacer.sp-disabled {
  pointer-events: none;
  opacity: 0.65;
}

.sp-preview {
  position: relative;
  width: calc(var(--body-line-height-computed) + (0.5rem * .5));
  height: calc(var(--body-line-height-computed) + (0.5rem * .5));
  float: left;
  z-index: 0;
}
.sp-preview,
.sp-preview .sp-preview-inner {
  border-radius: var(--border-radius);
}
.sp-preview .sp-preview-inner,
.sp-preview .sp-clear-display {
  border: var(--border-width) solid var(--border-color-translucent);
}

.sp-dd {
  float: left;
  font-size: 0;
  position: relative;
  margin-left: var(--spacer-1);
}
.sp-dd:after {
  content: "\f31a";
  display: block;
  font-family: var(--icon-font-family);
  font-size: var(--icon-font-size);
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.sp-sat,
.sp-val,
.sp-hue {
  border-radius: var(--border-radius);
}

/*rtl:begin:ignore*/
.sp-sat {
  background-image: linear-gradient(to right, #fff, rgba(204, 154, 129, 0));
}

.sp-val {
  background-image: linear-gradient(to top, #000, rgba(204, 154, 129, 0));
}

.sp-hue {
  background: linear-gradient(to bottom, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
}

/*rtl:end:ignore*/
/*rtl:begin:ignore*/
.sp-1 {
  height: 17%;
}

.sp-2 {
  height: 16%;
}

.sp-3 {
  height: 17%;
}

.sp-4 {
  height: 17%;
}

.sp-5 {
  height: 16%;
}

.sp-6 {
  height: 17%;
}

/*rtl:end:ignore*/
.sp-palette {
  max-width: 13.75rem;
}

.sp-thumb-el {
  position: relative;
}
.sp-palette .sp-thumb-el {
  display: inline-block;
  cursor: pointer;
}
.sp-thumb-el .sp-thumb-inner {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  box-shadow: 0 0 0 var(--border-width) rgba(var(--black-rgb), 0.15) inset;
  transition: box-shadow ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .sp-thumb-el .sp-thumb-inner {
    transition: none;
  }
}
.sp-thumb-el .sp-thumb-inner:hover {
  box-shadow: 0 0 0 var(--border-width) rgba(var(--black-rgb), 0.75) inset;
}
.sp-palette .sp-thumb-el {
  width: 1rem;
  height: 1rem;
}
.sp-palette .sp-thumb-el + .sp-thumb-el {
  margin-left: var(--spacer-1);
}

.sp-palette .sp-thumb-active.sp-thumb-dark .sp-thumb-inner:after, .sp-palette .sp-thumb-active.sp-thumb-light .sp-thumb-inner:after {
  content: "\f0e7";
  font-family: var(--icon-font-family);
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sp-palette .sp-thumb-active.sp-thumb-dark .sp-thumb-inner:after {
  color: var(--white);
}
.sp-palette .sp-thumb-active.sp-thumb-light .sp-thumb-inner:after {
  color: var(--black);
}

.sp-palette-row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.sp-palette-row + .sp-palette-row {
  margin-top: var(--spacer-1);
}
.sp-palette-row:empty {
  margin-top: 0;
}

.sp-cancel,
.sp-choose,
.sp-palette-toggle {
  float: left;
  width: 48%;
  cursor: pointer;
}

.sp-palette-button-container,
.sp-button-container {
  margin-top: calc(var(--spacer) * 0.5);
  text-align: center;
}

.sp-cancel,
.sp-palette-toggle {
  margin-right: 4%;
}

.sp-palette-toggle {
  margin-right: 0;
  width: auto;
  float: none;
}

/* ------------------------------------------------------------------------------
 *
 *  # Plupload multiple file uploader
 *
 *  Styles for plupload.min.js - multi runtime single and multiple file uploader
 *
 * ---------------------------------------------------------------------------- */
.plupload_wrapper {
  --plu-max-height: 250px;
  --plu-border-width: calc(var(--border-width) * 2);
  --plu-border-style: dashed;
  --plu-border-color: var(--border-color);
  --plu-header-bg: var(--dark);
  --plu-header-color: var(--white);
  --plu-placeholder-color: var(--gray-500);
  border: var(--plu-border-width) var(--plu-border-style) var(--plu-border-color);
  border-radius: var(--border-radius);
}

.plupload_header:after,
.plupload_filelist:empty:before,
.plupload_filelist li.plupload_droptext:before,
.plupload_file_action:after,
.plupload_delete a:after,
.plupload_failed a:after,
.plupload_done a:after {
  font-family: var(--icon-font-family);
  display: block;
  font-size: var(--icon-font-size);
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.plupload_header {
  display: none;
  position: relative;
  background-color: var(--plu-header-bg);
  color: var(--plu-header-color);
  margin: calc(var(--plu-border-width) * -1) calc(var(--plu-border-width) * -1) 0 calc(var(--plu-border-width) * -1);
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}
.plupload_header:after {
  content: "\f358";
  font-size: calc(var(--icon-font-size) * 2);
  position: absolute;
  top: 50%;
  left: var(--spacer);
  margin-top: calc(var(--icon-font-size) * -1);
}

.plupload_header_content {
  padding: var(--spacer);
  padding-left: calc(var(--spacer) * 2 + var(--icon-font-size) * 2);
}

.plupload_header_title {
  font-size: 1rem;
  font-weight: 600;
}

.plupload_header_text {
  font-size: var(--body-font-size-sm);
  line-height: var(--body-line-height-sm);
}

.plupload_clear,
.plupload_clearer {
  clear: both;
}

.plupload_clearer,
.plupload_progress_bar {
  display: block;
  font-size: 0;
  line-height: 0;
}

.plupload_button {
  --btn-padding-x: 0.875rem;
  --btn-padding-y: 0.5rem;
  --btn-font-family: ;
  --btn-font-size: var(--body-font-size);
  --btn-font-weight: 400;
  --btn-line-height: var(--body-line-height);
  --btn-border-width: var(--border-width);
  --btn-box-shadow: 0 0 0 0 transparent;
  --btn-disabled-opacity: 0.65;
  --btn-border-radius: var(--border-radius);
  --btn-focus-box-shadow: 0 0 0 0.125rem rgba(var(--btn-focus-shadow-rgb), .25);
  display: inline-block;
  font-family: var(--btn-font-family);
  font-size: var(--btn-font-size);
  font-weight: var(--btn-font-weight);
  line-height: var(--btn-line-height);
  padding: var(--btn-padding-y) var(--btn-padding-x);
  color: var(--btn-color);
  border: var(--btn-border-width) solid var(--btn-border-color);
  border-radius: var(--btn-border-radius);
  background-color: var(--btn-bg);
  box-shadow: var(--btn-box-shadow);
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .plupload_button {
    transition: none;
  }
}
.plupload_button:hover {
  color: var(--btn-hover-color);
  background-color: var(--btn-hover-bg);
  border-color: var(--btn-hover-border-color);
}
.plupload_button:focus {
  color: var(--btn-hover-color);
  background-color: var(--btn-hover-bg);
  border-color: var(--btn-hover-border-color);
  outline: 0;
  box-shadow: var(--btn-box-shadow), var(--btn-focus-box-shadow);
}
.plupload_button:active {
  color: var(--btn-active-color);
  background-color: var(--btn-active-bg);
  border-color: var(--btn-active-border-color);
  box-shadow: var(--btn-active-shadow);
}
.plupload_button:active:focus {
  box-shadow: var(--btn-active-shadow), var(--btn-focus-box-shadow);
}
.plupload_button:not(:last-child) {
  margin-right: var(--spacer-2);
}

.plupload_add {
  --btn-color: var(--body-color);
  --btn-bg: var(--gray-200);
  --btn-border-color: var(--gray-400);
  --btn-hover-color: var(--body-color);
  --btn-hover-bg: var(--gray-300);
  --btn-hover-border-color: var(--gray-500);
  --btn-focus-shadow-rgb: 207, 207, 209;
  --btn-active-color: var(--body-color);
  --btn-active-bg: var(--gray-400);
  --btn-active-border-color: var(--gray-600);
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: var(--body-color);
  --btn-disabled-bg: var(--gray-200);
  --btn-disabled-border-color: var(--gray-400);
}

.plupload_start {
  --btn-color: #fff;
  --btn-bg: #0c83ff;
  --btn-border-color: #0c83ff;
  --btn-hover-color: #fff;
  --btn-hover-bg: #0b76e6;
  --btn-hover-border-color: #0b76e6;
  --btn-focus-shadow-rgb: 48, 150, 255;
  --btn-active-color: #fff;
  --btn-active-bg: #0a6fd9;
  --btn-active-border-color: #0a6fd9;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #0c83ff;
  --btn-disabled-border-color: #0c83ff;
}

a.plupload_disabled {
  pointer-events: none;
  opacity: var(--btn-disabled-opacity);
}

.plupload_filelist {
  position: relative;
  margin: 0;
  padding: 0;
  list-style: none;
}
.plupload_scroll .plupload_filelist {
  height: var(--plu-max-height);
  overflow-y: auto;
}
.plupload_filelist li {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  padding: var(--spacer-2) var(--spacer);
  transition: all ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .plupload_filelist li {
    transition: none;
  }
}
.plupload_filelist li:hover {
  background-color: var(--gray-200);
}
.plupload_filelist li .plupload_file_name {
  margin-right: auto;
}
.plupload_filelist li .plupload_file_action {
  -ms-flex-order: 12;
      order: 12;
}
.plupload_filelist li .plupload_file_status {
  -ms-flex-order: 11;
      order: 11;
}
.plupload_filelist li .plupload_file_size {
  -ms-flex-order: 10;
      order: 10;
}
.plupload_filelist:empty,
.plupload_filelist li.plupload_droptext {
  background-color: transparent;
  font-size: 0;
  color: var(--plu-placeholder-color);
}
.plupload_filelist:empty:before,
.plupload_filelist li.plupload_droptext:before {
  content: "\f358";
  font-size: calc(var(--icon-font-size) * 5);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: calc(var(--spacer) * -1);
  z-index: 2;
  text-indent: 0;
  font-weight: normal;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.plupload_filelist:empty:after,
.plupload_filelist li.plupload_droptext:after {
  content: "Drag files to upload";
  font-size: 1.125rem;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: calc(var(--icon-font-size) * 2.5 + var(--spacer));
  text-indent: 0;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.plupload_filelist:empty:after {
  content: "Add files to upload";
}
@media (max-width: 575.98px) {
  .plupload_filelist {
    padding: calc(var(--spacer) * 0.5) 0;
  }
  .plupload_filelist li .plupload_file_size {
    display: none;
  }
}

.plupload_filelist_header {
  padding: var(--spacer);
}
.plupload_filelist_header .plupload_file_name,
.plupload_filelist_header .plupload_file_size,
.plupload_filelist_header .plupload_file_status,
.plupload_filelist_header .plupload_file_action {
  font-weight: 600;
}
@media (max-width: 575.98px) {
  .plupload_filelist_header {
    display: none;
  }
}

.plupload_filelist_footer {
  padding: var(--spacer);
}
.plupload_filelist_footer .plupload_file_size,
.plupload_filelist_footer .plupload_file_status,
.plupload_filelist_footer .plupload_upload_status {
  font-weight: 600;
}
@media (max-width: 575.98px) {
  .plupload_filelist_footer {
    text-align: center;
  }
  .plupload_filelist_footer .plupload_file_action,
.plupload_filelist_footer .plupload_file_status,
.plupload_filelist_footer .plupload_file_size {
    display: none;
  }
  .plupload_filelist_footer .plupload_progress {
    margin: auto;
  }
}

@media (min-width: 576px) {
  .plupload_filelist_header,
.plupload_filelist_footer {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
        align-items: center;
  }
  .plupload_filelist_header .plupload_file_name,
.plupload_filelist_footer .plupload_file_name {
    margin-right: auto;
  }
  .plupload_filelist_header .plupload_file_action,
.plupload_filelist_footer .plupload_file_action {
    -ms-flex-order: 12;
        order: 12;
  }
  .plupload_filelist_header .plupload_file_status,
.plupload_filelist_footer .plupload_file_status {
    -ms-flex-order: 11;
        order: 11;
  }
  .plupload_filelist_header .plupload_file_size,
.plupload_filelist_footer .plupload_file_size {
    -ms-flex-order: 10;
        order: 10;
  }
}

.plupload_file_action {
  margin-left: var(--spacer-4);
  line-height: 1;
  font-size: 0;
  text-align: right;
}
.plupload_file_action * {
  display: none;
}
.plupload_file_action:after {
  content: "\f3ab";
}
.plupload_filelist .plupload_file_action:after {
  content: none;
}
.plupload_delete .plupload_file_action > a {
  color: var(--body-color);
  line-height: 1;
  opacity: 0.5;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .plupload_delete .plupload_file_action > a {
    transition: none;
  }
}
.plupload_delete .plupload_file_action > a:hover {
  opacity: 1;
}

.plupload_uploading {
  background-color: rgba(var(--success-rgb), 0.1);
}

.plupload_delete a:after {
  content: "\f642";
}

.plupload_failed a {
  color: var(--вфтпук);
  cursor: default;
}
.plupload_failed a:after {
  content: "\f62d";
}

.plupload_done {
  color: var(--gray-600);
}
.plupload_done a {
  color: var(--success);
  cursor: default;
}
.plupload_done a:after {
  content: "\f33f";
}

.plupload_progress,
.plupload_upload_status {
  display: none;
}

.plupload_progress_container {
  background-color: rgba(var(--body-color-rgb), 0.1);
  border-radius: var(--border-radius);
}

.plupload_progress_bar {
  width: 0;
  height: 0.25rem;
  background: var(--success);
  border-radius: var(--border-radius);
}

.plupload_file_size,
.plupload_file_status,
.plupload_progress {
  width: 5rem;
  text-align: right;
}

/* ------------------------------------------------------------------------------
 *
 *  # Bootstrap file input
 *
 *  Styles for fileinput.min.js - an enhanced HTML 5 file input for Bootstrap
 *
 * ---------------------------------------------------------------------------- */
.file-input {
  --fi-preview-border: calc(var(--border-width) * 2) dashed var(--border-color);
  --fi-preview-spacer: 0.5rem;
  --fi-preview-thumb-width: 10rem;
  --fi-preview-thumb-height: 10rem;
  --fi-preview-progress-height: 0.375rem;
  --fi-placeholder-color: var(--gray-500);
}

.btn-file {
  position: relative;
  overflow: hidden;
}
.btn-file input[type=file] {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 100%;
  min-height: 100%;
  text-align: right;
  opacity: 0;
  background: none repeat scroll 0 0 transparent;
  cursor: inherit;
  display: block;
  visibility: visible;
}

.file-error-message {
  position: relative;
  background-color: rgba(var(--danger-rgb), 0.1);
  color: var(--danger);
  text-align: center;
  padding: calc(var(--spacer) * 0.8) var(--spacer);
  margin: var(--fi-preview-spacer);
  margin-top: 0;
  border-radius: var(--border-radius);
}
.file-drop-zone .file-error-message {
  margin-top: var(--spacer-2);
}
.file-error-message pre {
  margin-top: var(--spacer-2);
  text-align: left;
}
.file-error-message ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.file-caption-disabled {
  pointer-events: none;
}

.file-preview-detail-modal {
  text-align: left;
}

.file-preview {
  border: var(--fi-preview-border);
  width: 100%;
  margin-bottom: var(--spacer);
  position: relative;
  text-align: center;
  border-radius: var(--border-radius);
}
.file-preview .btn-close {
  position: absolute;
  top: var(--fi-preview-spacer);
  right: var(--fi-preview-spacer);
  z-index: 2;
}
.file-preview .btn-close[data-color-theme=dark], [data-color-theme=dark] .file-preview .btn-close:not([data-color-theme]), html[data-color-theme=dark] .file-preview .btn-close {
  color-scheme: dark;
  -webkit-filter: invert(1) grayscale(100%) brightness(200%);
          filter: invert(1) grayscale(100%) brightness(200%);
}
.file-preview .kv-zoom-cache {
  display: none;
}

.file-preview-thumbnails {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  position: relative;
}
@media (max-width: 575.98px) {
  .file-preview-thumbnails {
    -ms-flex-pack: center;
        justify-content: center;
  }
}

.file-preview-frame {
  margin: var(--fi-preview-spacer);
  margin-right: 0;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
}
.file-preview-frame .kv-file-content {
  position: relative;
  height: var(--fi-preview-thumb-height);
}

.kv-file-content .file-preview-image {
  border-top-left-radius: calc(var(--border-radius) - var(--border-width));
  border-top-right-radius: calc(var(--border-radius) - var(--border-width));
}

.file-thumbnail-footer {
  position: relative;
  background-color: var(--gray-100);
  border-top: var(--border-width) solid var(--border-color);
  border-bottom-right-radius: calc(var(--border-radius) - var(--border-width));
  border-bottom-left-radius: calc(var(--border-radius) - var(--border-width));
}

.file-preview-text {
  color: var(--link-color);
  border: 0;
  overflow-x: hidden;
}

.file-preview-other {
  width: var(--fi-preview-thumb-width);
}
.file-preview-other:after {
  content: "\f358";
  font-family: var(--icon-font-family);
  line-height: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: calc(var(--icon-font-size) * 4);
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.file-preview-status {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: var(--gray-100);
  border-top: var(--border-width) solid var(--border-color);
  padding: var(--spacer-2) var(--spacer);
  z-index: 1080;
  border-bottom-right-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}
.file-preview-status:empty {
  padding: 0;
  background-color: transparent;
  border: 0;
}

.file-thumb-loading {
  position: relative;
}

.file-thumb-loading:after,
.file-uploading:after {
  content: "\f5a7";
  font-family: var(--icon-font-family);
  display: inline-block;
  position: absolute;
  top: var(--fi-preview-spacer);
  right: calc(var(--spacer-4) + var(--fi-preview-spacer));
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  font-size: var(--icon-font-size);
  line-height: 1;
  z-index: 1080;
  -webkit-animation: rotation 1s linear infinite;
          animation: rotation 1s linear infinite;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.file-thumb-loading:after {
  top: var(--spacer);
  right: var(--spacer);
}

.file-upload-indicator {
  cursor: default;
  float: left;
  padding: var(--spacer-2);
  line-height: 1;
}

.file-input-new .file-preview,
.file-input-new .close,
.file-input-new .glyphicon-file,
.file-input-new .fileinput-remove-button,
.file-input-new .fileinput-upload-button {
  display: none;
}

.file-input-ajax-new .fileinput-remove-button,
.file-input-ajax-new .fileinput-upload-button,
.file-input-ajax-new .close {
  display: none;
}

.kv-hidden,
.file-caption-icon,
.file-zoom-dialog .modal-header:before,
.file-zoom-dialog .modal-header:after,
.hide-content .kv-file-content {
  display: none;
}

.file-footer-buttons {
  float: right;
}
.file-footer-buttons button {
  padding: var(--spacer-2);
  background-color: transparent;
  color: var(--body-color);
  border: 0;
  line-height: 1;
  cursor: pointer;
  opacity: 0.8;
  outline: 0;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .file-footer-buttons button {
    transition: none;
  }
}
.file-footer-buttons button:hover {
  opacity: 1;
}

.file-footer-caption {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: var(--spacer-2);
  border-bottom: var(--border-width) solid var(--border-color);
}

.file-size-info samp {
  font-size: var(--body-font-size-sm);
}

.file-drop-zone {
  height: 100%;
  vertical-align: middle;
  padding: var(--fi-preview-spacer);
  border-radius: var(--border-radius);
}

.file-drop-zone-title {
  font-size: 1.25rem;
  font-weight: 300;
  color: var(--fi-placeholder-color);
  padding: 5rem;
}
.clickable .file-drop-zone-title {
  cursor: pointer;
}
.clickable .file-drop-zone-title:hover, .clickable .file-drop-zone-title:focus {
  opacity: 1;
  background-color: var(--gray-100);
}

.kv-upload-progress {
  margin-bottom: var(--spacer);
}

.file-thumb-progress {
  position: absolute;
  top: calc(var(--fi-preview-progress-height) * -1);
  left: 0;
  right: 0;
}
.file-thumb-progress .progress,
.file-thumb-progress .progress-bar {
  height: var(--fi-preview-progress-height);
  border-radius: 0;
  font-size: 0;
}

.btn-file ::-ms-browse {
  width: 100%;
  height: 100%;
}

.file-zoom-fullscreen .modal-dialog {
  position: fixed;
  margin: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  max-width: 100%;
}
.file-zoom-fullscreen .modal-content {
  border-radius: 0;
}
.file-zoom-fullscreen .modal-body {
  overflow-y: auto;
}

.file-zoom-dialog .btn-navigate {
  position: absolute;
  top: 50%;
  padding: 0;
  border: 0;
  background-color: transparent;
  outline: none;
  color: var(--white);
  opacity: 0.7;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .file-zoom-dialog .btn-navigate {
    transition: none;
  }
}
.file-zoom-dialog .btn-navigate > i {
  display: block;
  font-size: calc(var(--icon-font-size) * 2);
}
.file-zoom-dialog .btn-navigate:not([disabled]):hover, .file-zoom-dialog .btn-navigate:not([disabled]):focus {
  outline: none;
  box-shadow: none !important;
  opacity: 1;
}
.file-zoom-dialog .btn-navigate[disabled] {
  opacity: 0.3;
}
.file-zoom-dialog .btn-kv-prev {
  left: calc((var(--icon-font-size) * 2 + var(--spacer)) * -1);
}
.file-zoom-dialog .btn-kv-next {
  right: calc((var(--icon-font-size) * 2 + var(--spacer)) * -1);
}
.file-zoom-dialog .floating-buttons {
  position: absolute;
  top: var(--spacer);
  right: var(--spacer);
}
.file-zoom-dialog .kv-zoom-actions .btn + .btn,
.file-zoom-dialog .floating-buttons .btn + .btn {
  margin-left: var(--spacer-2);
}

.file-zoom-content {
  text-align: center;
}

.file-drag-handle {
  cursor: move;
  float: left;
  line-height: 1;
  opacity: 0.6;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .file-drag-handle {
    transition: none;
  }
}
.file-drag-handle:hover {
  opacity: 1;
}
.file-drag-handle i {
  top: 0;
}

/* ------------------------------------------------------------------------------
 *
 *  # Dropzone file uploader
 *
 *  Styles for dropzone.min.js - open source library that provides drag’n’drop file uploads with image previews
 *
 * ---------------------------------------------------------------------------- */
.dropzone {
  --dz-min-height: 18rem;
  --dz-border-width: calc(var(--border-width) * 2);
  --dz-border-style: dashed;
  --dz-border-color: var(--border-color);
  --dz-placeholder-color: var(--gray-500);
  --dz-spacer: 0.3125rem;
  --dz-drag-bg: var(--gray-200);
  --dz-drag-color: var(--gray-400);
  --dz-drag-border-color: var(--gray-600);
  --dz-preview-spacer: 0.5rem;
  --dz-preview-width: 10rem;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  position: relative;
  border: var(--dz-border-width) var(--dz-border-style) var(--dz-border-color);
  min-height: var(--dz-min-height);
  padding: var(--dz-spacer);
  border-radius: var(--border-radius);
}
.dropzone.dz-clickable * {
  cursor: default;
}
.dropzone.dz-clickable,
.dropzone.dz-clickable .dz-message,
.dropzone.dz-clickable .dz-message * {
  cursor: pointer;
}
.dropzone .dz-message {
  opacity: 1;
}
.dropzone .dz-message .dz-button {
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}
.dropzone.dz-started .dz-message {
  display: none;
}
.dropzone.dz-drag-hover {
  border-color: var(--dz-drag-border-color);
  background-color: var(--dz-drag-bg);
  color: var(--dz-drag-color);
}

.dropzone .dz-default.dz-message {
  width: 100%;
  -ms-flex-item-align: center;
      align-self: center;
  text-align: center;
  opacity: 1;
  color: var(--dz-placeholder-color);
  border-radius: var(--border-radius);
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .dropzone .dz-default.dz-message {
    transition: none;
  }
}
.dropzone .dz-default.dz-message:before {
  content: "\f358";
  font-family: var(--icon-font-family);
  font-size: calc(var(--icon-font-size) * 4);
  display: block;
  margin-bottom: var(--spacer-2);
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.dropzone.dz-drag-hover .dz-message {
  opacity: 0.75;
}

.dropzone .dz-preview {
  position: relative;
  -ms-flex-item-align: start;
      align-self: flex-start;
  display: inline-block;
  margin: var(--dz-preview-spacer);
  padding: var(--dz-preview-spacer);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
}
.dropzone .dz-preview .dz-details {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-pack: center;
      justify-content: center;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  padding: var(--dz-preview-spacer);
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .dropzone .dz-preview .dz-details {
    transition: none;
  }
}
.dropzone .dz-preview:hover .dz-details {
  opacity: 1;
}
.dropzone .dz-preview:hover .dz-image img {
  -webkit-filter: blur(8px);
          filter: blur(8px);
  opacity: 0.5;
}
.dropzone .dz-preview.dz-error:hover .dz-details {
  display: none;
}
.dropzone .dz-preview .dz-error-mark,
.dropzone .dz-preview .dz-success-mark {
  display: none;
  position: absolute;
  z-index: 9;
  top: calc(var(--icon-font-size) * 0.5 * -1);
  right: calc(var(--icon-font-size) * 0.5 * -1);
  padding: calc(var(--spacer-1) * 0.5);
  background-color: var(--white);
  border-radius: var(--border-radius-pill);
}
.dropzone .dz-preview .dz-error-mark[data-color-theme=dark], [data-color-theme=dark] .dropzone .dz-preview .dz-error-mark:not([data-color-theme]), html[data-color-theme=dark] .dropzone .dz-preview .dz-error-mark,
.dropzone .dz-preview .dz-success-mark[data-color-theme=dark],
[data-color-theme=dark] .dropzone .dz-preview .dz-success-mark:not([data-color-theme]),
html[data-color-theme=dark] .dropzone .dz-preview .dz-success-mark {
  color-scheme: dark;
  background-color: #2c2d33;
}
.dropzone .dz-preview .dz-error-mark:after,
.dropzone .dz-preview .dz-success-mark:after {
  font-family: var(--icon-font-family);
  display: block;
  font-size: var(--icon-font-size);
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.dropzone .dz-preview .dz-error-mark span,
.dropzone .dz-preview .dz-error-mark svg,
.dropzone .dz-preview .dz-success-mark span,
.dropzone .dz-preview .dz-success-mark svg {
  display: none;
}
.dropzone .dz-preview .dz-error-mark {
  color: var(--danger);
}
.dropzone .dz-preview .dz-error-mark:after {
  content: "\f62d";
}
.dropzone .dz-preview .dz-success-mark {
  color: var(--success);
}
.dropzone .dz-preview .dz-success-mark:after {
  content: "\f33f";
}
.dropzone .dz-preview.dz-error .dz-error-mark, .dropzone .dz-preview.dz-success .dz-success-mark {
  display: block;
  opacity: 1;
}
.dropzone .dz-preview .dz-progress {
  position: absolute;
  bottom: var(--dz-preview-spacer);
  left: var(--dz-preview-spacer);
  right: var(--dz-preview-spacer);
  height: calc(var(--dz-preview-spacer) * 0.5);
  display: none;
}
.dropzone .dz-preview .dz-progress .dz-upload {
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background-color: var(--success);
  transition: width ease-in-out var(--transition-base-timer);
  border-radius: var(--border-radius);
}
@media (prefers-reduced-motion: reduce) {
  .dropzone .dz-preview .dz-progress .dz-upload {
    transition: none;
  }
}
.dropzone .dz-preview.dz-processing .dz-progress {
  display: block;
}
.dropzone .dz-preview.dz-success .dz-progress {
  display: block;
  opacity: 0;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .dropzone .dz-preview.dz-success .dz-progress {
    transition: none;
  }
}
.dropzone .dz-preview .dz-error-message {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-pack: center;
      justify-content: center;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  padding: var(--dz-preview-spacer);
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .dropzone .dz-preview .dz-error-message {
    transition: none;
  }
}
.dropzone .dz-preview.dz-error:hover .dz-error-message {
  opacity: 1;
}
.dropzone .dz-preview.dz-error .dz-progress .dz-upload {
  background-color: var(--danger);
}
.dropzone .dz-preview.dz-image-preview:hover .dz-details img {
  display: block;
  opacity: 0.1;
}
.dropzone .dz-preview .dz-image {
  width: var(--dz-preview-width);
  height: var(--dz-preview-width);
  overflow: hidden;
  border-radius: calc(var(--border-radius) - var(--border-width));
}
.dropzone .dz-preview .dz-image img {
  width: 100%;
  transition: opacity ease-in-out var(--transition-base-timer), -webkit-filter ease-in-out var(--transition-base-timer);
  transition: opacity ease-in-out var(--transition-base-timer), filter ease-in-out var(--transition-base-timer);
  transition: opacity ease-in-out var(--transition-base-timer), filter ease-in-out var(--transition-base-timer), -webkit-filter ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .dropzone .dz-preview .dz-image img {
    transition: none;
  }
}
.dropzone .dz-preview a.dz-remove {
  position: relative;
  margin-top: var(--dz-preview-spacer);
  color: var(--body-color);
  background-color: var(--gray-200);
  border: var(--border-width) solid var(--gray-400);
  display: block;
  text-align: center;
  padding: var(--spacer-1) var(--spacer-2);
  cursor: pointer;
  z-index: 21;
  transition: background-color ease-in-out var(--transition-base-timer);
  border-radius: var(--border-radius);
}
@media (prefers-reduced-motion: reduce) {
  .dropzone .dz-preview a.dz-remove {
    transition: none;
  }
}
.dropzone .dz-preview a.dz-remove:hover, .dropzone .dz-preview a.dz-remove:focus {
  color: var(--body-color);
  border-color: var(--gray-500);
  background-color: var(--gray-300);
}
.dropzone .dz-preview a.dz-remove:active {
  color: var(--body-color);
  border-color: var(--gray-600);
  background-color: var(--gray-400);
}

/* ------------------------------------------------------------------------------
*
*  # Noty notifications
*
*  Styles for noty.min.js - A dependency-free notification library
*
* ---------------------------------------------------------------------------- */
.noty_layout {
  --noty-spacer-y: 0.25rem;
  --noty-gutter-y: var(--spacer);
  --noty-gutter-x: var(--spacer);
  --noty-padding-y: calc(var(--spacer) * 0.8);
  --noty-padding-x: var(--spacer);
  --noty-width: 20rem;
  --noty-bg: var(--black);
  --noty-color: var(--white);
  --noty-border-width: var(--border-width);
  --noty-border-color: transparent;
  --noty-progress-height: 0.1875rem;
  --noty-progress-bg: rgba(var(--black-rgb), 0.25);
  --noty-shadow: var(--box-shadow-lg);
  --noty-border-radius: var(--border-radius);
}
.noty_layout[data-color-theme=dark], [data-color-theme=dark] .noty_layout:not([data-color-theme]), html[data-color-theme=dark] .noty_layout {
  color-scheme: dark;
  --noty-bg: #494c55;
}

.noty_bar {
  position: relative;
  border: var(--noty-border-width) solid var(--noty-border-color);
  -webkit-backface-visibility: hidden;
  background-color: var(--noty-bg);
  color: var(--noty-color);
  -webkit-transform: translate(0, 0) scale(1, 1);
          transform: translate(0, 0) scale(1, 1);
  -webkit-font-smoothing: subpixel-antialiased;
  box-shadow: var(--noty-shadow);
  border-radius: var(--noty-border-radius);
}

.noty_body {
  padding: var(--noty-padding-y) var(--noty-padding-x);
}
.noty_close_with_button .noty_body {
  padding-right: calc(var(--noty-padding-x) * 2);
}

.noty_buttons {
  padding: var(--noty-padding-y) var(--noty-padding-x);
  padding-top: 0;
  text-align: right;
}

.noty_layout_mixin, #noty_layout__bottomRight, #noty_layout__bottomCenter, #noty_layout__bottomLeft, #noty_layout__bottom, #noty_layout__centerRight, #noty_layout__centerLeft, #noty_layout__center, #noty_layout__topRight, #noty_layout__topCenter, #noty_layout__topLeft, #noty_layout__top {
  position: fixed;
  margin: 0;
  padding: 0;
  z-index: 1055;
  -webkit-transform: translateZ(0) scale(1, 1);
          transform: translateZ(0) scale(1, 1);
  -webkit-filter: blur(0);
          filter: blur(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
  max-width: 90%;
}

#noty_layout__top {
  top: 0;
  left: 5%;
  width: 90%;
}
#noty_layout__top .noty_bar:first-child {
  margin-top: var(--noty-gutter-y);
}

#noty_layout__topLeft {
  top: var(--noty-gutter-y);
  left: var(--noty-gutter-x);
  width: var(--noty-width);
}

#noty_layout__topCenter {
  top: 5%;
  left: 50%;
  width: var(--noty-width);
  -webkit-transform: translate(-50%) translateZ(0) scale(1, 1);
          transform: translate(-50%) translateZ(0) scale(1, 1);
}

#noty_layout__topRight {
  top: var(--noty-gutter-y);
  right: var(--noty-gutter-x);
  width: var(--noty-width);
}

#noty_layout__center {
  top: 50%;
  left: 50%;
  width: var(--noty-width);
  -webkit-transform: translate(-50%, -50%) translateZ(0) scale(1, 1);
          transform: translate(-50%, -50%) translateZ(0) scale(1, 1);
}

#noty_layout__centerLeft {
  top: 50%;
  left: var(--noty-gutter-x);
  width: var(--noty-width);
  -webkit-transform: translate(0, -50%) translateZ(0) scale(1, 1);
          transform: translate(0, -50%) translateZ(0) scale(1, 1);
}

#noty_layout__centerRight {
  top: 50%;
  right: var(--noty-gutter-x);
  width: var(--noty-width);
  -webkit-transform: translate(0, -50%) translateZ(0) scale(1, 1);
          transform: translate(0, -50%) translateZ(0) scale(1, 1);
}

#noty_layout__bottom {
  bottom: 0;
  left: 5%;
  width: 90%;
}
#noty_layout__bottom .noty_bar:last-child {
  margin-bottom: var(--noty-gutter-y);
}

#noty_layout__bottomLeft {
  bottom: var(--noty-gutter-y);
  left: var(--noty-gutter-x);
  width: var(--noty-width);
}

#noty_layout__bottomCenter {
  bottom: 5%;
  left: 50%;
  width: var(--noty-width);
  -webkit-transform: translate(calc(-50% - var(--noty-border-width))) translateZ(0) scale(1, 1);
          transform: translate(calc(-50% - var(--noty-border-width))) translateZ(0) scale(1, 1);
}

#noty_layout__bottomRight {
  bottom: var(--noty-gutter-y);
  right: var(--noty-gutter-x);
  width: var(--noty-width);
}

.noty_progressbar {
  display: none;
}
.noty_has_timeout.noty_has_progressbar .noty_progressbar {
  display: block;
  position: absolute;
  left: 0;
  bottom: 0;
  height: var(--noty-progress-height);
  width: 100%;
  background-color: var(--noty-progress-bg);
}

.noty_effects_open {
  opacity: 0;
  -webkit-transform: translate(50%);
          transform: translate(50%);
  -webkit-animation: noty_anim_in 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
          animation: noty_anim_in 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
}

.noty_effects_close {
  -webkit-animation: noty_anim_out 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
          animation: noty_anim_out 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
}

.noty_fix_effects_height {
  -webkit-animation: noty_anim_height 75ms ease-out;
          animation: noty_anim_height 75ms ease-out;
}

.noty_close_with_click {
  cursor: pointer;
}

.noty_close_button {
  position: absolute;
  top: var(--noty-padding-y);
  right: var(--noty-padding-x);
  font-size: var(--icon-font-size);
  background-color: transparent;
  color: inherit;
  text-align: center;
  line-height: 1;
  cursor: pointer;
  opacity: 0.75;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .noty_close_button {
    transition: none;
  }
}
.noty_close_button:hover {
  opacity: 1;
}

.noty_modal {
  --noty-backdrop-bg: var(--black);
  --noty-backdrop-opacity: 0.35;
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: var(--noty-backdrop-bg);
  z-index: 1050;
  opacity: var(--noty-backdrop-opacity);
  left: 0;
  top: 0;
}
.noty_modal.noty_modal_open {
  opacity: 0;
  -webkit-animation: noty_modal_in 0.3s ease-out;
          animation: noty_modal_in 0.3s ease-out;
}
.noty_modal.noty_modal_close {
  -webkit-animation: noty_modal_out 0.3s ease-out;
          animation: noty_modal_out 0.3s ease-out;
  -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
}

@-webkit-keyframes noty_modal_in {
  100% {
    opacity: var(--noty-backdrop-opacity);
  }
}

@keyframes noty_modal_in {
  100% {
    opacity: var(--noty-backdrop-opacity);
  }
}
@-webkit-keyframes noty_modal_out {
  100% {
    opacity: 0;
  }
}
@keyframes noty_modal_out {
  100% {
    opacity: 0;
  }
}
@-webkit-keyframes noty_anim_in {
  100% {
    -webkit-transform: translate(0);
            transform: translate(0);
    opacity: 1;
  }
}
@keyframes noty_anim_in {
  100% {
    -webkit-transform: translate(0);
            transform: translate(0);
    opacity: 1;
  }
}
@-webkit-keyframes noty_anim_out {
  100% {
    -webkit-transform: translate(50%);
            transform: translate(50%);
    opacity: 0;
  }
}
@keyframes noty_anim_out {
  100% {
    -webkit-transform: translate(50%);
            transform: translate(50%);
    opacity: 0;
  }
}
@-webkit-keyframes noty_anim_height {
  100% {
    height: 0;
  }
}
@keyframes noty_anim_height {
  100% {
    height: 0;
  }
}
.noty_theme__limitless.noty_bar {
  margin: var(--noty-spacer-y) 0;
  position: relative;
}
.noty_theme__limitless.noty_type__warning {
  --noty-bg: var(--warning);
}
.noty_theme__limitless.noty_type__error {
  --noty-bg: var(--danger);
}
.noty_theme__limitless.noty_type__info {
  --noty-bg: var(--primary);
}
.noty_theme__limitless.noty_type__success {
  --noty-bg: var(--success);
}
.noty_theme__limitless.noty_type__confirm {
  --noty-bg: var(--white);
  --noty-border-color: var(--border-color-translucent);
  --noty-shadow: var(--box-shadow-sm);
  background-clip: padding-box;
}
.noty_theme__limitless.noty_type__confirm[data-color-theme=dark], [data-color-theme=dark] .noty_theme__limitless.noty_type__confirm:not([data-color-theme]), html[data-color-theme=dark] .noty_theme__limitless.noty_type__confirm {
  color-scheme: dark;
  --noty-bg: #2c2d33;
}

/* ------------------------------------------------------------------------------
*
*  # Sweet Alerts component
*
*  Styles for sweet_alert.min.js - notification library
*
* ---------------------------------------------------------------------------- */
.swal2-shown.swal2-no-backdrop .swal2-container {
  background-color: transparent;
  box-shadow: var(--box-shadow);
}

.swal2-container {
  --swal-gutter-y: var(--spacer);
  --swal-gutter-x: var(--spacer);
  --swal-bg: var(--white);
  --swal-padding: 1.25rem;
  --swal-width: 31.25rem;
  --swal-max-width: 350px;
  --swal-margin-x: 0.625rem;
  --swal-content-margin: var(--spacer-1) var(--spacer-2) 0 var(--spacer-2);
  --swal-icon-border-width: 0.25rem;
  --swal-icon-size: 5rem;
  --swal-icon-spacer: 1rem;
  --swal-success-color: var(--success);
  --swal-warning-color: var(--warning);
  --swal-error-color: var(--danger);
  --swal-info-color: var(--primary);
  --swal-question-color: var(--secondary);
  --swal-progress-height: 0.25rem;
  --swal-progress-color: rgba(var(--black-rgb), 0.25);
  display: grid;
  grid-template-areas: "top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";
  grid-template-rows: minmax(-webkit-min-content, auto) minmax(-webkit-min-content, auto) minmax(-webkit-min-content, auto);
  grid-template-rows: minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  padding: var(--swal-gutter-y) var(--swal-gutter-x);
  z-index: 1080;
  transition: background-color ease-in-out var(--transition-base-timer);
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}
.swal2-container[data-color-theme=dark], [data-color-theme=dark] .swal2-container:not([data-color-theme]), html[data-color-theme=dark] .swal2-container {
  color-scheme: dark;
  --swal-bg: #2c2d33;
  --swal-progress-color: rgba(var(--white-rgb), 0.25);
}
.swal2-container.swal2-backdrop-show, .swal2-container.swal2-noanimation {
  background-color: rgba(var(--black-rgb), 0.35);
}
.swal2-container.swal2-backdrop-hide {
  background-color: transparent !important;
}
.swal2-container.swal2-top-start, .swal2-container.swal2-center-start, .swal2-container.swal2-bottom-start {
  grid-template-columns: minmax(0, 1fr) auto auto;
}
.swal2-container.swal2-top, .swal2-container.swal2-center, .swal2-container.swal2-bottom {
  grid-template-columns: auto minmax(0, 1fr) auto;
}
.swal2-container.swal2-top-end, .swal2-container.swal2-center-end, .swal2-container.swal2-bottom-end {
  grid-template-columns: auto auto minmax(0, 1fr);
}
.swal2-container.swal2-top-start > .swal2-popup {
  -ms-flex-item-align: start;
      align-self: start;
}
.swal2-container.swal2-top > .swal2-popup {
  grid-column: 2;
  align-self: start;
  justify-self: center;
}
.swal2-container.swal2-top-end > .swal2-popup, .swal2-container.swal2-top-right > .swal2-popup {
  grid-column: 3;
  align-self: start;
  justify-self: end;
}
.swal2-container.swal2-center-start > .swal2-popup, .swal2-container.swal2-center-left > .swal2-popup {
  grid-row: 2;
  align-self: center;
}
.swal2-container.swal2-center > .swal2-popup {
  grid-column: 2;
  grid-row: 2;
  align-self: center;
  justify-self: center;
}
.swal2-container.swal2-center-end > .swal2-popup, .swal2-container.swal2-center-right > .swal2-popup {
  grid-column: 3;
  grid-row: 2;
  align-self: center;
  justify-self: end;
}
.swal2-container.swal2-bottom-start > .swal2-popup, .swal2-container.swal2-bottom-left > .swal2-popup {
  grid-column: 1;
  grid-row: 3;
  align-self: end;
}
.swal2-container.swal2-bottom > .swal2-popup {
  grid-column: 2;
  grid-row: 3;
  justify-self: center;
  align-self: end;
}
.swal2-container.swal2-bottom-end > .swal2-popup, .swal2-container.swal2-bottom-right > .swal2-popup {
  grid-column: 3;
  grid-row: 3;
  align-self: end;
  justify-self: end;
}
.swal2-container.swal2-grow-row > .swal2-popup, .swal2-container.swal2-grow-fullscreen > .swal2-popup {
  grid-column: 1/4;
  width: 100%;
}
.swal2-container.swal2-grow-column > .swal2-popup, .swal2-container.swal2-grow-fullscreen > .swal2-popup {
  grid-row: 1/4;
  align-self: stretch;
  -ms-flex-line-pack: center;
      align-content: center;
}

.swal2-no-transition {
  transition: none !important;
}

.swal2-popup {
  grid-template-columns: minmax(0, 100%);
  background-color: var(--swal-bg);
  display: none;
  position: relative;
  max-width: 100%;
  padding: var(--swal-padding);
  width: var(--swal-width);
  margin-left: var(--swal-margin-x);
  margin-right: var(--swal-margin-x);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}
.swal2-popup:focus {
  outline: none;
}
.swal2-popup.swal2-loading {
  overflow-y: hidden;
}
@media (min-width: 576px) {
  .swal2-popup {
    margin-left: 0;
    margin-right: 0;
  }
}

.swal2-title {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.5715;
  text-align: center;
  position: relative;
  word-wrap: break-word;
  margin-top: var(--spacer-1);
  margin-bottom: 0;
}

.swal2-html-container {
  z-index: 1;
  text-align: center;
  position: relative;
  word-wrap: break-word;
  margin: var(--swal-content-margin);
  word-wrap: break-word;
  word-break: break-word;
}

.swal2-footer {
  -ms-flex-pack: center;
      justify-content: center;
  margin-top: var(--spacer);
  padding: var(--spacer-2) var(--spacer);
  border-top: var(--border-width) solid var(--border-color);
}

.swal2-actions {
  display: -ms-flexbox;
  display: flex;
  z-index: 1;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  width: 100%;
  margin-top: var(--spacer);
}
.swal2-actions > button + button {
  margin-left: calc(var(--spacer) * 0.5);
}
.swal2-actions:not(.swal2-loading) .swal2-styled[disabled] {
  cursor: no-drop;
  opacity: 0.4;
}

.swal2-loader {
  display: none;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  width: var(--icon-font-size);
  height: var(--icon-font-size);
  margin: 0 var(--spacer);
  -webkit-animation: rotation 1.5s linear 0s infinite normal;
          animation: rotation 1.5s linear 0s infinite normal;
  border-width: 2px;
  border-style: solid;
  border-radius: 100%;
  border-color: var(--primary) transparent var(--primary) transparent;
}

.swal2-close {
  background: transparent;
  border: 0;
  margin: 0;
  padding: 0;
  font-size: 1.5rem;
  color: var(--body-color);
  line-height: 1;
  position: absolute;
  top: 1rem;
  right: 1rem;
  cursor: pointer;
  opacity: 0.5;
  width: 1.5rem;
  height: 1.5rem;
  transition: opacity ease-in-out 0.15s;
}
@media (prefers-reduced-motion: reduce) {
  .swal2-close {
    transition: none;
  }
}
.swal2-close:hover, .swal2-close:focus {
  opacity: 1;
  outline: 0;
}

.swal2-timer-progress-bar {
  width: 100%;
  height: var(--swal-progress-height);
  background: var(--swal-progress-color);
}
.swal2-timer-progress-bar-container {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  grid-column: auto !important;
  overflow: hidden;
  border-bottom-right-radius: var(--border-radius-pill);
  border-bottom-left-radius: var(--border-radius-pill);
}

.swal2-input,
.swal2-file,
.swal2-textarea,
.swal2-select,
.swal2-radio,
.swal2-checkbox {
  display: none;
  margin: var(--spacer) auto 0 auto;
}

.swal2-popup .select2-container,
.swal2-popup .btn-group,
.swal2-popup .multiselect-native-select {
  margin-top: var(--spacer);
}

.swal2-checkbox {
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
}
.swal2-checkbox > span:not(.switchery) {
  margin-left: var(--spacer-2);
}

.swal2-radio {
  -ms-flex-pack: center;
      justify-content: center;
}
.swal2-radio label {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
      align-items: center;
}
.swal2-radio label + label {
  margin-left: var(--spacer);
}
.swal2-radio label input,
.swal2-radio label .uniform-choice {
  margin-right: var(--spacer-2);
}

.swal2-range {
  margin-top: var(--spacer);
  -ms-flex-pack: center;
      justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.swal2-range input {
  display: block;
  width: 100%;
}
.swal2-range output {
  font-size: 1rem;
  font-weight: 600;
  margin-top: calc(var(--spacer) * 0.5);
}

.swal2-inputerror, .swal2-inputerror:hover, .swal2-inputerror:focus {
  border-color: var(--danger) !important;
}

.swal2-validation-message {
  overflow: hidden;
  display: none;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  margin-left: 0 !important;
  margin-right: 0 !important;
  color: var(--danger);
  position: relative;
  padding-left: calc(var(--icon-font-size) + var(--spacer-2));
}
.swal2-validation-message:before {
  content: "\f62d";
  font-family: var(--icon-font-family);
  font-size: var(--icon-font-size);
  position: absolute;
  top: calc((var(--body-line-height-computed) - var(--icon-font-size)) * 0.5);
  left: 0;
  display: inline-block;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.swal2-image {
  margin: calc(var(--spacer) * 0.5) auto;
  max-width: 100%;
  height: auto;
}

.swal2-icon {
  border: var(--swal-icon-border-width) solid transparent;
  margin: calc(var(--spacer) * 0.5) auto var(--spacer) auto;
  padding: 0;
  position: relative;
  box-sizing: content-box;
  cursor: default;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: var(--swal-icon-size);
  height: var(--swal-icon-size);
  border-radius: 50%;
}
.swal2-icon.swal2-success {
  border-color: var(--swal-success-color);
}
.swal2-icon.swal2-success [class^=swal2-success-line][class$=tip] {
  height: calc(var(--swal-icon-size) * 0.5);
  width: calc(var(--swal-icon-size) * 0.25);
  /*rtl:ignore*/
  border-right: var(--swal-icon-border-width) solid var(--swal-success-color);
  border-top: var(--swal-icon-border-width) solid var(--swal-success-color);
  position: absolute;
  /*rtl:ignore*/
  left: calc(var(--swal-icon-size) * 0.25);
  top: calc(var(--swal-icon-size) * 0.5 + var(--swal-icon-border-width) * 0.5);
  opacity: 1;
  -webkit-animation: animate-checkmark ease 0.75s;
          animation: animate-checkmark ease 0.75s;
  /*rtl:begin:ignore*/
  -webkit-transform: scaleX(-1) rotate(135deg);
          transform: scaleX(-1) rotate(135deg);
  -webkit-transform-origin: left top;
          transform-origin: left top;
  /*rtl:end:ignore*/
}
.swal2-icon.swal2-error {
  border-color: var(--swal-error-color);
}
.swal2-icon.swal2-error .swal2-x-mark {
  position: relative;
  display: block;
  -ms-flex: 1;
      flex: 1;
}
.swal2-icon.swal2-error [class^=swal2-x-mark-line] {
  position: absolute;
  height: var(--swal-icon-border-width);
  width: calc(var(--swal-icon-size) - var(--swal-icon-spacer) * 2);
  background-color: var(--swal-error-color);
  display: block;
  top: calc((var(--swal-icon-size) - var(--swal-icon-border-width)) * 0.5);
}
.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  left: var(--swal-icon-spacer);
}
.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  right: var(--swal-icon-spacer);
}
.swal2-icon.swal2-warning {
  color: var(--swal-warning-color);
  border-color: var(--swal-warning-color);
  font-size: calc(var(--swal-icon-size) - var(--swal-icon-spacer));
  line-height: var(--swal-icon-size);
  -ms-flex-pack: center;
      justify-content: center;
}
.swal2-icon.swal2-info {
  color: var(--swal-info-color);
  border-color: var(--swal-info-color);
  font-size: calc(var(--swal-icon-size) - var(--swal-icon-spacer));
  line-height: var(--swal-icon-size);
  -ms-flex-pack: center;
      justify-content: center;
}
.swal2-icon.swal2-question {
  color: var(--swal-question-color);
  border-color: var(--swal-question-color);
  font-size: calc(var(--swal-icon-size) - var(--swal-icon-spacer));
  line-height: calc(var(--swal-icon-size) + var(--swal-icon-border-width));
  -ms-flex-pack: center;
      justify-content: center;
}

.swal2-progress-steps {
  --swal-step-distance: 2.5em;
  --swal-step-line-size: calc(var(--border-width) * 2);
  --swal-step-line-color: var(--gray-300);
  --swal-step-padding-y: 0.5rem;
  --swal-step-padding-x: 0.875rem;
  --swal-active-step-bg: var(--component-active-bg);
  --swal-active-step-color: var(--component-active-color);
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  margin-top: var(--spacer-2);
  margin-bottom: var(--spacer);
  padding: 0;
  font-weight: 600;
}
.swal2-progress-steps li {
  display: inline-block;
  position: relative;
}
.swal2-progress-steps .swal2-progress-step {
  z-index: 20;
  background: var(--swal-active-step-bg);
  color: var(--swal-active-step-color);
  text-align: center;
  padding: var(--swal-step-padding-y) var(--swal-step-padding-x);
  border: var(--swal-step-line-size) solid var(--swal-active-step-bg);
  min-width: calc(var(--body-line-height-computed) + var(--swal-step-padding-y) * 2 + var(--swal-step-line-size) * 2);
  border-radius: var(--border-radius-pill);
}
.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {
  background-color: var(--swal-active-step-bg);
}
.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step {
  background-color: transparent;
  color: var(--body-color);
  border-color: var(--swal-step-line-color);
}
.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {
  background-color: var(--swal-step-line-color);
}
.swal2-progress-steps .swal2-progress-step-line {
  z-index: 10;
  width: var(--swal-step-distance);
  height: var(--swal-step-line-size);
  background-color: var(--swal-active-step-bg);
}

body.swal2-toast-shown .swal2-container {
  background-color: transparent;
  max-width: 100%;
  width: var(--swal-max-width);
  pointer-events: none;
}
body.swal2-toast-shown .swal2-container.swal2-top {
  top: 0;
  right: auto;
  bottom: auto;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-top-end, body.swal2-toast-shown .swal2-container.swal2-top-right {
  top: 0;
  right: 0;
  bottom: auto;
  left: auto;
}
body.swal2-toast-shown .swal2-container.swal2-top-start, body.swal2-toast-shown .swal2-container.swal2-top-left {
  top: 0;
  right: auto;
  bottom: auto;
  left: 0;
}
body.swal2-toast-shown .swal2-container.swal2-center-start, body.swal2-toast-shown .swal2-container.swal2-center-left {
  top: 50%;
  right: auto;
  bottom: auto;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-center {
  top: 50%;
  right: auto;
  bottom: auto;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
body.swal2-toast-shown .swal2-container.swal2-center-end, body.swal2-toast-shown .swal2-container.swal2-center-right {
  top: 50%;
  right: 0;
  bottom: auto;
  left: auto;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-bottom-start, body.swal2-toast-shown .swal2-container.swal2-bottom-left {
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
}
body.swal2-toast-shown .swal2-container.swal2-bottom {
  top: auto;
  right: auto;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-bottom-end, body.swal2-toast-shown .swal2-container.swal2-bottom-right {
  top: auto;
  right: 0;
  bottom: 0;
  left: auto;
}

.swal2-popup.swal2-toast {
  --swal-bg: var(--white);
  --swal-padding: 0.75rem 1rem;
  --swal-content-margin: var(--spacer-1) 0 var(--spacer-1) var(--spacer-2);
  --swal-icon-border-width: 0.125rem;
  --swal-icon-size: 1.75rem;
  --swal-icon-spacer: 0.5rem;
  overflow-y: hidden;
  grid-column: 1/4 !important;
  grid-row: 1/4 !important;
  grid-template-columns: 1fr 99fr 1fr;
  pointer-events: all;
  margin: 0;
}
.swal2-popup.swal2-toast[data-color-theme=dark], [data-color-theme=dark] .swal2-popup.swal2-toast:not([data-color-theme]), html[data-color-theme=dark] .swal2-popup.swal2-toast {
  color-scheme: dark;
  --swal-bg: #383940;
}
.swal2-popup.swal2-toast > * {
  grid-column: 2;
}
.swal2-popup.swal2-toast .swal2-title {
  margin: 0 var(--spacer-2);
}
.swal2-popup.swal2-toast .swal2-footer {
  margin: 0 0 0 var(--spacer-2);
  padding: var(--swal-toast-padding-y) var(--swal-toast-padding-x);
}
.swal2-popup.swal2-toast .swal2-close {
  position: static;
  grid-column: 3/3;
  grid-row: 1/99;
}
.swal2-popup.swal2-toast .swal2-html-container {
  text-align: initial;
}
.swal2-popup.swal2-toast .swal2-html-container:empty {
  padding: 0;
}
.swal2-popup.swal2-toast .swal2-loader {
  grid-column: 1;
  grid-row: 1/99;
  align-self: center;
}
.swal2-popup.swal2-toast .swal2-icon {
  grid-column: 1;
  grid-row: 1/99;
  align-self: center;
  min-width: var(--swal-icon-size);
  margin: 0;
}
.swal2-popup.swal2-toast .swal2-icon:before {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
}
.swal2-popup.swal2-toast .swal2-actions {
  -ms-flex-preferred-size: auto !important;
      flex-basis: auto !important;
  width: auto;
  height: auto;
  margin: 0 0 0 var(--spacer);
}

[class^=swal2] {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.swal2-show {
  -webkit-animation: show-notification var(--transition-base-timer);
          animation: show-notification var(--transition-base-timer);
}

.swal2-hide {
  -webkit-animation: hide-notification var(--transition-base-timer) forwards;
          animation: hide-notification var(--transition-base-timer) forwards;
}

.swal2-noanimation {
  -webkit-animation: none;
          animation: none;
  transition: none;
}

.swal2-animate-success-icon,
.swal2-animate-error-icon,
.swal2-warning,
.swal2-info,
.swal2-question {
  -webkit-animation: animate-circle 0.5s;
          animation: animate-circle 0.5s;
}

.swal2-animate-x-mark {
  -webkit-animation: animate-x-mark 0.5s;
          animation: animate-x-mark 0.5s;
}

@-webkit-keyframes show-notification {
  0% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}

@keyframes show-notification {
  0% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@-webkit-keyframes hide-notification {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
    opacity: 0;
  }
}
@keyframes hide-notification {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
    opacity: 0;
  }
}
@-webkit-keyframes animate-checkmark {
  0% {
    height: 0;
    width: 0;
    opacity: 0;
  }
  30% {
    height: 0;
    width: calc(var(--swal-icon-size) * 0.25);
    opacity: 0;
  }
  60% {
    height: calc(var(--swal-icon-size) * 0.5);
    width: calc(var(--swal-icon-size) * 0.25);
    opacity: 1;
  }
  100% {
    height: calc(var(--swal-icon-size) * 0.5);
    width: calc(var(--swal-icon-size) * 0.25);
    opacity: 1;
  }
}
@keyframes animate-checkmark {
  0% {
    height: 0;
    width: 0;
    opacity: 0;
  }
  30% {
    height: 0;
    width: calc(var(--swal-icon-size) * 0.25);
    opacity: 0;
  }
  60% {
    height: calc(var(--swal-icon-size) * 0.5);
    width: calc(var(--swal-icon-size) * 0.25);
    opacity: 1;
  }
  100% {
    height: calc(var(--swal-icon-size) * 0.5);
    width: calc(var(--swal-icon-size) * 0.25);
    opacity: 1;
  }
}
@-webkit-keyframes animate-circle {
  0% {
    color: transparent;
    border-color: transparent;
  }
  25% {
    color: transparent;
  }
}
@keyframes animate-circle {
  0% {
    color: transparent;
    border-color: transparent;
  }
  25% {
    color: transparent;
  }
}
@-webkit-keyframes animate-x-mark {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes animate-x-mark {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* ------------------------------------------------------------------------------
 *
 *  # NoUI slider
 *
 *  Styles for nouislider.min.js - range slider plugin
 *
 * ---------------------------------------------------------------------------- */
.noUi-target {
  --noui-bg: var(--gray-300);
  --noui-box-shadow: inset 0 1px 1px rgba(var(--black-rgb), 0.1);
  --noui-connect-bg: var(--primary);
  --noui-height: 0.375rem;
  --noui-vertical-height: 10rem;
  --noui-handle-size: calc(0.375rem * 3.5);
  --noui-handle-bg: var(--white);
  --noui-handle-inner-bg: var(--black);
  --noui-handle-inner-hover-bg: var(--primary);
  --noui-handle-border-width: var(--border-width);
  --noui-handle-border-color: rgba(var(--black-rgb), 0.25);
  --noui-pips-spacer-y: 1rem;
  --noui-pips-spacer-x: 1rem;
  --noui-pips-color: var(--gray-700);
  --noui-pips-font-size: var(--body-font-size-sm);
  --noui-pips-marker-width: 0.0625rem;
  --noui-pips-marker-height: 0.1875rem;
  position: relative;
  background-color: var(--noui-bg);
  border-radius: var(--border-radius-pill);
  box-shadow: var(--noui-box-shadow);
}
.noUi-target, .noUi-target * {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.noUi-base,
.noUi-connects {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 100%;
}

.noUi-connects {
  overflow: hidden;
  z-index: 0;
  border-radius: var(--border-radius-pill);
}

.noUi-connect,
.noUi-origin {
  will-change: transform;
  position: absolute;
  z-index: 1;
  top: 0;
  /*rtl:begin:ignore*/
  left: 0;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  /*rtl:end:ignore*/
}

.noUi-state-drag * {
  cursor: inherit !important;
}

.noUi-connect {
  background-color: var(--noui-connect-bg);
  width: 100%;
  height: 100%;
}

.noUi-origin {
  width: 100%;
  height: 100%;
}

[disabled] .noUi-target {
  opacity: 0.65;
}

.noUi-horizontal {
  height: var(--noui-height);
}
.noUi-horizontal .noUi-handle {
  top: calc(var(--noui-handle-size) * 0.5 * -1 + var(--noui-height) * 0.5);
  right: calc(var(--noui-handle-size) * 0.5 * -1);
}
.noUi-horizontal .noUi-origin {
  height: 0;
  left: auto;
  right: 0;
}
.noUi-horizontal.has-pips {
  margin-bottom: calc(var(--noui-pips-spacer-y) + var(--body-line-height-computed));
}

.noUi-vertical {
  display: inline-block;
  width: var(--noui-height);
  height: var(--noui-vertical-height);
}
.noUi-vertical + .noUi-vertical {
  margin-left: calc(var(--spacer) * 1.5);
}
.noUi-vertical .noUi-handle {
  top: calc(var(--noui-handle-size) * 0.5 * -1);
  /*rtl:ignore*/
  left: calc(var(--noui-handle-size) * 0.5 * -1 + var(--noui-height) * 0.5);
}
.noUi-vertical .noUi-origin {
  width: 0;
}
.noUi-vertical.has-pips {
  margin-right: calc(var(--noui-pips-spacer-y) + var(--body-line-height-computed));
}

.noUi-draggable {
  cursor: w-resize;
}
.noUi-vertical .noUi-draggable {
  cursor: n-resize;
}

.noUi-touch-area {
  width: 100%;
  height: 100%;
}

.noUi-handle {
  background-color: var(--noui-handle-bg);
  cursor: pointer;
  top: calc(var(--noui-height) * -1);
  position: absolute;
  z-index: 1;
  border: var(--noui-handle-border-width) solid var(--noui-handle-border-color);
  outline: 0;
  width: var(--noui-handle-size);
  height: var(--noui-handle-size);
  background-clip: content-box;
  border-radius: var(--border-radius-pill);
}
.noUi-handle, .noUi-handle:after {
  transition: all ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .noUi-handle, .noUi-handle:after {
    transition: none;
  }
}
.noUi-handle:after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: calc(var(--noui-height) * 0.5 * -1);
  margin-left: calc(var(--noui-height) * 0.5 * -1);
  background-color: var(--noui-handle-inner-bg);
  width: var(--noui-height);
  height: var(--noui-height);
  border-radius: var(--border-radius-pill);
}
.noUi-handle:hover:after, .noUi-handle:focus:after {
  background-color: var(--noui-handle-inner-hover-bg);
}

.noui-slider-white .noUi-handle:after {
  content: none;
}

.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
  transition: -webkit-transform var(--transition-base-timer);
  transition: transform var(--transition-base-timer);
  transition: transform var(--transition-base-timer), -webkit-transform var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
    transition: none;
  }
}

.noui-slider-lg {
  --noui-height: 0.5rem;
}

.noui-slider-sm {
  --noui-height: 0.25rem;
}

.noui-slider-solid {
  --noui-handle-bg: var(--primary);
  --noui-handle-border-color: var(--primary);
}
.noui-slider-solid .noUi-handle:after {
  background-color: var(--white);
}
.noui-slider-solid .noUi-handle:hover:after, .noui-slider-solid .noUi-handle:focus:after {
  opacity: 0.75;
}
.noui-slider-solid.noui-slider-secondary {
  --noui-handle-bg: var(--secondary);
  --noui-handle-border-color: var(--secondary);
}
.noui-slider-solid.noui-slider-danger {
  --noui-handle-bg: var(--danger);
  --noui-handle-border-color: var(--danger);
}
.noui-slider-solid.noui-slider-success {
  --noui-handle-bg: var(--success);
  --noui-handle-border-color: var(--success);
}
.noui-slider-solid.noui-slider-warning {
  --noui-handle-bg: var(--warning);
  --noui-handle-border-color: var(--warning);
}
.noui-slider-solid.noui-slider-info {
  --noui-handle-bg: var(--info);
  --noui-handle-border-color: var(--info);
}

.noui-slider-secondary {
  --noui-handle-inner-hover-bg: var(--secondary);
  --noui-connect-bg: var(--secondary);
}

.noui-slider-danger {
  --noui-handle-inner-hover-bg: var(--danger);
  --noui-connect-bg: var(--danger);
}

.noui-slider-success {
  --noui-handle-inner-hover-bg: var(--success);
  --noui-connect-bg: var(--success);
}

.noui-slider-warning {
  --noui-handle-inner-hover-bg: var(--warning);
  --noui-connect-bg: var(--warning);
}

.noui-slider-info {
  --noui-handle-inner-hover-bg: var(--info);
  --noui-connect-bg: var(--info);
}

.noUi-tooltip {
  --noui-tooltip-bg: var(--black);
  --noui-tooltip-color: var(--white);
  --noui-tooltip-padding-y: calc(var(--spacer) * 0.4);
  --noui-tooltip-padding-x: calc(var(--spacer) * 0.6);
  --noui-tooltip-arrow-height: 0.4rem;
  position: absolute;
  background-color: var(--noui-tooltip-bg);
  color: var(--noui-tooltip-color);
  padding: var(--noui-tooltip-padding-y) var(--noui-tooltip-padding-x);
  bottom: var(--noui-tooltip-arrow-height);
  left: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0);
          transform: translate3d(-50%, -50%, 0);
  opacity: 0;
  visibility: hidden;
  border-radius: var(--border-radius);
  transition: all ease-in-out var(--transition-base-timer);
}
.noUi-tooltip[data-color-theme=dark], [data-color-theme=dark] .noUi-tooltip:not([data-color-theme]), html[data-color-theme=dark] .noUi-tooltip {
  color-scheme: dark;
  --noui-tooltip-color: var(--black);
  --noui-tooltip-bg: var(--white);
}
@media (prefers-reduced-motion: reduce) {
  .noUi-tooltip {
    transition: none;
  }
}
.noUi-tooltip:after {
  content: "";
  border: var(--noui-tooltip-arrow-height) solid transparent;
  border-top-color: var(--noui-tooltip-bg);
  position: absolute;
  left: 50%;
  bottom: calc(var(--noui-tooltip-arrow-height) * 2 * -1);
  -webkit-transform: translate3d(-50%, 0, 0);
          transform: translate3d(-50%, 0, 0);
  width: 0;
  height: 0;
}
.noUi-handle:hover .noUi-tooltip {
  opacity: 1;
  visibility: visible;
}

.noUi-pips {
  position: absolute;
  color: var(--noui-pips-color);
  font-size: var(--noui-pips-font-size);
}

.noUi-value {
  position: absolute;
  text-align: center;
}

.noUi-value-sub {
  opacity: 0.75;
}

.noUi-marker {
  position: absolute;
  background-color: var(--noui-pips-color);
}

.noUi-pips-horizontal {
  padding-top: calc(var(--noui-pips-spacer-y) * 0.5);
  height: calc(var(--noui-pips-spacer-y) + var(--body-line-height-computed));
  top: 100%;
  left: 0;
  width: 100%;
}

.noUi-value-horizontal {
  padding-top: var(--noui-pips-spacer-y);
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.noUi-value-horizontal.noUi-value-sub {
  padding-top: calc(var(--noui-pips-spacer-y) * 0.5);
}
.noUi-rtl .noUi-value-horizontal {
  /*rtl:begin:ignore*/
  -webkit-transform: translateX(50%);
          transform: translateX(50%);
  /*rtl:end:ignore*/
}

.noUi-marker-horizontal.noUi-marker {
  width: var(--noui-pips-marker-width);
  height: var(--noui-pips-marker-height);
}
.noUi-marker-horizontal.noUi-marker-sub {
  height: calc(var(--noui-pips-marker-height) * 2);
}
.noUi-marker-horizontal.noUi-marker-large {
  height: calc(var(--noui-pips-marker-height) * 3);
}

.noUi-pips-vertical {
  padding-left: calc(var(--noui-pips-spacer-x) * 0.5);
  height: 100%;
  top: 0;
  left: 100%;
}

.noUi-value-vertical {
  padding-left: var(--noui-pips-spacer-x);
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.noUi-rtl .noUi-value-vertical {
  /*rtl:begin:ignore*/
  -webkit-transform: translateY(50%);
          transform: translateY(50%);
  /*rtl:end:ignore*/
}

.noUi-marker-vertical.noUi-marker {
  width: var(--noui-pips-marker-height);
  height: var(--noui-pips-marker-width);
}
.noUi-marker-vertical.noUi-marker-sub {
  width: calc(var(--noui-pips-marker-height) * 2);
}
.noUi-marker-vertical.noUi-marker-large {
  width: calc(var(--noui-pips-marker-height) * 3);
}

/* ------------------------------------------------------------------------------
*
*  # ION Range Slider
*
*  Styles for ion_rangeslider.min.js - range slider plugin
*
* ---------------------------------------------------------------------------- */
.irs {
  --ion-bg: var(--gray-300);
  --ion-connect-bg: var(--primary);
  --ion-height: 0.375rem;
  --ion-tooltip-bg: var(--black);
  --ion-tooltip-color: var(--white);
  --ion-tooltip-padding-y: calc(var(--spacer) * 0.4);
  --ion-tooltip-padding-x: calc(var(--spacer) * 0.6);
  --ion-tooltip-arrow-height: 0.4rem;
  --ion-handle-size: calc(0.375rem * 3.5);
  --ion-handle-bg: var(--white);
  --ion-handle-inner-bg: var(--black);
  --ion-handle-inner-hover-bg: var(--primary);
  --ion-handle-border-width: var(--border-width);
  --ion-handle-border-color: rgba(var(--black-rgb), 0.25);
  --ion-pips-spacer-y: 1rem;
  --ion-pips-spacer-x: 1rem;
  --ion-pips-color: var(--gray-700);
  --ion-pips-font-size: var(--body-font-size-sm);
  --ion-pips-marker-width: 0.0625rem;
  --ion-pips-marker-height: 0.1875rem;
  position: relative;
  display: block;
  height: calc(var(--ion-tooltip-arrow-height) + var(--ion-tooltip-padding-y) * 2 + var(--ion-pips-font-size) + var(--ion-height) * 4);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  /*rtl:ignore*/
  direction: ltr;
}

.irs-hidden-input {
  position: absolute !important;
  display: block !important;
  top: 0 !important;
  left: 0 !important;
  width: 0 !important;
  height: 0 !important;
  font-size: 0 !important;
  line-height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  outline: none !important;
  z-index: -9999 !important;
  background: none !important;
  border-style: solid !important;
  border-color: transparent !important;
}

.irs-line {
  position: relative;
  display: block;
  overflow: hidden;
  outline: none;
  height: var(--ion-height);
  top: calc(var(--ion-tooltip-arrow-height) + var(--ion-tooltip-padding-y) * 2 + var(--ion-pips-font-size) + var(--ion-height) * 2);
  background-color: var(--ion-bg);
  border-radius: var(--border-radius-pill);
}

.irs-line-left,
.irs-line-mid,
.irs-line-right {
  position: absolute;
  display: block;
  top: 0;
}

.irs-line-left {
  /*rtl:ignore*/
  left: 0;
  width: 11%;
}

.irs-line-mid {
  /*rtl:ignore*/
  left: 9%;
  width: 82%;
}

.irs-line-right {
  /*rtl:ignore*/
  right: 0;
  width: 11%;
}

.irs-bar {
  position: absolute;
  display: block;
  top: calc(var(--ion-tooltip-arrow-height) + var(--ion-tooltip-padding-y) * 2 + var(--ion-pips-font-size) + var(--ion-height) * 2);
  /*rtl:ignore*/
  left: 0;
  width: 0;
  height: var(--ion-height);
  background-color: var(--ion-connect-bg);
  border-radius: var(--border-radius-pill);
}

.irs-bar-edge {
  position: absolute;
  display: block;
  top: calc(var(--ion-tooltip-arrow-height) + var(--ion-tooltip-padding-y) * 2 + var(--ion-pips-font-size) + var(--ion-height) * 2);
  /*rtl:ignore*/
  left: 0;
  height: 0.75rem;
  width: 0.5625rem;
}

.irs-shadow {
  position: absolute;
  display: none;
  top: calc(var(--ion-tooltip-arrow-height) + var(--ion-tooltip-padding-y) * 2 + var(--ion-pips-font-size) + var(--ion-height) * 2);
  /*rtl:ignore*/
  left: 0;
  width: 0;
  height: var(--ion-height);
  background-color: var(--body-color);
  opacity: 0.25;
  border-radius: var(--border-radius-pill);
}

.irs-handle {
  position: absolute;
  display: block;
  background-color: var(--ion-handle-bg);
  border: var(--ion-handle-border-width) solid var(--ion-handle-border-color);
  cursor: pointer;
  top: calc(var(--ion-tooltip-arrow-height) + var(--ion-tooltip-padding-y) * 2 + var(--ion-pips-font-size) + var(--ion-height));
  cursor: pointer;
  z-index: 1;
  width: var(--ion-handle-size);
  height: var(--ion-handle-size);
  background-clip: content-box;
  border-radius: var(--border-radius-pill);
}
.irs-handle:after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: calc(var(--ion-height) * 0.5 * -1);
  margin-left: calc(var(--ion-height) * 0.5 * -1);
  background-color: var(--ion-handle-inner-bg);
  width: var(--ion-height);
  height: var(--ion-height);
  border-radius: var(--border-radius-pill);
}
.irs-handle, .irs-handle:after {
  transition: background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .irs-handle, .irs-handle:after {
    transition: none;
  }
}
.irs-handle:hover:after, .irs-handle:focus:after {
  background-color: var(--ion-handle-inner-hover-bg);
}
.irs-handle.type_last {
  z-index: 2;
}

.irs-min,
.irs-max,
.irs-from,
.irs-to,
.irs-single {
  position: absolute;
  display: block;
  cursor: default;
}

.irs-min,
.irs-max {
  color: var(--ion-pips-color);
  font-size: var(--ion-pips-font-size);
  line-height: 1;
  top: 0;
  padding: var(--ion-tooltip-padding-y);
  background-color: var(--ion-bg);
  border-radius: var(--border-radius);
}

.irs-min {
  /*rtl:ignore*/
  left: 0;
}

.irs-max {
  /*rtl:ignore*/
  right: 0;
}

.irs-from,
.irs-to,
.irs-single {
  top: 0;
  /*rtl:ignore*/
  left: 0;
  white-space: nowrap;
  color: var(--ion-tooltip-color);
  font-size: var(--ion-pips-font-size);
  line-height: 1;
  padding: var(--ion-tooltip-padding-y);
  top: 0;
  background-color: var(--ion-tooltip-bg);
  border-radius: var(--border-radius);
}
.irs-from:after,
.irs-to:after,
.irs-single:after {
  content: "";
  position: absolute;
  display: block;
  bottom: calc(var(--ion-tooltip-arrow-height) * 2 * -1);
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  overflow: hidden;
  border: var(--ion-tooltip-arrow-height) solid transparent;
  border-top-color: var(--ion-tooltip-bg);
  width: 0;
  height: 0;
}

.irs-grid {
  position: absolute;
  display: none;
  bottom: 0;
  /*rtl:ignore*/
  left: 0;
  width: 100%;
  height: calc(var(--ion-pips-font-size) + var(--ion-pips-spacer-y));
}

.irs-with-grid {
  height: calc(var(--ion-tooltip-arrow-height) + var(--ion-tooltip-padding-y) * 2 + var(--ion-pips-font-size) + var(--ion-height) * 7 + var(--ion-pips-spacer-y));
}
.irs-with-grid .irs-grid {
  display: block;
}

.irs-grid-pol {
  position: absolute;
  top: 0;
  /*rtl:ignore*/
  left: 0;
  width: var(--ion-pips-marker-width);
  height: calc(var(--ion-pips-marker-height) * 2);
  background-color: var(--ion-pips-color);
}
.irs-grid-pol.small {
  height: var(--ion-pips-marker-height);
}

.irs-grid-text {
  position: absolute;
  bottom: 0;
  /*rtl:ignore*/
  left: 0;
  white-space: nowrap;
  text-align: center;
  font-size: var(--ion-pips-font-size);
  color: var(--ion-pips-color);
  line-height: 1;
  padding: 0 var(--ion-pips-marker-height);
}

.irs-disable-mask {
  position: absolute;
  display: block;
  top: 0;
  /*rtl:ignore*/
  left: -1%;
  width: 102%;
  height: 100%;
  cursor: default;
  background: transparent;
  z-index: 2;
}

.irs-disabled {
  opacity: 0.65;
}

/* ------------------------------------------------------------------------------
 *
 *  # Prism
 *
 *  Styles for prism.min.js - lightweight, extensible syntax highlighter
 *
 * ---------------------------------------------------------------------------- */
.code-toolbar {
  --syntax-padding: var(--spacer);
  --syntax-bg: var(--gray-100);
  --syntax-fg: var(--gray-900);
  --syntax-gutter: var(--gray-500);
  --syntax-color-selection: #e5e5e6;
  --syntax-color-punctuation: #a0a1a7;
  --syntax-color-line: rgba(56, 58, 66, 0.05);
  --syntax-color-1: #0184bc;
  --syntax-color-2: #4078f2;
  --syntax-color-3: #a626a4;
  --syntax-color-4: #50a14f;
  --syntax-color-5: #e45649;
  --syntax-color-6: #ca1243;
  --syntax-color-7: #b76b01;
  position: relative;
}
.code-toolbar[data-color-theme=dark], [data-color-theme=dark] .code-toolbar:not([data-color-theme]), html[data-color-theme=dark] .code-toolbar {
  color-scheme: dark;
  --syntax-bg: rgba(var(--black-rgb), 0.25);
  --syntax-color-selection: #3e4451;
  --syntax-color-punctuation: #5c6370;
  --syntax-color-line: rgba(153, 187, 255, 0.05);
  --syntax-color-1: #56b6c2;
  --syntax-color-2: #61afef;
  --syntax-color-3: #c678dd;
  --syntax-color-4: #98c379;
  --syntax-color-5: #e06c75;
  --syntax-color-6: #be5046;
  --syntax-color-7: #d19a66;
}
.code-toolbar .toolbar {
  display: inline-block;
  position: absolute;
  top: var(--spacer);
  text-shadow: none;
  /*rtl:ignore*/
  right: var(--spacer);
}
.code-toolbar .toolbar span,
.code-toolbar .toolbar .toolbar-item {
  display: inline-block;
}
.code-toolbar .toolbar a {
  cursor: pointer;
}
.code-toolbar .toolbar button {
  background: none;
  border: 0;
  color: inherit;
  font-family: var(--body-font-family);
  line-height: normal;
  overflow: visible;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  background-color: transparent;
  color: var(--link-color);
  border: var(--border-width) solid var(--link-color);
  background-color: var(--syntax-bg);
  border-radius: var(--border-radius);
  transition: color ease-in-out var(--transition-base-timer), border-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .code-toolbar .toolbar button {
    transition: none;
  }
}
.code-toolbar .toolbar button:hover, .code-toolbar .toolbar button:focus {
  color: var(--link-hover-color);
  border-color: var(--link-hover-color);
}
.code-toolbar .toolbar a,
.code-toolbar .toolbar button,
.code-toolbar .toolbar span {
  padding: calc(var(--spacer-1) * 1.5) var(--spacer-2);
  font-size: var(--body-font-size-sm);
  outline: 0;
}
.code-toolbar .toolbar a:hover, .code-toolbar .toolbar a:focus,
.code-toolbar .toolbar button:hover,
.code-toolbar .toolbar button:focus,
.code-toolbar .toolbar span:hover,
.code-toolbar .toolbar span:focus {
  text-decoration: none;
}

code[class*=language-],
pre[class*=language-] {
  background: var(--syntax-bg);
  color: var(--syntax-fg);
  /*rtl:ignore*/
  direction: ltr;
  white-space: pre;
  word-spacing: normal;
  word-break: break-all;
  line-height: 1.5;
  -moz-tab-size: 4;
    -o-tab-size: 4;
       tab-size: 4;
  -webkit-hyphens: none;
     -moz-hyphens: none;
      -ms-hyphens: none;
          hyphens: none;
  border-radius: var(--border-radius);
}
code[class*=language-]::-moz-selection, code[class*=language-] *::-moz-selection, pre[class*=language-]::-moz-selection, pre[class*=language-] *::-moz-selection {
  background: var(--syntax-color-selection);
  color: inherit;
}
code[class*=language-]::-moz-selection, code[class*=language-]::selection,
code[class*=language-] *::-moz-selection,
code[class*=language-] *::selection,
pre[class*=language-]::-moz-selection,
pre[class*=language-]::selection,
pre[class*=language-] *::-moz-selection,
pre[class*=language-] *::selection {
  background: var(--syntax-color-selection);
  color: inherit;
}

pre[class*=language-] {
  overflow: auto;
  position: relative;
  padding: var(--syntax-padding);
  margin-bottom: 0;
}
pre[class*=language-] code {
  background-color: transparent;
}
pre[data-line] {
  position: relative;
  /*rtl:ignore*/
  padding-left: 3rem;
}
pre code {
  padding: 0;
}

.token.bold {
  font-weight: bold;
}
.token.comment, .token.italic {
  font-style: italic;
}
.token.entity {
  cursor: help;
}
.token.namespace {
  opacity: 0.8;
}
.token.comment, .token.prolog, .token.cdata {
  color: var(--syntax-color-punctuation);
}
.token.doctype, .token.punctuation, .token.entity {
  color: var(--syntax-fg);
}
.token.attr-name, .token.class-name, .token.boolean, .token.constant, .token.number, .token.atrule {
  color: var(--syntax-color-7);
}
.token.keyword {
  color: var(--syntax-color-3);
}
.token.property, .token.tag, .token.symbol, .token.deleted, .token.important {
  color: var(--syntax-color-5);
}
.token.selector, .token.string, .token.char, .token.builtin, .token.inserted, .token.regex, .token.attr-value, .token.attr-value > .token.punctuation {
  color: var(--syntax-color-4);
}
.token.variable, .token.operator, .token.function {
  color: var(--syntax-color-2);
}
.token.url {
  color: var(--syntax-color-1);
}

.token.attr-value > .token.punctuation.attr-equals,
.token.special-attr > .token.attr-value > .token.value.css {
  color: var(--syntax-fg);
}

.language-css .token.selector {
  color: var(--syntax-color-5);
}
.language-css .token.property {
  color: var(--syntax-fg);
}
.language-css .token.function, .language-css .token.url > .token.function {
  color: var(--syntax-color-1);
}
.language-css .token.url > .token.string.url {
  color: var(--syntax-color-4);
}
.language-css .token.important, .language-css .token.atrule .token.rule {
  color: var(--syntax-color-3);
}

.language-javascript .token.operator {
  color: var(--syntax-color-3);
}
.language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation {
  color: var(--syntax-color-6);
}

.language-json .token.operator {
  color: var(--syntax-fg);
}
.language-json .token.null.keyword {
  color: var(--syntax-color-7);
}

.language-markdown .token.url, .language-markdown .token.url > .token.operator, .language-markdown .token.url-reference.url > .token.string {
  color: var(--syntax-fg);
}
.language-markdown .token.url > .token.content {
  color: var(--syntax-color-2);
}
.language-markdown .token.url > .token.url, .language-markdown .token.url-reference.url {
  color: var(--syntax-color-1);
}
.language-markdown .token.blockquote.punctuation, .language-markdown .token.hr.punctuation {
  color: var(--syntax-color-punctuation);
  font-style: italic;
}
.language-markdown .token.code-snippet {
  color: var(--syntax-color-4);
}
.language-markdown .token.bold .token.content {
  color: var(--syntax-color-7);
}
.language-markdown .token.italic .token.content {
  color: var(--syntax-color-3);
}
.language-markdown .token.strike .token.content, .language-markdown .token.strike .token.punctuation, .language-markdown .token.list.punctuation, .language-markdown .token.title.important > .token.punctuation {
  color: var(--syntax-color-5);
}

.line-highlight {
  position: absolute;
  left: 0;
  right: 0;
  margin-top: var(--spacer);
  background: var(--syntax-color-line);
  pointer-events: none;
  white-space: pre;
  line-height: inherit;
}
.line-highlight:before, .line-highlight:after {
  content: attr(data-start);
  position: absolute;
  top: 0;
  /*rtl:ignore*/
  left: calc(var(--spacer) * 0.5);
  text-align: center;
  font-size: var(--body-font-size-xs);
  background: var(--syntax-color-selection);
  color: var(--syntax-fg);
  padding: 0.1em 0.6em;
  border-radius: var(--border-radius-sm);
}
.line-highlight[data-end]:after {
  content: attr(data-end);
  top: auto;
  bottom: 0;
}

.line-numbers .line-numbers-rows {
  position: absolute;
  pointer-events: none;
  top: 0;
  font-size: 100%;
  width: 3rem;
  /*rtl:begin:ignore*/
  left: -4rem;
  border-right: var(--border-width) solid var(--border-color);
  /*rtl:end:ignore*/
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.line-numbers .line-numbers-rows > span {
  pointer-events: none;
  display: block;
  counter-increment: linenumber;
}
.line-numbers .line-numbers-rows > span:before {
  content: counter(linenumber);
  color: var(--syntax-gutter);
  display: block;
  /*rtl:begin:ignore*/
  padding-right: var(--spacer-2);
  text-align: right;
  /*rtl:end:ignore*/
}

pre.line-numbers {
  position: relative;
  /*rtl:ignore*/
  padding-left: 4rem;
  counter-reset: linenumber;
}
pre.line-numbers > code {
  position: relative;
}

/* ------------------------------------------------------------------------------
 *
 *  # Dragula - drag and drop library
 *
 *  Styles for Dragula Drag and drop plugin
 *
 * ---------------------------------------------------------------------------- */
.gu-mirror {
  position: fixed !important;
  margin: 0 !important;
  z-index: 9999 !important;
  opacity: 0.8;
}

.gu-unselectable.dropdown-menu li:nth-last-child(2) {
  margin-bottom: 0;
}

.nav-pills-toolbar > li.gu-mirror:not(.active) > a {
  border: 0;
}

.gu-hide {
  display: none !important;
}

.gu-unselectable {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.gu-transit {
  opacity: 0.5;
}

.dragula-handle {
  color: var(--gray-600);
  cursor: move;
}
.dragula-handle:hover, .dragula-handle:focus {
  color: var(--body-color);
}

/* ------------------------------------------------------------------------------
 *
 *  # Headroom
 *
 *  Styles for headroom.min.js - hides BS navbar component on page scroll
 *
 * ---------------------------------------------------------------------------- */
.headroom {
  transition: box-shadow ease-in-out 0.2s, -webkit-transform ease-in-out 0.2s;
  transition: transform ease-in-out 0.2s, box-shadow ease-in-out 0.2s;
  transition: transform ease-in-out 0.2s, box-shadow ease-in-out 0.2s, -webkit-transform ease-in-out 0.2s;
  will-change: transform;
}

.navbar-slide-top.headroom--pinned {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.navbar-slide-top.headroom--unpinned {
  -webkit-transform: translateY(-100%);
          transform: translateY(-100%);
  box-shadow: none;
}

.navbar-slide-bottom.headroom--pinned {
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
  box-shadow: none;
}
.navbar-slide-bottom.headroom--unpinned {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}

/* ------------------------------------------------------------------------------
*
*  # Floating action buttons
*
*  Styles for fab.min.js - material design floating action button with menu
*
* ---------------------------------------------------------------------------- */
.fab-menu {
  --fab-main-btn-size: calc(var(--spacer-2) + var(--icon-font-size) * 0.5);
  --fab-inner-btn-spacing: var(--spacer-2);
  --fab-inner-btn-size: calc((var(--spacer-2) - var(--fab-btn-gap)) * 2 + var(--icon-font-size));
  position: relative;
  display: inline-block;
  z-index: 997;
}

.fab-menu-absolute {
  position: absolute;
}

.fab-menu-fixed {
  position: fixed;
  z-index: 998;
}

.content-wrapper > .fab-menu-top {
  top: calc(var(--fab-main-btn-size) * -1);
}
.navbar .fab-menu-top {
  bottom: 0;
  -webkit-transform: translate(50%, calc(50% + var(--navbar-padding-y)));
          transform: translate(50%, calc(50% + var(--navbar-padding-y)));
}

.fab-menu-bottom {
  bottom: 0;
  transition: bottom ease-in-out var(--transition-base-timer);
}
.fab-menu-bottom.reached-bottom {
  bottom: calc(calc(var(--body-line-height-computed) + var(--navbar-link-padding-y) * 2) + calc(var(--spacer) * 2));
}
.navbar .fab-menu-bottom {
  top: 0;
  -webkit-transform: translate(calc(50% - var(--navbar-padding-y)), 50%);
          transform: translate(calc(50% - var(--navbar-padding-y)), 50%);
}

.fab-menu-inner {
  list-style: none;
  margin: 0;
  padding: 0;
  position: absolute;
  left: 0;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  width: 100%;
  -ms-flex-align: center;
      align-items: center;
}

.fab-menu-btn {
  padding: var(--fab-main-btn-size);
  z-index: 999;
}
.fab-menu-btn i {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: calc(var(--icon-font-size) * 0.5 * -1);
  margin-left: calc(var(--icon-font-size) * 0.5 * -1);
}

.fab-icon-close,
.fab-icon-open {
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  transition: opacity ease-in-out calc(var(--transition-base-timer) * 2), -webkit-transform ease-in-out calc(var(--transition-base-timer) * 2);
  transition: transform ease-in-out calc(var(--transition-base-timer) * 2), opacity ease-in-out calc(var(--transition-base-timer) * 2);
  transition: transform ease-in-out calc(var(--transition-base-timer) * 2), opacity ease-in-out calc(var(--transition-base-timer) * 2), -webkit-transform ease-in-out calc(var(--transition-base-timer) * 2);
}
.fab-menu[data-fab-toggle=hover]:hover .fab-icon-close, .fab-menu[data-fab-state=open] .fab-icon-close,
.fab-menu[data-fab-toggle=hover]:hover .fab-icon-open,
.fab-menu[data-fab-state=open] .fab-icon-open {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

.fab-menu[data-fab-toggle=hover]:hover .fab-icon-open, .fab-menu[data-fab-state=open] .fab-icon-open {
  opacity: 0;
}

.fab-icon-close {
  opacity: 0;
}
.fab-menu[data-fab-toggle=hover]:hover .fab-icon-close, .fab-menu[data-fab-state=open] .fab-icon-close {
  opacity: 1;
}

.fab-menu .fab-menu-inner > li {
  visibility: hidden;
  opacity: 0;
  transition: all ease-in-out calc(var(--transition-base-timer) * 2);
}
.fab-menu[data-fab-toggle=hover]:hover .fab-menu-inner > li, .fab-menu[data-fab-state=open] .fab-menu-inner > li {
  visibility: visible;
  opacity: 1;
}

.fab-menu-top .fab-menu-inner {
  top: 100%;
}
.fab-menu-top .fab-menu-inner > li:not(:first-child) {
  margin-top: -100%;
}
.fab-menu-top[data-fab-toggle=hover]:hover .fab-menu-inner > li, .fab-menu-top[data-fab-state=open] .fab-menu-inner > li {
  margin-top: var(--fab-inner-btn-spacing);
}

.fab-menu-bottom .fab-menu-inner {
  bottom: 100%;
  -ms-flex-direction: column-reverse;
      flex-direction: column-reverse;
}
.fab-menu-bottom .fab-menu-inner > li:not(:first-child) {
  margin-bottom: -100%;
}
.fab-menu-bottom[data-fab-toggle=hover]:hover .fab-menu-inner > li, .fab-menu-bottom[data-fab-state=open] .fab-menu-inner > li {
  margin-bottom: var(--fab-inner-btn-spacing);
}

.fab-menu-inner div[data-fab-label] {
  --fab-tooltip-padding-y: calc(var(--spacer) * 0.4);
  --fab-tooltip-padding-x: calc(var(--spacer) * 0.6);
  --fab-tooltip-bg: var(--black);
  --fab-tooltip-color: var(--white);
  --fab-tooltip-shadow: var(--box-shadow);
  position: relative;
}
.fab-menu-inner div[data-fab-label][data-color-theme=dark], [data-color-theme=dark] .fab-menu-inner div[data-fab-label]:not([data-color-theme]), html[data-color-theme=dark] .fab-menu-inner div[data-fab-label] {
  color-scheme: dark;
  --fab-tooltip-color: var(--black);
  --fab-tooltip-bg: var(--white);
}
.fab-menu-inner div[data-fab-label]:after {
  content: attr(data-fab-label);
  position: absolute;
  top: 50%;
  right: calc(100% + var(--spacer-2));
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: var(--fab-tooltip-color);
  background-color: var(--fab-tooltip-bg);
  padding: var(--fab-tooltip-padding-y) var(--fab-tooltip-padding-x);
  visibility: hidden;
  opacity: 0;
  white-space: nowrap;
  box-shadow: var(--fab-tooltip-shadow);
  transition: all ease-in-out calc(var(--transition-base-timer) * 2);
  border-radius: var(--border-radius);
}
.fab-menu-bottom .fab-menu-inner div[data-fab-label]:after {
  margin-top: calc((var(--fab-tooltip-padding-y) + var(--fab-inner-btn-spacing)) * -1);
}
.fab-menu-inner div[data-fab-label].fab-label-end:after {
  right: auto;
  left: calc(100% + var(--spacer-2));
}
.fab-menu[data-fab-toggle=hover] .fab-menu-inner div[data-fab-label]:hover:after, .fab-menu[data-fab-state=open] .fab-menu-inner div[data-fab-label]:hover:after {
  visibility: visible;
  opacity: 1;
}
.fab-menu-inner div[data-fab-label].fab-label-light {
  --fab-tooltip-bg: var(--white);
  --fab-tooltip-color: var(--black);
}
.fab-menu-inner div[data-fab-label].fab-label-light[data-color-theme=dark], [data-color-theme=dark] .fab-menu-inner div[data-fab-label].fab-label-light:not([data-color-theme]), html[data-color-theme=dark] .fab-menu-inner div[data-fab-label].fab-label-light {
  color-scheme: dark;
  --fab-tooltip-bg: var(--black);
  --fab-tooltip-color: var(--white);
}
.fab-menu-inner div[data-fab-label].fab-label-visible:after {
  visibility: visible;
  opacity: 1;
}

/* ------------------------------------------------------------------------------
*
*  # Fancytree
*
*  Styles for fancytree_all.min.js - tree plugin for jQuery
*
* ---------------------------------------------------------------------------- */
.fancytree-container {
  --ft-node-padding-y: 0.25rem;
  --ft-node-padding-x: 0.5rem;
  --ft-node-selected-bg: var(--gray-300);
  --ft-node-selected-color: var(--body-color);
  --ft-node-active-bg: var(--component-active-bg);
  --ft-node-active-color: var(--component-active-color);
  --ft-check-width: 1.25rem;
  --ft-check-height: 1.25rem;
  --ft-check-border-width: calc(var(--border-width) * 2);
  --ft-check-border-color: var(--gray-400);
  --ft-check-bg: var(--white);
  --ft-check-border-radius: 0.1875em;
  --ft-check-transition: box-shadow var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out;
  --ft-check-checked-border-color: transparent;
  --ft-check-checked-bg: var(--component-active-bg);
  --ft-radio-border-radius: 100rem;
  list-style: none;
  white-space: nowrap;
  padding: 0;
  margin: 0;
  overflow: auto;
  position: relative;
}
.fancytree-container:focus {
  outline: 0;
}
.fancytree-container ul {
  list-style: none;
  padding-left: var(--icon-font-size);
  margin: 0;
}
.ui-fancytree-disabled .fancytree-container {
  opacity: 0.65;
}

.ui-fancytree-disabled .fancytree-container,
.ui-fancytree-disabled .fancytree-title,
.ui-fancytree-disabled .fancytree-expander {
  cursor: default;
}
.ui-fancytree-disabled .fancytree-treefocus .fancytree-selected .fancytree-title {
  background-color: var(--ft-node-selected-bg);
  color: var(--ft-node-selected-color);
}

.fancytree-expander:after,
.fancytree-icon:after,
.fancytree-checkbox:after,
.fancytree-drag-helper-img:after,
.fancytree-drop-before:after,
.fancytree-drop-after:after,
.fancytree-loading .fancytree-expander:after,
.fancytree-statusnode-wait .fancytree-icon:after {
  font-family: var(--icon-font-family);
  display: inline-block;
  font-size: var(--icon-font-size);
  line-height: 1;
  vertical-align: top;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fancytree-icon,
.fancytree-custom-icon,
.fancytree-expander {
  display: inline-block;
  vertical-align: top;
  margin-left: var(--ft-node-padding-x);
  margin-top: calc((var(--body-line-height-computed) + var(--ft-node-padding-y) * 2 - var(--icon-font-size)) * 0.5);
  width: var(--icon-font-size);
  height: var(--icon-font-size);
}

.fancytree-checkbox {
  vertical-align: top;
  margin-top: calc((var(--body-line-height-computed) + var(--ft-node-padding-y) * 2 - var(--ft-check-height)) * 0.5);
  margin-left: var(--ft-node-padding-x);
}

img.fancytree-icon {
  border: 0;
}

.fancytree-exp-c .fancytree-expander:after {
  content: "\f31c";
}
.fancytree-exp-cl .fancytree-expander:after {
  content: "\f31c";
}
.fancytree-exp-cd .fancytree-expander:after, .fancytree-exp-cdl .fancytree-expander:after {
  content: "\f31c";
}
.fancytree-exp-e .fancytree-expander:after, .fancytree-exp-ed .fancytree-expander:after {
  content: "\f31a";
}
.fancytree-exp-el .fancytree-expander:after, .fancytree-exp-edl .fancytree-expander:after {
  content: "\f31a";
}

.fancytree-statusnode-error .fancytree-icon:after {
  content: "\f62d";
}

.fancytree-loading .fancytree-expander,
.fancytree-statusnode-wait .fancytree-icon {
  margin-left: auto;
  margin-right: auto;
  display: inline-block;
  text-align: center;
  width: var(--icon-font-size);
  height: var(--icon-font-size);
}
.fancytree-loading .fancytree-expander:after,
.fancytree-statusnode-wait .fancytree-icon:after {
  content: "\f5a7";
  transition: all ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .fancytree-loading .fancytree-expander:after,
.fancytree-statusnode-wait .fancytree-icon:after {
    transition: none;
  }
}

.fancytree-ico-c .fancytree-icon:after,
.fancytree-ico-e .fancytree-icon:after {
  content: "\f3eb";
}

.fancytree-has-children.fancytree-ico-c .fancytree-icon:after {
  content: "\f3e6";
}
.fancytree-has-children.fancytree-ico-e .fancytree-icon:after {
  content: "\f3e4";
}

.fancytree-ico-cf .fancytree-icon:after,
.fancytree-ico-ef .fancytree-icon:after {
  content: "\f40c";
}

.fancytree-has-children.fancytree-ico-cf .fancytree-icon:after {
  content: "\f415";
}
.fancytree-has-children.fancytree-ico-ef .fancytree-icon:after {
  content: "\f40f";
}

.fancytree-checkbox {
  width: var(--ft-check-width);
  height: var(--ft-check-height);
  border: var(--ft-check-border-width) solid var(--ft-check-border-color);
  background-color: var(--ft-check-bg);
  display: inline-block;
  text-align: center;
  position: relative;
  cursor: pointer;
  border-radius: var(--ft-check-border-radius);
  transition: var(--ft-check-transition);
}
@media (prefers-reduced-motion: reduce) {
  .fancytree-checkbox {
    transition: none;
  }
}
.fancytree-checkbox[data-color-theme=dark], [data-color-theme=dark] .fancytree-checkbox:not([data-color-theme]), html[data-color-theme=dark] .fancytree-checkbox {
  color-scheme: dark;
  --ft-check-bg: #2c2d33;
}
.fancytree-checkbox:after {
  content: "";
  position: absolute;
  width: calc(var(--ft-check-width) - var(--ft-check-border-width) * 2);
  height: calc(var(--ft-check-width) - var(--ft-check-border-width) * 2);
  top: 0;
  left: 0;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2.5' d='M5 10l4 4l6-8'/%3e%3c/svg%3e");
  background-position: center;
  background-size: contain;
  opacity: 0;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .fancytree-checkbox:after {
    transition: none;
  }
}
.fancytree-selected .fancytree-checkbox:after, .fancytree-partsel .fancytree-checkbox:after {
  opacity: 1;
}
.fancytree-unselectable .fancytree-checkbox {
  opacity: 0.65;
  cursor: default;
}
.fancytree-selected .fancytree-checkbox, .fancytree-partsel .fancytree-checkbox {
  border-color: var(--ft-check-checked-border-color);
  background-color: var(--ft-check-checked-bg);
}
.fancytree-has-children:not(.fancytree-selected) .fancytree-checkbox:after {
  background-image: none;
}
.fancytree-partsel.fancytree-has-children:not(.fancytree-selected) .fancytree-checkbox:after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2.5' d='M5 10h10'/%3e%3c/svg%3e");
}
.fancytree-radio .fancytree-checkbox {
  border-radius: var(--ft-radio-border-radius);
}
.fancytree-radio .fancytree-checkbox:after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}

.fancytree-drag-helper .fancytree-title {
  padding-right: calc(var(--ft-node-padding-x) * 2 + var(--icon-font-size));
  border: var(--border-width) solid var(--border-color);
  opacity: 0.8;
  margin: 0;
}

.fancytree-drag-helper-img:after {
  position: absolute;
  top: 50%;
  margin-top: calc(var(--icon-font-size) * 0.5 * -1);
  right: var(--ft-node-padding-x);
  z-index: 10;
}
.fancytree-drop-accept .fancytree-drag-helper-img:after {
  content: "\f33f";
  color: var(--success);
}
.fancytree-drop-reject .fancytree-drag-helper-img:after {
  content: "\f62d";
  color: var(--danger);
}

#fancytree-drop-marker.fancytree-drop-before, #fancytree-drop-marker.fancytree-drop-after {
  width: 15rem;
  border-top: var(--border-width) solid var(--border-color);
  position: absolute !important;
}

.fancytree-drag-source {
  opacity: 0.5;
}

.fancytree-drop-target.fancytree-drop-accept a {
  background-color: var(--primary) !important;
  color: var(--white) !important;
  text-decoration: none;
}

.fancytree-node {
  display: inherit;
  width: 100%;
  position: relative;
  margin-bottom: 1px;
}

.fancytree-title {
  border: 0;
  padding: var(--ft-node-padding-y) var(--ft-node-padding-x);
  margin-left: var(--spacer-1);
  display: inline-block;
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: all ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .fancytree-title {
    transition: none;
  }
}
.fancytree-title > input {
  border: 0;
  outline: 0;
  padding: 0;
  background-color: transparent;
  color: var(--body-color);
}
.fancytree-selected .fancytree-title, .fancytree-active .fancytree-title {
  background-color: var(--ft-node-selected-bg);
  color: var(--ft-node-selected-color);
}
.fancytree-treefocus .fancytree-selected .fancytree-title {
  background-color: var(--ft-node-active-bg);
  color: var(--ft-node-active-color);
}

.fancytree-ext-table .fancytree-node {
  display: inline-block;
  width: auto;
  margin: 0;
}
.fancytree-ext-table .fancytree-title {
  display: inline;
}
.fancytree-ext-table.fancytree-container {
  white-space: normal;
}
.fancytree-ext-table .fancytree-expander,
.fancytree-ext-table .fancytree-icon,
.fancytree-ext-table .fancytree-custom-icon {
  margin-top: calc((var(--icon-font-size) - var(--body-font-size)) * 0.5);
}
.fancytree-ext-table .fancytree-checkbox {
  margin: 0 auto;
  float: none;
}
.fancytree-ext-table tbody tr {
  transition: all ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .fancytree-ext-table tbody tr {
    transition: none;
  }
}
.fancytree-ext-table tbody tr td:first-child {
  text-align: center;
}
.fancytree-ext-table tbody tr .fancytree-title {
  background-color: transparent;
  color: inherit !important;
  padding: 0;
  transition: none;
}
.fancytree-ext-table tbody tr.fancytree-focused, .fancytree-ext-table tbody tr.fancytree-active {
  background-color: var(--gray-200);
}
.fancytree-ext-table tbody tr.fancytree-selected {
  background-color: var(--gray-300);
}
.fancytree-ext-table tbody tr.fancytree-selected .fancytree-title {
  background-color: transparent;
}
.fancytree-treefocus .fancytree-ext-table tbody tr.fancytree-selected .fancytree-title {
  transition: color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .fancytree-treefocus .fancytree-ext-table tbody tr.fancytree-selected .fancytree-title {
    transition: none;
  }
}

.fancytree-ext-childcounter .fancytree-childcounter {
  position: absolute;
  right: 0;
  top: var(--ft-node-padding-y);
  color: rgba(var(--body-color-rgb), 0.75);
}

.fancytree-helper-hidden {
  display: none;
}

.fancytree-helper-indeterminate-cb {
  color: rgba(var(--body-color-rgb), 0.75);
}

.fancytree-helper-disabled {
  color: rgba(var(--body-color-rgb), 0.5);
}

.fancytree-helper-spin {
  -webkit-animation: rotation 1s linear infinite;
          animation: rotation 1s linear infinite;
}

/* ------------------------------------------------------------------------------
 *
 *  # FullCalendar
 *
 *  Styles for fullcalendar JS files - JavaScript event calendar
 *
 * ---------------------------------------------------------------------------- */
.fc-not-allowed,
.fc-not-allowed .fc-event {
  cursor: not-allowed;
}

.fc-unselectable {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.fc {
  --fc-bg: var(--white);
  --fc-cell-border-width: var(--border-width);
  --fc-cell-border-color: var(--border-color);
  --fc-cell-padding-y: 0.75rem;
  --fc-cell-padding-x: 1.25rem;
  --fc-event-bg: var(--primary);
  --fc-event-font-size: var(--body-font-size-sm);
  --fc-event-color: var(--white);
  --fc-event-border-width: var(--border-width);
  --fc-event-border-color: var(--primary);
  --fc-event-spacer-y: 0.1875rem;
  --fc-event-spacer-x: 0.5rem;
  --fc-event-padding-y: 0.1875rem;
  --fc-event-padding-x: 0.5rem;
  --fc-event-more-bg: var(--gray-200);
  --fc-event-more-hover-bg: var(--gray-300);
  --fc-event-resizer-size: 0.5rem;
  --fc-today-bg: rgba(var(--yellow-rgb), 0.1);
  --fc-bgevent-bg: rgba(var(--success-rgb), 0.4);
  --fc-nonbusiness-bg: rgba(var(--body-color-rgb), 0.05);
  --fc-highlight-bg: rgba(var(--primary-rgb), 0.1);
  --fc-weekday-color: rgba(var(--body-color-rgb), 0.5);
  --fc-popover-bg: var(--white);
  --fc-popover-border-width: var(--border-width);
  --fc-popover-border-color: var(--border-color-translucent);
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
}
.fc[data-color-theme=dark], [data-color-theme=dark] .fc:not([data-color-theme]), html[data-color-theme=dark] .fc {
  color-scheme: dark;
  --fc-bg: #2c2d33;
  --fc-popover-bg: #32333a;
}
.fc table {
  border-collapse: collapse;
  border-spacing: 0;
}
.fc th {
  text-align: center;
}
.fc th,
.fc td {
  vertical-align: top;
  padding: 0;
}
.fc a[data-navlink] {
  cursor: pointer;
}

.fc-direction-ltr {
  /*rtl:begin:ignore*/
  direction: ltr;
  text-align: left;
  /*rtl:end:ignore*/
}

.fc-direction-rtl {
  /*rtl:begin:ignore*/
  direction: rtl;
  text-align: right;
  /*rtl:end:ignore*/
}

.fc-theme-standard td,
.fc-theme-standard th {
  border: var(--fc-cell-border-width) solid var(--fc-cell-border-color);
}

.fc-liquid-hack td,
.fc-liquid-hack th {
  position: relative;
}

@font-face {
  font-family: "fcicons";
  src: url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype");
  font-weight: normal;
  font-style: normal;
}
.fc-icon {
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 1em;
  height: 1em;
  font-size: var(--icon-font-size);
  text-align: center;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  font-family: "fcicons" !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fc-icon-chevron-left:before {
  content: "\e900";
}

.fc-icon-chevron-right:before {
  content: "\e901";
}

.fc-icon-chevrons-left:before {
  content: "\e902";
}

.fc-icon-chevrons-right:before {
  content: "\e903";
}

.fc-icon-minus-square:before {
  content: "\e904";
}

.fc-icon-plus-square:before {
  content: "\e905";
}

.fc-icon-x:before {
  content: "\e906";
}

.fc .fc-button {
  --btn-padding-y: 0.5rem;
  --btn-padding-x: 0.875rem;
  --btn-font-size: var(--body-font-size);
  --btn-border-radius: var(--border-radius);
  --btn-font-weight: 400;
  --btn-line-height: var(--body-line-height);
  --btn-border-width: var(--border-width);
  --btn-box-shadow: 0 0 0 0 transparent;
  --btn-disabled-opacity: 0.65;
  --btn-focus-box-shadow: 0 0 0 0.125rem rgba(var(--btn-focus-shadow-rgb), .25);
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
      align-items: center;
  padding: var(--btn-padding-y) var(--btn-padding-x);
  font-size: var(--btn-font-size);
  font-weight: var(--btn-font-weight);
  line-height: var(--btn-line-height);
  color: var(--btn-color);
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border: var(--btn-border-width) solid var(--btn-border-color);
  border-radius: var(--btn-border-radius);
  background-color: var(--btn-bg);
  box-shadow: var(--btn-box-shadow);
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .fc .fc-button {
    transition: none;
  }
}
.fc .fc-button:hover, .fc .fc-button:focus {
  color: var(--btn-hover-color);
  background-color: var(--btn-hover-bg);
  border-color: var(--btn-hover-border-color);
}
.fc .fc-button:focus {
  outline: 0;
  box-shadow: var(--btn-box-shadow), var(--btn-focus-box-shadow);
}
.fc .fc-button:active, .fc .fc-button.fc-button-active {
  color: var(--btn-active-color);
  background-color: var(--btn-active-bg);
  border-color: var(--btn-active-border-color);
  box-shadow: var(--btn-active-shadow);
}
.fc .fc-button:active:focus, .fc .fc-button.fc-button-active:focus {
  box-shadow: var(--btn-active-shadow), var(--btn-focus-box-shadow);
}
.fc .fc-button:disabled {
  color: var(--btn-disabled-color);
  pointer-events: none;
  background-color: var(--btn-disabled-bg);
  border-color: var(--btn-disabled-border-color);
  opacity: var(--btn-disabled-opacity);
  box-shadow: none;
}
.fc .fc-button .fc-icon {
  margin-top: calc((var(--body-line-height-computed) - var(--icon-font-size)) / 2);
  margin-bottom: calc((var(--body-line-height-computed) - var(--icon-font-size)) / 2);
}
.fc .fc-button-primary {
  --btn-color: var(--body-color);
  --btn-bg: var(--gray-200);
  --btn-border-color: var(--gray-400);
  --btn-hover-color: var(--body-color);
  --btn-hover-bg: var(--gray-300);
  --btn-hover-border-color: var(--gray-500);
  --btn-focus-shadow-rgb: 207, 207, 209;
  --btn-active-color: var(--body-color);
  --btn-active-bg: var(--gray-400);
  --btn-active-border-color: var(--gray-600);
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: var(--body-color);
  --btn-disabled-bg: var(--gray-200);
  --btn-disabled-border-color: var(--gray-400);
}
.fc .fc-prev-button .fc-icon,
.fc .fc-next-button .fc-icon {
  margin-left: calc((var(--btn-padding-x) - var(--btn-padding-y) - var(--btn-border-width)) * -1);
  margin-right: calc((var(--btn-padding-x) - var(--btn-padding-y) - var(--btn-border-width)) * -1);
}
.fc .fc-button-group {
  position: relative;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}
.fc .fc-button-group > .fc-button {
  position: relative;
  -ms-flex: 1 1 auto;
      flex: 1 1 auto;
}
.fc .fc-button-group > .fc-button:hover, .fc .fc-button-group > .fc-button:focus, .fc .fc-button-group > .fc-button:active, .fc .fc-button-group > .fc-button.fc-button-active {
  z-index: 1;
}

/*rtl:begin:ignore*/
.fc-direction-ltr .fc-button-group > .fc-button:not(:first-child) {
  margin-left: calc(var(--btn-border-width) * -1);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.fc-direction-ltr .fc-button-group > .fc-button:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/*rtl:end:ignore*/
/*rtl:begin:ignore*/
.fc-direction-rtl .fc-button-group > .fc-button:not(:first-child) {
  margin-right: calc(var(--btn-border-width) * -1);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.fc-direction-rtl .fc-button-group > .fc-button:not(:last-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/*rtl:end:ignore*/
.fc .fc-toolbar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
      justify-content: space-between;
  -ms-flex-align: center;
      align-items: center;
}
.fc .fc-toolbar.fc-header-toolbar {
  margin-bottom: var(--spacer);
}
.fc .fc-toolbar.fc-footer-toolbar {
  margin-top: var(--spacer);
}
.fc .fc-toolbar-title {
  font-size: 1.125rem;
  margin-bottom: 0;
}

/*rtl:begin:ignore*/
.fc-direction-ltr .fc-toolbar > * > :not(:first-child) {
  margin-left: var(--spacer-2);
}

/*rtl:end:ignore*/
/*rtl:begin:ignore*/
.fc-direction-rtl .fc-toolbar > * > :not(:first-child) {
  margin-right: var(--spacer-2);
}
.fc-direction-rtl .fc-toolbar-ltr {
  -ms-flex-direction: row-reverse;
      flex-direction: row-reverse;
}

/*rtl:end:ignore*/
.fc .fc-scroller {
  -webkit-overflow-scrolling: touch;
  position: relative;
}
.fc .fc-scroller-liquid {
  height: 100%;
}
.fc .fc-scroller-liquid-absolute {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}
.fc .fc-scroller-harness {
  position: relative;
  overflow: hidden;
  /*rtl:ignore*/
  direction: ltr;
}
.fc .fc-scroller-harness-liquid {
  height: 100%;
}

.fc-direction-rtl .fc-scroller-harness > .fc-scroller {
  /*rtl:ignore*/
  direction: rtl;
}

.fc-theme-standard .fc-scrollgrid {
  border: var(--fc-cell-border-width) solid var(--fc-cell-border-color);
}

.fc .fc-scrollgrid {
  border-collapse: separate;
  border-right-width: 0;
  border-bottom-width: 0;
}
.fc .fc-scrollgrid,
.fc .fc-scrollgrid table {
  width: 100%;
  table-layout: fixed;
}
.fc .fc-scrollgrid table {
  border-top-style: hidden;
  border-left-style: hidden;
  border-right-style: hidden;
}
.fc .fc-scrollgrid-liquid {
  height: 100%;
}
.fc .fc-scrollgrid-section,
.fc .fc-scrollgrid-section table,
.fc .fc-scrollgrid-section > td {
  height: 1px;
}
.fc .fc-scrollgrid-section > * {
  border-top-width: 0;
  border-left-width: 0;
}
.fc .fc-scrollgrid-section-liquid {
  height: auto;
}
.fc .fc-scrollgrid-section-liquid > td {
  height: 100%;
}
.fc .fc-scrollgrid-section-header > *,
.fc .fc-scrollgrid-section-footer > * {
  border-bottom-width: 0;
}
.fc .fc-scrollgrid-section-body table,
.fc .fc-scrollgrid-section-footer table {
  border-bottom-style: hidden;
}
.fc .fc-scrollgrid-section-sticky > * {
  background-color: var(--fc-bg);
  position: -webkit-sticky;
  position: sticky;
  z-index: 3;
}
.fc .fc-scrollgrid-section-sticky.fc-scrollgrid-section-header > * {
  top: 0;
}
.fc .fc-scrollgrid-section-sticky.fc-scrollgrid-section-footer > * {
  bottom: 0;
}
.fc .fc-scrollgrid-sticky-shim {
  height: 1px;
  margin-bottom: -1px;
}
.fc .fc-view-harness {
  -ms-flex-positive: 1;
      flex-grow: 1;
  position: relative;
}
.fc .fc-view-harness-active > .fc-view {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.fc .fc-col-header-cell-cushion {
  display: inline-block;
  padding: var(--fc-cell-padding-y) var(--fc-cell-padding-x);
  color: var(--body-color);
  font-weight: 600;
}

.fc-sticky {
  position: -webkit-sticky;
  position: sticky;
}

.fc-event {
  font-size: var(--fc-event-font-size);
}

.fc .fc-bg-event,
.fc .fc-non-business,
.fc .fc-highlight {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.fc .fc-non-business {
  background-color: var(--fc-nonbusiness-bg);
}
.fc .fc-bg-event {
  background: var(--fc-bgevent-bg);
  opacity: 0.2;
}
.fc .fc-highlight {
  background-color: var(--fc-highlight-bg);
}
.fc .fc-cell-shaded,
.fc .fc-day-disabled {
  background-color: var(--light);
}

.fc-daygrid-event .fc-event-time {
  -ms-flex-order: 3;
      order: 3;
}

a.fc-event, a.fc-event:hover {
  text-decoration: none;
}

.fc-event[href], .fc-event.fc-event-draggable {
  cursor: pointer;
}
.fc-event .fc-event-main {
  position: relative;
  z-index: 2;
}

.fc-event-dragging:not(.fc-event-selected) {
  opacity: 0.75;
}
.fc-event-dragging.fc-event-selected {
  box-shadow: var(--box-shadow);
}

.fc-event .fc-event-resizer {
  display: none;
  position: absolute;
  z-index: 4;
}
.fc-event:hover .fc-event-resizer, .fc-event-selected .fc-event-resizer {
  display: block;
}
.fc-event-selected .fc-event-resizer:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: calc(var(--fc-event-padding-y) * 2 + var(--body-line-height-computed));
  height: calc(var(--fc-event-padding-y) * 2 + var(--body-line-height-computed));
  z-index: 1080;
}

.fc-event-selected {
  box-shadow: var(--box-shadow);
}
.fc-event-selected:before {
  content: "";
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.fc-event-selected:after {
  content: "";
  background: rgba(var(--black-rgb), 0.15);
  position: absolute;
  z-index: 1;
  top: calc(var(--fc-event-border-width) * -1);
  right: calc(var(--fc-event-border-width) * -1);
  bottom: calc(var(--fc-event-border-width) * -1);
  left: calc(var(--fc-event-border-width) * -1);
}

.fc-h-event {
  display: block;
  border: var(--fc-event-border-width) solid var(--fc-event-border-color);
  background-color: var(--fc-event-bg);
}
.fc-h-event .fc-event-main {
  color: var(--fc-event-color);
}
.fc-h-event .fc-event-main-frame {
  display: -ms-flexbox;
  display: flex;
}
.fc-h-event .fc-event-time {
  max-width: 100%;
  overflow: hidden;
}
.fc-h-event .fc-event-title-container {
  -ms-flex-positive: 1;
      flex-grow: 1;
  -ms-flex-negative: 1;
      flex-shrink: 1;
  min-width: 0;
}
.fc-h-event .fc-event-title {
  display: inline-block;
  vertical-align: top;
  left: 0;
  right: 0;
  max-width: 100%;
  overflow: hidden;
}
.fc-h-event.fc-event-selected:before {
  top: calc(var(--spacer-2) * -1);
  bottom: calc(var(--spacer-2) * -1);
}
.fc-h-event:not(.fc-event-selected) .fc-event-resizer {
  top: calc(var(--fc-event-border-width) * -1);
  bottom: calc(var(--fc-event-border-width) * -1);
  width: var(--fc-event-resizer-size);
}
.fc-h-event .fc-h-event.fc-event-selected .fc-event-resizer {
  top: 50%;
  margin-top: calc(var(--fc-event-border-width) * -1);
}

/*rtl:begin:ignore*/
.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start) {
  border-left-width: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end) {
  border-right-width: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start {
  cursor: w-resize;
  left: calc(var(--fc-event-border-width) * -1);
}
.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end {
  cursor: e-resize;
  right: calc(var(--fc-event-border-width) * -1);
}
.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start {
  left: calc(var(--fc-event-border-width) * -1);
}
.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end {
  right: calc(var(--fc-event-border-width) * -1);
}

/*rtl:end:ignore*/
/*rtl:begin:ignore*/
.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end) {
  border-left-width: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start) {
  border-right-width: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end {
  cursor: w-resize;
  left: calc(var(--fc-event-border-width) * -1);
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}
.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start {
  cursor: e-resize;
  right: calc(var(--fc-event-border-width) * -1);
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
}
.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end {
  left: calc(var(--fc-event-border-width) * -1);
}
.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start {
  right: calc(var(--fc-event-border-width) * -1);
}

/*rtl:end:ignore*/
.fc .fc-popover {
  position: absolute;
  z-index: 1070;
  box-shadow: var(--box-shadow);
  border-radius: var(--border-radius);
}
.fc .fc-popover .fc-daygrid-event.fc-event-start,
.fc .fc-popover .fc-daygrid-event.fc-event-end {
  margin-left: 0;
  margin-right: 0;
}
.fc .fc-popover .fc-h-event {
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}
.fc .fc-popover-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
      flex-direction: row;
  -ms-flex-pack: justify;
      justify-content: space-between;
  -ms-flex-align: center;
      align-items: center;
  padding: var(--fc-cell-padding-y) var(--fc-cell-padding-y) calc(var(--fc-cell-padding-y) - var(--fc-event-spacer-y)) var(--fc-cell-padding-y);
}
.fc .fc-popover-title {
  font-weight: 600;
}
.fc .fc-popover-close {
  cursor: pointer;
  opacity: 0.6;
  transition: opacity ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .fc .fc-popover-close {
    transition: none;
  }
}
.fc .fc-popover-close:hover {
  opacity: 1;
}
.fc .fc-more-popover .fc-popover-body {
  min-width: 220px;
  padding: var(--fc-cell-padding-y);
  padding-top: 0;
}

.fc-theme-standard .fc-popover {
  border: var(--fc-popover-border-width) solid var(--fc-popover-border-color);
  background-color: var(--fc-popover-bg);
}

.fc-daygrid-day-frame:before, .fc-daygrid-day-frame:after,
.fc-daygrid-day-events:before,
.fc-daygrid-day-events:after,
.fc-daygrid-event-harness:before,
.fc-daygrid-event-harness:after {
  content: "";
  clear: both;
  display: table;
}

.fc .fc-daygrid-body {
  position: relative;
  z-index: 1;
}
.fc .fc-daygrid-day.fc-day-today {
  background-color: var(--fc-today-bg);
}
.fc .fc-daygrid-day-frame {
  position: relative;
  min-height: 100%;
}
.fc .fc-daygrid-day-top {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row-reverse;
      flex-direction: row-reverse;
}
.fc .fc-day-other .fc-daygrid-day-top {
  opacity: 0.3;
}
.fc .fc-daygrid-day-number {
  position: relative;
  z-index: 4;
  padding: var(--fc-event-padding-x);
  padding-bottom: calc(var(--fc-event-padding-x) - var(--fc-event-spacer-y));
  color: var(--body-color);
}
.fc .fc-daygrid-day-events {
  margin-top: var(--fc-event-spacer-y);
}
.fc .fc-daygrid-body-balanced .fc-daygrid-day-events {
  position: absolute;
  left: 0;
  right: 0;
}
.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events {
  position: relative;
  min-height: calc(var(--body-line-height-computed) + var(--fc-event-padding-y) * 2 + var(--fc-event-border-width) * 2 + var(--fc-event-spacer-y));
}
.fc .fc-daygrid-body-natural .fc-daygrid-day-events {
  margin-bottom: calc(var(--fc-event-padding-y) + var(--fc-event-spacer-y));
}
.fc .fc-daygrid-event-harness {
  position: relative;
}
.fc .fc-daygrid-event-harness-abs {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
.fc .fc-daygrid-bg-harness {
  position: absolute;
  top: 0;
  bottom: 0;
}
.fc .fc-daygrid-day-bg .fc-non-business {
  z-index: 1;
}
.fc .fc-daygrid-day-bg .fc-bg-event {
  z-index: 2;
}
.fc .fc-daygrid-day-bg .fc-highlight {
  z-index: 3;
}
.fc .fc-daygrid-event {
  z-index: 6;
  margin-top: var(--fc-event-spacer-y);
}
.fc .fc-daygrid-event.fc-event-mirror {
  z-index: 7;
}
.fc .fc-daygrid-day-bottom:before {
  content: "";
  clear: both;
  display: table;
}
.fc .fc-daygrid-more-link {
  position: relative;
  z-index: 4;
  margin: var(--fc-event-spacer-y) var(--fc-event-spacer-x) 0 var(--fc-event-spacer-x);
  background-color: var(--fc-event-more-bg);
  color: var(--link-color);
  display: block;
  padding: var(--fc-event-padding-y) var(--fc-event-padding-y);
  text-align: center;
  font-size: var(--body-font-size-sm);
  line-height: var(--body-line-height-sm);
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .fc .fc-daygrid-more-link {
    transition: none;
  }
}
.fc .fc-daygrid-more-link:hover, .fc .fc-daygrid-more-link:focus {
  background-color: var(--fc-event-more-hover-bg);
}
.fc .fc-daygrid-week-number {
  position: absolute;
  z-index: 5;
  top: 0;
  padding: var(--fc-event-padding-x);
  min-width: 1.5em;
  text-align: center;
  color: var(--fc-weekday-color);
}

.fc-liquid-hack .fc-daygrid-day-frame {
  position: static;
}

/*rtl:begin:ignore*/
.fc-direction-ltr .fc-daygrid-event.fc-event-start,
.fc-direction-rtl .fc-daygrid-event.fc-event-end {
  margin-left: var(--fc-event-spacer-x);
}

.fc-direction-ltr .fc-daygrid-event.fc-event-end,
.fc-direction-rtl .fc-daygrid-event.fc-event-start {
  margin-right: var(--fc-event-spacer-x);
}

.fc-direction-ltr .fc-daygrid-week-number {
  left: 0;
}

.fc-direction-rtl .fc-daygrid-week-number {
  right: 0;
}

/*rtl:end:ignore*/
.fc-daygrid-event {
  position: relative;
  white-space: nowrap;
  border-radius: var(--border-radius);
  padding: var(--fc-event-padding-y) var(--fc-event-padding-x);
}

.fc-daygrid-block-event .fc-event-time {
  font-weight: 600;
}

.fc-daygrid-dot-event {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  color: var(--body-color);
  padding: var(--fc-event-padding-y) 0;
  background-color: var(--fc-bg);
  border: var(--fc-cell-border-width) solid var(--fc-cell-border-color);
  transition: color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .fc-daygrid-dot-event {
    transition: none;
  }
}
.fc-daygrid-dot-event .fc-event-title {
  margin-right: var(--spacer-2);
  -ms-flex-positive: 1;
      flex-grow: 1;
  -ms-flex-negative: 1;
      flex-shrink: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 600;
}
.fc-daygrid-dot-event.fc-event-selected:before {
  top: calc(var(--spacer-2) * -1);
  bottom: calc(var(--spacer-2) * -1);
}

.fc-daygrid-event-dot {
  margin: 0 var(--fc-event-spacer-x);
  box-sizing: content-box;
  padding: var(--spacer-1);
  background-color: var(--fc-event-bg);
  border-radius: var(--border-radius-pill);
}

/*rtl:begin:ignore*/
.fc-direction-ltr .fc-daygrid-event .fc-event-time {
  margin-right: var(--spacer-2);
}

.fc-direction-rtl .fc-daygrid-event .fc-event-time {
  margin-left: var(--spacer-2);
}

/*rtl:end:ignore*/
.fc-v-event {
  display: block;
  border: var(--fc-event-border-width) solid var(--fc-event-border-color);
  background-color: var(--fc-event-bg);
}
.fc-v-event .fc-event-main {
  color: var(--fc-event-color);
  height: 100%;
}
.fc-v-event .fc-event-main-frame {
  height: 100%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
}
.fc-v-event .fc-event-time {
  -ms-flex-positive: 0;
      flex-grow: 0;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  max-height: 100%;
  overflow: hidden;
}
.fc-v-event .fc-event-title-container {
  -ms-flex-positive: 1;
      flex-grow: 1;
  -ms-flex-negative: 1;
      flex-shrink: 1;
  min-height: 0;
}
.fc-v-event .fc-event-title {
  top: 0;
  bottom: 0;
  max-height: 100%;
  overflow: hidden;
}
.fc-v-event:not(.fc-event-start) {
  border-top-width: 0;
}
.fc-v-event:not(.fc-event-end) {
  border-bottom-width: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.fc-v-event.fc-event-selected:before {
  left: calc(var(--spacer-2) * -1);
  right: calc(var(--spacer-2) * -1);
}
.fc-v-event .fc-event-resizer-start {
  cursor: n-resize;
}
.fc-v-event .fc-event-resizer-end {
  cursor: s-resize;
}
.fc-v-event:not(.fc-event-selected) .fc-event-resizer {
  height: var(--fc-event-resizer-size);
  left: calc(var(--fc-event-border-width) * -1);
  right: calc(var(--fc-event-border-width) * -1);
}
.fc-v-event:not(.fc-event-selected) .fc-event-resizer-start {
  top: calc(var(--fc-event-border-width) * -1);
}
.fc-v-event:not(.fc-event-selected) .fc-event-resizer-end {
  bottom: calc(var(--fc-event-border-width) * -1);
}
.fc-v-event.fc-event-selected .fc-event-resizer {
  left: 50%;
  margin-left: calc(var(--fc-event-border-width) * -1);
}
.fc-v-event.fc-event-selected .fc-event-resizer-start {
  top: calc(var(--fc-event-border-width) * -1);
}
.fc-v-event.fc-event-selected .fc-event-resizer-end {
  bottom: calc(var(--fc-event-border-width) * -1);
}

.fc .fc-timegrid .fc-daygrid-body {
  z-index: 2;
}
.fc .fc-timegrid-divider {
  padding: 0 0 var(--fc-event-border-width);
}
.fc .fc-timegrid-body {
  position: relative;
  z-index: 1;
  min-height: 100%;
}
.fc .fc-timegrid-axis-chunk {
  position: relative;
}
.fc .fc-timegrid-axis-chunk > table {
  position: relative;
  z-index: 1;
}
.fc .fc-timegrid-slots {
  position: relative;
  z-index: 1;
}
.fc .fc-timegrid-slot {
  height: calc(var(--body-line-height-computed) + var(--fc-event-padding-y) * 2 + var(--fc-event-border-width) * 2);
  border-bottom: 0;
}
.fc .fc-timegrid-slot:empty:before {
  content: " ";
}
.fc .fc-timegrid-slot-minor {
  border-top-style: dotted;
}
.fc .fc-timegrid-slot-label-cushion {
  display: inline-block;
  white-space: nowrap;
}
.fc .fc-timegrid-slot-label {
  vertical-align: middle;
}
.fc .fc-timegrid-axis-cushion,
.fc .fc-timegrid-slot-label-cushion {
  padding: 0 var(--fc-event-padding-x);
}
.fc .fc-timegrid-axis-frame-liquid {
  height: 100%;
}
.fc .fc-timegrid-axis-frame {
  overflow: hidden;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
}
.fc .fc-timegrid-axis-cushion {
  max-width: 60px;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  color: var(--body-color);
  font-weight: 600;
}
.fc .fc-timegrid-col.fc-day-today {
  background-color: var(--fc-today-bg);
}
.fc .fc-timegrid-col-frame {
  min-height: 100%;
  position: relative;
}
.fc .fc-timegrid-col-bg {
  z-index: 2;
}
.fc .fc-timegrid-col-bg .fc-non-business {
  z-index: 1;
}
.fc .fc-timegrid-col-bg .fc-bg-event {
  z-index: 2;
}
.fc .fc-timegrid-col-bg .fc-highlight {
  z-index: 3;
}
.fc .fc-timegrid-bg-harness {
  position: absolute;
  left: 0;
  right: 0;
}
.fc .fc .fc-timegrid-col-events {
  z-index: 3;
}
.fc .fc-timegrid-now-indicator-container {
  bottom: 0;
  overflow: hidden;
}
.fc .fc-timegrid-now-indicator-line {
  position: absolute;
  z-index: 4;
  left: 0;
  right: 0;
  border-style: solid;
  border-color: var(--fc-event-color);
  border-width: 1em 0 0;
}
.fc .fc-timegrid-now-indicator-arrow {
  position: absolute;
  z-index: 4;
  margin-top: calc(var(--fc-event-padding-y) * -1);
  border-style: solid;
  border-color: var(--fc-event-color);
}

.fc-liquid-hack .fc-timegrid-axis-frame-liquid,
.fc-liquid-hack .fc-timegrid-col-frame {
  height: auto;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.fc-media-screen .fc-timegrid-cols {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.fc-media-screen .fc-timegrid-cols > table {
  height: 100%;
}
.fc-media-screen .fc-timegrid-col-bg,
.fc-media-screen .fc-timegrid-col-events,
.fc-media-screen .fc-timegrid-now-indicator-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.fc-timegrid-event-harness {
  position: absolute;
}
.fc-timegrid-event-harness > .fc-timegrid-event {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.fc-timegrid-event-harness-inset .fc-timegrid-event,
.fc-timegrid-event.fc-event-mirror,
.fc-timegrid-more-link {
  box-shadow: 0 0 0 var(--fc-event-border-width) var(--fc-bg);
}

.fc-timegrid-event,
.fc-timegrid-more-link {
  border-radius: var(--border-radius);
}

.fc-timegrid-event .fc-event-main {
  padding: var(--fc-event-padding-y) var(--fc-event-padding-x) 0;
}
.fc-timegrid-event .fc-event-time {
  white-space: nowrap;
}

.fc-timegrid-event-short .fc-event-main-frame {
  -ms-flex-direction: row;
      flex-direction: row;
  overflow: hidden;
}
.fc-timegrid-event-short .fc-event-time:after {
  content: " - ";
}

.fc-timegrid-more-link {
  position: absolute;
  z-index: 9999;
  color: inherit;
  background-color: var(--fc-event-more-bg);
  cursor: pointer;
  margin-bottom: var(--fc-event-spacer-y);
}

.fc-timegrid-more-link-inner {
  padding: var(--fc-event-padding-y) var(--fc-event-padding-x);
  top: 0;
}

/*rtl:begin:ignore*/
.fc-direction-ltr .fc-timegrid-col-events {
  margin: 0 2.5% 0 calc(var(--fc-event-border-width) * 2);
}
.fc-direction-ltr .fc-timegrid-more-link {
  right: 0;
}
.fc-direction-ltr .fc-timegrid-now-indicator-arrow {
  left: 0;
  border-width: 1em 0 1em 1em;
  border-top-color: transparent;
  border-bottom-color: transparent;
}

/*rtl:end:ignore*/
/*rtl:begin:ignore*/
.fc-direction-rtl .fc-timegrid-col-events {
  margin: 0 2px 0 calc(var(--fc-event-border-width) * 2);
}
.fc-direction-rtl .fc-timegrid-more-link {
  left: 0;
}
.fc-direction-rtl .fc-timegrid-now-indicator-arrow {
  right: 0;
  border-width: 1em 1em 1em 0;
  border-top-color: transparent;
  border-bottom-color: transparent;
}

/*rtl:end:ignore*/
.fc-theme-standard .fc-list {
  border: var(--fc-cell-border-width) solid var(--fc-cell-border-color);
}

.fc .fc-list-empty {
  height: 100%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
      justify-content: center;
  -ms-flex-align: center;
      align-items: center;
}
.fc .fc-list-empty-cushion {
  margin: var(--spacer) 0;
}
.fc .fc-list-table {
  width: 100%;
  border-style: hidden;
}
.fc .fc-list-table th {
  padding: 0;
}
.fc .fc-list-table tr > * {
  border-left: 0;
  border-right: 0;
}
.fc .fc-list-sticky .fc-list-day > * {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}
.fc .fc-list-table td,
.fc .fc-list-day-cushion {
  padding: var(--fc-cell-padding-y) var(--fc-cell-padding-x);
}
.fc .fc-list-day-cushion:after {
  content: "";
  clear: both;
  display: table;
}
.fc .fc-list-event.fc-event-forced-url {
  cursor: pointer;
}
.fc .fc-list-event-title a {
  color: inherit;
  text-decoration: none;
}
.fc .fc-list-event-graphic,
.fc .fc-list-event-time {
  white-space: nowrap;
  width: 1px;
}
.fc .fc-list-event-dot {
  display: inline-block;
  margin: 0 var(--fc-event-spacer-x);
  box-sizing: content-box;
  padding: var(--spacer-1);
  background-color: var(--fc-event-bg);
  border-radius: var(--border-radius-pill);
}
.fc .fc-list-day-text,
.fc .fc-list-day-side-text {
  color: var(--body-color);
}
.fc .fc-list-day-text:focus,
.fc .fc-list-day-side-text:focus {
  outline: 0;
}

.fc-theme-standard .fc-list-day-cushion {
  background-color: var(--light);
}

.fc-theme-bootstrap a:not([href]) {
  color: inherit;
}

/*rtl:begin:ignore*/
.fc-direction-ltr .fc-list-day-text,
.fc-direction-rtl .fc-list-day-side-text {
  float: left;
}

.fc-direction-ltr .fc-list-day-side-text,
.fc-direction-rtl .fc-list-day-text {
  float: right;
}

.fc-direction-ltr .fc-list-table .fc-list-event-graphic {
  padding-right: 0;
}

.fc-direction-rtl .fc-list-table .fc-list-event-graphic {
  padding-left: 0;
}

/*rtl:end:ignore*/
@media (max-width: 575.98px) {
  .fc .fc-toolbar {
    display: block;
    text-align: center;
  }
  .fc .fc-toolbar .fc-toolbar-title {
    margin-top: var(--spacer);
    margin-bottom: var(--spacer);
  }
  .fc .fc-view-harness {
    overflow: auto;
    min-height: 600px;
    box-shadow: 0 0 0 var(--fc-cell-border-width) var(--fc-cell-border-color) inset;
  }
  .fc .fc-view-harness .fc-view {
    min-width: 900px;
  }
}
/* ------------------------------------------------------------------------------
 *
 *  # Image cropper
 *
 *  Styles for cropper.min.js - a simple jQuery image cropping plugin
 *
 * ---------------------------------------------------------------------------- */
.image-cropper-container {
  /*rtl:ignore*/
  direction: ltr;
  height: 400px;
  width: 100%;
  overflow: hidden;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");
}

.eg-preview .preview {
  /*rtl:ignore*/
  direction: ltr;
}
.eg-preview .preview-lg {
  width: 150px;
  height: 150px;
}
.eg-preview .preview-md {
  width: 120px;
  height: 120px;
}
.eg-preview .preview-sm {
  width: 90px;
  height: 90px;
}
.eg-preview .preview-xs {
  width: 60px;
  height: 60px;
}
.eg-preview .preview-xxs {
  width: 40px;
  height: 40px;
}

.cropper-container {
  --cropper-grid-center-size: calc(var(--border-width) * 9);
  --cropper-grid-size: var(--border-width);
  --cropper-grid-color: var(--gray-300);
  --cropper-resizer-size: 0.375rem;
  --cropper-resizer-color: var(--primary);
  /*rtl:ignore*/
  direction: ltr !important;
  font-size: 0;
  line-height: 0;
  position: relative;
  -ms-touch-action: none;
      touch-action: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.cropper-container img {
  display: block;
  width: 100%;
  min-width: 0 !important;
  max-width: none !important;
  height: 100%;
  min-height: 0 !important;
  max-height: none !important;
  image-orientation: 0deg !important;
}

.cropper-wrap-box,
.cropper-canvas,
.cropper-drag-box,
.cropper-crop-box,
.cropper-modal {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.cropper-wrap-box {
  overflow: hidden;
}

.cropper-drag-box {
  background-color: var(--white);
  opacity: 0;
}

.cropper-modal {
  background-color: var(--black);
  opacity: 0.35;
}

.cropper-view-box {
  display: block;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.cropper-dashed {
  position: absolute;
  display: block;
  border: 0 dashed var(--cropper-grid-color);
  opacity: 0.5;
}
.cropper-dashed.dashed-h {
  top: 33.33333%;
  /*rtl:ignore*/
  left: 0;
  left: 0;
  width: 100%;
  height: 33.33333%;
  border-top-width: var(--cropper-grid-size);
  border-bottom-width: var(--cropper-grid-size);
}
.cropper-dashed.dashed-v {
  top: 0;
  /*rtl:ignore*/
  left: 33.33333%;
  width: 33.33333%;
  height: 100%;
  border-right-width: var(--cropper-grid-size);
  border-left-width: var(--cropper-grid-size);
}

.cropper-center {
  position: absolute;
  top: 50%;
  /*rtl:ignore*/
  left: 50%;
  display: block;
  width: 0;
  height: 0;
  opacity: 0.75;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.cropper-center:before, .cropper-center:after {
  content: " ";
  position: absolute;
  display: block;
  background-color: var(--cropper-grid-color);
}
.cropper-center:before {
  top: 0;
  /*rtl:ignore*/
  left: calc((var(--cropper-grid-center-size) - var(--cropper-grid-size)) * 0.5 * -1);
  width: var(--cropper-grid-center-size);
  height: var(--cropper-grid-size);
}
.cropper-center:after {
  top: calc((var(--cropper-grid-center-size) - var(--cropper-grid-size)) * 0.5 * -1);
  /*rtl:ignore*/
  left: 0;
  width: var(--cropper-grid-size);
  height: var(--cropper-grid-center-size);
}

.cropper-face,
.cropper-line,
.cropper-point {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  opacity: 0.1;
}

.cropper-face {
  top: 0;
  /*rtl:ignore*/
  left: 0;
  background-color: var(--white);
}

.cropper-line {
  background-color: var(--cropper-resizer-color);
}
.cropper-line.line-e {
  top: 0;
  /*rtl:ignore*/
  right: calc(var(--cropper-resizer-size) * 0.5 * -1);
  width: var(--cropper-resizer-size);
  cursor: e-resize;
}
.cropper-line.line-n {
  top: calc(var(--cropper-resizer-size) * 0.5 * -1);
  /*rtl:ignore*/
  left: 0;
  height: var(--cropper-resizer-size);
  cursor: n-resize;
}
.cropper-line.line-w {
  top: 0;
  /*rtl:ignore*/
  left: calc(var(--cropper-resizer-size) * 0.5 * -1);
  width: var(--cropper-resizer-size);
  cursor: w-resize;
}
.cropper-line.line-s {
  bottom: calc(var(--cropper-resizer-size) * 0.5 * -1);
  /*rtl:ignore*/
  left: 0;
  height: var(--cropper-resizer-size);
  cursor: s-resize;
}

.cropper-point {
  background-color: var(--cropper-resizer-color);
  opacity: 0.75;
  width: var(--cropper-resizer-size);
  height: var(--cropper-resizer-size);
}
.cropper-point.point-e {
  top: 50%;
  /*rtl:ignore*/
  right: calc(var(--cropper-resizer-size) * 0.5 * -1);
  margin-top: calc(var(--cropper-resizer-size) * 0.5 * -1);
  cursor: e-resize;
}
.cropper-point.point-n {
  top: calc(var(--cropper-resizer-size) * 0.5 * -1);
  /*rtl:ignore*/
  left: 50%;
  margin-left: calc(var(--cropper-resizer-size) * 0.5 * -1);
  cursor: n-resize;
}
.cropper-point.point-w {
  top: 50%;
  /*rtl:ignore*/
  left: calc(var(--cropper-resizer-size) * 0.5 * -1);
  margin-top: calc(var(--cropper-resizer-size) * 0.5 * -1);
  cursor: w-resize;
}
.cropper-point.point-s {
  bottom: calc(var(--cropper-resizer-size) * 0.5 * -1);
  /*rtl:ignore*/
  left: 50%;
  margin-left: calc(var(--cropper-resizer-size) * 0.5 * -1);
  cursor: s-resize;
}
.cropper-point.point-ne {
  top: calc(var(--cropper-resizer-size) * 0.5 * -1);
  /*rtl:ignore*/
  right: calc(var(--cropper-resizer-size) * 0.5 * -1);
  cursor: ne-resize;
}
.cropper-point.point-nw {
  top: calc(var(--cropper-resizer-size) * 0.5 * -1);
  /*rtl:ignore*/
  left: calc(var(--cropper-resizer-size) * 0.5 * -1);
  cursor: nw-resize;
}
.cropper-point.point-sw {
  bottom: calc(var(--cropper-resizer-size) * 0.5 * -1);
  /*rtl:ignore*/
  left: calc(var(--cropper-resizer-size) * 0.5 * -1);
  cursor: sw-resize;
}
.cropper-point.point-se {
  /*rtl:ignore*/
  right: calc(var(--cropper-resizer-size) * 0.5 * -1);
  bottom: calc(var(--cropper-resizer-size) * 0.5 * -1);
  cursor: se-resize;
  opacity: 1;
  width: var(--cropper-resizer-size);
  height: var(--cropper-resizer-size);
}
.cropper-point.point-se:before {
  content: " ";
  position: absolute;
  /*rtl:ignore*/
  right: -50%;
  bottom: -50%;
  display: block;
  background-color: var(--cropper-resizer-color);
  opacity: 0;
  width: 200%;
  height: 200%;
}

.cropper-bg {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");
}
.cropper-bg[data-color-theme=dark], [data-color-theme=dark] .cropper-bg:not([data-color-theme]), html[data-color-theme=dark] .cropper-bg {
  color-scheme: dark;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAAAM0lEQVQ4EWPU0TX6z4AH+Pj44JFlYGDCK0uE5KgBgyEQWQjF85YtW/BG5mg0DoZopDgWANQBBZDyunGoAAAAAElFTkSuQmCC");
}

.cropper-invisible {
  opacity: 0;
}

.cropper-hide {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
}

.cropper-hidden {
  display: none !important;
}

.cropper-move {
  cursor: move;
}

.cropper-crop {
  cursor: crosshair;
}

/* ------------------------------------------------------------------------------
 *
 *  # GLightbox - lightbox extension
 *
 *  Styles for glightbox.min.js - Mac-style "lightbox" plugin
 *
 * ---------------------------------------------------------------------------- */
.glightbox-container {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999 !important;
  overflow: hidden;
  -ms-touch-action: none;
      touch-action: none;
  -webkit-text-size-adjust: 100%;
     -moz-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
          text-size-adjust: 100%;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  outline: none;
}
.glightbox-container.inactive {
  display: none;
}
.glightbox-container .gcontainer {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 9999;
  overflow: hidden;
}
.glightbox-container .gslider {
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
  position: relative;
  display: -ms-flexbox !important;
  display: flex !important;
  -ms-flex-pack: center;
      justify-content: center;
  -ms-flex-align: center;
      align-items: center;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
  transition: -webkit-transform 0.25s ease;
  transition: transform 0.25s ease;
  transition: transform 0.25s ease, -webkit-transform 0.25s ease;
}
.glightbox-container .gslide {
  width: 100%;
  position: absolute;
  opacity: 1;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  opacity: 0;
}
.glightbox-container .gslide.current {
  opacity: 1;
  z-index: 99999;
  position: relative;
}
.glightbox-container .gslide.prev {
  opacity: 1;
  z-index: 9999;
}
.glightbox-container .gslide iframe,
.glightbox-container .gslide video {
  outline: none !important;
  border: none;
  min-height: 165px;
  -webkit-overflow-scrolling: touch;
  -ms-touch-action: auto;
  touch-action: auto;
}
.glightbox-container .gslide-inner-content {
  width: 100%;
}
.glightbox-container .ginner-container {
  position: relative;
  width: 100%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
      justify-content: center;
  -ms-flex-direction: column;
      flex-direction: column;
  max-width: 100%;
  margin: auto;
  height: 100vh;
}
.glightbox-container .ginner-container.gvideo-container {
  width: 100%;
}
.glightbox-container .ginner-container.desc-bottom, .glightbox-container .ginner-container.desc-top {
  -ms-flex-direction: column;
      flex-direction: column;
}
.glightbox-container .ginner-container.desc-left, .glightbox-container .ginner-container.desc-right {
  max-width: 100% !important;
}

.gslide-image {
  -ms-flex-align: center;
      align-items: center;
}
.gslide-image img {
  max-height: 100vh;
  display: block;
  padding: 0;
  float: none;
  outline: none;
  border: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  max-width: 100vw;
  width: auto;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  -ms-touch-action: none;
      touch-action: none;
  margin: auto;
  min-width: 200px;
}
.gslide-image img.zoomable {
  position: relative;
}
.gslide-image img.dragging {
  cursor: -webkit-grabbing !important;
  cursor: grabbing !important;
  transition: none;
}
.desc-top .gslide-image img, .desc-bottom .gslide-image img {
  width: auto;
}
.desc-left .gslide-image img, .desc-right .gslide-image img {
  width: auto;
  max-width: 100%;
}

.gslide-video {
  position: relative;
  max-width: 100vh;
  width: 100% !important;
}
.gslide-video::before {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 0, 0, 0.34);
  display: none;
}
.gslide-video.playing::before {
  display: none;
}
.gslide-video .gvideo-wrapper {
  width: 100%;
  margin: auto;
}
.gslide-video.fullscreen {
  max-width: 100% !important;
  min-width: 100%;
  height: 75vh;
}
.gslide-video.fullscreen video {
  max-width: 100% !important;
  width: 100% !important;
}

.gslide-inline {
  background-color: #fff;
  text-align: left;
  max-height: calc(100vh - 40px);
  overflow: auto;
  max-width: 100%;
}
.gslide-inline .ginlined-content {
  padding: 20px;
  width: 100%;
}
.gslide-inline .dragging {
  cursor: -webkit-grabbing !important;
  cursor: grabbing !important;
  transition: none;
}

.gslide-media {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  width: auto;
  box-shadow: var(--box-shadow-sm);
}
.zoomed .gslide-media {
  box-shadow: none !important;
}
.desc-top .gslide-media, .desc-bottom .gslide-media {
  margin: 0 auto;
  -ms-flex-direction: column;
      flex-direction: column;
}

.gslide-description {
  position: relative;
  -ms-flex: 1 0 100%;
      flex: 1 0 100%;
}
.gslide-description.description-left, .gslide-description.description-right {
  max-width: 100%;
}
.gslide-description.description-bottom, .gslide-description.description-top {
  margin: 0 auto;
  width: 100%;
}
.gslide-description p {
  margin-bottom: var(--spacer-2);
}
.gslide-description p:last-child {
  margin-bottom: 0;
}
.zoomed .gslide-description {
  display: none;
}

.ginlined-content {
  overflow: auto;
  display: block !important;
  opacity: 1;
}

.gslide-external {
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  min-width: 100%;
  background-color: var(--white);
  padding: 0;
  overflow: auto;
  max-height: 75vh;
  height: 100%;
}

.glightbox-button-hidden {
  display: none;
}

.download-original-image {
  position: absolute;
  top: var(--spacer-2);
  right: var(--spacer-2);
  width: 2.5rem;
  height: 2.5rem;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  color: var(--white);
  border-radius: var(--border-radius-pill);
  background-color: rgba(var(--black-rgb), 0.1);
}
.download-original-image:hover {
  color: var(--white);
  background-color: rgba(var(--black-rgb), 0.15);
}

/*
 * Description for mobiles
 * something like facebook does the description
 * for the photos
*/
.glightbox-mobile .glightbox-container .gslide-description {
  height: auto !important;
  width: 100%;
  background: transparent;
  position: absolute;
  bottom: 15px;
  padding: 19px 11px;
  max-width: 100vw !important;
  -ms-flex-order: 2 !important;
      order: 2 !important;
  max-height: 78vh;
  overflow: auto !important;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.75) 100%);
  transition: opacity 0.25s linear;
  padding-bottom: 50px;
}
.glightbox-mobile .glightbox-container .gslide-title {
  color: var(--white);
  font-size: 1em;
}
.glightbox-mobile .glightbox-container .gslide-desc {
  color: rgba(var(--body-color-rgb), 0.75);
}
.glightbox-mobile .glightbox-container .gslide-desc a,
.glightbox-mobile .glightbox-container .gslide-desc string,
.glightbox-mobile .glightbox-container .gslide-desc .desc-more {
  color: var(--white);
}
.glightbox-mobile .glightbox-container .gslide-desc a {
  font-weight: bold;
}
.glightbox-mobile .glightbox-container .gslide-desc * {
  color: inherit;
}
.glightbox-mobile .glightbox-container .gslide-desc .desc-more {
  opacity: 0.4;
}

.gdesc-open .gslide-media {
  transition: opacity 0.25s ease;
  opacity: 0.4;
}
.gdesc-open .gdesc-inner {
  padding-bottom: 30px;
}

.gdesc-closed .gslide-media {
  transition: opacity 0.25s ease;
  opacity: 1;
}

.greset {
  transition: all 0.25s ease;
}

.gabsolute {
  position: absolute;
}

.grelative {
  position: relative;
}

.glightbox-desc {
  display: none !important;
}

.glightbox-open {
  overflow: hidden;
}

.gloader {
  height: 25px;
  width: 25px;
  -webkit-animation: lightboxLoader 0.8s infinite linear;
          animation: lightboxLoader 0.8s infinite linear;
  border: 2px solid #fff;
  border-right-color: transparent;
  border-radius: 50%;
  position: absolute;
  display: block;
  z-index: 9999;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 47%;
}

.goverlay {
  width: 100%;
  height: calc(100vh + 1px);
  position: fixed;
  top: -1px;
  left: 0;
  background-color: rgba(var(--black-rgb), 0.9);
  will-change: opacity;
}

.gprev,
.gnext,
.gclose {
  z-index: 99999;
  cursor: pointer;
  width: 26px;
  height: 44px;
  border: none;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
      justify-content: center;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-direction: column;
      flex-direction: column;
  -webkit-animation: gfadeIn 0.25s ease;
          animation: gfadeIn 0.25s ease;
}
.gprev svg,
.gnext svg,
.gclose svg {
  display: block;
  width: 25px;
  height: auto;
  margin: 0;
  padding: 0;
}
.gprev.disabled,
.gnext.disabled,
.gclose.disabled {
  opacity: 0.1;
}
.gprev .garrow,
.gnext .garrow,
.gclose .garrow {
  stroke: var(--white);
}

iframe.wait-autoplay {
  opacity: 0;
}

.glightbox-closing .gnext,
.glightbox-closing .gprev,
.glightbox-closing .gclose {
  opacity: 0 !important;
}

.glightbox-clean .gslide-description {
  background-color: var(--white);
}
.glightbox-clean .gdesc-inner {
  padding: var(--spacer);
}
.glightbox-clean .gslide-title {
  margin-bottom: var(--spacer);
}
.glightbox-clean .gslide-desc {
  font-size: var(--body-font-size-sm);
  line-height: var(--body-line-height-sm);
  margin-bottom: 0;
}
.glightbox-clean .gslide-video {
  background-color: var(--black);
}
.glightbox-clean .gprev,
.glightbox-clean .gnext,
.glightbox-clean .gclose {
  background-color: transparent;
  border-radius: var(--border-radius-pill);
}
.glightbox-clean .gprev:hover,
.glightbox-clean .gnext:hover,
.glightbox-clean .gclose:hover {
  background-color: rgba(var(--black-rgb), 0.25);
}
.glightbox-clean .gprev path,
.glightbox-clean .gnext path,
.glightbox-clean .gclose path {
  fill: var(--white);
}
.glightbox-clean .gprev {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 60px;
  height: 60px;
}
.glightbox-clean .gnext {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
}
.glightbox-clean .gclose {
  width: 46px;
  height: 46px;
  top: 20px;
  right: 20px;
  position: absolute;
}
.glightbox-clean .gclose:hover {
  opacity: 1;
}
.glightbox-clean .gclose svg {
  width: 18px;
  height: auto;
}
.glightbox-clean button:focus:not(.focused):not(.disabled) {
  outline: none;
}

.gfadeIn {
  -webkit-animation: gfadeIn 0.25s ease;
          animation: gfadeIn 0.25s ease;
}

.gfadeOut {
  -webkit-animation: gfadeOut 0.25s ease;
          animation: gfadeOut 0.25s ease;
}

.gslideOutLeft {
  -webkit-animation: gslideOutLeft 0.25s ease;
          animation: gslideOutLeft 0.25s ease;
}

.gslideInLeft {
  -webkit-animation: gslideInLeft 0.25s ease;
          animation: gslideInLeft 0.25s ease;
}

.gslideOutRight {
  -webkit-animation: gslideOutRight 0.25s ease;
          animation: gslideOutRight 0.25s ease;
}

.gslideInRight {
  -webkit-animation: gslideInRight 0.25s ease;
          animation: gslideInRight 0.25s ease;
}

.gzoomIn {
  -webkit-animation: gzoomIn 0.25s ease;
          animation: gzoomIn 0.25s ease;
}

.gzoomOut {
  -webkit-animation: gzoomOut 0.25s ease;
          animation: gzoomOut 0.25s ease;
}

@-webkit-keyframes lightboxLoader {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes lightboxLoader {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@-webkit-keyframes gfadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes gfadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes gfadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes gfadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@-webkit-keyframes gslideInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-60%, 0, 0);
            transform: translate3d(-60%, 0, 0);
  }
  to {
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes gslideInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-60%, 0, 0);
            transform: translate3d(-60%, 0, 0);
  }
  to {
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@-webkit-keyframes gslideOutLeft {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(-60%, 0, 0);
            transform: translate3d(-60%, 0, 0);
    opacity: 0;
    visibility: hidden;
  }
}
@keyframes gslideOutLeft {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(-60%, 0, 0);
            transform: translate3d(-60%, 0, 0);
    opacity: 0;
    visibility: hidden;
  }
}
@-webkit-keyframes gslideInRight {
  from {
    opacity: 0;
    visibility: visible;
    -webkit-transform: translate3d(60%, 0, 0);
            transform: translate3d(60%, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes gslideInRight {
  from {
    opacity: 0;
    visibility: visible;
    -webkit-transform: translate3d(60%, 0, 0);
            transform: translate3d(60%, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@-webkit-keyframes gslideOutRight {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(60%, 0, 0);
            transform: translate3d(60%, 0, 0);
    opacity: 0;
  }
}
@keyframes gslideOutRight {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(60%, 0, 0);
            transform: translate3d(60%, 0, 0);
    opacity: 0;
  }
}
@-webkit-keyframes gzoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
            transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 1;
  }
}
@keyframes gzoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
            transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes gzoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
            transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
@keyframes gzoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
            transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
@media (min-width: 768px) {
  .glightbox-container .ginner-container {
    width: auto;
    height: auto;
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .glightbox-container .ginner-container.desc-top .gslide-description {
    -ms-flex-order: 0;
        order: 0;
  }
  .glightbox-container .ginner-container.desc-top .gslide-image,
.glightbox-container .ginner-container.desc-top .gslide-image img {
    -ms-flex-order: 1;
        order: 1;
  }
  .glightbox-container .ginner-container.desc-left .gslide-description {
    -ms-flex-order: 0;
        order: 0;
  }
  .glightbox-container .ginner-container.desc-left .gslide-image {
    -ms-flex-order: 1;
        order: 1;
  }
  .gslide-image img {
    max-height: 97vh;
    max-width: 100%;
  }
  .gslide-image img.zoomable {
    cursor: -webkit-zoom-in;
    cursor: zoom-in;
  }
  .zoomed .gslide-image img.zoomable {
    cursor: -webkit-grab;
    cursor: grab;
  }
  .gslide-inline {
    max-height: 95vh;
  }
  .gslide-external {
    max-height: 100vh;
  }
  .gslide-description.description-left, .gslide-description.description-right {
    max-width: 275px;
  }
  .goverlay {
    background-color: rgba(var(--black-rgb), 0.9);
  }
  .glightbox-clean .description-left .gdesc-inner,
.glightbox-clean .description-right .gdesc-inner {
    position: absolute;
    height: 100%;
    overflow-y: auto;
  }
  .glightbox-clean .gprev {
    bottom: 50%;
    -webkit-transform: translateY(50%);
            transform: translateY(50%);
  }
  .glightbox-clean .gnext {
    bottom: 50%;
    -webkit-transform: translateY(50%);
            transform: translateY(50%);
  }
}
/* ------------------------------------------------------------------------------
 *
 *  # Datatables library
 *
 *  Add advanced interaction controls to any HTML table
 *
 * ---------------------------------------------------------------------------- */
.gridjs-container {
  position: relative;
}

.gridjs-temp {
  position: relative;
}

.gridjs-head {
  width: 100%;
  padding: var(--spacer);
  border-bottom: var(--border-width) solid var(--border-color);
}

.gridjs-footer {
  position: relative;
  padding: var(--spacer);
  border-top: var(--border-width) solid var(--border-color);
}

.gridjs-head:empty,
.gridjs-footer:empty {
  padding: 0;
  border: 0;
}
.gridjs-head button,
.gridjs-footer button {
  cursor: pointer;
  background-color: transparent;
  background-image: none;
  padding: 0;
  margin: 0;
  border: none;
  outline: none;
}

.gridjs-wrapper {
  position: relative;
  overflow: auto;
  width: 100%;
}

.gridjs-search {
  position: relative;
  display: block;
  max-width: 20rem;
}
.gridjs-search:after {
  content: "\f4a8";
  font-family: var(--icon-font-family);
  position: absolute;
  top: 50%;
  right: calc(var(--spacer) * 0.75);
  font-size: var(--icon-font-size);
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  display: inline-block;
  line-height: 1;
  opacity: 0.75;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.gridjs-input {
  --gridjs-input-padding-y: 0.5rem;
  --gridjs-input-padding-x: 0.875rem;
  --gridjs-input-bg: var(--white);
  --gridjs-input-color: var(--body-color);
  --gridjs-input-font-size: var(--body-font-size);
  --gridjs-input-line-height: var(--body-line-height);
  --gridjs-input-border-width: var(--border-width);
  --gridjs-input-border-color: var(--gray-400);
  --gridjs-input-border-radius: var(--border-radius);
  --gridjs-input-focus-bg: var(--white);
  --gridjs-input-focus-border-color: var(--component-active-bg);
  --gridjs-input-focus-box-shadow: var(--focus-ring-box-shadow);
  outline: none;
  display: block;
  width: 100%;
  color: var(--gridjs-input-color);
  background-color: var(--gridjs-input-bg);
  border: var(--gridjs-input-border-width) solid var(--gridjs-input-border-color);
  padding: var(--gridjs-input-padding-y) calc(var(--gridjs-input-padding-x) + var(--icon-font-size) + var(--spacer-2)) var(--gridjs-input-padding-y) var(--gridjs-input-padding-x);
  font-size: var(--gridjs-input-font-size);
  line-height: var(--gridjs-input-line-height);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-clip: padding-box;
  border-radius: var(--gridjs-input-border-radius);
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
.gridjs-input[data-color-theme=dark], [data-color-theme=dark] .gridjs-input:not([data-color-theme]), html[data-color-theme=dark] .gridjs-input {
  color-scheme: dark;
  --gridjs-input-bg: #2c2d33;
  --gridjs-input-focus-bg: #2c2d33;
}
@media (prefers-reduced-motion: reduce) {
  .gridjs-input {
    transition: none;
  }
}
.gridjs-input:focus {
  background-color: var(--gridjs-input-focus-bg);
  border-color: var(--gridjs-input-focus-border-color);
  outline: 0;
  box-shadow: 0 0 0 0 transparent, var(--gridjs-input-focus-box-shadow);
}

@media (max-width: 767.98px) {
  .gridjs-pagination .gridjs-summary {
    margin-bottom: var(--spacer-2);
  }
}
@media (min-width: 768px) {
  .gridjs-pagination {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
        align-items: center;
  }
}
.gridjs-pagination .gridjs-pages {
  --pagination-padding-x: 0.875rem;
  --pagination-padding-y: 0.5rem;
  --pagination-font-size: var(--body-font-size);
  --pagination-margin-start: calc(var(--pagination-border-width) * 2);
  --pagination-color: var(--body-color);
  --pagination-bg: transparent;
  --pagination-border-width: var(--border-width);
  --pagination-border-color: transparent;
  --pagination-border-radius: var(--border-radius);
  --pagination-hover-color: var(--body-color);
  --pagination-hover-bg: var(--gray-200);
  --pagination-hover-border-color: var(--border-color);
  --pagination-focus-color: var(--body-color);
  --pagination-focus-bg: var(--gray-200);
  --pagination-focus-box-shadow: ;
  --pagination-active-color: var(--component-active-color);
  --pagination-active-bg: var(--component-active-bg);
  --pagination-active-border-color: var(--component-active-bg);
  --pagination-disabled-color: var(--gray-500);
  --pagination-disabled-bg: transparent;
  --pagination-disabled-border-color: transparent;
  margin-left: auto;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.gridjs-pagination .gridjs-pages button {
  position: relative;
  display: block;
  padding: var(--pagination-padding-y) var(--pagination-padding-x);
  font-size: var(--pagination-font-size);
  color: var(--pagination-color);
  min-width: calc(var(--pagination-font-size) * var(--body-line-height) + var(--pagination-border-width) * 2 + var(--pagination-padding-y) * 2);
  text-align: center;
  background-color: var(--pagination-bg);
  border: var(--pagination-border-width) solid var(--pagination-border-color);
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
  border-radius: var(--pagination-border-radius);
}
@media (prefers-reduced-motion: reduce) {
  .gridjs-pagination .gridjs-pages button {
    transition: none;
  }
}
.gridjs-pagination .gridjs-pages button:hover {
  z-index: 2;
  color: var(--pagination-hover-color);
  background-color: var(--pagination-hover-bg);
  border-color: var(--pagination-hover-border-color);
}
.gridjs-pagination .gridjs-pages button:focus {
  z-index: 3;
  color: var(--pagination-focus-color);
  background-color: var(--pagination-focus-bg);
  outline: 0;
  box-shadow: var(--pagination-focus-box-shadow);
}
.gridjs-pagination .gridjs-pages button:disabled, .gridjs-pagination .gridjs-pages button[disabled], .gridjs-pagination .gridjs-pages button:hover:disabled {
  color: var(--pagination-disabled-color);
  pointer-events: none;
  background-color: var(--pagination-disabled-bg);
  border-color: var(--pagination-disabled-border-color);
}
.gridjs-pagination .gridjs-pages button.gridjs-spread {
  cursor: default;
  pointer-events: none;
}
.gridjs-pagination .gridjs-pages button.gridjs-currentPage {
  z-index: 3;
  color: var(--pagination-active-color);
  background-color: var(--pagination-active-bg);
  border-color: var(--pagination-active-border-color);
}
.gridjs-pagination .gridjs-pages button:not(:first-child) {
  margin-left: var(--pagination-margin-start);
}

.gridjs-sort {
  float: right;
  height: var(--icon-font-size);
  width: calc(var(--icon-font-size) * 0.5);
  margin-top: calc((var(--body-line-height-computed) - var(--icon-font-size)) * 0.5);
  background-size: 100%;
  background-color: transparent;
  background-repeat: no-repeat;
  background-position-x: center;
  cursor: pointer;
  padding: 0;
  border: none;
  outline: none;
  background-size: contain;
}
.gridjs-sort[data-color-theme=dark], [data-color-theme=dark] .gridjs-sort:not([data-color-theme]), html[data-color-theme=dark] .gridjs-sort {
  color-scheme: dark;
  -webkit-filter: invert(1) grayscale(100%) brightness(200%);
          filter: invert(1) grayscale(100%) brightness(200%);
}
.gridjs-sort-neutral {
  opacity: 0.25;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIGlkPSJDYXBhXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHdpZHRoPSI0MDEuOTk4cHgiIGhlaWdodD0iNDAxLjk5OHB4IiB2aWV3Qm94PSIwIDAgNDAxLjk5OCA0MDEuOTk4IiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA0MDEuOTk4IDQwMS45OTg7IgoJIHhtbDpzcGFjZT0icHJlc2VydmUiPgo8Zz4KCTxnPgoJCTxwYXRoIGQ9Ik03My4wOTIsMTY0LjQ1MmgyNTUuODEzYzQuOTQ5LDAsOS4yMzMtMS44MDcsMTIuODQ4LTUuNDI0YzMuNjEzLTMuNjE2LDUuNDI3LTcuODk4LDUuNDI3LTEyLjg0NwoJCQljMC00Ljk0OS0xLjgxMy05LjIyOS01LjQyNy0xMi44NUwyMTMuODQ2LDUuNDI0QzIxMC4yMzIsMS44MTIsMjA1Ljk1MSwwLDIwMC45OTksMHMtOS4yMzMsMS44MTItMTIuODUsNS40MjRMNjAuMjQyLDEzMy4zMzEKCQkJYy0zLjYxNywzLjYxNy01LjQyNCw3LjkwMS01LjQyNCwxMi44NWMwLDQuOTQ4LDEuODA3LDkuMjMxLDUuNDI0LDEyLjg0N0M2My44NjMsMTYyLjY0NSw2OC4xNDQsMTY0LjQ1Miw3My4wOTIsMTY0LjQ1MnoiLz4KCQk8cGF0aCBkPSJNMzI4LjkwNSwyMzcuNTQ5SDczLjA5MmMtNC45NTIsMC05LjIzMywxLjgwOC0xMi44NSw1LjQyMWMtMy42MTcsMy42MTctNS40MjQsNy44OTgtNS40MjQsMTIuODQ3CgkJCWMwLDQuOTQ5LDEuODA3LDkuMjMzLDUuNDI0LDEyLjg0OEwxODguMTQ5LDM5Ni41N2MzLjYyMSwzLjYxNyw3LjkwMiw1LjQyOCwxMi44NSw1LjQyOHM5LjIzMy0xLjgxMSwxMi44NDctNS40MjhsMTI3LjkwNy0xMjcuOTA2CgkJCWMzLjYxMy0zLjYxNCw1LjQyNy03Ljg5OCw1LjQyNy0xMi44NDhjMC00Ljk0OC0xLjgxMy05LjIyOS01LjQyNy0xMi44NDdDMzM4LjEzOSwyMzkuMzUzLDMzMy44NTQsMjM3LjU0OSwzMjguOTA1LDIzNy41NDl6Ii8+Cgk8L2c+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPC9zdmc+");
  background-position-y: center;
}
.gridjs-sort-asc {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIGlkPSJDYXBhXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHdpZHRoPSIyOTIuMzYycHgiIGhlaWdodD0iMjkyLjM2MXB4IiB2aWV3Qm94PSIwIDAgMjkyLjM2MiAyOTIuMzYxIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCAyOTIuMzYyIDI5Mi4zNjE7IgoJIHhtbDpzcGFjZT0icHJlc2VydmUiPgo8Zz4KCTxwYXRoIGQ9Ik0yODYuOTM1LDE5Ny4yODdMMTU5LjAyOCw2OS4zODFjLTMuNjEzLTMuNjE3LTcuODk1LTUuNDI0LTEyLjg0Ny01LjQyNHMtOS4yMzMsMS44MDctMTIuODUsNS40MjRMNS40MjQsMTk3LjI4NwoJCUMxLjgwNywyMDAuOTA0LDAsMjA1LjE4NiwwLDIxMC4xMzRzMS44MDcsOS4yMzMsNS40MjQsMTIuODQ3YzMuNjIxLDMuNjE3LDcuOTAyLDUuNDI1LDEyLjg1LDUuNDI1aDI1NS44MTMKCQljNC45NDksMCw5LjIzMy0xLjgwOCwxMi44NDgtNS40MjVjMy42MTMtMy42MTMsNS40MjctNy44OTgsNS40MjctMTIuODQ3UzI5MC41NDgsMjAwLjkwNCwyODYuOTM1LDE5Ny4yODd6Ii8+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPC9zdmc+");
  background-position-y: 35%;
}
.gridjs-sort-desc {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIGlkPSJDYXBhXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHdpZHRoPSIyOTIuMzYycHgiIGhlaWdodD0iMjkyLjM2MnB4IiB2aWV3Qm94PSIwIDAgMjkyLjM2MiAyOTIuMzYyIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCAyOTIuMzYyIDI5Mi4zNjI7IgoJIHhtbDpzcGFjZT0icHJlc2VydmUiPgo8Zz4KCTxwYXRoIGQ9Ik0yODYuOTM1LDY5LjM3N2MtMy42MTQtMy42MTctNy44OTgtNS40MjQtMTIuODQ4LTUuNDI0SDE4LjI3NGMtNC45NTIsMC05LjIzMywxLjgwNy0xMi44NSw1LjQyNAoJCUMxLjgwNyw3Mi45OTgsMCw3Ny4yNzksMCw4Mi4yMjhjMCw0Ljk0OCwxLjgwNyw5LjIyOSw1LjQyNCwxMi44NDdsMTI3LjkwNywxMjcuOTA3YzMuNjIxLDMuNjE3LDcuOTAyLDUuNDI4LDEyLjg1LDUuNDI4CgkJczkuMjMzLTEuODExLDEyLjg0Ny01LjQyOEwyODYuOTM1LDk1LjA3NGMzLjYxMy0zLjYxNyw1LjQyNy03Ljg5OCw1LjQyNy0xMi44NDdDMjkyLjM2Miw3Ny4yNzksMjkwLjU0OCw3Mi45OTgsMjg2LjkzNSw2OS4zNzd6Ii8+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPC9zdmc+");
  background-position-y: 65%;
}

.gridjs-table {
  max-width: 100%;
  overflow: auto;
}
.gridjs-table.table-bordered > :not(caption) > * {
  border-bottom-width: 0;
}
.gridjs-head ~ .gridjs-wrapper .gridjs-table.table-bordered > :not(caption) > * {
  border-top-width: 0;
}
.gridjs-table.table-bordered > :not(caption) > * > *:first-child {
  border-left: 0;
}
.gridjs-table.table-bordered > :not(caption) > * > *:last-child {
  border-right: 0;
}

.gridjs-th {
  position: relative;
}
.gridjs-th .gridjs-th-content {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  float: left;
}

.gridjs-th-sort {
  cursor: pointer;
}
.gridjs-th-sort .gridjs-th-content {
  width: calc(100% - var(--icon-font-size));
}

.gridjs-table .gridjs-th-fixed {
  --gridjs-header-bg: var(--white);
  position: -webkit-sticky;
  position: sticky;
  background-color: var(--gridjs-header-bg);
}
.gridjs-table .gridjs-th-fixed[data-color-theme=dark], [data-color-theme=dark] .gridjs-table .gridjs-th-fixed:not([data-color-theme]), html[data-color-theme=dark] .gridjs-table .gridjs-th-fixed {
  color-scheme: dark;
  --gridjs-header-bg: #2c2d33;
}
.gridjs-table .gridjs-th-fixed:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: calc(var(--table-border-width) * -1);
  border-bottom: var(--table-border-width) solid var(--table-group-separator-color);
}

.gridjs-tr-selected td {
  background-color: var(--table-active-bg);
}

.gridjs-message {
  text-align: center;
}

.gridjs-td .gridjs-checkbox {
  display: block;
  margin: auto;
  cursor: pointer;
}

.gridjs-resizable {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: var(--spacer-1);
}
.gridjs-resizable:hover {
  cursor: ew-resize;
  background-color: var(--primary);
}

/* ------------------------------------------------------------------------------
 *
 *  # Datatables library
 *
 *  Add advanced interaction controls to any HTML table
 *
 * ---------------------------------------------------------------------------- */
.dataTables_wrapper {
  --dt-spacer-y: var(--spacer);
  --dt-spacer-x: var(--spacer);
  --dt-filter-width: 15rem;
  position: relative;
}

.dataTable thead th,
.dataTable thead td {
  position: relative;
}
.dataTable thead .sorting {
  cursor: pointer;
  padding-right: calc(var(--dt-spacer-y) * 2);
}
.dataTable thead .sorting:before,
.dataTable thead .sorting:after,
.dataTable thead .sorting_asc_disabled:before,
.dataTable thead .sorting_desc_disabled:after {
  content: "";
  font-family: var(--icon-font-family);
  position: absolute;
  top: 50%;
  right: var(--dt-spacer-y);
  font-size: var(--body-font-size);
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  display: inline-block;
  line-height: 1;
  opacity: 0.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.dataTable thead .sorting:not([class*=sorting_asc]):not([class*=sorting_desc]):before {
  -webkit-transform: translateY(-20%);
          transform: translateY(-20%);
}
.dataTable thead .sorting:not([class*=sorting_asc]):not([class*=sorting_desc]):after {
  -webkit-transform: translateY(-80%);
          transform: translateY(-80%);
}
.dataTable thead .sorting:not(.sorting_desc):before, .dataTable thead .sorting_asc_disabled:before {
  content: "\f31a";
}
.dataTable thead .sorting:not(.sorting_asc):after, .dataTable thead .sorting_desc_disabled:after {
  content: "\f31d";
}
.dataTable thead .sorting_asc:before, .dataTable thead .sorting_asc:after, .dataTable thead .sorting_desc:before, .dataTable thead .sorting_desc:after {
  opacity: 1;
}
.dataTable tbody th.active,
.dataTable tbody td.active {
  background-color: var(--table-hover-bg);
}
.dataTable .dataTables_empty {
  text-align: center;
}

.dataTables_processing {
  position: absolute;
  top: 50%;
  left: 50%;
  padding: var(--spacer);
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  text-align: center;
  background-color: var(--gray-100);
}

.datatable-header,
.datatable-footer {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: justify;
      justify-content: space-between;
  padding: var(--dt-spacer-y);
  padding-bottom: 0;
}
.card-body .datatable-header,
.card-body .datatable-footer {
  padding-left: 0;
  padding-right: 0;
}
.datatable-header-accent,
.datatable-footer-accent {
  background-color: var(--table-accent-bg);
}

.datatable-header {
  border-bottom: var(--border-width) solid var(--border-color);
}

.datatable-footer {
  border-top: var(--border-width) solid var(--border-color);
}

.dataTables_length {
  margin-bottom: var(--dt-spacer-y);
}
.dataTables_length > label {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
}

.dataTables_filter {
  margin-bottom: var(--dt-spacer-y);
}
.dataTables_filter > label {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
}

.dataTables_info {
  margin-bottom: var(--dt-spacer-y);
}

.dataTables_paginate {
  margin-bottom: var(--dt-spacer-y);
}

.dataTables_scroll {
  clear: both;
}
.dataTables_scroll .dataTables_scrollHead table {
  border-bottom: 0;
}
.dataTables_scroll .dataTables_scrollHead th,
.dataTables_scroll .dataTables_scrollHead td {
  white-space: nowrap;
}
.dataTables_scroll .dataTables_scrollBody {
  -webkit-overflow-scrolling: touch;
}
.dataTables_scroll .dataTables_scrollBody table {
  border-bottom: 0;
}
.dataTables_scroll .dataTables_scrollBody table thead th[class*=sorting]:before, .dataTables_scroll .dataTables_scrollBody table thead th[class*=sorting]:after {
  content: none;
}
.dataTables_scroll .dataTables_scrollBody table tbody tr:first-child > td {
  border-top: 0;
}
.dataTables_scroll .dataTables_scrollBody th,
.dataTables_scroll .dataTables_scrollBody td {
  white-space: nowrap;
}
.dataTables_scroll .dataTables_scrollBody th > .dataTables_sizing,
.dataTables_scroll .dataTables_scrollBody td > .dataTables_sizing {
  height: 0;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.card-body + .dataTables_wrapper,
.card-body + * > .dataTables_wrapper {
  border-top: var(--border-width) solid var(--border-color);
}
.card-body > .dataTables_wrapper .datatable-footer {
  border-top: 0;
}
.card-body > .dataTables_wrapper .datatable-footer .dataTables_length,
.card-body > .dataTables_wrapper .datatable-footer .dataTables_filter,
.card-body > .dataTables_wrapper .datatable-footer .dataTables_info,
.card-body > .dataTables_wrapper .datatable-footer .dataTables_paginate {
  margin-bottom: 0;
}

.card > .dataTables_wrapper .table-bordered > *:first-child > * {
  border-top: 0;
}
.card > .dataTables_wrapper .table-bordered > *:last-child > * {
  border-bottom: 0;
}
.card > .dataTables_wrapper .table-bordered > * > * > *:first-child {
  border-left: 0;
}
.card > .dataTables_wrapper .table-bordered > * > * > *:last-child {
  border-right: 0;
}

.datatable-scroll-lg,
.datatable-scroll,
.datatable-scroll-sm {
  min-height: 0.01%;
}

.datatable-scroll-wrap {
  width: 100%;
  min-height: 0.01%;
  overflow-x: auto;
}

@media (max-width: 575.98px) {
  .datatable-scroll-sm {
    width: 100%;
    overflow-x: scroll;
  }
  .datatable-scroll-sm th,
.datatable-scroll-sm td {
    white-space: nowrap;
  }
}
@media (max-width: 767.98px) {
  .datatable-scroll {
    width: 100%;
    overflow-x: scroll;
  }
  .datatable-scroll th,
.datatable-scroll td {
    white-space: nowrap;
  }
}
@media (max-width: 991.98px) {
  .datatable-scroll-lg {
    width: 100%;
    overflow-x: scroll;
  }
  .datatable-scroll-lg th,
.datatable-scroll-lg td {
    white-space: nowrap;
  }
}
@media (min-width: 576px) {
  .dataTables_filter .form-control {
    width: var(--dt-filter-width);
  }
}
@media (max-width: 575.98px) {
  .datatable-header,
.datatable-footer {
    display: block;
  }
}
/* ------------------------------------------------------------------------------
 *
 *  # Columns reorder
 *
 *  Easily modify the column order of a table through drop-and-drag of column headers
 *
 * ---------------------------------------------------------------------------- */
.DTCR_clonedTable {
  --dt-spacer-y: var(--spacer);
  background-color: rgba(var(--light-rgb), 0.75);
  z-index: 202;
  cursor: move;
}
.DTCR_clonedTable th,
.DTCR_clonedTable td {
  border: var(--border-width) solid var(--border-color) !important;
}

.DTCR_pointer {
  width: var(--border-width);
  background-color: var(--primary);
  z-index: 201;
}

/* ------------------------------------------------------------------------------
 *
 *  # Row Reorder extension
 *
 *  RowReorder adds the ability for rows in a DataTable to be reordered through
 *  user interaction with the table.
 *
 * ---------------------------------------------------------------------------- */
.dt-rowReorder-float {
  position: absolute !important;
  table-layout: static;
  outline: var(--table-border-width) dashed var(--table-border-color);
  outline-offset: calc(var(--table-border-width) * -1);
  background-color: rgba(var(--light-rgb), 0.75);
  z-index: 1030;
  cursor: move;
}

.dt-rowReorder-moving {
  outline: var(--table-border-width) solid var(--table-border-color);
  outline-offset: calc(var(--table-border-width) * -1);
}

.dt-rowReorder-noOverflow {
  overflow-x: hidden;
}

.dataTable td.reorder {
  text-align: center;
  cursor: move;
}

/* ------------------------------------------------------------------------------
 *
 *  # Fixed columns
 *
 *  Extension that "freezes" in place the left most columns in a scrolling DataTable
 *
 * ---------------------------------------------------------------------------- */
.dtfc-fixed-left,
.dtfc-fixed-right {
  --dtfc-bg: var(--white);
}
.dtfc-fixed-left[data-color-theme=dark], [data-color-theme=dark] .dtfc-fixed-left:not([data-color-theme]), html[data-color-theme=dark] .dtfc-fixed-left,
.dtfc-fixed-right[data-color-theme=dark],
[data-color-theme=dark] .dtfc-fixed-right:not([data-color-theme]),
html[data-color-theme=dark] .dtfc-fixed-right {
  color-scheme: dark;
  --dtfc-bg: #2c2d33;
}

.dataTable tr > .dtfc-fixed-left,
.dataTable tr > .dtfc-fixed-right {
  z-index: 1;
  background-color: var(--dtfc-bg);
}

/* ------------------------------------------------------------------------------
 *
 *  # Autofill extension
 *
 *  Spreadsheets such as Excel and Google Docs have a very handy data duplication
 *  option of an auto fill tool
 *
 * ---------------------------------------------------------------------------- */
.dt-autofill-handle {
  position: absolute;
  z-index: 102;
  border: var(--border-width) solid var(--primary);
  background-color: var(--primary);
  width: var(--spacer-2);
  height: var(--spacer-2);
}

.dt-autofill-select {
  position: absolute;
  z-index: 1001;
  background-color: var(--primary);
  background-image: repeating-linear-gradient(45deg, transparent, transparent 0.3125rem, rgba(var(--primary-rgb), 0.75) 0.3125rem, rgba(var(--primary-rgb), 0.75) 0.625rem);
}
.dt-autofill-select.top, .dt-autofill-select.bottom {
  height: var(--border-width);
}
.dt-autofill-select.left, .dt-autofill-select.right {
  width: var(--border-width);
}

.dt-autofill-list {
  --dtaf-bg: var(--white);
  position: fixed;
  top: 50%;
  left: 50%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
      align-items: flex-start;
  -ms-flex-direction: column;
      flex-direction: column;
  width: 31.25rem;
  background-color: var(--dtaf-bg);
  border: var(--border-width) solid var(--border-color);
  z-index: 1055;
  padding: var(--spacer-2) 0;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  border-radius: var(--border-radius);
}
.dt-autofill-list[data-color-theme=dark], [data-color-theme=dark] .dt-autofill-list:not([data-color-theme]), html[data-color-theme=dark] .dt-autofill-list {
  color-scheme: dark;
  --dtaf-bg: #2c2d33;
}
.dt-autofill-list ul {
  margin: 0;
  padding: 0;
  list-style: none;
  width: 100%;
}
.dt-autofill-list ul li {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  cursor: pointer;
  transition: background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .dt-autofill-list ul li {
    transition: none;
  }
}
.dt-autofill-list ul li:hover {
  background-color: var(--gray-200);
}
.dt-autofill-list ul li:active {
  background-color: var(--gray-300);
}
.dt-autofill-list .dt-autofill-question {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  padding: var(--spacer-2) var(--spacer);
}
.dt-autofill-list .dt-autofill-button {
  padding: var(--spacer-1) var(--spacer);
  text-align: right;
  margin-left: auto;
}
.dt-autofill-list .dt-autofill-button .btn {
  padding: 0;
  background-color: transparent;
  border: 0;
  font-size: 0;
  color: var(--body-color);
}
.dt-autofill-list .dt-autofill-button .btn:after {
  content: "\f31c";
  font-family: var(--icon-font-family);
  display: block;
  font-size: var(--icon-font-size);
  width: var(--icon-font-size);
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.dt-autofill-list .dtaf-popover-close {
  position: relative;
  margin-left: auto;
  margin-right: var(--spacer);
  margin-top: var(--spacer-2);
  margin-bottom: var(--spacer);
  padding: var(--spacer-1);
  background-color: var(--gray-200);
  text-align: center;
  cursor: pointer;
  z-index: 12;
  font-size: 0;
  line-height: 1;
  border-radius: var(--border-radius);
  transition: background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .dt-autofill-list .dtaf-popover-close {
    transition: none;
  }
}
.dt-autofill-list .dtaf-popover-close:after {
  content: "\f642";
  font-family: var(--icon-font-family);
  font-size: var(--body-font-size);
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.dt-autofill-list .dtaf-popover-close:hover {
  background-color: var(--gray-300);
}

.dt-autofill-background {
  --dtaf-backdrop-zindex: 1050;
  --dtaf-backdrop-bg: var(--black);
  --dtaf-backdrop-opacity: 0.35;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--dtaf-backdrop-zindex);
  width: 100vw;
  height: 100vh;
  background-color: var(--dtaf-backdrop-bg);
  opacity: var(--dtaf-backdrop-opacity);
}

/* ------------------------------------------------------------------------------
 *
 *  # Select extension
 *
 *  Adds item selection capabilities to a DataTable
 *
 * ---------------------------------------------------------------------------- */
.dataTable tbody > tr.selected,
.dataTable tbody > tr > .selected {
  background-color: var(--table-active-bg);
}

.dataTables_wrapper .select-info,
.dataTables_wrapper .select-item {
  margin-left: var(--spacer-2);
}
@media (max-width: 575.98px) {
  .dataTables_wrapper .select-info,
.dataTables_wrapper .select-item {
    margin-left: 0;
    display: block;
  }
}

.dataTable tbody .select-checkbox {
  --dt-check-width: 1.25rem;
  --dt-check-height: 1.25rem;
  --dt-check-bg: var(--white);
  --dt-check-border: calc(var(--border-width) * 2) solid var(--gray-400);
  --dt-check-border-radius: 0.1875em;
  --dt-checked-bg-color: var(--component-active-bg);
  --dt-checked-border-color: transparent;
  position: relative;
}
.dataTable tbody .select-checkbox[data-color-theme=dark], [data-color-theme=dark] .dataTable tbody .select-checkbox:not([data-color-theme]), html[data-color-theme=dark] .dataTable tbody .select-checkbox {
  color-scheme: dark;
  --dt-check-bg: #2c2d33;
}
.dataTable tbody .select-checkbox:after {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  cursor: pointer;
  width: var(--dt-check-width);
  height: var(--dt-check-height);
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  border-radius: var(--dt-check-border-radius);
  transition: box-shadow var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .dataTable tbody .select-checkbox:after {
    transition: none;
  }
}
.dataTable tbody .select-checkbox:after {
  content: "";
  background-color: var(--dt-check-bg);
  border: var(--dt-check-border);
}
.dataTable tbody .selected .select-checkbox:after {
  background-color: var(--dt-checked-bg-color);
  border-color: var(--dt-checked-border-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2.5' d='M5 10l4 4l6-8'/%3e%3c/svg%3e");
}

/* ------------------------------------------------------------------------------
 *
 *  # Buttons extension
 *
 *  The Buttons extension for DataTables provides a common set of options, API
 *  methods and styling to display buttons that will interact with a DataTable
 *
 * ---------------------------------------------------------------------------- */
.dt-buttons-full .dt-buttons {
  float: none;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
      justify-content: center;
  margin: 0;
  border-bottom: var(--border-width) solid var(--border-color);
  padding: var(--spacer);
  padding-bottom: calc(var(--spacer) * 0.5);
}
.dt-buttons-full .dt-buttons > .btn {
  margin-bottom: calc(var(--spacer) * 0.5);
  float: none;
}

.dt-buttons {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-bottom: var(--dt-spacer-y);
}
.dt-buttons > .btn {
  border-radius: 0;
}
.dt-buttons > .btn:first-child {
  border-top-left-radius: var(--btn-border-radius);
  border-bottom-left-radius: var(--btn-border-radius);
}
.dt-buttons > .btn:last-of-type {
  border-top-right-radius: var(--btn-border-radius);
  border-bottom-right-radius: var(--btn-border-radius);
}
.dt-buttons > .btn + .btn {
  margin-left: calc(var(--btn-border-width) * -1);
}
@media (max-width: 575.98px) {
  .dt-buttons {
    float: none;
    text-align: center;
    display: block;
  }
  .dt-buttons .btn {
    float: none;
  }
}

.dt-button-background {
  --dt-backdrop-zindex: 1050;
  --dt-backdrop-bg: var(--black);
  --dt-backdrop-opacity: 0.35;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: var(--dt-backdrop-bg);
  z-index: var(--dt-backdrop-zindex);
  opacity: var(--dt-backdrop-opacity);
}

.dt-button-collection {
  position: relative;
  opacity: 1 !important;
}
.dt-button-collection:not(.fixed) .dropdown-menu {
  top: 100% !important;
  left: auto !important;
  right: 0 !important;
}
.dt-button-collection .dropdown-menu {
  display: block;
  z-index: 1051;
}
.dt-button-collection.fixed .dropdown-menu {
  position: fixed;
  top: 50%;
  left: 50%;
  padding: var(--spacer-2);
  -webkit-column-gap: var(--spacer-1);
     -moz-column-gap: var(--spacer-1);
          column-gap: var(--spacer-1);
  -webkit-transform: translate(-50%);
          transform: translate(-50%);
}
.dt-button-collection.fixed .dropdown-item {
  border-radius: var(--border-radius);
}
.dt-button-collection > * {
  -webkit-column-break-inside: avoid;
  -moz-column-break-inside: avoid;
       break-inside: avoid;
}
.dt-button-collection.two-column .dropdown-menu {
  width: 18.75rem;
  -webkit-column-count: 2;
     -moz-column-count: 2;
          column-count: 2;
}
.dt-button-collection.three-column .dropdown-menu {
  width: 28.13rem;
  -webkit-column-count: 3;
     -moz-column-count: 3;
          column-count: 3;
}
.dt-button-collection.four-column .dropdown-menu {
  width: 37.5rem;
  -webkit-column-count: 4;
     -moz-column-count: 4;
          column-count: 4;
}

.dt-button-info {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%);
          transform: translate(-50%);
  background-color: var(--white);
  padding: var(--spacer);
  border: var(--border-width) solid var(--border-color);
  z-index: 1080;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-lg);
}
.dt-button-info[data-color-theme=dark], [data-color-theme=dark] .dt-button-info:not([data-color-theme]), html[data-color-theme=dark] .dt-button-info {
  color-scheme: dark;
  background-color: var(--black);
}
.dt-button-info h2 {
  font-size: 1rem;
  margin-bottom: var(--spacer-1);
}

/* ------------------------------------------------------------------------------
 *
 *  # Key Table extension
 *
 *  KeyTable provides Excel like cell navigation on any table. Events (focus, blur,
 *  action etc) can be assigned to individual cells, columns, rows or all cells.
 *
 * ---------------------------------------------------------------------------- */
.dataTable th.focus,
.dataTable td.focus {
  outline: calc(var(--border-width) * 2) solid var(--primary);
  outline-offset: calc(var(--border-width) * -1);
}
.dataTable th.focus-success,
.dataTable td.focus-success {
  outline-color: var(--success);
}
.dataTable th.focus-info,
.dataTable td.focus-info {
  outline-color: var(--info);
}
.dataTable th.focus-warning,
.dataTable td.focus-warning {
  outline-color: var(--warning);
}
.dataTable th.focus-danger,
.dataTable td.focus-danger {
  outline-color: var(--danger);
}

/* ------------------------------------------------------------------------------
 *
 *  # Datatables Scroller
 *
 *  Drawing the rows required for the current display only, for fast operation
 *
 * ---------------------------------------------------------------------------- */
.DTS tbody th,
.DTS tbody td {
  white-space: nowrap;
}
.DTS .DTS_Loading {
  position: absolute;
  top: 50%;
  left: 50%;
  width: calc(var(--spacer) * 10);
  height: var(--spacer);
  z-index: 1;
  border: var(--border-width) solid var(--border-color);
  padding: var(--spacer) 0;
  text-align: center;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.DTS .dataTables_scrollBody {
  z-index: 2;
}

/* ------------------------------------------------------------------------------
 *
 *  # Responsive extension
 *
 *  Optimise the table's layout for different screen sizes through the dynamic 
 *  insertion and removal of columns from the table
 *
 * ---------------------------------------------------------------------------- */
.dtr-inline.collapsed tbody tr td:before,
.dtr-inline.collapsed tbody tr th:before {
  margin-right: var(--spacer-2);
}
.dtr-inline.collapsed tbody tr td:first-child,
.dtr-inline.collapsed tbody tr th:first-child {
  position: relative;
  cursor: pointer;
  white-space: nowrap;
}
.dtr-inline.collapsed tbody tr td:first-child.dataTables_empty:before,
.dtr-inline.collapsed tbody tr th:first-child.dataTables_empty:before {
  display: none;
}

.dtr-column tbody td.control,
.dtr-column tbody th.control {
  position: relative;
  cursor: pointer;
}

.dtr-inline.collapsed tbody tr td:first-child:before,
.dtr-inline.collapsed tbody tr th:first-child:before,
.dtr-column tbody tr td.control:before,
.dtr-column tbody tr th.control:before {
  content: "\f263";
  font-family: var(--icon-font-family);
  display: inline-block;
  font-size: var(--icon-font-size);
  width: var(--icon-font-size);
  line-height: 1;
  position: relative;
  top: calc((var(--icon-font-size) - var(--body-line-height-computed)) * 0.5);
  vertical-align: middle;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dataTable.dtr-inline.collapsed tbody tr.parent td:first-child:before,
.dataTable.dtr-inline.collapsed tbody tr.parent th:first-child:before,
.dataTable.dtr-column tbody tr.parent td.control:before,
.dataTable.dtr-column tbody tr.parent th.control:before {
  content: "\f25f";
}

.dtr-inline.collapsed tbody tr.child td:before {
  display: none;
}

.dataTable tr.child:hover {
  background-color: transparent;
}
.dataTable tr.child .dtr-title {
  display: table-cell;
  font-weight: 600;
  padding-right: calc(var(--spacer) * 2);
}
.dataTable tr.child .dtr-data {
  display: table-cell;
  padding: var(--spacer-2) 0;
  white-space: normal;
}
.dataTable tr td.child {
  white-space: normal;
  position: relative;
}
.dataTable tr td.child > ul {
  display: table;
  table-layout: fixed;
  width: 100%;
  list-style: none;
  margin: 0;
  padding: 0;
}
.dataTable tr td.child > ul > li {
  display: table-row;
}

/* ------------------------------------------------------------------------------
*
*  # Maps
*
*  Common styles for all maps
*
* ---------------------------------------------------------------------------- */
.map-container {
  height: 500px;
}

/* ------------------------------------------------------------------------------
*
*  # ECharts maps
*
*  Custom styles for ECharts maps
*
* ---------------------------------------------------------------------------- */
.map-echarts {
  --map-bg: #f6fbff;
  --map-placeholder-color: #2283e2;
  --map-hover-color: #D53E60;
  --map-border-color: rgba(255,255,255,0.5);
}
.map-echarts[data-color-theme=dark], [data-color-theme=dark] .map-echarts:not([data-color-theme]), html[data-color-theme=dark] .map-echarts {
  color-scheme: dark;
  --map-bg: #343840;
  --map-border-color: rgba(0,0,0,0.25);
}

/* ------------------------------------------------------------------------------
*
*  # Leaflet maps
*
*  Styles for Leafletjs maps
*
* ---------------------------------------------------------------------------- */
.map-leaflet {
  --map-tiles-filter: brightness(0.6) invert(1) contrast(3) hue-rotate(200deg) saturate(0.3) brightness(0.7);
}
.map-leaflet[data-color-theme=dark], [data-color-theme=dark] .map-leaflet:not([data-color-theme]), html[data-color-theme=dark] .map-leaflet {
  color-scheme: dark;
  -webkit-filter: var(--map-tiles-filter, none);
          filter: var(--map-tiles-filter, none);
}

.leaflet-pane,
.leaflet-tile,
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-tile-container,
.leaflet-pane > svg,
.leaflet-pane > canvas,
.leaflet-zoom-box,
.leaflet-image-layer,
.leaflet-layer {
  position: absolute;
  left: 0;
  top: 0;
}

.leaflet-container {
  font-size: var(--body-font-size-sm);
  overflow: hidden;
  -webkit-tap-highlight-color: transparent;
}
.leaflet-container .leaflet-marker-pane img,
.leaflet-container .leaflet-shadow-pane img,
.leaflet-container .leaflet-tile-pane img,
.leaflet-container img.leaflet-image-layer,
.leaflet-container .leaflet-tile {
  max-width: none !important;
  max-height: none !important;
  width: auto;
  padding: 0;
}
.leaflet-container.leaflet-touch-zoom {
  -ms-touch-action: pan-x pan-y;
      touch-action: pan-x pan-y;
}
.leaflet-container.leaflet-touch-drag {
  -ms-touch-action: none;
      touch-action: none;
  -ms-touch-action: pinch-zoom;
      touch-action: pinch-zoom;
}
.leaflet-container.leaflet-touch-drag.leaflet-touch-zoom {
  -ms-touch-action: none;
      touch-action: none;
}

.leaflet-tile,
.leaflet-marker-icon,
.leaflet-marker-shadow {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  -webkit-user-drag: none;
}

.leaflet-safari .leaflet-tile {
  image-rendering: -webkit-optimize-contrast;
}
.leaflet-safari .leaflet-tile-container {
  width: 1600px;
  height: 1600px;
  -webkit-transform-origin: 0 0;
}

.leaflet-marker-icon,
.leaflet-marker-shadow {
  display: block;
}

.leaflet-tile {
  -webkit-filter: inherit;
          filter: inherit;
  visibility: hidden;
}

.leaflet-tile-loaded {
  visibility: inherit;
}

.leaflet-zoom-box {
  border: var(--border-width) dotted var(--border-color);
  background-color: var(--gray-100);
  width: 0;
  height: 0;
  z-index: 800;
}

.leaflet-pane {
  z-index: 400;
}
.leaflet-pane canvas {
  z-index: 100;
}
.leaflet-pane svg {
  z-index: 200;
}

.leaflet-tile-pane {
  z-index: 200;
}

.leaflet-overlay-pane {
  z-index: 400;
}
.leaflet-overlay-pane svg {
  max-width: none !important;
  max-height: none !important;
  -moz-user-select: none;
}

.leaflet-shadow-pane {
  z-index: 500;
}

.leaflet-marker-pane {
  z-index: 600;
}

.leaflet-tooltip-pane {
  z-index: 650;
}

.leaflet-popup-pane {
  z-index: 700;
}

.leaflet-control {
  position: relative;
  z-index: 800;
  pointer-events: auto;
  float: left;
  clear: both;
}

.leaflet-top,
.leaflet-bottom {
  position: absolute;
  z-index: 1000;
  pointer-events: none;
}

.leaflet-top {
  top: 0;
}
.leaflet-top .leaflet-control {
  margin-top: var(--spacer-2);
}

.leaflet-right {
  right: 0;
}
.leaflet-right .leaflet-control {
  float: right;
  margin-right: var(--spacer-2);
}

.leaflet-bottom {
  bottom: 0;
}
.leaflet-bottom .leaflet-control {
  margin-bottom: var(--spacer-2);
}

.leaflet-left {
  left: 0;
}
.leaflet-left .leaflet-control {
  margin-left: var(--spacer-2);
}

.leaflet-fade-anim .leaflet-popup {
  opacity: 0;
  transition: opacity var(--transition-base-timer) linear;
}
.leaflet-fade-anim .leaflet-map-pane .leaflet-popup {
  opacity: 1;
}

.leaflet-zoom-animated {
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
}

svg.leaflet-zoom-animated {
  will-change: transform;
}

.leaflet-zoom-anim .leaflet-zoom-animated {
  transition: -webkit-transform 0.25s cubic-bezier(0, 0, 0.25, 1);
  transition: transform 0.25s cubic-bezier(0, 0, 0.25, 1);
  transition: transform 0.25s cubic-bezier(0, 0, 0.25, 1), -webkit-transform 0.25s cubic-bezier(0, 0, 0.25, 1);
}

.leaflet-zoom-anim .leaflet-tile,
.leaflet-pan-anim .leaflet-tile {
  transition: none;
}

.leaflet-zoom-anim .leaflet-zoom-hide {
  visibility: hidden;
}

.leaflet-interactive {
  cursor: pointer;
}

.leaflet-grab {
  cursor: -webkit-grab;
  cursor: grab;
}

.leaflet-crosshair,
.leaflet-crosshair .leaflet-interactive {
  cursor: crosshair;
}

.leaflet-popup-pane,
.leaflet-control {
  cursor: auto;
}

.leaflet-dragging .leaflet-grab,
.leaflet-dragging .leaflet-grab .leaflet-interactive,
.leaflet-dragging .leaflet-marker-draggable {
  cursor: move;
}

.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-image-layer,
.leaflet-pane > svg path,
.leaflet-tile-container {
  pointer-events: none;
}

.leaflet-marker-icon.leaflet-interactive,
.leaflet-image-layer.leaflet-interactive,
.leaflet-pane > svg path.leaflet-interactive,
svg.leaflet-image-layer.leaflet-interactive path {
  pointer-events: auto;
}

.leaflet-bar {
  border-radius: var(--border-radius);
}
.leaflet-bar a {
  background-color: var(--gray-200);
  border-bottom: var(--border-width) solid var(--border-color);
  width: 26px;
  height: 26px;
  line-height: 26px;
  display: block;
  text-align: center;
  color: var(--body-color);
  transition: background-color ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .leaflet-bar a {
    transition: none;
  }
}
.leaflet-bar a:hover, .leaflet-bar a:focus {
  background-color: var(--gray-300);
}
.leaflet-bar a.leaflet-disabled {
  cursor: default;
  background-color: var(--gray-100);
  color: rgba(var(--body-color-rgb), 0.5);
}
.leaflet-bar a:first-child {
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}
.leaflet-bar a:last-child {
  border-bottom: 0;
  border-bottom-right-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

.leaflet-touch .leaflet-bar a {
  width: 2rem;
  height: 2rem;
}
.leaflet-touch .leaflet-control-zoom-in,
.leaflet-touch .leaflet-control-zoom-out {
  font-size: 1.375rem;
}
.leaflet-touch .leaflet-control-zoom-in span,
.leaflet-touch .leaflet-control-zoom-out span {
  display: inline-block;
  position: relative;
  top: 1px;
}

.leaflet-control-layers {
  background-color: var(--gray-200);
  border-radius: var(--border-radius);
}

.leaflet-control-layers-toggle {
  background-image: url(../../../../../../assets/images/vendor/leaflet/layers.png);
  width: 36px;
  height: 36px;
}
.leaflet-retina .leaflet-control-layers-toggle {
  background-image: url(../../../../../../assets/images/vendor/leaflet/layers-2x.png);
  background-size: 26px 26px;
}
.leaflet-touch .leaflet-control-layers-toggle {
  width: 44px;
  height: 44px;
}
.leaflet-control-layers-toggle label {
  display: block;
}

.leaflet-control-layers .leaflet-control-layers-list,
.leaflet-control-layers-expanded .leaflet-control-layers-toggle {
  display: none;
}

.leaflet-control-layers-expanded {
  padding: var(--spacer-1) var(--spacer-2) var(--spacer-1) var(--spacer-1);
  color: var(--body-color);
  background: var(--gray-200);
}
.leaflet-control-layers-expanded .leaflet-control-layers-list {
  display: block;
  position: relative;
}

.leaflet-control-layers-scrollbar {
  overflow-y: scroll;
  overflow-x: hidden;
  padding-right: var(--spacer-1);
}

.leaflet-control-layers-selector {
  margin-top: 2px;
  position: relative;
  top: 1px;
}

.leaflet-control-layers-separator {
  height: 0;
  border-top: var(--border-width) solid var(--border-color);
  margin: var(--spacer-1) calc(var(--spacer-2) * -1) var(--spacer-1) calc(var(--spacer-1) * -1);
}

.leaflet-default-icon-path {
  background-image: url(../../../../../../assets/images/vendor/leaflet/marker-icon.png);
}

.leaflet-div-icon {
  background-color: var(--gray-100);
  border: var(--border-width) solid var(--border-color);
}

.leaflet-control-attribution {
  background: var(--gray-100);
  margin: 0;
}
.leaflet-control-attribution svg {
  display: inline !important;
}

.leaflet-control-attribution,
.leaflet-control-scale-line {
  padding: 0 var(--spacer-1);
  color: var(--body-color);
}

.leaflet-left .leaflet-control-scale {
  margin-left: var(--spacer-1);
}

.leaflet-bottom .leaflet-control-scale {
  margin-bottom: var(--spacer-1);
}

.leaflet-control-scale-line {
  border: var(--border-width) solid var(--gray-600);
  border-top: none;
  line-height: 1.1;
  padding: var(--spacer-1);
  white-space: nowrap;
  overflow: hidden;
  background-color: var(--gray-100);
}
.leaflet-control-scale-line:not(:first-child) {
  border-top: var(--border-width) solid var(--gray-600);
  border-bottom: none;
  margin-top: calc(var(--border-width) * -1);
}
.leaflet-control-scale-line:not(:first-child):not(:last-child) {
  border-bottom: var(--border-width) solid var(--gray-600);
}

.leaflet-touch .leaflet-control-attribution,
.leaflet-touch .leaflet-control-layers,
.leaflet-touch .leaflet-bar {
  box-shadow: none;
}
.leaflet-touch .leaflet-control-layers,
.leaflet-touch .leaflet-bar {
  border: var(--border-width) solid var(--gray-600);
  background-clip: padding-box;
}

.leaflet-popup {
  position: absolute;
  text-align: center;
  margin-bottom: var(--spacer);
}

.leaflet-popup-content-wrapper {
  text-align: left;
  border-radius: var(--border-radius);
}

.leaflet-popup-content {
  padding: var(--spacer-2) var(--spacer);
  min-height: 1px;
}
.leaflet-popup-content p {
  margin-bottom: var(--spacer);
}

.leaflet-popup-tip-container {
  width: calc(var(--spacer) * 2);
  height: var(--spacer);
  position: absolute;
  left: 50%;
  margin-top: -1px;
  margin-left: calc(var(--spacer) * -1);
  overflow: hidden;
  pointer-events: none;
}

.leaflet-popup-tip {
  width: var(--spacer);
  height: var(--spacer);
  padding: 1px;
  margin: calc(var(--spacer-2) * -1) auto 0;
  pointer-events: auto;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}

.leaflet-popup-content-wrapper,
.leaflet-popup-tip {
  background: var(--gray-100);
  color: var(--body-color);
  box-shadow: var(--box-shadow);
}

.leaflet-container a.leaflet-popup-close-button {
  position: absolute;
  top: 0;
  right: 0;
  border: none;
  text-align: center;
  padding: var(--spacer-2);
  color: var(--gray-700);
  text-decoration: none;
  background: transparent;
}
.leaflet-container a.leaflet-popup-close-button:hover, .leaflet-container a.leaflet-popup-close-button:focus {
  color: var(--body-color);
}

.leaflet-popup-scrolled {
  overflow: auto;
  border-bottom: var(--border-width) solid var(--body-color);
  border-top: var(--border-width) solid var(--body-color);
}

.leaflet-tooltip {
  --leaflet-tooltip-padding-y: calc(var(--spacer) * 0.4);
  --leaflet-tooltip-padding-x: calc(var(--spacer) * 0.6);
  --leaflet-tooltip-bg: var(--black);
  --leaflet-tooltip-color: var(--white);
  position: absolute;
  padding: var(--leaflet-tooltip-padding-y) var(--leaflet-tooltip-padding-x);
  background-color: var(--leaflet-tooltip-bg);
  color: var(--leaflet-tooltip-color);
  white-space: nowrap;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  pointer-events: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}
.leaflet-tooltip[data-color-theme=dark], [data-color-theme=dark] .leaflet-tooltip:not([data-color-theme]), html[data-color-theme=dark] .leaflet-tooltip {
  color-scheme: dark;
  --leaflet-tooltip-bg: var(--white);
  --leaflet-tooltip-color: var(--black);
}

.leaflet-tooltip.leaflet-interactive {
  cursor: pointer;
  pointer-events: auto;
}

@media print {
  .leaflet-control {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
         print-color-adjust: exact;
  }
}
.leaflet-cluster-anim .leaflet-marker-icon,
.leaflet-cluster-anim .leaflet-marker-shadow {
  transition: opacity var(--transition-collapse-timer) ease-in, -webkit-transform var(--transition-collapse-timer) ease-out;
  transition: transform var(--transition-collapse-timer) ease-out, opacity var(--transition-collapse-timer) ease-in;
  transition: transform var(--transition-collapse-timer) ease-out, opacity var(--transition-collapse-timer) ease-in, -webkit-transform var(--transition-collapse-timer) ease-out;
}

.leaflet-cluster-spider-leg {
  transition: stroke-dashoffset var(--transition-collapse-timer) ease-out, stroke-opacity var(--transition-collapse-timer) ease-in;
}

.marker-cluster-small {
  background-color: rgba(181, 226, 140, 0.6);
}
.marker-cluster-small div {
  background-color: rgba(110, 204, 57, 0.6);
}

.marker-cluster-medium {
  background-color: rgba(241, 211, 87, 0.6);
}
.marker-cluster-medium div {
  background-color: rgba(240, 194, 12, 0.6);
}

.marker-cluster-large {
  background-color: rgba(253, 156, 115, 0.6);
}
.marker-cluster-large div {
  background-color: rgba(241, 128, 23, 0.6);
}

.marker-cluster {
  background-clip: padding-box;
  border-radius: var(--border-radius-pill);
}
.marker-cluster div {
  width: 30px;
  height: 30px;
  margin-left: var(--spacer-1);
  margin-top: var(--spacer-1);
  text-align: center;
  font-size: var(--body-font-size-sm);
  border-radius: var(--border-radius-pill);
}
.marker-cluster span {
  line-height: 30px;
}

/* ------------------------------------------------------------------------------
*
*  # Chart styling
*
*  Charts base - container and sizing setup
*
* ---------------------------------------------------------------------------- */
.chart-container {
  position: relative;
  width: 100%;
}
.chart-container.has-scroll {
  overflow-x: scroll;
  overflow-y: visible;
  max-width: 100%;
}
@media (max-width: 575.98px) {
  .chart-container {
    overflow-x: scroll;
    overflow-y: visible;
    max-width: 100%;
  }
}

.chart {
  position: relative;
  display: block;
  width: 100%;
}
[dir=rtl] .chart {
  /*rtl:ignore*/
  direction: ltr;
}
.chart.has-minimum-width {
  min-width: 37.5rem;
}

.has-fixed-height {
  height: 400px;
}

.chart-pie {
  width: 100%;
  height: 400px;
  min-width: 31.25rem;
}

/* ------------------------------------------------------------------------------
*
*  # C3 charts
*
*  Styles for C3.js visualization library
*
* ---------------------------------------------------------------------------- */
.c3 {
  --c3-tooltip-padding-x: calc(var(--spacer) * 0.6);
  --c3-tooltip-padding-y: calc(var(--spacer) * 0.4);
  --c3-tooltip-font-size: var(--body-font-size);
  --c3-tooltip-color: var(--white);
  --c3-tooltip-bg: var(--black);
  --c3-tooltip-border-radius: var(--border-radius);
  --c3-tooltip-border-color: rgba(var(--white-rgb), .2);
}
.c3[data-color-theme=dark], [data-color-theme=dark] .c3:not([data-color-theme]), html[data-color-theme=dark] .c3 {
  color-scheme: dark;
  --c3-tooltip-color: var(--black);
  --c3-tooltip-bg: var(--white);
  --c3-tooltip-border-color: rgba(var(--black-rgb), .2);
}
.c3 svg {
  font-size: var(--body-font-size-sm);
}
.c3 path,
.c3 line {
  fill: none;
}
.c3 text {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.c3-axis line,
.c3-axis path,
.c3-axis-y line,
.c3-axis-y path,
.c3-axis-x line,
.c3-axis-x path {
  stroke: var(--gray-500);
}
.c3-axis .tick,
.c3-axis-y .tick,
.c3-axis-x .tick {
  fill: rgba(var(--body-color-rgb), 0.75);
}

.c3-axis-y-label,
.c3-axis-y2-label,
.c3-axis-x-label,
.c3-axis-x2-label,
.c3-ygrid-line text,
.c3-xgrid-line text {
  fill: var(--body-color);
}

.c3-legend-item-tile,
.c3-xgrid-focus,
.c3-ygrid,
.c3-event-rect,
.c3-bars path,
.c3 path.domain {
  shape-rendering: crispEdges;
}

.c3-grid line {
  stroke: var(--gray-400);
}

.c3-xgrid,
.c3-ygrid {
  stroke-dasharray: 3 3;
}

.c3-text {
  font-weight: 600;
}
.c3-text.c3-empty {
  fill: var(--body-color);
}

.c3-line {
  stroke-width: 2px;
}

.c3-area {
  stroke-width: 0;
  opacity: 0.4;
}

.c3-bar {
  stroke-width: 0;
}
.c3-bar._expanded_ {
  fill-opacity: 0.75;
}

.c3-chart-arc path {
  stroke: var(--card-bg);
  stroke-width: 2px;
}
.c3-chart-arc text {
  fill: var(--white);
  font-size: var(--body-font-size);
}
.c3-chart-arc .c3-gauge-value {
  fill: var(--body-color);
  font-size: 1.625rem;
}

.c3-chart-arcs-title {
  font-size: 1rem;
  fill: var(--body-color);
}

.c3-chart-arcs .c3-chart-arcs-background {
  fill: rgba(var(--body-color-rgb), 0.25);
  stroke: none;
}
.c3-chart-arcs .c3-chart-arcs-gauge-unit {
  fill: var(--body-color);
  font-size: 1rem;
}
.c3-chart-arcs .c3-chart-arcs-gauge-max,
.c3-chart-arcs .c3-chart-arcs-gauge-min {
  fill: var(--body-color);
}

.c3-target.c3-focused {
  opacity: 1;
}
.c3-target.c3-focused path.c3-line,
.c3-target.c3-focused path.c3-step {
  stroke-width: 2px;
}
.c3-target.c3-defocused {
  opacity: 0.3 !important;
}

.c3-region {
  fill: var(--gray-500);
}

.c3-brush .extent {
  fill-opacity: 0.1;
}

.c3-legend-item {
  font-size: var(--body-font-size-sm);
}
.c3-legend-item text {
  fill: var(--body-color);
}

.c3-circle {
  fill: currentColor;
}

.c3-tooltip {
  border-collapse: collapse;
  border-spacing: 0;
  background-color: var(--c3-tooltip-bg);
  color: var(--c3-tooltip-color);
  empty-cells: show;
  border-radius: var(--c3-tooltip-border-radius);
}
.c3-tooltip th {
  padding: var(--c3-tooltip-padding-y) var(--c3-tooltip-padding-x);
  font-weight: 600;
  border-top-left-radius: var(--c3-tooltip-border-radius);
  border-top-right-radius: var(--c3-tooltip-border-radius);
}
.c3-tooltip td {
  padding: var(--c3-tooltip-padding-y) var(--c3-tooltip-padding-x);
  border-top: var(--border-width) solid var(--c3-tooltip-border-color);
  white-space: nowrap;
}
.c3-tooltip td > span {
  display: inline-block;
  margin-right: var(--spacer-2);
  padding: var(--spacer-1);
}
.c3-tooltip td.value {
  text-align: right;
}

/* ------------------------------------------------------------------------------
*
*  # D3.js library
*
*  Basic styles for D3.js visualization library
*
* ---------------------------------------------------------------------------- */
.d3-text {
  fill: var(--body-color);
}

.d3-slice-border {
  stroke: var(--card-bg);
  stroke-width: 1.5px;
}

.d3-state-empty {
  fill: var(--gray-300);
}

.d3-bg {
  fill: var(--card-bg);
}

.d3-axis path,
.d3-axis line {
  stroke: var(--gray-500);
  shape-rendering: crispEdges;
}
.d3-axis path {
  fill: none;
}
.d3-axis .tick,
.d3-axis .tick text {
  font-size: var(--body-font-size-sm);
  fill: rgba(var(--body-color-rgb), 0.75);
}

.d3-axis-transparent path {
  stroke: none;
}

.d3-axis-title {
  font-size: var(--body-font-size-sm);
  fill: var(--body-color);
}

.d3-tip {
  --d3-tooltip-padding-x: calc(var(--spacer) * 0.6);
  --d3-tooltip-padding-y: calc(var(--spacer) * 0.4);
  --d3-tooltip-font-size: var(--body-font-size);
  --d3-tooltip-color: var(--white);
  --d3-tooltip-bg: var(--black);
  --d3-tooltip-arrow-width: 0.8rem;
  --d3-tooltip-arrow-height: 0.4rem;
  --d3-tooltip-border-radius: var(--border-radius);
  position: absolute;
  padding: var(--d3-tooltip-padding-y) var(--d3-tooltip-padding-x);
  background-color: var(--d3-tooltip-bg);
  color: var(--d3-tooltip-color);
  margin-bottom: calc(var(--d3-tooltip-arrow-height) * -1);
  font-size: var(--d3-tooltip-font-size);
  z-index: 1080;
  box-shadow: var(--box-shadow);
  border-radius: var(--d3-tooltip-border-radius);
}
.d3-tip[data-color-theme=dark], [data-color-theme=dark] .d3-tip:not([data-color-theme]), html[data-color-theme=dark] .d3-tip {
  color-scheme: dark;
  --d3-tooltip-color: var(--black);
  --d3-tooltip-bg: var(--white);
}
.d3-tip .d3-tip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}

.d3-tip.n .d3-tip-arrow {
  bottom: calc(var(--d3-tooltip-arrow-height) * -1);
  left: 50%;
  border-width: var(--d3-tooltip-arrow-height) var(--d3-tooltip-arrow-height) 0;
  border-top-color: var(--d3-tooltip-bg);
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.d3-tip.e .d3-tip-arrow {
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  /*rtl:begin:ignore*/
  left: calc(var(--d3-tooltip-arrow-height) * -1);
  border-width: var(--d3-tooltip-arrow-height) var(--d3-tooltip-arrow-height) var(--d3-tooltip-arrow-height) 0;
  border-right-color: var(--d3-tooltip-bg);
  /*rtl:end:ignore*/
}
.d3-tip.w .d3-tip-arrow {
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  /*rtl:begin:ignore*/
  right: calc(var(--d3-tooltip-arrow-height) * -1);
  border-width: var(--d3-tooltip-arrow-height) 0 var(--d3-tooltip-arrow-height) var(--d3-tooltip-arrow-height);
  border-left-color: var(--d3-tooltip-bg);
  /*rtl:end:ignore*/
}
.d3-tip.s .d3-tip-arrow {
  top: 0;
  left: 50%;
  border-width: 0 var(--d3-tooltip-arrow-height) var(--d3-tooltip-arrow-height);
  border-bottom-color: var(--d3-tooltip-bg);
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}

.d3-line {
  fill: none;
}
.d3-line-thin {
  stroke-width: 1px;
}
.d3-line-medium {
  stroke-width: 1.5px;
}
.d3-line-strong {
  stroke-width: 2px;
}

.d3-line-circle {
  fill: var(--card-bg);
  cursor: pointer;
}
.d3-line-circle-thin {
  stroke-width: 1px;
}
.d3-line-circle-medium {
  stroke-width: 1.5px;
}
.d3-line-circle-strong {
  stroke-width: 2px;
}

.d3-line-connect {
  fill: none;
  stroke: var(--gray-400);
}

.d3-grid .tick line {
  stroke-width: 1px;
  stroke: var(--gray-400);
}

.d3-grid-dashed .tick line {
  stroke-dasharray: 4, 2;
  stroke: var(--gray-400);
}
.d3-grid-dashed path {
  stroke-width: 0;
}

.d3-crosshair-overlay {
  fill: none;
  pointer-events: all;
}

.d3-crosshair-pointer text {
  fill: var(--body-color);
}

.d3-crosshair-line {
  fill: none;
  stroke: var(--gray-400);
  stroke-width: 1px;
  shape-rendering: crispEdges;
  pointer-events: none;
}

.bullet-tick line {
  stroke: var(--gray-500);
  stroke-width: 1px;
  shape-rendering: crispEdges;
}
.bullet-tick text {
  fill: rgba(var(--body-color-rgb), 0.75);
  font-size: var(--body-font-size-sm);
}

.bullet-marker {
  stroke-width: 2px;
  shape-rendering: crispEdges;
}

.bullet-title {
  fill: var(--body-color);
  font-weight: 600;
}

.bullet-subtitle {
  fill: var(--body-color);
}

.bullet-1 .bullet-range-1 {
  fill: rgba(var(--primary-rgb), 0.8);
}

.bullet-1 .bullet-range-2 {
  fill: rgba(var(--primary-rgb), 0.4);
}

.bullet-1 .bullet-range-3 {
  fill: var(--primary);
}

.bullet-1 .bullet-measure-1 {
  fill: rgba(var(--primary), 0.9);
}

.bullet-1 .bullet-measure-2 {
  fill: var(--white);
}

.bullet-1 .bullet-marker {
  stroke: rgba(var(--black), 0.25);
}

.bullet-2 .bullet-range-1 {
  fill: rgba(var(--danger-rgb), 0.6);
}

.bullet-2 .bullet-range-2 {
  fill: rgba(var(--danger-rgb), 0.3);
}

.bullet-2 .bullet-range-3 {
  fill: var(--danger);
}

.bullet-2 .bullet-measure-1 {
  fill: rgba(var(--danger-rgb), 0.9);
}

.bullet-2 .bullet-measure-2 {
  fill: var(--white);
}

.bullet-2 .bullet-marker {
  stroke: rgba(var(--black), 0.25);
}

.bullet-3 .bullet-range-1 {
  fill: rgba(var(--success-rgb), 0.7);
}

.bullet-3 .bullet-range-2 {
  fill: rgba(var(--success-rgb), 0.35);
}

.bullet-3 .bullet-range-3 {
  fill: var(--success);
}

.bullet-3 .bullet-measure-1 {
  fill: rgba(var(--success-rgb), 0.9);
}

.bullet-3 .bullet-measure-2 {
  fill: var(--white);
}

.bullet-3 .bullet-marker {
  stroke: rgba(var(--black), 0.25);
}

.counter-icon {
  font-size: calc(var(--icon-font-size) * 2);
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.d3-legend text {
  fill: var(--body-color);
  font-size: var(--body-font-size-sm);
}
.d3-legend line {
  stroke: var(--gray-500);
  stroke-width: 1px;
}

.chart-widget-legend {
  margin: var(--spacer-2) 0 0 0;
  padding: 0;
  fill: var(--body-color);
  font-size: var(--body-font-size-sm);
  text-align: center;
}
.chart-widget-legend li {
  margin: var(--spacer-1) var(--spacer-2) 0;
  padding: var(--spacer-2) var(--spacer-2) var(--spacer-1);
  display: inline-block;
}

/* ------------------------------------------------------------------------------
 *
 *  # Blog layouts
 *
 *  Blog layouts with various blog layouts
 *
 * ---------------------------------------------------------------------------- */
.blog-horizontal .card-img-actions {
  width: 100%;
}
@media (min-width: 576px) {
  .blog-horizontal .card-img-actions {
    width: 45%;
    float: left;
    max-width: 25rem;
    z-index: 10;
  }
}

@media (min-width: 576px) {
  .blog-horizontal-xs .card-img-actions {
    width: 35%;
    max-width: 12.5rem;
  }
}

@media (min-width: 576px) {
  .blog-horizontal-sm .card-img-actions {
    width: 40%;
    max-width: 18.75rem;
  }
}

@media (min-width: 576px) {
  .blog-horizontal-lg .card-img-actions {
    width: 50%;
    max-width: 31.25rem;
  }
}

/* ------------------------------------------------------------------------------
 *
 *  # Mail list
 *
 *  Inbox page - list, read and write
 *
 * ---------------------------------------------------------------------------- */
.table-inbox {
  --inbox-read-row-bg: var(--gray-100);
  --inbox-unread-row-bg: var(--card-bg);
  --inbox-img-size: 2rem;
  table-layout: fixed;
  min-width: 768px;
}
.table-inbox tr {
  cursor: pointer;
  transition: all ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .table-inbox tr {
    transition: none;
  }
}
.table-inbox tr:not([class*=bg-]) {
  background-color: var(--inbox-read-row-bg);
}
.table-inbox tr td:not(:first-child) {
  padding-left: 0;
}
.table-inbox tr.unread {
  font-weight: 700;
}
.table-inbox tr.unread:not([class*=bg-]) {
  background-color: var(--inbox-unread-row-bg);
}

.table-inbox-checkbox {
  width: calc(var(--table-cell-padding-x) * 3);
}

.table-inbox-star,
.table-inbox-attachment {
  width: calc(var(--table-cell-padding-x) + var(--icon-font-size));
}

.table-inbox-image {
  width: calc(var(--table-cell-padding-x) + var(--inbox-img-size));
}

.table-inbox-name {
  width: 15rem;
}

@media (max-width: 575.98px) {
  .table-inbox-subject {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.table-inbox-time {
  text-align: right;
  width: 5.5rem;
}

/* ------------------------------------------------------------------------------
 *
 *  # User profile
 *
 *  Styles for all user profile layouts
 *
 * ---------------------------------------------------------------------------- */
.profile-cover {
  --profile-cover-height: 21.88rem;
  --profile-cover-text-shadow: 0 0 0.1875rem rgba(var(--black-rgb), 0.5);
  position: relative;
}

.profile-cover-text {
  text-shadow: var(--profile-cover-text-shadow);
}

.profile-cover-img {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: var(--profile-cover-height);
}

/* ------------------------------------------------------------------------------
 *
 *  # Login and related forms
 *
 *  Styles related to user login - logins, registration, password revovery, unlock etc.
 *
 * ---------------------------------------------------------------------------- */
.login-cover {
  background: url(../../../../../../assets/images/login_cover.jpg) no-repeat;
  background-size: cover;
}

@media (min-width: 576px) {
  .login-form {
    width: 25rem;
  }
}

/* ------------------------------------------------------------------------------
 *
 *  # Timeline
 *
 *  Styles for timeline in 3 layouts: left, right and centered
 *
 * ---------------------------------------------------------------------------- */
.timeline {
  --timeline-content-padding-x: calc(0.625rem * 2);
  --timeline-line-width: calc(var(--border-width) * 2);
  --timeline-line-color: var(--gray-400);
  --timeline-icon-bg: var(--white);
  --timeline-icon-size: 3rem;
  --timeline-icon-border-width: calc(var(--timeline-line-width) * 2);
  position: relative;
}
.timeline:before, .timeline:after {
  content: "";
  position: absolute;
  left: 50%;
  margin-left: calc(var(--timeline-line-width) * -1);
  background-color: var(--timeline-line-color);
  z-index: 1;
  width: calc(var(--timeline-line-width) * 2);
  height: calc(var(--timeline-line-width) * 2);
  border-radius: var(--border-radius-pill);
}
.timeline:before {
  top: calc(var(--timeline-line-width) * 2 * -1);
}
.timeline:after {
  bottom: calc(var(--timeline-line-width) * 2 * -1);
}

.timeline-container {
  position: relative;
  padding-top: calc(var(--spacer) * 0.5);
  margin-top: calc(var(--spacer) * 0.5 * -1);
  padding-bottom: 1px;
}
.timeline-container:before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: calc(var(--timeline-line-width) * 0.5 * -1);
  background-color: var(--timeline-line-color);
  height: 100%;
  width: var(--timeline-line-width);
}

.timeline-row {
  position: relative;
}

.timeline-date {
  text-align: center;
  background-color: var(--body-bg);
  position: relative;
  z-index: 1;
  padding-top: var(--spacer);
  padding-bottom: var(--spacer);
  margin-bottom: var(--spacer);
}
.timeline-date:before, .timeline-date:after {
  content: "";
  position: absolute;
  left: 50%;
  margin-left: calc(var(--timeline-line-width) * -1);
  background-color: var(--timeline-line-color);
  z-index: 1;
  width: calc(var(--timeline-line-width) * 2);
  height: calc(var(--timeline-line-width) * 2);
  border-radius: var(--border-radius-pill);
}
.timeline-date:before {
  top: 0;
}
.timeline-date:after {
  bottom: 0;
}
.card .timeline-date {
  background-color: var(--card-bg);
}

.timeline-time {
  text-align: center;
  padding-top: var(--spacer);
  padding-bottom: var(--spacer);
  background-color: var(--body-bg);
  position: relative;
  margin-bottom: var(--spacer);
}
.timeline-time:before, .timeline-time:after {
  content: "";
  position: absolute;
  left: 50%;
  margin-left: calc(var(--timeline-line-width) * -1);
  background-color: var(--timeline-line-color);
  width: calc(var(--timeline-line-width) * 2);
  height: calc(var(--timeline-line-width) * 2);
  border-radius: var(--border-radius-pill);
}
.timeline-time:before {
  top: 0;
}
.timeline-time:after {
  bottom: 0;
}
.card .timeline-time {
  background-color: var(--card-bg);
}
@media (min-width: 768px) {
  .timeline-time:before, .timeline-time:after {
    content: none;
  }
}

.timeline-icon {
  margin: 0 auto var(--spacer) auto;
  background-color: car(--timeline-icon-bg);
  border: var(--timeline-icon-border-width) solid var(--body-bg);
  width: var(--timeline-icon-size);
  height: var(--timeline-icon-size);
  border-radius: var(--border-radius-pill);
}
.card .timeline-icon {
  border-color: var(--card-bg);
}
.timeline-icon div {
  height: 100%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  box-shadow: 0 0 0 var(--timeline-line-width) var(--timeline-line-color) inset;
  border-radius: var(--border-radius-pill);
}
.timeline-icon div[class*=bg-]:not(.bg-white):not(.bg-light):not(.bg-transparent) {
  box-shadow: none;
}
.timeline-icon img {
  width: calc(var(--timeline-icon-size) - var(--timeline-icon-border-width) * 2);
  height: calc(var(--timeline-icon-size) - var(--timeline-icon-border-width) * 2);
  border-radius: var(--border-radius-pill);
}

@media (min-width: 768px) {
  .timeline-center .timeline-row-start {
    margin-right: 50%;
    padding-right: calc(var(--timeline-icon-size) - var(--timeline-icon-border-width));
  }
  .timeline-center .timeline-row-end {
    margin-left: 50%;
    padding-left: calc(var(--timeline-icon-size) - var(--timeline-icon-border-width));
  }
  .timeline-start .timeline-container:before, .timeline-start:before, .timeline-start:after,
.timeline-start .timeline-date:before,
.timeline-start .timeline-date:after {
    left: calc(var(--timeline-icon-size) * 0.5);
  }
  .timeline-start .timeline-row,
.timeline-start .timeline-date {
    padding-left: calc(var(--timeline-icon-size) + var(--timeline-content-padding-x) * 0.5);
  }
  .timeline-end .timeline-container:before, .timeline-end:before, .timeline-end:after,
.timeline-end .timeline-date:before,
.timeline-end .timeline-date:after {
    left: auto;
    right: calc(var(--timeline-icon-size) * 0.5);
  }
  .timeline-end:before, .timeline-end:after,
.timeline-end .timeline-date:before,
.timeline-end .timeline-date:after {
    margin-left: 0;
    margin-right: calc(var(--timeline-line-width) * 0.5 * -1);
  }
  .timeline-end .timeline-row,
.timeline-end .timeline-date {
    padding-right: calc(var(--timeline-icon-size) + var(--timeline-content-padding-x) * 0.5);
  }
  .timeline-start .timeline-date,
.timeline-end .timeline-date {
    padding-top: calc(var(--spacer) * 0.5);
    padding-bottom: calc(var(--spacer) * 0.5);
  }
  .timeline-icon {
    position: absolute;
    top: calc(var(--spacer) * 0.5);
  }
  .timeline-icon:after {
    content: "";
    position: absolute;
    top: 50%;
    margin-top: calc(var(--timeline-line-width) * 0.5 * -1);
    height: var(--timeline-line-width);
    width: calc(var(--timeline-icon-size) * 0.5 - var(--timeline-icon-border-width) * 2);
    background-color: var(--timeline-line-color);
    z-index: 1;
  }
  .timeline-start .timeline-icon {
    left: 0;
  }
  .timeline-start .timeline-icon:after {
    left: 100%;
    margin-left: var(--timeline-icon-border-width);
  }
  .timeline-end .timeline-icon {
    right: 0;
  }
  .timeline-end .timeline-icon:after {
    right: 100%;
    margin-right: var(--timeline-icon-border-width);
  }
  .timeline-center .timeline-row-start .timeline-icon {
    left: 100%;
    margin-left: calc(var(--timeline-icon-size) * 0.5 * -1);
  }
  .timeline-center .timeline-row-start .timeline-icon:after {
    right: 100%;
    margin-right: var(--timeline-icon-border-width);
  }
  .timeline-center .timeline-row-end .timeline-icon {
    right: 100%;
    margin-right: calc(var(--timeline-icon-size) * 0.5 * -1);
  }
  .timeline-center .timeline-row-end .timeline-icon:after {
    left: 100%;
    margin-left: var(--timeline-icon-border-width);
  }
  .timeline-center .timeline-row-full .timeline-icon {
    position: static;
  }
  .timeline-center .timeline-row-full .timeline-icon:after {
    content: none;
  }
  .timeline-time {
    padding: 0;
    text-align: inherit;
    background-color: transparent;
  }
  .timeline-time:before {
    content: none;
  }
  .timeline-start .timeline-time,
.timeline-end .timeline-time {
    padding-top: calc(var(--spacer) * 0.5);
    margin-bottom: var(--spacer);
    padding-left: calc(var(--timeline-content-padding-x) * 0.5);
  }
  .timeline-end .timeline-time {
    text-align: right;
    padding-left: 0;
    padding-right: calc(var(--timeline-content-padding-x) * 0.5);
  }
  .timeline-center .timeline-time,
.timeline-center .timeline-row-full .timeline-time {
    position: absolute;
    left: 100%;
    top: calc(var(--spacer) * 0.5 + var(--timeline-icon-border-width));
    width: 100%;
    padding-left: calc(var(--timeline-icon-size) - var(--timeline-icon-border-width));
  }
  .timeline-center .timeline-row-end .timeline-time,
.timeline-center .timeline-row-full .timeline-time {
    left: auto;
    right: 100%;
    padding-left: 0;
    padding-right: calc(var(--timeline-icon-size) - var(--timeline-icon-border-width));
    text-align: right;
  }
  .timeline-center .timeline-row-full .timeline-time {
    right: 50%;
    top: var(--timeline-icon-border-width);
  }
}
/* ------------------------------------------------------------------------------
 *
 *  # Chat layouts
 *
 *  Conversation chat styles - layouts, chat elements, colors, options
 *
 * ---------------------------------------------------------------------------- */
.media-chat-scrollable {
  max-height: 32.5rem;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column-reverse;
      flex-direction: column-reverse;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.media-chat {
  --chat-message-padding-y: 0.625rem;
  --chat-message-padding-x: 1rem;
  --chat-message-bg: var(--gray-300);
  --chat-message-color: var(--body-color);
}

@media (min-width: 576px) {
  .media-chat-item {
    width: 75%;
  }
}

.media-chat-message {
  position: relative;
  padding: var(--chat-message-padding-y) var(--chat-message-padding-x);
  display: inline-block;
  -ms-flex-align: start;
      align-items: flex-start;
  color: var(--chat-message-color);
  border-radius: var(--border-radius);
}
.media-chat-message:not([class*=bg-]) {
  background-color: var(--chat-message-bg);
}

.media-chat-item-reverse {
  --chat-message-bg: var(--primary);
  --chat-message-color: var(--white);
  text-align: right;
  -ms-flex-direction: row-reverse;
      flex-direction: row-reverse;
  -ms-flex-item-align: end;
      align-self: flex-end;
}
.media-chat-item-reverse .media-chat-message {
  text-align: left;
}

.typing-indicator {
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.typing-indicator span {
  height: 0.125rem;
  width: 0.125rem;
  margin: 0 0.0625rem;
  background-color: var(--body-color);
  display: block;
  border-radius: var(--border-radius-pill);
  opacity: 0.4;
}
.typing-indicator span:nth-of-type(1) {
  -webkit-animation: 1.2s blink infinite 0.2s;
          animation: 1.2s blink infinite 0.2s;
}
.typing-indicator span:nth-of-type(2) {
  -webkit-animation: 1.2s blink infinite 0.4s;
          animation: 1.2s blink infinite 0.4s;
}
.typing-indicator span:nth-of-type(3) {
  -webkit-animation: 1.2s blink infinite 0.6s;
          animation: 1.2s blink infinite 0.6s;
}
@-webkit-keyframes blink {
  50% {
    opacity: 1;
  }
}
@keyframes blink {
  50% {
    opacity: 1;
  }
}

/* ------------------------------------------------------------------------------
 *
 *  # Ribbons
 *
 *  Styles for ribbons - corner, vertical, horizontal
 *
 * ---------------------------------------------------------------------------- */
.ribbon-container {
  width: 6.5rem;
  height: 6.5rem;
  overflow: hidden;
  position: absolute;
  top: calc(var(--card-border-width) * -1);
  right: calc(var(--card-border-width) * -1);
}

.ribbon {
  text-align: center;
  position: relative;
  padding: 0.3125rem 0;
  left: -0.6875rem;
  top: 1.563rem;
  width: 9.375rem;
  z-index: 10;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}

/* ------------------------------------------------------------------------------
 *
 *  # Helper classes
 *
 *  Custom helper classes used in the template.
 *
 * ---------------------------------------------------------------------------- */
.spinner {
  display: inline-block;
  -webkit-animation: rotation 1s linear infinite;
          animation: rotation 1s linear infinite;
}

.spinner-reverse {
  display: inline-block;
  -webkit-animation: rotation_reverse 1s linear infinite;
          animation: rotation_reverse 1s linear infinite;
}

@-webkit-keyframes rotation {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes rotation {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@-webkit-keyframes rotation_reverse {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
  }
}
@keyframes rotation_reverse {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
  }
}
.invert-dark[data-color-theme=dark], [data-color-theme=dark] .invert-dark:not([data-color-theme]), html[data-color-theme=dark] .invert-dark {
  color-scheme: dark;
  -webkit-filter: invert(1) grayscale(100);
          filter: invert(1) grayscale(100);
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none !important;
}

/* ------------------------------------------------------------------------------
 *
 *  # Demo styles
 *
 *  Styles used for demostration purposes only
 *
 * ---------------------------------------------------------------------------- */
.glyphs > div > div {
  padding: var(--spacer-2) var(--spacer);
  transition: all ease-in-out var(--transition-base-timer);
}
@media (prefers-reduced-motion: reduce) {
  .glyphs > div > div {
    transition: none;
  }
}
.glyphs > div > div:hover {
  background-color: var(--dark);
  color: var(--white);
  border-radius: var(--border-radius);
}
.glyphs > div > div:hover[data-color-theme=dark], [data-color-theme=dark] .glyphs > div > div:hover:not([data-color-theme]), html[data-color-theme=dark] .glyphs > div > div:hover {
  color-scheme: dark;
  background-color: var(--white);
  color: var(--black);
}

.demo-velocity-box {
  padding: var(--spacer-2) var(--spacer);
  margin-bottom: var(--spacer);
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
      justify-content: space-between;
  -ms-flex-align: center;
      align-items: center;
  border: var(--border-width) solid var(--border-color);
  text-align: center;
  background-color: var(--light);
  border-radius: var(--border-radius);
  outline: calc(var(--border-width) * 2) solid transparent;
  box-shadow: 0 0 0 0 var(--primary);
}