@charset "UTF-8";.blog-horizontal .card-img-actions{width:100%}@media (min-width:576px){.blog-horizontal .card-img-actions{width:45%;float:right;max-width:25rem;z-index:10}}@media (min-width:576px){.blog-horizontal-xs .card-img-actions{width:35%;max-width:12.5rem}}@media (min-width:576px){.blog-horizontal-sm .card-img-actions{width:40%;max-width:18.75rem}}@media (min-width:576px){.blog-horizontal-lg .card-img-actions{width:50%;max-width:31.25rem}}.table-inbox{--inbox-read-row-bg:var(--gray-100);--inbox-unread-row-bg:var(--card-bg);--inbox-img-size:2rem;table-layout:fixed;min-width:768px}.table-inbox tr{cursor:pointer;transition:all ease-in-out var(--transition-base-timer)}@media (prefers-reduced-motion:reduce){.table-inbox tr{transition:none}}.table-inbox tr:not([class*=bg-]){background-color:var(--inbox-read-row-bg)}.table-inbox tr td:not(:first-child){padding-right:0}.table-inbox tr.unread{font-weight:700}.table-inbox tr.unread:not([class*=bg-]){background-color:var(--inbox-unread-row-bg)}.table-inbox-checkbox{width:calc(var(--table-cell-padding-x) * 3)}.table-inbox-attachment,.table-inbox-star{width:calc(var(--table-cell-padding-x) + var(--icon-font-size))}.table-inbox-image{width:calc(var(--table-cell-padding-x) + var(--inbox-img-size))}.table-inbox-name{width:15rem}@media (max-width:575.98px){.table-inbox-subject{display:block;overflow:hidden;text-overflow:ellipsis}}.table-inbox-time{text-align:left;width:5.5rem}.profile-cover{--profile-cover-height:21.88rem;--profile-cover-text-shadow:0 0 0.1875rem rgba(var(--black-rgb), 0.5);position:relative}.profile-cover-text{text-shadow:var(--profile-cover-text-shadow)}.profile-cover-img{background-position:50% 50%;background-repeat:no-repeat;background-size:cover;height:var(--profile-cover-height)}.login-cover{background:url(../../../../../../assets/images/login_cover.jpg) no-repeat;background-size:cover}@media (min-width:576px){.login-form{width:25rem}}.timeline{--timeline-content-padding-x:calc(0.625rem * 2);--timeline-line-width:calc(var(--border-width) * 2);--timeline-line-color:var(--gray-400);--timeline-icon-bg:var(--white);--timeline-icon-size:3rem;--timeline-icon-border-width:calc(var(--timeline-line-width) * 2);position:relative}.timeline:after,.timeline:before{content:"";position:absolute;right:50%;margin-right:calc(var(--timeline-line-width) * -1);background-color:var(--timeline-line-color);z-index:1;width:calc(var(--timeline-line-width) * 2);height:calc(var(--timeline-line-width) * 2);border-radius:var(--border-radius-pill)}.timeline:before{top:calc(var(--timeline-line-width) * 2 * -1)}.timeline:after{bottom:calc(var(--timeline-line-width) * 2 * -1)}.timeline-container{position:relative;padding-top:calc(var(--spacer) * .5);margin-top:calc(var(--spacer) * .5 * -1);padding-bottom:1px}.timeline-container:before{content:"";position:absolute;top:0;right:50%;margin-right:calc(var(--timeline-line-width) * .5 * -1);background-color:var(--timeline-line-color);height:100%;width:var(--timeline-line-width)}.timeline-row{position:relative}.timeline-date{text-align:center;background-color:var(--body-bg);position:relative;z-index:1;padding-top:var(--spacer);padding-bottom:var(--spacer);margin-bottom:var(--spacer)}.timeline-date:after,.timeline-date:before{content:"";position:absolute;right:50%;margin-right:calc(var(--timeline-line-width) * -1);background-color:var(--timeline-line-color);z-index:1;width:calc(var(--timeline-line-width) * 2);height:calc(var(--timeline-line-width) * 2);border-radius:var(--border-radius-pill)}.timeline-date:before{top:0}.timeline-date:after{bottom:0}.card .timeline-date{background-color:var(--card-bg)}.timeline-time{text-align:center;padding-top:var(--spacer);padding-bottom:var(--spacer);background-color:var(--body-bg);position:relative;margin-bottom:var(--spacer)}.timeline-time:after,.timeline-time:before{content:"";position:absolute;right:50%;margin-right:calc(var(--timeline-line-width) * -1);background-color:var(--timeline-line-color);width:calc(var(--timeline-line-width) * 2);height:calc(var(--timeline-line-width) * 2);border-radius:var(--border-radius-pill)}.timeline-time:before{top:0}.timeline-time:after{bottom:0}.card .timeline-time{background-color:var(--card-bg)}@media (min-width:768px){.timeline-time:after,.timeline-time:before{content:none}}.timeline-icon{margin:0 auto var(--spacer) auto;background-color:car(--timeline-icon-bg);border:var(--timeline-icon-border-width) solid var(--body-bg);width:var(--timeline-icon-size);height:var(--timeline-icon-size);border-radius:var(--border-radius-pill)}.card .timeline-icon{border-color:var(--card-bg)}.timeline-icon div{height:100%;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;box-shadow:0 0 0 var(--timeline-line-width) var(--timeline-line-color) inset;border-radius:var(--border-radius-pill)}.timeline-icon div[class*=bg-]:not(.bg-white):not(.bg-light):not(.bg-transparent){box-shadow:none}.timeline-icon img{width:calc(var(--timeline-icon-size) - var(--timeline-icon-border-width) * 2);height:calc(var(--timeline-icon-size) - var(--timeline-icon-border-width) * 2);border-radius:var(--border-radius-pill)}@media (min-width:768px){.timeline-center .timeline-row-start{margin-left:50%;padding-left:calc(var(--timeline-icon-size) - var(--timeline-icon-border-width))}.timeline-center .timeline-row-end{margin-right:50%;padding-right:calc(var(--timeline-icon-size) - var(--timeline-icon-border-width))}.timeline-start .timeline-container:before,.timeline-start .timeline-date:after,.timeline-start .timeline-date:before,.timeline-start:after,.timeline-start:before{right:calc(var(--timeline-icon-size) * .5)}.timeline-start .timeline-date,.timeline-start .timeline-row{padding-right:calc(var(--timeline-icon-size) + var(--timeline-content-padding-x) * .5)}.timeline-end .timeline-container:before,.timeline-end .timeline-date:after,.timeline-end .timeline-date:before,.timeline-end:after,.timeline-end:before{right:auto;left:calc(var(--timeline-icon-size) * .5)}.timeline-end .timeline-date:after,.timeline-end .timeline-date:before,.timeline-end:after,.timeline-end:before{margin-right:0;margin-left:calc(var(--timeline-line-width) * .5 * -1)}.timeline-end .timeline-date,.timeline-end .timeline-row{padding-left:calc(var(--timeline-icon-size) + var(--timeline-content-padding-x) * .5)}.timeline-end .timeline-date,.timeline-start .timeline-date{padding-top:calc(var(--spacer) * .5);padding-bottom:calc(var(--spacer) * .5)}.timeline-icon{position:absolute;top:calc(var(--spacer) * .5)}.timeline-icon:after{content:"";position:absolute;top:50%;margin-top:calc(var(--timeline-line-width) * .5 * -1);height:var(--timeline-line-width);width:calc(var(--timeline-icon-size) * .5 - var(--timeline-icon-border-width) * 2);background-color:var(--timeline-line-color);z-index:1}.timeline-start .timeline-icon{right:0}.timeline-start .timeline-icon:after{right:100%;margin-right:var(--timeline-icon-border-width)}.timeline-end .timeline-icon{left:0}.timeline-end .timeline-icon:after{left:100%;margin-left:var(--timeline-icon-border-width)}.timeline-center .timeline-row-start .timeline-icon{right:100%;margin-right:calc(var(--timeline-icon-size) * .5 * -1)}.timeline-center .timeline-row-start .timeline-icon:after{left:100%;margin-left:var(--timeline-icon-border-width)}.timeline-center .timeline-row-end .timeline-icon{left:100%;margin-left:calc(var(--timeline-icon-size) * .5 * -1)}.timeline-center .timeline-row-end .timeline-icon:after{right:100%;margin-right:var(--timeline-icon-border-width)}.timeline-center .timeline-row-full .timeline-icon{position:static}.timeline-center .timeline-row-full .timeline-icon:after{content:none}.timeline-time{padding:0;text-align:inherit;background-color:transparent}.timeline-time:before{content:none}.timeline-end .timeline-time,.timeline-start .timeline-time{padding-top:calc(var(--spacer) * .5);margin-bottom:var(--spacer);padding-right:calc(var(--timeline-content-padding-x) * .5)}.timeline-end .timeline-time{text-align:left;padding-right:0;padding-left:calc(var(--timeline-content-padding-x) * .5)}.timeline-center .timeline-row-full .timeline-time,.timeline-center .timeline-time{position:absolute;right:100%;top:calc(var(--spacer) * .5 + var(--timeline-icon-border-width));width:100%;padding-right:calc(var(--timeline-icon-size) - var(--timeline-icon-border-width))}.timeline-center .timeline-row-end .timeline-time,.timeline-center .timeline-row-full .timeline-time{right:auto;left:100%;padding-right:0;padding-left:calc(var(--timeline-icon-size) - var(--timeline-icon-border-width));text-align:left}.timeline-center .timeline-row-full .timeline-time{left:50%;top:var(--timeline-icon-border-width)}}.media-chat-scrollable{max-height:32.5rem;display:-ms-flexbox;display:flex;-ms-flex-direction:column-reverse;flex-direction:column-reverse;overflow:auto;-webkit-overflow-scrolling:touch}.media-chat{--chat-message-padding-y:0.625rem;--chat-message-padding-x:1rem;--chat-message-bg:var(--gray-300);--chat-message-color:var(--body-color)}@media (min-width:576px){.media-chat-item{width:75%}}.media-chat-message{position:relative;padding:var(--chat-message-padding-y) var(--chat-message-padding-x);display:inline-block;-ms-flex-align:start;align-items:flex-start;color:var(--chat-message-color);border-radius:var(--border-radius)}.media-chat-message:not([class*=bg-]){background-color:var(--chat-message-bg)}.media-chat-item-reverse{--chat-message-bg:var(--primary);--chat-message-color:var(--white);text-align:left;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-item-align:end;align-self:flex-end}.media-chat-item-reverse .media-chat-message{text-align:right}.typing-indicator{display:-ms-inline-flexbox;display:inline-flex}.typing-indicator span{height:.125rem;width:.125rem;margin:0 .0625rem;background-color:var(--body-color);display:block;border-radius:var(--border-radius-pill);opacity:.4}.typing-indicator span:nth-of-type(1){-webkit-animation:1.2s blink infinite .2s;animation:1.2s blink infinite .2s}.typing-indicator span:nth-of-type(2){-webkit-animation:1.2s blink infinite .4s;animation:1.2s blink infinite .4s}.typing-indicator span:nth-of-type(3){-webkit-animation:1.2s blink infinite .6s;animation:1.2s blink infinite .6s}@-webkit-keyframes blink{50%{opacity:1}}@keyframes blink{50%{opacity:1}}.spinner{display:inline-block;-webkit-animation:rotation 1s linear infinite;animation:rotation 1s linear infinite}.spinner-reverse{display:inline-block;-webkit-animation:rotation_reverse 1s linear infinite;animation:rotation_reverse 1s linear infinite}@-webkit-keyframes rotation{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(-360deg);transform:rotate(-360deg)}}@keyframes rotation{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(-360deg);transform:rotate(-360deg)}}@-webkit-keyframes rotation_reverse{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes rotation_reverse{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.invert-dark[data-color-theme=dark],[data-color-theme=dark] .invert-dark:not([data-color-theme]),html[data-color-theme=dark] .invert-dark{color-scheme:dark;-webkit-filter:invert(1) grayscale(100);filter:invert(1) grayscale(100)}.scrollbar-hidden::-webkit-scrollbar{display:none!important}