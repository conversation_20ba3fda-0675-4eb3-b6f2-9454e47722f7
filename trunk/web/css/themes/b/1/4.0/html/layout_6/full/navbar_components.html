<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<title>Limitless - Responsive Web Application Kit by <PERSON></title>

	<!-- Global stylesheets -->
	<link href="../../../assets/fonts/inter/inter.css" rel="stylesheet" type="text/css">
	<link href="../../../assets/icons/phosphor/styles.min.css" rel="stylesheet" type="text/css">
	<link href="assets/css/ltr/all.min.css" id="stylesheet" rel="stylesheet" type="text/css">
	<!-- /global stylesheets -->

	<!-- Core JS files -->
	<script src="../../../assets/demo/demo_configurator.js"></script>
	<script src="../../../assets/js/bootstrap/bootstrap.bundle.min.js"></script>
	<!-- /core JS files -->

	<!-- Theme JS files -->
	<script src="../../../assets/js/jquery/jquery.min.js"></script>
	<script src="../../../assets/js/vendor/forms/selects/select2.min.js"></script>
	<script src="../../../assets/js/vendor/forms/selects/bootstrap_multiselect.js"></script>
	<script src="../../../assets/js/vendor/ui/moment/moment.min.js"></script>
	<script src="../../../assets/js/vendor/pickers/daterangepicker.js"></script>

	<script src="assets/js/app.js"></script>
	<script src="../../../assets/demo/pages/navbar_components.js"></script>
	<!-- /theme JS files -->

</head>

<body>

	<!-- Main navbar -->
	<div class="navbar navbar-expand-xl navbar-static shadow">
		<div class="container-fluid">
			<div class="navbar-brand flex-1">
				<a href="index.html" class="d-inline-flex align-items-center">
					<img src="../../../assets/images/logo_icon.svg" alt="">
					<img src="../../../assets/images/logo_text_dark.svg" class="d-none d-sm-inline-block h-16px invert-dark ms-3" alt="">
				</a>
			</div>

			<div class="d-flex w-100 w-xl-auto overflow-auto overflow-xl-visible scrollbar-hidden border-top border-top-xl-0 order-1 order-xl-0 pt-2 pt-xl-0 mt-2 mt-xl-0">
				<ul class="nav gap-1 justify-content-center flex-nowrap flex-xl-wrap mx-auto">
					<li class="nav-item">
						<a href="index.html" class="navbar-nav-link rounded">
							<i class="ph-house me-2"></i>
							Home
						</a>
					</li>

					<li class="nav-item nav-item-dropdown">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded active" data-bs-toggle="dropdown" data-bs-auto-close="outside">
							<i class="ph-rows me-2"></i>
							Navigation
						</a>

						<div class="dropdown-menu p-0">
							<div class="d-xl-flex">
								<div class="d-flex flex-row flex-xl-column bg-light overflow-auto overflow-xl-visible rounded-top rounded-top-xl-0 rounded-start-xl">
									<div class="flex-1 border-bottom border-bottom-xl-0 p-2 p-xl-3">
										<div class="fw-bold border-bottom d-none d-xl-block pb-2 mb-2">Navigation</div>
										<ul class="nav nav-pills flex-xl-column flex-nowrap text-nowrap justify-content-center wmin-xl-300" role="tablist">
											<li class="nav-item" role="presentation">
												<a href="#tab_page" class="nav-link rounded" data-bs-toggle="tab" aria-selected="false" role="tab">
													<i class="ph-layout me-2"></i>
													Page
													<i class="ph-arrow-right nav-item-active-indicator d-none d-xl-inline-block ms-auto"></i>
												</a>
											</li>
											<li class="nav-item" role="presentation">
												<a href="#tab_navbars" class="nav-link rounded active" data-bs-toggle="tab" aria-selected="true" tabindex="-1" role="tab">
													<i class="ph-rows me-2"></i>
													Navbars
													<i class="ph-arrow-right nav-item-active-indicator d-none d-xl-inline-block ms-auto"></i>
												</a>
											</li>
											<li class="nav-item" role="presentation">
												<a href="#tab_sidebar_types" class="nav-link rounded" data-bs-toggle="tab" aria-selected="false" tabindex="-1" role="tab">
													<i class="ph-columns me-2"></i>
													Sidebar types
													<i class="ph-arrow-right nav-item-active-indicator d-none d-xl-inline-block ms-auto"></i>
												</a>
											</li>
											<li class="nav-item" role="presentation">
												<a href="#tab_sidebar_content" class="nav-link rounded" data-bs-toggle="tab" aria-selected="false" tabindex="-1" role="tab">
													<i class="ph-sidebar-simple me-2"></i>
													Sidebar content
													<i class="ph-arrow-right nav-item-active-indicator d-none d-xl-inline-block ms-auto"></i>
												</a>
											</li>
											<li class="nav-item" role="presentation">
												<a href="#tab_navigation" class="nav-link rounded" data-bs-toggle="tab" aria-selected="false" tabindex="-1" role="tab">
													<i class="ph-list-dashes me-2"></i>
													Navigation
													<i class="ph-arrow-right nav-item-active-indicator d-none d-xl-inline-block ms-auto"></i>
												</a>
											</li>
										</ul>
									</div>
								</div>

								<div class="tab-content flex-xl-1">
									<div class="tab-pane dropdown-scrollable-xl fade p-3" id="tab_page" role="tabpanel">
										<div class="row">
											<div class="col-lg-4 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Sections</div>
												<a href="layout_no_header.html" class="dropdown-item rounded">No header</a>
												<a href="layout_no_footer.html" class="dropdown-item rounded">No footer</a>
												<a href="layout_fixed_header.html" class="dropdown-item rounded">Fixed header</a>
												<a href="layout_fixed_footer.html" class="dropdown-item rounded">Fixed footer</a>
											</div>

											<div class="col-lg-4 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Sidebars</div>
												<a href="layout_2_sidebars_1_side.html" class="dropdown-item rounded">2 sidebars on 1 side</a>
												<a href="layout_2_sidebars_2_sides.html" class="dropdown-item rounded">2 sidebars on 2 sides</a>
												<a href="layout_3_sidebars.html" class="dropdown-item rounded">3 sidebars</a>
											</div>

											<div class="col-lg-4">
												<div class="fw-bold border-bottom pb-2 mb-2">Layout</div>
												<a href="layout_static.html" class="dropdown-item rounded">Static layout</a>
												<a href="layout_boxed_page.html" class="dropdown-item rounded">Boxed page</a>
												<a href="layout_liquid_content.html" class="dropdown-item rounded">Liquid content</a>
											</div>
										</div>
									</div>

									<div class="tab-pane dropdown-scrollable-xl fade show active p-3" id="tab_navbars" role="tabpanel">
										<div class="row">
											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Single</div>
												<a href="navbar_single_top_static.html" class="dropdown-item rounded">Top static</a>
												<a href="navbar_single_top_fixed.html" class="dropdown-item rounded">Top fixed</a>
												<a href="navbar_single_bottom_static.html" class="dropdown-item rounded">Bottom static</a>
												<a href="navbar_single_bottom_fixed.html" class="dropdown-item rounded">Bottom fixed</a>
											</div>

											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Multiple</div>
												<a href="navbar_multiple_top_static.html" class="dropdown-item rounded">Top static</a>
												<a href="navbar_multiple_top_fixed.html" class="dropdown-item rounded">Top fixed</a>
												<a href="navbar_multiple_bottom_static.html" class="dropdown-item rounded">Bottom static</a>
												<a href="navbar_multiple_bottom_fixed.html" class="dropdown-item rounded">Bottom fixed</a>
												<a href="navbar_multiple_top_bottom_fixed.html" class="dropdown-item rounded">Top and bottom fixed</a>
											</div>

											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Content</div>
												<a href="navbar_component_single.html" class="dropdown-item rounded">Single navbar</a>
												<a href="navbar_component_multiple.html" class="dropdown-item rounded">Multiple navbars</a>
											</div>

											<div class="col-lg-3">
												<div class="fw-bold border-bottom pb-2 mb-2">Other</div>
												<a href="navbar_colors.html" class="dropdown-item rounded">Color options</a>
												<a href="navbar_sizes.html" class="dropdown-item rounded">Sizing options</a>
												<a href="navbar_components.html" class="dropdown-item rounded active">Navbar components</a>
											</div>
										</div>
									</div>

									<div class="tab-pane dropdown-scrollable-xl fade p-3" id="tab_sidebar_types" role="tabpanel">
										<div class="row">
											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Main</div>
												<a href="sidebar_default_resizable.html" class="dropdown-item rounded">Resizable</a>
												<a href="sidebar_default_resized.html" class="dropdown-item rounded">Resized</a>
												<a href="sidebar_default_collapsible.html" class="dropdown-item rounded">Collapsible</a>
												<a href="sidebar_default_collapsed.html" class="dropdown-item rounded">Collapsed</a>
												<a href="sidebar_default_hideable.html" class="dropdown-item rounded">Hideable</a>
												<a href="sidebar_default_hidden.html" class="dropdown-item rounded">Hidden</a>
												<a href="sidebar_default_color_dark.html" class="dropdown-item rounded">Dark color</a>
											</div>

											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Secondary</div>
												<a href="sidebar_secondary_collapsible.html" class="dropdown-item rounded">Collapsible</a>
												<a href="sidebar_secondary_collapsed.html" class="dropdown-item rounded">Collapsed</a>
												<a href="sidebar_secondary_hideable.html" class="dropdown-item rounded">Hideable</a>
												<a href="sidebar_secondary_hidden.html" class="dropdown-item rounded">Hidden</a>
												<a href="sidebar_secondary_color_dark.html" class="dropdown-item rounded">Dark color</a>
											</div>

											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Right</div>
												<a href="sidebar_right_collapsible.html" class="dropdown-item rounded">Collapsible</a>
												<a href="sidebar_right_collapsed.html" class="dropdown-item rounded">Collapsed</a>
												<a href="sidebar_right_hideable.html" class="dropdown-item rounded">Hideable</a>
												<a href="sidebar_right_hidden.html" class="dropdown-item rounded">Hidden</a>
												<a href="sidebar_right_color_dark.html" class="dropdown-item rounded">Dark color</a>
											</div>

											<div class="col-lg-3">
												<div class="fw-bold border-bottom pb-2 mb-2">Content</div>
												<a href="sidebar_content_left.html" class="dropdown-item rounded">Left aligned</a>
												<a href="sidebar_content_left_stretch.html" class="dropdown-item rounded">Left stretched</a>
												<a href="sidebar_content_left_sections.html" class="dropdown-item rounded">Left sectioned</a>
												<a href="sidebar_content_right.html" class="dropdown-item rounded">Right aligned</a>
												<a href="sidebar_content_right_stretch.html" class="dropdown-item rounded">Right stretched</a>
												<a href="sidebar_content_right_sections.html" class="dropdown-item rounded">Right sectioned</a>
												<a href="sidebar_content_color_dark.html" class="dropdown-item rounded">Dark color</a>
											</div>
										</div>
									</div>

									<div class="tab-pane dropdown-scrollable-xl fade p-3" id="tab_sidebar_content" role="tabpanel">
										<div class="row">
											<div class="col-lg-6 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Sticky areas</div>
												<a href="sidebar_sticky_header.html" class="dropdown-item rounded">Header</a>
												<a href="sidebar_sticky_footer.html" class="dropdown-item rounded">Footer</a>
												<a href="sidebar_sticky_header_footer.html" class="dropdown-item rounded">Header and footer</a>
												<a href="sidebar_sticky_custom.html" class="dropdown-item rounded">Custom elements</a>
											</div>

											<div class="col-lg-6">
												<div class="fw-bold border-bottom pb-2 mb-2">Other</div>
												<a href="sidebar_components.html" class="dropdown-item rounded">Sidebar components</a>
											</div>
										</div>
									</div>

									<div class="tab-pane dropdown-scrollable-xl fade p-3" id="tab_navigation" role="tabpanel">
										<div class="row">
											<div class="col-lg-6 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Vertical</div>
												<a href="navigation_vertical_styles.html" class="dropdown-item rounded">Navigation styles</a>
												<a href="navigation_vertical_collapsible.html" class="dropdown-item rounded">Collapsible menu</a>
												<a href="navigation_vertical_accordion.html" class="dropdown-item rounded">Accordion menu</a>
												<a href="navigation_vertical_bordered.html" class="dropdown-item rounded">Bordered navigation</a>
												<a href="navigation_vertical_right_icons.html" class="dropdown-item rounded">Right icons</a>
												<a href="navigation_vertical_badges.html" class="dropdown-item rounded">Badges</a>
												<a href="navigation_vertical_disabled.html" class="dropdown-item rounded">Disabled items</a>
											</div>

											<div class="col-lg-6">
												<div class="fw-bold border-bottom pb-2 mb-2">Horizontal</div>
												<a href="navigation_horizontal_styles.html" class="dropdown-item rounded">Navigation styles</a>
												<a href="navigation_horizontal_elements.html" class="dropdown-item rounded">Navigation elements</a>
												<a href="navigation_horizontal_tabs.html" class="dropdown-item rounded">Tabbed navigation</a>
												<a href="navigation_horizontal_disabled.html" class="dropdown-item rounded">Disabled items</a>
												<a href="navigation_horizontal_mega.html" class="dropdown-item rounded">Mega menu</a>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</li>

					<li class="nav-item nav-item-dropdown-xl dropdown">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded" data-bs-toggle="dropdown">
							<i class="ph-note-blank me-2"></i>
							Starter kit
						</a>

						<div class="dropdown-menu">
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-columns me-2"></i>
									Sidebars
								</a>
								<div class="dropdown-menu">
									<a href="../seed/layout_2_sidebars_1_side.html" class="dropdown-item rounded">2 sidebars on 1 side</a>
									<a href="../seed/layout_2_sidebars_2_sides.html" class="dropdown-item rounded">2 sidebars on 2 sides</a>
									<a href="../seed/layout_3_sidebars.html" class="dropdown-item rounded">3 sidebars</a>
								</div>
							</div>
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-rows me-2"></i>
									Sections
								</a>
								<div class="dropdown-menu">
									<a href="../seed/layout_no_header.html" class="dropdown-item rounded">No header</a>
									<a href="../seed/layout_no_footer.html" class="dropdown-item rounded">No footer</a>
									<a href="../seed/layout_fixed_header.html" class="dropdown-item rounded">Fixed header</a>
									<a href="../seed/layout_fixed_footer.html" class="dropdown-item rounded">Fixed footer</a>
								</div>
							</div>
							<div class="dropdown-divider"></div>
							<a href="../seed/layout_static.html" class="dropdown-item rounded">Static layout</a>
							<a href="../seed/layout_boxed_page.html" class="dropdown-item rounded">Boxed page</a>
							<a href="../seed/layout_liquid_content.html" class="dropdown-item rounded">Liquid content</a>
						</div>
					</li>

					<li class="nav-item nav-item-dropdown-xl dropdown">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded" data-bs-toggle="dropdown">
							<i class="ph-arrows-clockwise me-2"></i>
							Switch
						</a>

						<div class="dropdown-menu dropdown-menu-end">
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-layout me-2"></i>
									Layouts
								</a>
								<div class="dropdown-menu">
									<a href="../../layout_1/full/index.html" class="dropdown-item">Default layout</a>
									<a href="../../layout_2/full/index.html" class="dropdown-item">Layout 2</a>
									<a href="../../layout_3/full/index.html" class="dropdown-item">Layout 3</a>
									<a href="../../layout_4/full/index.html" class="dropdown-item">Layout 4</a>
									<a href="../../layout_5/full/index.html" class="dropdown-item">Layout 5</a>
									<a href="index.html" class="dropdown-item active">Layout 6</a>
									<a href="../../layout_7/full/index.html" class="dropdown-item disabled">
										Layout 7
										<span class="opacity-75 fs-sm ms-auto">Coming soon</span>
									</a>
								</div>
							</div>
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-swatches me-2"></i>
									Themes
								</a>
								<div class="dropdown-menu">
									<a href="index.html" class="dropdown-item active">Default</a>
									<a href="../../../LTR/material/full/index.html" class="dropdown-item disabled">
										Material
										<span class="opacity-75 fs-sm ms-auto">Coming soon</span>
									</a>
									<a href="../../../LTR/clean/full/index.html" class="dropdown-item disabled">
										Clean
										<span class="opacity-75 fs-sm ms-auto">Coming soon</span>
									</a>
								</div>
							</div>
						</div>
					</li>
				</ul>
			</div>

			<ul class="nav gap-1 flex-xl-1 justify-content-end order-0 order-xl-1">
				<li class="nav-item nav-item-dropdown-xl dropdown">
					<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded-pill" data-bs-toggle="dropdown">
						<i class="ph-squares-four"></i>
					</a>

					<div class="dropdown-menu dropdown-menu-end dropdown-menu-scrollable-sm wmin-lg-600 p-0">
						<div class="d-flex align-items-center border-bottom p-3">
							<h6 class="mb-0">Browse apps</h6>
							<a href="#" class="ms-auto">
								View all
								<i class="ph-arrow-circle-right ms-1"></i>
							</a>
						</div>

						<div class="row row-cols-1 row-cols-sm-2 g-0">
							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start border-end-sm border-bottom p-3">
									<div>
										<img src="../../../assets/images/demo/logos/1.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Customer data platform</div>
										<div class="text-muted">Unify customer data from multiple sources</div>
									</div>
								</button>
							</div>

							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start border-bottom p-3">
									<div>
										<img src="../../../assets/images/demo/logos/2.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Data catalog</div>
										<div class="text-muted">Discover, inventory, and organize data assets</div>
									</div>
								</button>
							</div>

							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start border-end-sm border-bottom border-bottom-sm-0 rounded-bottom-start p-3">
									<div>
										<img src="../../../assets/images/demo/logos/3.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Data governance</div>
										<div class="text-muted">The collaboration hub and data marketplace</div>
									</div>
								</button>
							</div>

							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start rounded-bottom-end p-3">
									<div>
										<img src="../../../assets/images/demo/logos/4.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Data privacy</div>
										<div class="text-muted">Automated provisioning of non-production datasets</div>
									</div>
								</button>
							</div>
						</div>
					</div>
				</li>

				<li class="nav-item">
					<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded-pill" data-bs-toggle="offcanvas" data-bs-target="#notifications">
						<i class="ph-bell"></i>
						<span class="badge bg-yellow text-black position-absolute top-0 end-0 translate-middle-top zindex-1 rounded-pill mt-1 me-1">2</span>
					</a>
				</li>

				<li class="nav-item nav-item-dropdown-xl dropdown">
					<a href="#" class="navbar-nav-link align-items-center rounded-pill p-1" data-bs-toggle="dropdown">
						<div class="status-indicator-container">
							<img src="../../../assets/images/demo/users/face11.jpg" class="w-32px h-32px rounded-pill" alt="">
							<span class="status-indicator bg-success"></span>
						</div>
						<span class="d-none d-md-inline-block mx-md-2">Victoria</span>
					</a>

					<div class="dropdown-menu dropdown-menu-end">
						<a href="#" class="dropdown-item">
							<i class="ph-user-circle me-2"></i>
							My profile
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-currency-circle-dollar me-2"></i>
							My subscription
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-shopping-cart me-2"></i>
							My orders
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-envelope-open me-2"></i>
							My inbox
							<span class="badge bg-primary rounded-pill ms-auto">26</span>
						</a>
						<div class="dropdown-divider"></div>
						<a href="#" class="dropdown-item">
							<i class="ph-gear me-2"></i>
							Account settings
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-sign-out me-2"></i>
							Logout
						</a>
					</div>
				</li>
			</ul>
		</div>
	</div>
	<!-- /main navbar -->


	<!-- Page content -->
	<div class="page-content">

		<!-- Main content -->
		<div class="content-wrapper">

			<!-- Inner content -->
			<div class="content-inner">

				<!-- Page header -->
				<div class="page-header">
					<div class="page-header-content container d-lg-flex">
						<div class="d-flex">
							<h4 class="page-title mb-0">
								Navbars - <span class="fw-normal">Components</span>
							</h4>

							<a href="#page_header" class="btn btn-light align-self-center collapsed d-lg-none border-transparent rounded-pill p-0 ms-auto" data-bs-toggle="collapse">
								<i class="ph-caret-down collapsible-indicator ph-sm m-1"></i>
							</a>
						</div>

						<div class="collapse d-lg-block my-lg-auto ms-lg-auto" id="page_header">
							<div class="d-sm-flex align-items-center mb-3 mb-lg-0 ms-lg-3">
								<div class="dropdown w-100 w-sm-auto">
									<a href="#" class="d-flex align-items-center text-body lh-1 dropdown-toggle py-sm-2" data-bs-toggle="dropdown" data-bs-display="static">
										<img src="../../../assets/images/brands/tesla.svg" class="w-32px h-32px me-2" alt="">
										<div class="me-auto me-lg-1">
											<div class="fs-sm text-muted mb-1">Customer</div>
											<div class="fw-semibold">Tesla Motors Inc</div>
										</div>
									</a>

									<div class="dropdown-menu dropdown-menu-lg-end w-100 w-lg-auto wmin-300 wmin-sm-350 pt-0">
										<div class="d-flex align-items-center p-3">
											<h6 class="fw-semibold mb-0">Customers</h6>
											<a href="#" class="ms-auto">
												View all
												<i class="ph-arrow-circle-right ms-1"></i>
											</a>
										</div>
										<a href="#" class="dropdown-item active py-2">
											<img src="../../../assets/images/brands/tesla.svg" class="w-32px h-32px me-2" alt="">
											<div>
												<div class="fw-semibold">Tesla Motors Inc</div>
												<div class="fs-sm text-muted">42 users</div>
											</div>
										</a>
										<a href="#" class="dropdown-item py-2">
											<img src="../../../assets/images/brands/debijenkorf.svg" class="w-32px h-32px me-2" alt="">
											<div>
												<div class="fw-semibold">De Bijenkorf</div>
												<div class="fs-sm text-muted">49 users</div>
											</div>
										</a>
										<a href="#" class="dropdown-item py-2">
											<img src="../../../assets/images/brands/klm.svg" class="w-32px h-32px me-2" alt="">
											<div>
												<div class="fw-semibold">Royal Dutch Airlines</div>
												<div class="fs-sm text-muted">18 users</div>
											</div>
										</a>
										<a href="#" class="dropdown-item py-2">
											<img src="../../../assets/images/brands/shell.svg" class="w-32px h-32px me-2" alt="">
											<div>
												<div class="fw-semibold">Royal Dutch Shell</div>
												<div class="fs-sm text-muted">54 users</div>
											</div>
										</a>
										<a href="#" class="dropdown-item py-2">
											<img src="../../../assets/images/brands/bp.svg" class="w-32px h-32px me-2" alt="">
											<div>
												<div class="fw-semibold">BP plc</div>
												<div class="fs-sm text-muted">23 users</div>
											</div>
										</a>
									</div>
								</div>

								<div class="vr d-none d-sm-block flex-shrink-0 my-2 mx-3"></div>

								<div class="d-inline-flex mt-3 mt-sm-0">
									<a href="#" class="status-indicator-container ms-1">
										<img src="../../../assets/images/demo/users/face24.jpg" class="w-32px h-32px rounded-pill" alt="">
										<span class="status-indicator bg-warning"></span>
									</a>
									<a href="#" class="status-indicator-container ms-1">
										<img src="../../../assets/images/demo/users/face1.jpg" class="w-32px h-32px rounded-pill" alt="">
										<span class="status-indicator bg-success"></span>
									</a>
									<a href="#" class="status-indicator-container ms-1">
										<img src="../../../assets/images/demo/users/face3.jpg" class="w-32px h-32px rounded-pill" alt="">
										<span class="status-indicator bg-danger"></span>
									</a>
									<a href="#" class="btn btn-outline-primary btn-icon w-32px h-32px rounded-pill ms-3">
										<i class="ph-plus"></i>
									</a>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /page header -->


				<!-- Content area -->
				<div class="content container pt-0">

					<!-- Custom components -->
					<div class="card">
						<div class="card-header">
							<h5 class="mb-0">Custom components</h5>
						</div>

						<div class="card-body">
							<p class="mb-3">Besides navigation, navbar component supports custom content such as: dropdowns with submenu, dropdown menus with custom content, full width dropdowns, language selection, forms, tabs, multi level menus, date pickers etc. Everything is adapted to use in different navbar color schemes and sizes. Examples below demonstrate some of these components, other examples related to <code>mega menu</code> can be found <a href="navigation_horizontal_mega.html">on this page</a>.</p>

							<div class="mb-4">
								<p>Mixed components</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-mixed">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-mixed">
											<ul class="navbar-nav mt-2 mt-xl-0">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">List</a>

													<div class="dropdown-menu start-0 end-0 py-0 mx-xl-3">
														<div class="p-3">
															<div class="row">
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">Form components</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-3 mb-xl-0">
																		<a href="form_inputs.html" class="dropdown-item rounded">Basic inputs</a>
																		<a href="form_controls_extended.html" class="dropdown-item rounded">Extended controls</a>
																		<a href="form_select2.html" class="dropdown-item rounded">Select2 selects</a>
																		<a href="form_floating_labels.html" class="dropdown-item rounded">Floating labels</a>
																		<a href="form_validation.html" class="dropdown-item rounded">Validation</a>
																	</div>
																</div>
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">UI components</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-3 mb-xl-0">
																		<a href="components_modals.html" class="dropdown-item rounded">Modals <span class="badge bg-secondary rounded-pill ms-auto">10+</span></a>
																		<a href="components_dropdowns.html" class="dropdown-item rounded">Dropdown menus</a>
																		<a href="components_buttons.html" class="dropdown-item rounded">Buttons</a>
																		<a href="components_tabs.html" class="dropdown-item rounded">Tabs component</a>
																		<a href="components_breadcrumbs.html" class="dropdown-item rounded">Breadcrumbs</a>
																	</div>
																</div>
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">Sidebars</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-3 mb-xl-0">
																		<a href="sidebar_default_collapse.html" class="dropdown-item rounded">Main sidebar</a>
																		<a href="sidebar_secondary_after.html" class="dropdown-item rounded">Secondary sidebar</a>
																		<a href="sidebar_right_default_collapse.html" class="dropdown-item rounded">Right sidebar</a>
																		<a href="sidebar_content_left.html" class="dropdown-item rounded">Content sidebar</a>
																		<a href="sidebar_components.html" class="dropdown-item rounded">Sidebar components</a>
																	</div>
																</div>
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">Navigation</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-3 mb-xl-0">
																		<a href="navigation_horizontal_click.html" class="dropdown-item rounded">Submenu on click</a>
																		<a href="navigation_horizontal_hover.html" class="dropdown-item rounded">Submenu on hover</a>
																		<a href="navigation_horizontal_elements.html" class="dropdown-item rounded">With custom elements</a>
																		<a href="navigation_horizontal_tabs.html" class="dropdown-item rounded">Tabbed navigation</a>
																		<a href="navigation_horizontal_mega.html" class="dropdown-item rounded">Horizontal mega menu</a>
																	</div>
																</div>
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">Navbars</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-3 mb-xl-0">
																		<a href="navbar_single_top_static.html" class="dropdown-item rounded">Single navbar</a>
																		<a href="navbar_multiple_top_static.html" class="dropdown-item rounded">Multiple navbars</a>
																		<a href="navbar_colors.html" class="dropdown-item rounded">Color options</a>
																		<a href="navbar_hideable.html" class="dropdown-item rounded">Hide on scroll</a>
																		<a href="navbar_components.html" class="dropdown-item rounded active">Navbar components</a>
																	</div>
																</div>
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">Extensions</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-0">
																		<a href="jqueryui_interactions.html" class="dropdown-item rounded">jQuery UI</a>
																		<a href="animations_velocity_basic.html" class="dropdown-item rounded">Animations <span class="badge badge-pill badge-info ms-auto">14</span></a>
																		<a href="uploader_plupload.html" class="dropdown-item rounded">File uploaders</a>
																		<a href="extension_image_cropper.html" class="dropdown-item rounded">Image cropper</a>
																		<a href="fullcalendar_views.html" class="dropdown-item rounded">Calendars</a>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</li>

												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown" data-bs-auto-close="outside">Tabs</a>

													<div class="dropdown-menu py-0 wmin-xl-350">
														<div class="d-flex align-items-center p-3 pb-2">
															<span class="fw-semibold">Tabs example</span>
															<a href="#" class="text-body ms-auto">
																<i class="ph-gear"></i>
															</a>
														</div>

														<ul class="nav nav-tabs nav-tabs-underline nav-justified">
															<li class="nav-item">
																<a href="#tab1" class="nav-link active" data-bs-toggle="tab">
																	<i class="icon-compose me-2"></i>
																	Form example
																</a>
															</li>
															<li class="nav-item">
																<a href="#tab2" class="nav-link" data-bs-toggle="tab">
																	<i class="icon-list3 me-2"></i>
																	List example
																</a>
															</li>
														</ul>

														<div class="tab-content">
															<div class="tab-pane fade show active" id="tab1">
																<form class="p-3" action="#">
																	<div class="form-control-feedback form-control-feedback-end mb-3">
																		<input type="text" class="form-control" placeholder="Your email">
																		<div class="form-control-feedback-icon">
																			<i class="ph-at"></i>
																		</div>
																	</div>

																	<div class="mb-3">
																		<select class="form-select">
																			<option value="updates" selected>Website updates</option>
																			<option value="discounts">Discount offers</option>
																			<option value="catalog">Catalog</option>
																			<option value="prints">Prints</option>
																			<option value="promo">Promotions</option>
																		</select>
																	</div>

																	<div class="mb-3">
																		<textarea class="form-control" cols="3" rows="3" placeholder="Your message"></textarea>
																	</div>

																	<div class="d-flex align-items-center justify-content-between">
																		<label class="form-check mb-0">
																			<input type="checkbox" class="form-check-input" checked>
																			<span class="form-check-label">Save as draft</span>
																		</label>

																		<button type="submit" class="btn btn-primary">
																			Submit
																			<i class="ph-paper-plane-tilt ms-2"></i>
																		</button>
																	</div>
																</form>
															</div>

															<div class="tab-pane fade" id="tab2">
																<div class="p-3">
																	<div class="d-flex align-items-start mb-3">
																		<div class="status-indicator-container me-2">
																			<img src="../../../assets/images/demo/users/face1.jpg" class="w-40px h-40px rounded-pill" alt="">
																			<span class="status-indicator bg-success"></span>
																		</div>

																		<div class="flex-fill">
																			<a href="#" class="fw-semibold">James Alexander</a>
																			<div class="fs-sm text-muted">Santa Ana, CA.</div>
																		</div>

																		<div class="d-flex align-self-center ms-3">
																			<a href="#" class="text-body">
																				<i class="ph-phone"></i>
																			</a>
																			<a href="#" class="text-body ms-2">
																				<i class="ph-envelope"></i>
																			</a>
																		</div>
																	</div>

																	<div class="d-flex align-items-start mb-3">
																		<div class="status-indicator-container me-2">
																			<img src="../../../assets/images/demo/users/face2.jpg" class="w-40px h-40px rounded-pill" alt="">
																			<span class="status-indicator bg-success"></span>
																		</div>

																		<div class="flex-fill">
																			<a href="#" class="fw-semibold">Jeremy Victorino</a>
																			<div class="fs-sm text-muted">Dowagiac, MI.</div>
																		</div>

																		<div class="d-flex align-self-center ms-3">
																			<a href="#" class="text-body">
																				<i class="ph-phone"></i>
																			</a>
																			<a href="#" class="text-body ms-2">
																				<i class="ph-envelope"></i>
																			</a>
																		</div>
																	</div>

																	<div class="d-flex align-items-start mb-3">
																		<div class="status-indicator-container me-2">
																			<img src="../../../assets/images/demo/users/face3.jpg" class="w-40px h-40px rounded-pill" alt="">
																			<span class="status-indicator bg-success"></span>
																		</div>

																		<div class="flex-fill">
																			<a href="#" class="fw-semibold">Margo Baker</a>
																			<div class="fs-sm text-muted">Kasaan, AK.</div>
																		</div>

																		<div class="d-flex align-self-center ms-3">
																			<a href="#" class="text-body">
																				<i class="ph-phone"></i>
																			</a>
																			<a href="#" class="text-body ms-2">
																				<i class="ph-envelope"></i>
																			</a>
																		</div>
																	</div>

																	<div class="d-flex align-items-start mb-3">
																		<div class="status-indicator-container me-2">
																			<img src="../../../assets/images/demo/users/face4.jpg" class="w-40px h-40px rounded-pill" alt="">
																			<span class="status-indicator bg-success"></span>
																		</div>

																		<div class="flex-fill">
																			<a href="#" class="fw-semibold">Beatrix Diaz</a>
																			<div class="fs-sm text-muted">Neenah, WI.</div>
																		</div>

																		<div class="d-flex align-self-center ms-3">
																			<a href="#" class="text-body">
																				<i class="ph-phone"></i>
																			</a>
																			<a href="#" class="text-body ms-2">
																				<i class="ph-envelope"></i>
																			</a>
																		</div>
																	</div>

																	<div class="d-flex align-items-start">
																		<div class="status-indicator-container me-2">
																			<img src="../../../assets/images/demo/users/face5.jpg" class="w-40px h-40px rounded-pill" alt="">
																			<span class="status-indicator bg-success"></span>
																		</div>

																		<div class="flex-fill">
																			<a href="#" class="fw-semibold">Richard Vango</a>
																			<div class="fs-sm text-muted">Grapevine, TX.</div>
																		</div>

																		<div class="d-flex align-self-center ms-3">
																			<a href="#" class="text-body">
																				<i class="ph-phone"></i>
																			</a>
																			<a href="#" class="text-body ms-2">
																				<i class="ph-envelope"></i>
																			</a>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</li>

												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">Classic</a>

													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">Second level</a>
														<div class="dropdown-submenu">
															<a href="#" class="dropdown-item dropdown-toggle">Second level with child</a>
															<div class="dropdown-menu">
																<a href="#" class="dropdown-item">Third level</a>
																<div class="dropdown-submenu">
																	<a href="#" class="dropdown-item dropdown-toggle">Third level with child</a>
																	<div class="dropdown-menu">
																		<a href="#" class="dropdown-item">Fourth level</a>
																		<a href="#" class="dropdown-item">Fourth level</a>
																		<a href="#" class="dropdown-item">Fourth level</a>
																	</div>
																</div>
																<a href="#" class="dropdown-item">Third level</a>
															</div>
														</div>
														<a href="#" class="dropdown-item">Second level</a>
													</div>
												</li>
											</ul>

											<ul class="navbar-nav ms-xl-auto mt-2 mt-xl-0">
												<li class="nav-item dropdown ms-lg-2">
													<a href="#" class="navbar-nav-link align-items-center rounded p-1" data-bs-toggle="dropdown">
														<div class="status-indicator-container">
															<img src="../../../assets/images/demo/users/face11.jpg" class="w-32px h-32px rounded-sm" alt="">
															<span class="status-indicator bg-success"></span>
														</div>
														<span class="d-none d-lg-inline-block mx-lg-2">Victoria</span>
													</a>

													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">
															<i class="ph-user-circle me-2"></i>
															My profile
														</a>
														<a href="#" class="dropdown-item">
															<i class="ph-currency-circle-dollar me-2"></i>
															My subscription
														</a>
														<a href="#" class="dropdown-item">
															<i class="ph-shopping-cart me-2"></i>
															My orders
														</a>
														<a href="#" class="dropdown-item">
															<i class="ph-envelope-open me-2"></i>
															My inbox
															<span class="badge bg-primary rounded-pill ms-auto">26</span>
														</a>
														<div class="dropdown-divider"></div>
														<a href="#" class="dropdown-item">
															<i class="ph-gear me-2"></i>
															Account settings
														</a>
														<a href="#" class="dropdown-item">
															<i class="ph-sign-out me-2"></i>
															Logout
														</a>
													</div>
												</li>
											</ul>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-mixed2">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-mixed2">
											<ul class="navbar-nav mt-2 mt-xl-0">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">List</a>

													<div class="dropdown-menu start-0 end-0 py-0 mx-xl-3">
														<div class="p-3">
															<div class="row">
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">Form components</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-3 mb-xl-0">
																		<a href="form_inputs.html" class="dropdown-item rounded">Basic inputs</a>
																		<a href="form_controls_extended.html" class="dropdown-item rounded">Extended controls</a>
																		<a href="form_select2.html" class="dropdown-item rounded">Select2 selects</a>
																		<a href="form_floating_labels.html" class="dropdown-item rounded">Floating labels</a>
																		<a href="form_validation.html" class="dropdown-item rounded">Validation</a>
																	</div>
																</div>
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">UI components</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-3 mb-xl-0">
																		<a href="components_modals.html" class="dropdown-item rounded">Modals <span class="badge bg-secondary rounded-pill ms-auto">10+</span></a>
																		<a href="components_dropdowns.html" class="dropdown-item rounded">Dropdown menus</a>
																		<a href="components_buttons.html" class="dropdown-item rounded">Buttons</a>
																		<a href="components_tabs.html" class="dropdown-item rounded">Tabs component</a>
																		<a href="components_breadcrumbs.html" class="dropdown-item rounded">Breadcrumbs</a>
																	</div>
																</div>
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">Sidebars</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-3 mb-xl-0">
																		<a href="sidebar_default_collapse.html" class="dropdown-item rounded">Main sidebar</a>
																		<a href="sidebar_secondary_after.html" class="dropdown-item rounded">Secondary sidebar</a>
																		<a href="sidebar_right_default_collapse.html" class="dropdown-item rounded">Right sidebar</a>
																		<a href="sidebar_content_left.html" class="dropdown-item rounded">Content sidebar</a>
																		<a href="sidebar_components.html" class="dropdown-item rounded">Sidebar components</a>
																	</div>
																</div>
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">Navigation</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-3 mb-xl-0">
																		<a href="navigation_horizontal_click.html" class="dropdown-item rounded">Submenu on click</a>
																		<a href="navigation_horizontal_hover.html" class="dropdown-item rounded">Submenu on hover</a>
																		<a href="navigation_horizontal_elements.html" class="dropdown-item rounded">With custom elements</a>
																		<a href="navigation_horizontal_tabs.html" class="dropdown-item rounded">Tabbed navigation</a>
																		<a href="navigation_horizontal_mega.html" class="dropdown-item rounded">Horizontal mega menu</a>
																	</div>
																</div>
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">Navbars</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-3 mb-xl-0">
																		<a href="navbar_single_top_static.html" class="dropdown-item rounded">Single navbar</a>
																		<a href="navbar_multiple_top_static.html" class="dropdown-item rounded">Multiple navbars</a>
																		<a href="navbar_colors.html" class="dropdown-item rounded">Color options</a>
																		<a href="navbar_hideable.html" class="dropdown-item rounded">Hide on scroll</a>
																		<a href="navbar_components.html" class="dropdown-item rounded active">Navbar components</a>
																	</div>
																</div>
																<div class="col-xl-2">
																	<div class="dropdown-header fw-semibold p-0">Extensions</div>
																	<div class="dropdown-divider mb-2"></div>
																	<div class="mb-0">
																		<a href="jqueryui_interactions.html" class="dropdown-item rounded">jQuery UI</a>
																		<a href="animations_velocity_basic.html" class="dropdown-item rounded">Animations <span class="badge badge-pill badge-info ms-auto">14</span></a>
																		<a href="uploader_plupload.html" class="dropdown-item rounded">File uploaders</a>
																		<a href="extension_image_cropper.html" class="dropdown-item rounded">Image cropper</a>
																		<a href="fullcalendar_views.html" class="dropdown-item rounded">Calendars</a>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</li>

												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown" data-bs-auto-close="outside">Tabs</a>

													<div class="dropdown-menu py-0 wmin-xl-350">
														<div class="d-flex align-items-center p-3 pb-2">
															<span class="fw-semibold">Tabs example</span>
															<a href="#" class="text-body ms-auto">
																<i class="ph-gear"></i>
															</a>
														</div>

														<ul class="nav nav-tabs nav-tabs-underline nav-justified">
															<li class="nav-item">
																<a href="#tab_light1" class="nav-link active" data-bs-toggle="tab">
																	<i class="icon-compose me-2"></i>
																	Form example
																</a>
															</li>
															<li class="nav-item">
																<a href="#tab_light2" class="nav-link" data-bs-toggle="tab">
																	<i class="icon-list3 me-2"></i>
																	List example
																</a>
															</li>
														</ul>

														<div class="tab-content">
															<div class="tab-pane fade show active" id="tab_light1">
																<form class="p-3" action="#">
																	<div class="form-control-feedback form-control-feedback-end mb-3">
																		<input type="text" class="form-control" placeholder="Your email">
																		<div class="form-control-feedback-icon">
																			<i class="ph-at"></i>
																		</div>
																	</div>

																	<div class="mb-3">
																		<select class="form-select">
																			<option value="updates" selected>Website updates</option>
																			<option value="discounts">Discount offers</option>
																			<option value="catalog">Catalog</option>
																			<option value="prints">Prints</option>
																			<option value="promo">Promotions</option>
																		</select>
																	</div>

																	<div class="mb-3">
																		<textarea class="form-control" cols="3" rows="3" placeholder="Your message"></textarea>
																	</div>

																	<div class="d-flex align-items-center justify-content-between">
																		<label class="form-check mb-0">
																			<input type="checkbox" class="form-check-input" checked>
																			<span class="form-check-label">Save as draft</span>
																		</label>

																		<button type="submit" class="btn btn-primary">
																			Submit
																			<i class="ph-paper-plane-tilt ms-2"></i>
																		</button>
																	</div>
																</form>
															</div>

															<div class="tab-pane fade" id="tab_light2">
																<div class="p-3">
																	<div class="d-flex align-items-start mb-3">
																		<div class="status-indicator-container me-2">
																			<img src="../../../assets/images/demo/users/face1.jpg" class="w-40px h-40px rounded-pill" alt="">
																			<span class="status-indicator bg-success"></span>
																		</div>

																		<div class="flex-fill">
																			<a href="#" class="fw-semibold">James Alexander</a>
																			<div class="fs-sm text-muted">Santa Ana, CA.</div>
																		</div>

																		<div class="d-flex align-self-center ms-3">
																			<a href="#" class="text-body">
																				<i class="ph-phone"></i>
																			</a>
																			<a href="#" class="text-body ms-2">
																				<i class="ph-envelope"></i>
																			</a>
																		</div>
																	</div>

																	<div class="d-flex align-items-start mb-3">
																		<div class="status-indicator-container me-2">
																			<img src="../../../assets/images/demo/users/face2.jpg" class="w-40px h-40px rounded-pill" alt="">
																			<span class="status-indicator bg-success"></span>
																		</div>

																		<div class="flex-fill">
																			<a href="#" class="fw-semibold">Jeremy Victorino</a>
																			<div class="fs-sm text-muted">Dowagiac, MI.</div>
																		</div>

																		<div class="d-flex align-self-center ms-3">
																			<a href="#" class="text-body">
																				<i class="ph-phone"></i>
																			</a>
																			<a href="#" class="text-body ms-2">
																				<i class="ph-envelope"></i>
																			</a>
																		</div>
																	</div>

																	<div class="d-flex align-items-start mb-3">
																		<div class="status-indicator-container me-2">
																			<img src="../../../assets/images/demo/users/face3.jpg" class="w-40px h-40px rounded-pill" alt="">
																			<span class="status-indicator bg-success"></span>
																		</div>

																		<div class="flex-fill">
																			<a href="#" class="fw-semibold">Margo Baker</a>
																			<div class="fs-sm text-muted">Kasaan, AK.</div>
																		</div>

																		<div class="d-flex align-self-center ms-3">
																			<a href="#" class="text-body">
																				<i class="ph-phone"></i>
																			</a>
																			<a href="#" class="text-body ms-2">
																				<i class="ph-envelope"></i>
																			</a>
																		</div>
																	</div>

																	<div class="d-flex align-items-start mb-3">
																		<div class="status-indicator-container me-2">
																			<img src="../../../assets/images/demo/users/face4.jpg" class="w-40px h-40px rounded-pill" alt="">
																			<span class="status-indicator bg-success"></span>
																		</div>

																		<div class="flex-fill">
																			<a href="#" class="fw-semibold">Beatrix Diaz</a>
																			<div class="fs-sm text-muted">Neenah, WI.</div>
																		</div>

																		<div class="d-flex align-self-center ms-3">
																			<a href="#" class="text-body">
																				<i class="ph-phone"></i>
																			</a>
																			<a href="#" class="text-body ms-2">
																				<i class="ph-envelope"></i>
																			</a>
																		</div>
																	</div>

																	<div class="d-flex align-items-start">
																		<div class="status-indicator-container me-2">
																			<img src="../../../assets/images/demo/users/face5.jpg" class="w-40px h-40px rounded-pill" alt="">
																			<span class="status-indicator bg-success"></span>
																		</div>

																		<div class="flex-fill">
																			<a href="#" class="fw-semibold">Richard Vango</a>
																			<div class="fs-sm text-muted">Grapevine, TX.</div>
																		</div>

																		<div class="d-flex align-self-center ms-3">
																			<a href="#" class="text-body">
																				<i class="ph-phone"></i>
																			</a>
																			<a href="#" class="text-body ms-2">
																				<i class="ph-envelope"></i>
																			</a>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</li>

												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">Classic</a>

													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">Second level</a>
														<div class="dropdown-submenu">
															<a href="#" class="dropdown-item dropdown-toggle">Second level with child</a>
															<div class="dropdown-menu">
																<a href="#" class="dropdown-item">Third level</a>
																<div class="dropdown-submenu">
																	<a href="#" class="dropdown-item dropdown-toggle">Third level with child</a>
																	<div class="dropdown-menu">
																		<a href="#" class="dropdown-item">Fourth level</a>
																		<a href="#" class="dropdown-item">Fourth level</a>
																		<a href="#" class="dropdown-item">Fourth level</a>
																	</div>
																</div>
																<a href="#" class="dropdown-item">Third level</a>
															</div>
														</div>
														<a href="#" class="dropdown-item">Second level</a>
													</div>
												</li>
											</ul>

											<ul class="navbar-nav ms-xl-auto mt-2 mt-xl-0">
												<li class="nav-item dropdown ms-lg-2">
													<a href="#" class="navbar-nav-link align-items-center rounded p-1" data-bs-toggle="dropdown">
														<div class="status-indicator-container">
															<img src="../../../assets/images/demo/users/face11.jpg" class="w-32px h-32px rounded-sm" alt="">
															<span class="status-indicator bg-success"></span>
														</div>
														<span class="d-none d-lg-inline-block mx-lg-2">Victoria</span>
													</a>

													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">
															<i class="ph-user-circle me-2"></i>
															My profile
														</a>
														<a href="#" class="dropdown-item">
															<i class="ph-currency-circle-dollar me-2"></i>
															My subscription
														</a>
														<a href="#" class="dropdown-item">
															<i class="ph-shopping-cart me-2"></i>
															My orders
														</a>
														<a href="#" class="dropdown-item">
															<i class="ph-envelope-open me-2"></i>
															My inbox
															<span class="badge bg-primary rounded-pill ms-auto">26</span>
														</a>
														<div class="dropdown-divider"></div>
														<a href="#" class="dropdown-item">
															<i class="ph-gear me-2"></i>
															Account settings
														</a>
														<a href="#" class="dropdown-item">
															<i class="ph-sign-out me-2"></i>
															Logout
														</a>
													</div>
												</li>
											</ul>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<p>Date range picker</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-daterange">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-daterange">
											<ul class="navbar-nav mt-2 mt-xl-0">
												<li class="nav-item daterange-ranges">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle">
														<i class="ph-calendar me-2"></i>
														<span></span>
													</a>
												</li>
											</ul>

											<div class="mt-2 mt-xl-0 ms-xl-auto">
												<button type="button" class="btn btn-flat-white border-transparent w-100 w-xl-auto daterange-ranges-button dropdown-toggle">
													<i class="ph-calendar me-2"></i>
													<span></span>
												</button>
											</div>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-daterange2">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-daterange2">
											<ul class="navbar-nav mt-2 mt-xl-0">
												<li class="nav-item daterange-ranges">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle">
														<i class="ph-calendar me-2"></i>
														<span></span>
													</a>
												</li>
											</ul>

											<div class="mt-2 mt-xl-0 ms-xl-auto">
												<button type="button" class="btn btn-light w-100 w-xl-auto daterange-ranges-button dropdown-toggle">
													<i class="ph-calendar me-2"></i>
													<span></span>
												</button>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div>
								<p>Language selector</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-language">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-language">
											<ul class="navbar-nav mt-2 mt-xl-0">
												<li class="nav-item dropdown">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">
														<img src="../../../assets/images/lang/es.svg" class="h-16px me-2" alt="">
														España
													</a>

													<div class="dropdown-menu">
														<a href="#" class="dropdown-item">
															<img src="../../../assets/images/lang/gb.svg" class="h-16px me-2" alt="">
															English
														</a>
														<a href="#" class="dropdown-item">
															<img src="../../../assets/images/lang/ua.svg" class="h-16px me-2" alt="">
															Українська
														</a>
														<a href="#" class="dropdown-item">
															<img src="../../../assets/images/lang/de.svg" class="h-16px me-2" alt="">
															Deutsch
														</a>
														<a href="#" class="dropdown-item active">
															<img src="../../../assets/images/lang/es.svg" class="h-16px me-2" alt="">
															España
														</a>
													</div>
												</li>
											</ul>

											<ul class="navbar-nav ms-xl-auto">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded">
														<img src="../../../assets/images/lang/gb.svg" class="h-16px me-2" alt="">
														EN
													</a>
												</li>
												<li class="nav-item ms-xl-1">
													<a href="#" class="navbar-nav-link rounded">
														<img src="../../../assets/images/lang/de.svg" class="h-16px me-2" alt="">
														DE
													</a>
												</li>
												<li class="nav-item ms-xl-1">
													<a href="#" class="navbar-nav-link rounded">
														<img src="../../../assets/images/lang/it.svg" class="h-16px me-2" alt="">
														IT
													</a>
												</li>
											</ul>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-language2">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-language2">
											<ul class="navbar-nav mt-2 mt-xl-0">
												<li class="nav-item dropdown">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">
														<img src="../../../assets/images/lang/es.svg" class="h-16px me-2" alt="">
														España
													</a>

													<div class="dropdown-menu">
														<a href="#" class="dropdown-item">
															<img src="../../../assets/images/lang/gb.svg" class="h-16px me-2" alt="">
															English
														</a>
														<a href="#" class="dropdown-item">
															<img src="../../../assets/images/lang/ua.svg" class="h-16px me-2" alt="">
															Українська
														</a>
														<a href="#" class="dropdown-item">
															<img src="../../../assets/images/lang/de.svg" class="h-16px me-2" alt="">
															Deutsch
														</a>
														<a href="#" class="dropdown-item active">
															<img src="../../../assets/images/lang/es.svg" class="h-16px me-2" alt="">
															España
														</a>
													</div>
												</li>
											</ul>

											<ul class="navbar-nav ms-xl-auto">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded">
														<img src="../../../assets/images/lang/gb.svg" class="h-16px me-2" alt="">
														EN
													</a>
												</li>
												<li class="nav-item ms-xl-1">
													<a href="#" class="navbar-nav-link rounded">
														<img src="../../../assets/images/lang/de.svg" class="h-16px me-2" alt="">
														DE
													</a>
												</li>
												<li class="nav-item ms-xl-1">
													<a href="#" class="navbar-nav-link rounded">
														<img src="../../../assets/images/lang/it.svg" class="h-16px me-2" alt="">
														IT
													</a>
												</li>
											</ul>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- /custom components -->


					<!-- Navbar navigation -->
					<div class="card">
						<div class="card-header">
							<h5 class="mb-0">Navbar navigation</h5>
						</div>

						<div class="card-body">
							<div class="mb-4">
								<h6>Basic navigation</h6>
								<p class="mb-3">Navbar navigation links build on our <code>.nav</code> options with their own modifier class and require the use of toggler classes for proper responsive styling. Navigation in navbars will also grow to occupy as much horizontal space as possible to keep your navbar contents securely aligned. Dropdown menus require a wrapping element for positioning, so be sure to use separate and nested elements for <code>.nav-item</code> and <code>.nav-link</code>.</p>

								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-navigation">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-navigation">
											<ul class="navbar-nav mt-2 mt-xl-0">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded active">Active link</a>
												</li>
												<li class="nav-item ms-xl-1">
													<a href="#" class="navbar-nav-link rounded">Link</a>
												</li>
												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">Dropdown</a>
													<div class="dropdown-menu">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</li>
											</ul>

											<ul class="navbar-nav ms-xl-auto">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded">Link</a>
												</li>
												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">Dropdown</a>
													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</li>
											</ul>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-navigation2">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-navigation2">
											<ul class="navbar-nav mt-2 mt-xl-0">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded active">Active link</a>
												</li>
												<li class="nav-item ms-xl-1">
													<a href="#" class="navbar-nav-link rounded">Link</a>
												</li>
												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">Dropdown</a>
													<div class="dropdown-menu">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</li>
											</ul>

											<ul class="navbar-nav ms-xl-auto">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded">Link</a>
												</li>
												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">Dropdown</a>
													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</li>
											</ul>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<h6>Navigation icons</h6>
								<p class="mb-3">Navbar navigation supports icons: text with left and right positioned icons, multiple icons within 1 item, icons only and carets for items with dropdown menu. By default, sidebar control buttons are placed in the left navigation. To add icons, place <code>&lt;i></code> element with icon class to the navigation link element. To use with text, depending on the position place icon before or after item text. Use <code>.ms-2</code> or <code>.me-2</code> to add left/right spacer between the icon and nav link text.</p>

								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-navigation-icons">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-navigation-icons">
											<ul class="navbar-nav mt-2 mt-xl-0">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded active">
														<i class="ph-download-simple"></i>
														<span class="d-xl-none ms-2">Downloads</span>
													</a>
												</li>

												<li class="nav-item ms-xl-1">
													<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
														<i class="ph-chart-bar"></i>
														<span class="d-xl-none ms-2">My statistics</span>
													</a>
												</li>
											</ul>

											<ul class="navbar-nav ms-xl-auto">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded">
														<i class="ph-list-dashes me-2"></i>
														Log
													</a>
												</li>
												<li class="nav-item ms-xl-1">
													<a href="#" class="navbar-nav-link rounded">
														<i class="ph-sign-out me-2"></i>
														Logout
													</a>
												</li>
												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded dropdown-toggle" data-bs-toggle="dropdown">
														<i class="ph-gear"></i>
														<span class="d-xl-none ms-2">Settings</span>
													</a>
													
													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</li>
											</ul>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-navigation-icons2">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-navigation-icons2">
											<ul class="navbar-nav mt-2 mt-xl-0">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded active">
														<i class="ph-download-simple"></i>
														<span class="d-xl-none ms-2">Downloads</span>
													</a>
												</li>

												<li class="nav-item ms-xl-1">
													<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
														<i class="ph-chart-bar"></i>
														<span class="d-xl-none ms-2">My statistics</span>
													</a>
												</li>
											</ul>

											<ul class="navbar-nav ms-xl-auto">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded">
														<i class="ph-list-dashes me-2"></i>
														Log
													</a>
												</li>
												<li class="nav-item ms-xl-1">
													<a href="#" class="navbar-nav-link rounded">
														<i class="ph-sign-out me-2"></i>
														Logout
													</a>
												</li>
												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded dropdown-toggle" data-bs-toggle="dropdown">
														<i class="ph-gear"></i>
														<span class="d-xl-none ms-2">Settings</span>
													</a>
													
													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</li>
											</ul>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<h6>Badges</h6>
								<p class="mb-3">Flexibility of the navbar navigation also allows you to use 2 kinds of badges - <code>inline</code> and <code>floating</code>. Both types can have left and right positions. To use inline badge, just add badge markup next to the text label, default placement is absolute with top-right position. To use left positioned elements, place it before text in inline version and use our utility classes to adjust position.</p>

								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid justify-content-start">
										<div class="navbar-brand flex-1 flex-xl-0">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="d-none d-sm-inline-block text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<ul class="nav">
											<li class="nav-item">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-squares-four"></i>
													<span class="badge bg-yellow text-black ms-2">New</span>
												</a>
											</li>
											<li class="nav-item ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-list-checks"></i>
													<span class="badge bg-yellow text-black rounded-pill ms-2">89</span>
												</a>
											</li>
										</ul>

										<ul class="nav ms-xl-auto">
											<li class="nav-item position-relative ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-envelope"></i>
													<span class="badge bg-yellow text-black position-absolute top-0 start-100 translate-middle mt-1">2</span>
												</a>
											</li>
											<li class="nav-item position-relative ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-check-square-offset"></i>
													<span class="badge bg-yellow text-black position-absolute top-0 end-0 translate-middle-top zindex-1 rounded-pill mt-1 me-1">4</span>
												</a>
											</li>
										</ul>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid justify-content-start">
										<div class="navbar-brand flex-1 flex-xl-0">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="d-none d-sm-inline-block text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<ul class="nav">
											<li class="nav-item">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-squares-four"></i>
													<span class="badge bg-yellow text-black ms-2">New</span>
												</a>
											</li>
											<li class="nav-item ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-list-checks"></i>
													<span class="badge bg-yellow text-black rounded-pill ms-2">89</span>
												</a>
											</li>
										</ul>

										<ul class="nav ms-xl-auto">
											<li class="nav-item position-relative ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-envelope"></i>
													<span class="badge bg-yellow text-black position-absolute top-0 start-100 translate-middle mt-1">2</span>
												</a>
											</li>
											<li class="nav-item position-relative ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-check-square-offset"></i>
													<span class="badge bg-yellow text-black position-absolute top-0 end-0 translate-middle-top zindex-1 rounded-pill mt-1 me-1">4</span>
												</a>
											</li>
										</ul>
									</div>
								</div>
							</div>
							
							<div>
								<h6>Badge marks</h6>
								<p class="mb-3">Instead of badges, which should contain text by default, navbar component supports badge marks - small rounded indicators. These indicators support all available colors and can have 2 different styles - circle and ring. To use ring, use a combination of our border, background and padding utility classes. Colors are controlled by color classes without any additional CSS. Both variations do not require <code>.badge</code> class by default. Also these indicators support left/right alignment and static/absolute positioning.</p>

								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid justify-content-start">
										<div class="navbar-brand flex-1 flex-xl-0">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="d-none d-sm-inline-block text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<ul class="nav">
											<li class="nav-item">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-squares-four"></i>
													<span class="bg-yellow rounded-pill p-1 ms-2"></span>
												</a>
											</li>
											<li class="nav-item ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-list-checks"></i>
													<span class="border border-yellow rounded-pill p-1 ms-2"></span>
												</a>
											</li>
										</ul>

										<ul class="nav ms-xl-auto">
											<li class="nav-item position-relative ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-envelope"></i>
													<span class="bg-yellow position-absolute top-0 end-0 rounded-pill p-1"></span>
												</a>
											</li>
											<li class="nav-item position-relative ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-check-square-offset"></i>
													<span class="border border-yellow position-absolute top-0 end-0 rounded-pill p-1"></span>
												</a>
											</li>
										</ul>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid justify-content-start">
										<div class="navbar-brand flex-1 flex-xl-0">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="d-none d-sm-inline-block text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<ul class="nav">
											<li class="nav-item">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-squares-four"></i>
													<span class="bg-yellow rounded-pill p-1 ms-2"></span>
												</a>
											</li>
											<li class="nav-item ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-list-checks"></i>
													<span class="border border-yellow rounded-pill p-1 ms-2"></span>
												</a>
											</li>
										</ul>

										<ul class="nav ms-xl-auto">
											<li class="nav-item position-relative ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-envelope"></i>
													<span class="bg-yellow position-absolute top-0 end-0 rounded-pill p-1"></span>
												</a>
											</li>
											<li class="nav-item position-relative ms-1">
												<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded">
													<i class="ph-check-square-offset"></i>
													<span class="border border-yellow position-absolute top-0 end-0 rounded-pill p-1"></span>
												</a>
											</li>
										</ul>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- /navbar navigation -->


					<!-- Basic components -->
					<div class="card">
						<div class="card-header">
							<h5 class="mb-0">Basic components</h5>
						</div>

						<div class="card-body">
							<div class="mb-4">
								<h6>Navbar buttons</h6>
								<p class="mb-3">Various buttons are supported as part of navbar components. This is also a great reminder that vertical alignment utilities can be used to align different sized elements. Button groups and button dropdowns in different colors, sizes and styles are also supported. For multiple buttons, use reponsive <code>spacing</code> utility classes for proper vertical and horizontal alignment.

								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-buttons">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-buttons">
											<div class="mt-3 mt-xl-0">
												<button type="button" class="btn btn-outline-light me-2" data-color-theme="dark">Button</button>

												<div class="btn-group">
													<button type="button" class="btn btn-yellow dropdown-toggle" data-bs-toggle="dropdown">Menu</button>
													<div class="dropdown-menu">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</div>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<button type="button" class="btn btn-light" data-color-theme="dark">
													<i class="ph-gear me-2"></i>
													Settings
												</button>
											</div>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-buttons2">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-buttons2">
											<div class="mt-3 mt-xl-0">
												<button type="button" class="btn btn-outline-primary me-2">Button</button>

												<div class="btn-group">
													<button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">Menu</button>
													<div class="dropdown-menu">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</div>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<button type="button" class="btn btn-light">
													<i class="ph-gear me-2"></i>
													Settings
												</button>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<h6>Navbar text</h6>
								<p class="mb-3">Wrap strings of text in an element with <code>.navbar-text</code>, usually on a <code>&lt;span&gt;</code> tag for proper leading and color. This class adjusts vertical alignment and horizontal spacing for strings of text. In some cases links within <code>.navbar-text</code> may need color adjustments, use <code>color</code> utility classes to style links properly.

								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-text">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-text">
											<span class="navbar-text d-block d-xl-inline-block">
												<i class="ph-user-circle me-1"></i>
												Signed in as
												<a href="#">Eugene</a>
											</span>

											<span class="navbar-text ms-xl-auto">
												<i class="ph-bell me-1"></i>
												12 new messages
											</span>

											<ul class="navbar-nav mt-2 mt-xl-0 ms-xl-3">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded">Link</a>
												</li>
												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">Dropdown</a>
													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</li>
											</ul>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-text2">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-text2">
											<span class="navbar-text d-block d-xl-inline-block">
												<i class="ph-user-circle me-1"></i>
												Signed in as
												<a href="#">Eugene</a>
											</span>

											<span class="navbar-text ms-xl-auto">
												<i class="ph-bell me-1"></i>
												12 new messages
											</span>

											<ul class="navbar-nav mt-2 mt-xl-0 ms-xl-3">
												<li class="nav-item">
													<a href="#" class="navbar-nav-link rounded">Link</a>
												</li>
												<li class="nav-item dropdown ms-xl-1">
													<a href="#" class="navbar-nav-link rounded dropdown-toggle" data-bs-toggle="dropdown">Dropdown</a>
													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</li>
											</ul>
										</div>
									</div>
								</div>
							</div>

							<div>
								<h6>Progress bars</h6>
								<p class="mb-3">Provide up-to-date feedback on the progress of a workflow or action with simple yet flexible progress bars. Progress bars inside navbar support all possible styling options: colors, animations, labels, appearance, sizes etc. Also you can add text and icon labels to display current action, it's also available in both left and right positions.</p>

								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-progress">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-progress">
											<div class="progress mt-2 mt-xl-0 wmin-xl-200" data-color-theme="dark">
												<div class="progress-bar bg-warning progress-bar-striped progress-bar-animated" style="width: 60%" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
											</div>

											<div class="navbar-text ms-xl-auto">
												<i class="ph-spinner spinner me-2"></i>
												Loading data
											</div>

											<div class="progress ms-xl-3 mb-2 mb-xl-0 wmin-xl-200" data-color-theme="dark">
												<div class="progress-bar bg-teal progress-bar-striped progress-bar-animated" style="width: 60%" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
											</div>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-progress2">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-progress2">
											<div class="progress mt-2 mt-xl-0 wmin-xl-200">
												<div class="progress-bar bg-warning progress-bar-striped progress-bar-animated" style="width: 60%" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
											</div>

											<div class="navbar-text ms-xl-auto">
												<i class="ph-spinner spinner me-2"></i>
												Loading data
											</div>

											<div class="progress ms-xl-3 mb-2 mb-xl-0 wmin-xl-200">
												<div class="progress-bar bg-teal progress-bar-striped progress-bar-animated" style="width: 60%" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- /basic components -->


					<!-- Navbar forms -->
					<div class="card">
						<div class="card-header">
							<h5 class="mb-0">Form components</h5>
						</div>

						<div class="card-body">
							<p class="mb-3">Place form content within container with <code>spacing</code> utility classes for proper vertical alignment and collapsed behavior in narrow viewports. Use the <code>alignment</code> options to decide where it resides within the navbar content. Navbar supports all form components: checkboxes, radios, default and custom selects, file inputs etc. Some form controls, like input groups, may require <code>width</code> utility classes to be show up properly within a navbar.</p>

							<div class="mb-4">
								<p>Basic form controls</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-basic-dark">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-basic-dark">
											<form class="mt-3 mt-xl-0">
												<input type="text" class="form-control bg-transparent wmin-xl-200" placeholder="Text field" data-color-theme="dark">
											</form>

											<form class="d-flex align-items-center flex-nowrap flex-xl-nowrap mt-3 mb-2 my-xl-0 ms-xl-auto">
												<input type="text" class="form-control bg-transparent wmin-xl-200" placeholder="Text field with button" data-color-theme="dark">
												<button type="submit" class="btn btn-primary ms-3 ms-xl-2">Submit</button>
											</form>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-basic-light">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-basic-light">
											<form class="mt-3 mt-xl-0">
												<input type="text" class="form-control wmin-xl-200" placeholder="Text field">
											</form>

											<form class="d-flex align-items-center flex-nowrap flex-xl-nowrap mt-3 mb-2 my-xl-0 ms-xl-auto">
												<input type="text" class="form-control wmin-xl-200" placeholder="Text field with button">
												<button type="submit" class="btn btn-primary ms-3 ms-xl-2">Submit</button>
											</form>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<p>Default and custom selects</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-selects-dark">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-selects-dark">
											<form class="mt-3 mt-xl-0">
												<select class="form-select bg-transparent wmin-xl-200" data-color-theme="dark">
													<option value="">Select your state</option>
													<option value="AK">Alaska</option>
													<option value="HI">Hawaii</option>
													<option value="CA">California</option>
													<option value="NV">Nevada</option>
													<option value="OR">Oregon</option>
													<option value="WA">Washington</option>
													<option value="AZ">Arizona</option>
												</select>
											</form>

											<form class="d-flex align-items-center flex-nowrap flex-xl-nowrap mt-3 mb-2 my-xl-0 ms-xl-auto">
												<select class="form-select bg-transparent wmin-xl-200" data-color-theme="dark">
													<option value="">Select your state</option>
													<option value="AK">Alaska</option>
													<option value="HI">Hawaii</option>
													<option value="CA">California</option>
													<option value="NV">Nevada</option>
													<option value="OR">Oregon</option>
													<option value="WA">Washington</option>
													<option value="AZ">Arizona</option>
												</select>
												<button type="submit" class="btn btn-primary ms-3 ms-xl-2">Submit</button>
											</form>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-selects-light">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-selects-light">
											<form class="mt-3 mt-xl-0">
												<select class="form-select wmin-xl-200">
													<option value="">Select your state</option>
													<option value="AK">Alaska</option>
													<option value="HI">Hawaii</option>
													<option value="CA">California</option>
													<option value="NV">Nevada</option>
													<option value="OR">Oregon</option>
													<option value="WA">Washington</option>
													<option value="AZ">Arizona</option>
												</select>
											</form>

											<form class="d-flex align-items-center flex-nowrap flex-xl-nowrap mt-3 mb-2 my-xl-0 ms-xl-auto">
												<select class="form-select wmin-xl-200">
													<option value="">Select your state</option>
													<option value="AK">Alaska</option>
													<option value="HI">Hawaii</option>
													<option value="CA">California</option>
													<option value="NV">Nevada</option>
													<option value="OR">Oregon</option>
													<option value="WA">Washington</option>
													<option value="AZ">Arizona</option>
												</select>
												<button type="submit" class="btn btn-primary ms-3 ms-xl-2">Submit</button>
											</form>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<p>Input group, file select</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-inputs-dark">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-inputs-dark">
											<div class="mt-3 mt-xl-0">
												<div class="input-group" data-color-theme="dark">
													<span class="input-group-text">
														<i class="ph-spinner spinner"></i>
													</span>
													<input type="text" class="form-control bg-transparent wmin-xl-200" placeholder="Input group">
													<button type="button" class="btn btn-light btn-icon dropdown-toggle" data-bs-toggle="dropdown">
														<i class="ph-gear"></i>
													</button>

													<div class="dropdown-menu dropdown-menu-end" data-color-theme="light">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</div>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<input type="file" class="form-control bg-transparent wmin-xl-200" data-color-theme="dark">
											</div>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-inputs-light">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-inputs-light">
											<div class="mt-3 mt-xl-0">
												<div class="input-group">
													<span class="input-group-text">
														<i class="ph-spinner spinner"></i>
													</span>
													<input type="text" class="form-control wmin-xl-200" placeholder="Input group">
													<button type="button" class="btn btn-light btn-icon dropdown-toggle" data-bs-toggle="dropdown">
														<i class="ph-gear"></i>
													</button>

													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">Action</a>
														<a href="#" class="dropdown-item">Another action</a>
														<a href="#" class="dropdown-item">Something else here</a>
														<a href="#" class="dropdown-item">One more line</a>
													</div>
												</div>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<input type="file" class="form-control wmin-xl-200">
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<p>Input with icons</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-icons-dark">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-icons-dark">
											<div class="mt-3 mt-xl-0">
												<div class="form-control-feedback form-control-feedback-start mb-3 mb-xl-0" data-color-theme="dark">
													<input type="search" class="form-control bg-transparent wmin-xl-200" placeholder="Search field">
													<div class="form-control-feedback-icon">
														<i class="ph-magnifying-glass"></i>
													</div>
												</div>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<div class="form-control-feedback form-control-feedback-end" data-color-theme="dark">
													<input type="text" class="form-control bg-transparent wmin-xl-200" placeholder="Text field">
													<div class="form-control-feedback-icon">
														<i class="ph-spinner spinner"></i>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-icons-light">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-icons-light">
											<div class="mt-3 mt-xl-0">
												<div class="form-control-feedback form-control-feedback-start mb-3 mb-xl-0">
													<input type="search" class="form-control wmin-xl-200" placeholder="Search field">
													<div class="form-control-feedback-icon">
														<i class="ph-magnifying-glass"></i>
													</div>
												</div>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<div class="form-control-feedback form-control-feedback-end">
													<input type="text" class="form-control wmin-xl-200" placeholder="Text field">
													<div class="form-control-feedback-icon">
														<i class="ph-spinner spinner"></i>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<p>Select2 select</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-select2-dark">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-select2-dark">
											<div class="mt-3 mt-xl-0">
												<div class="wmin-xl-200" data-color-theme="dark">
													<select class="form-control form-control-select2" data-container-css-class="bg-transparent">
														<option value="AK">Alaska</option>
														<option value="HI">Hawaii</option>
														<option value="CA">California</option>
														<option value="NV">Nevada</option>
														<option value="OR">Oregon</option>
														<option value="WA">Washington</option>
														<option value="AZ">Arizona</option>
													</select>
												</div>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<div class="wmin-xl-250" data-color-theme="dark">
													<select class="form-control form-control-select2" multiple="multiple" data-container-css-class="bg-transparent">
														<option value="AK" selected>Alaska</option>
														<option value="HI">Hawaii</option>
														<option value="CA">California</option>
														<option value="NV">Nevada</option>
														<option value="OR" selected>Oregon</option>
														<option value="WA">Washington</option>
														<option value="AZ">Arizona</option>
													</select>
												</div>
											</div>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-select2-light">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-select2-light">
											<div class="mt-3 mt-xl-0">
												<div class="wmin-xl-200">
													<select class="form-control form-control-select2">
														<option value="AK">Alaska</option>
														<option value="HI">Hawaii</option>
														<option value="CA">California</option>
														<option value="NV">Nevada</option>
														<option value="OR">Oregon</option>
														<option value="WA">Washington</option>
														<option value="AZ">Arizona</option>
													</select>
												</div>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<div class="wmin-xl-250">
													<select class="form-control form-control-select2" multiple="multiple">
														<option value="AK" selected>Alaska</option>
														<option value="HI">Hawaii</option>
														<option value="CA">California</option>
														<option value="NV">Nevada</option>
														<option value="OR" selected>Oregon</option>
														<option value="WA">Washington</option>
														<option value="AZ">Arizona</option>
													</select>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<p>Multiselect select</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-multiselect-dark">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-multiselect-dark">
											<div class="mt-3 mt-xl-0">
												<div class="wmin-xl-200" data-color-theme="dark">
													<select class="form-control form-control-multiselect" data-button-class="btn bg-transparent">
														<option value="AK">Alaska</option>
														<option value="HI">Hawaii</option>
														<option value="CA">California</option>
														<option value="NV">Nevada</option>
														<option value="OR">Oregon</option>
														<option value="WA">Washington</option>
														<option value="AZ">Arizona</option>
													</select>
												</div>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<div class="wmin-xl-200" data-color-theme="dark">
													<select class="form-control form-control-multiselect" data-button-class="btn bg-transparent" multiple="multiple">
														<option value="AK" selected>Alaska</option>
														<option value="HI">Hawaii</option>
														<option value="CA">California</option>
														<option value="NV" selected>Nevada</option>
														<option value="OR">Oregon</option>
														<option value="WA">Washington</option>
														<option value="AZ">Arizona</option>
													</select>
												</div>
											</div>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-multiselect-light">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-multiselect-light">
											<div class="mt-3 mt-xl-0">
												<div class="wmin-xl-200">
													<select class="form-control form-control-multiselect">
														<option value="AK">Alaska</option>
														<option value="HI">Hawaii</option>
														<option value="CA">California</option>
														<option value="NV">Nevada</option>
														<option value="OR">Oregon</option>
														<option value="WA">Washington</option>
														<option value="AZ">Arizona</option>
													</select>
												</div>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<div class="wmin-xl-200">
													<select class="form-control form-control-multiselect" multiple="multiple">
														<option value="AK" selected>Alaska</option>
														<option value="HI">Hawaii</option>
														<option value="CA">California</option>
														<option value="NV" selected>Nevada</option>
														<option value="OR">Oregon</option>
														<option value="WA">Washington</option>
														<option value="AZ">Arizona</option>
													</select>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<p>Checkboxes - both alignment</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-checks-dark">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-checks-dark">
											<div class="hstack gap-3 mt-3 mt-xl-0" data-color-theme="dark">
												<label class="form-check">
													<input type="checkbox" class="form-check-input" checked>
													<span class="form-check-label">Checked</span>
												</label>

												<label class="form-check">
													<input type="checkbox" class="form-check-input">
													<span class="form-check-label">Unchecked</span>
												</label>
											</div>

											<div class="hstack gap-3 mt-3 mb-2 my-xl-0 ms-xl-auto" data-color-theme="dark">
												<label class="form-check form-check-reverse">
													<input type="checkbox" class="form-check-input" checked>
													<span class="form-check-label">Checked</span>
												</label>

												<label class="form-check form-check-reverse">
													<input type="checkbox" class="form-check-input">
													<span class="form-check-label">Unchecked</span>
												</label>
											</div>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-checks-light">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-checks-light">
											<div class="hstack gap-3 mt-3 mt-xl-0">
												<label class="form-check">
													<input type="checkbox" class="form-check-input" checked>
													<span class="form-check-label">Checked</span>
												</label>

												<label class="form-check">
													<input type="checkbox" class="form-check-input">
													<span class="form-check-label">Unchecked</span>
												</label>
											</div>

											<div class="hstack gap-3 mt-3 mb-2 my-xl-0 ms-xl-auto">
												<label class="form-check form-check-reverse">
													<input type="checkbox" class="form-check-input" checked>
													<span class="form-check-label">Checked</span>
												</label>

												<label class="form-check form-check-reverse">
													<input type="checkbox" class="form-check-input">
													<span class="form-check-label">Unchecked</span>
												</label>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="mb-4">
								<p>Radios - both alignment</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-radios-dark">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-radios-dark">
											<div class="hstack gap-3 mt-3 mt-xl-0" data-color-theme="dark">
												<label class="form-check">
													<input type="radio" class="form-check-input" name="navbar-dark-radio-left" checked>
													<span class="form-check-label">Checked</span>
												</label>

												<label class="form-check">
													<input type="radio" class="form-check-input" name="navbar-dark-radio-left">
													<span class="form-check-label">Unchecked</span>
												</label>
											</div>

											<div class="hstack gap-3 mt-3 mb-2 my-xl-0 ms-xl-auto" data-color-theme="dark">
												<label class="form-check form-check-reverse">
													<input type="radio" class="form-check-input" name="navbar-dark-radio-right" checked>
													<span class="form-check-label">Checked</span>
												</label>

												<label class="form-check form-check-reverse">
													<input type="radio" class="form-check-input" name="navbar-dark-radio-right">
													<span class="form-check-label">Unchecked</span>
												</label>
											</div>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-radios-light">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-radios-light">
											<div class="hstack gap-3 mt-3 mt-xl-0">
												<label class="form-check">
													<input type="radio" class="form-check-input" name="navbar-light-radio-left" checked>
													<span class="form-check-label">Checked</span>
												</label>

												<label class="form-check">
													<input type="radio" class="form-check-input" name="navbar-light-radio-left">
													<span class="form-check-label">Unchecked</span>
												</label>
											</div>

											<div class="hstack gap-3 mt-3 mb-2 my-xl-0 ms-xl-auto">
												<label class="form-check form-check-reverse">
													<input type="radio" class="form-check-input" name="navbar-light-radio-right" checked>
													<span class="form-check-label">Checked</span>
												</label>

												<label class="form-check form-check-reverse">
													<input type="radio" class="form-check-input" name="navbar-light-radio-right">
													<span class="form-check-label">Unchecked</span>
												</label>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div>
								<p>Switches</p>
								<div class="navbar navbar-expand-xl navbar-dark rounded mb-2">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-white lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-toggles-dark">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-toggles-dark">
											<div class="mt-3 mt-xl-0">
												<label class="form-check form-switch">
													<input type="checkbox" class="form-check-input" checked data-color-theme="dark">
													<span class="form-check-label">Checked</span>
												</label>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<label class="form-check form-switch form-check-reverse">
													<input type="checkbox" class="form-check-input" checked data-color-theme="dark">
													<span class="form-check-label">Refresh</span>
												</label>
											</div>
										</div>
									</div>
								</div>

								<div class="navbar navbar-expand-xl border rounded">
									<div class="container-fluid">
										<div class="navbar-brand">
											<a href="index.html" class="d-inline-flex align-items-center">
												<img src="../../../assets/images/logo_icon.svg" alt="">
												<h4 class="text-body lh-1 mb-0 ms-3">Limitless</h4>
											</a>
										</div>

										<div class="d-xl-none">
											<button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-form-toggles-light">
												<i class="ph-list"></i>
											</button>
										</div>

										<div class="navbar-collapse collapse" id="navbar-form-toggles-light">
											<div class="mt-3 mt-xl-0">
												<label class="form-check form-switch">
													<input type="checkbox" class="form-check-input" checked>
													<span class="form-check-label">Checked</span>
												</label>
											</div>

											<div class="mt-3 mb-2 my-xl-0 ms-xl-auto">
												<label class="form-check form-switch form-check-reverse">
													<input type="checkbox" class="form-check-input" checked>
													<span class="form-check-label">Refresh</span>
												</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- /navbar forms -->

				</div>
				<!-- /content area -->


				<!-- Footer -->
				<div class="navbar navbar-sm navbar-footer border-top">
					<div class="container-fluid">
						<span>&copy; 2022 <a href="https://themeforest.net/item/limitless-responsive-web-application-kit/13080328">Limitless Web App Kit</a></span>

						<ul class="nav">
							<li class="nav-item">
								<a href="https://kopyov.ticksy.com/" class="navbar-nav-link navbar-nav-link-icon rounded" target="_blank">
									<div class="d-flex align-items-center mx-md-1">
										<i class="ph-lifebuoy"></i>
										<span class="d-none d-md-inline-block ms-2">Support</span>
									</div>
								</a>
							</li>
							<li class="nav-item ms-md-1">
								<a href="https://demo.interface.club/limitless/demo/Documentation/index.html" class="navbar-nav-link navbar-nav-link-icon rounded" target="_blank">
									<div class="d-flex align-items-center mx-md-1">
										<i class="ph-file-text"></i>
										<span class="d-none d-md-inline-block ms-2">Docs</span>
									</div>
								</a>
							</li>
							<li class="nav-item ms-md-1">
								<a href="https://themeforest.net/item/limitless-responsive-web-application-kit/13080328?ref=kopyov" class="navbar-nav-link navbar-nav-link-icon text-primary bg-primary bg-opacity-10 fw-semibold rounded" target="_blank">
									<div class="d-flex align-items-center mx-md-1">
										<i class="ph-shopping-cart"></i>
										<span class="d-none d-md-inline-block ms-2">Purchase</span>
									</div>
								</a>
							</li>
						</ul>
					</div>
				</div>
				<!-- /footer -->

			</div>
			<!-- /inner content -->

		</div>
		<!-- /main content -->

	</div>
	<!-- /page content -->


	<!-- Notifications -->
	<div class="offcanvas offcanvas-end" tabindex="-1" id="notifications">
		<div class="offcanvas-header py-0">
			<h5 class="offcanvas-title py-3">Activity</h5>
			<button type="button" class="btn btn-light btn-sm btn-icon border-transparent rounded-pill" data-bs-dismiss="offcanvas">
				<i class="ph-x"></i>
			</button>
		</div>

		<div class="offcanvas-body p-0">
			<div class="bg-light fw-medium py-2 px-3">New notifications</div>
			<div class="p-3">
				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face1.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-success"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">James</a> has completed the task <a href="#">Submit documents</a> from <a href="#">Onboarding</a> list

						<div class="bg-light rounded p-2 my-2">
							<label class="form-check ms-1">
								<input type="checkbox" class="form-check-input" checked disabled>
								<del class="form-check-label">Submit personal documents</del>
							</label>
						</div>

						<div class="fs-sm text-muted mt-1">2 hours ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face3.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-warning"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Margo</a> has added 4 users to <span class="fw-semibold">Customer enablement</span> channel

						<div class="d-flex my-2">
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face10.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-danger"></span>
							</a>
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face11.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-success"></span>
							</a>
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face12.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-success"></span>
							</a>
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face13.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-success"></span>
							</a>
							<button type="button" class="btn btn-light btn-icon d-inline-flex align-items-center justify-content-center w-32px h-32px rounded-pill p-0">
								<i class="ph-plus ph-sm"></i>
							</button>
						</div>

						<div class="fs-sm text-muted mt-1">3 hours ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start">
					<div class="me-3">
						<div class="bg-warning bg-opacity-10 text-warning rounded-pill">
							<i class="ph-warning p-2"></i>
						</div>
					</div>
					<div class="flex-1">
						Subscription <a href="#">#466573</a> from 10.12.2021 has been cancelled. Refund case <a href="#">#4492</a> created
						<div class="fs-sm text-muted mt-1">4 hours ago</div>
					</div>
				</div>
			</div>

			<div class="bg-light fw-medium py-2 px-3">Older notifications</div>
			<div class="p-3">
				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face25.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-success"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Nick</a> requested your feedback and approval in support request <a href="#">#458</a>

						<div class="my-2">
							<a href="#" class="btn btn-success btn-sm me-1">
								<i class="ph-checks ph-sm me-1"></i>
								Approve
							</a>
							<a href="#" class="btn btn-light btn-sm">
								Review
							</a>
						</div>

						<div class="fs-sm text-muted mt-1">3 days ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face24.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-grey"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Mike</a> added 1 new file(s) to <a href="#">Product management</a> project

						<div class="bg-light rounded p-2 my-2">
							<div class="d-flex align-items-center">
								<div class="me-2">
									<img src="../../../assets/images/icons/pdf.svg" width="34" height="34" alt="">
								</div>
								<div class="flex-fill">
									new_contract.pdf
									<div class="fs-sm text-muted">112KB</div>
								</div>
								<div class="ms-2">
									<button type="button" class="btn btn-flat-dark text-body btn-icon btn-sm border-transparent rounded-pill">
										<i class="ph-arrow-down"></i>
									</button>
								</div>
							</div>
						</div>

						<div class="fs-sm text-muted mt-1">1 day ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<div class="me-3">
						<div class="bg-success bg-opacity-10 text-success rounded-pill">
							<i class="ph-calendar-plus p-2"></i>
						</div>
					</div>
					<div class="flex-fill">
						All hands meeting will take place coming Thursday at 13:45.

						<div class="my-2">
							<a href="#" class="btn btn-primary btn-sm">
								<i class="ph-calendar-plus ph-sm me-1"></i>
								Add to calendar
							</a>
						</div>

						<div class="fs-sm text-muted mt-1">2 days ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face4.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-danger"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Christine</a> commented on your community <a href="#">post</a> from 10.12.2021

						<div class="fs-sm text-muted mt-1">2 days ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<div class="me-3">
						<div class="bg-primary bg-opacity-10 text-primary rounded-pill">
							<i class="ph-users-four p-2"></i>
						</div>
					</div>
					<div class="flex-fill">
						<span class="fw-semibold">HR department</span> requested you to complete internal survey by Friday

						<div class="fs-sm text-muted mt-1">3 days ago</div>
					</div>
				</div>

				<div class="text-center">
					<div class="spinner-border" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- /notifications -->


	<!-- Demo config -->
	<div class="offcanvas offcanvas-end" tabindex="-1" id="demo_config">
		<div class="position-absolute top-50 end-100 visible">
			<button type="button" class="btn btn-primary btn-icon translate-middle-y rounded-end-0" data-bs-toggle="offcanvas" data-bs-target="#demo_config">
				<i class="ph-gear"></i>
			</button>
		</div>

		<div class="offcanvas-header border-bottom py-0">
			<h5 class="offcanvas-title py-3">Demo configuration</h5>
			<button type="button" class="btn btn-light btn-sm btn-icon border-transparent rounded-pill" data-bs-dismiss="offcanvas">
				<i class="ph-x"></i>
			</button>
		</div>

		<div class="offcanvas-body">
			<div class="fw-semibold mb-2">Color mode</div>
			<div class="list-group mb-3">
				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-2">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-sun ph-lg me-3"></i>
							<div>
								<span class="fw-bold">Light theme</span>
								<div class="fs-sm text-muted">Set light theme or reset to default</div>
							</div>
						</div>
						<input type="radio" class="form-check-input cursor-pointer ms-auto" name="main-theme" value="light" checked>
					</div>
				</label>

				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-2">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-moon ph-lg me-3"></i>
							<div>
								<span class="fw-bold">Dark theme</span>
								<div class="fs-sm text-muted">Switch to dark theme</div>
							</div>
						</div>
						<input type="radio" class="form-check-input cursor-pointer ms-auto" name="main-theme" value="dark">
					</div>
				</label>

				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-0">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-translate ph-lg me-3"></i>
							<div>
								<span class="fw-bold">Auto theme</span>
								<div class="fs-sm text-muted">Set theme based on system mode</div>
							</div>
						</div>
						<input type="radio" class="form-check-input cursor-pointer ms-auto" name="main-theme" value="auto">
					</div>
				</label>
			</div>

			<div class="fw-semibold mb-2">Direction</div>
			<div class="list-group mb-3">
				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-0">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-translate ph-lg me-3"></i>
							<div>
								<span class="fw-bold">RTL direction</span>
								<div class="text-muted">Toggle between LTR and RTL</div>
							</div>
						</div>
						<input type="checkbox" name="layout-direction" value="rtl" class="form-check-input cursor-pointer m-0 ms-auto">
					</div>
				</label>
			</div>

			<div class="fw-semibold mb-2">Layouts</div>
			<div class="row">
				<div class="col-12">
					<a href="../../layout_1/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_1.png" class="img-fluid img-thumbnail" alt="">
					</a>				</div>
				<div class="col-12">
					<a href="../../layout_2/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_2.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="../../layout_3/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_3.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="../../layout_4/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_4.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="../../layout_5/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_5.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="index.html" class="d-block">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_6.png" class="img-fluid img-thumbnail bg-primary bg-opacity-20 border-primary" alt="">
					</a>
				</div>
			</div>
		</div>

		<div class="border-top text-center py-2 px-3">
			<a href="https://themeforest.net/item/limitless-responsive-web-application-kit/13080328?ref=kopyov" class="btn btn-yellow fw-semibold w-100 my-1" target="_blank">
				<i class="ph-shopping-cart me-2"></i>
				Purchase Limitless
			</a>
		</div>
	</div>
	<!-- /demo config -->

</body>
</html>
