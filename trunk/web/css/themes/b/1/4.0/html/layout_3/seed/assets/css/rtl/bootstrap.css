@charset "UTF-8";
/* ------------------------------------------------------------------------------
 *
 *  # Bootstrap v5.1.0 (https://getbootstrap.com)
 *
 *  Copyright 2011-2018 The Bootstrap Authors
 *  Copyright 2011-2018 Twitter, Inc.
 *  Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 *
 * ---------------------------------------------------------------------------- */
/* ------------------------------------------------------------------------------
 *
 *  # Global configuration
 *
 *  Here you can change main theme, enable or disable certain components and
 *  optional styles. This allows you to include only components that you need.
 *
 *  'true'  - enables component and includes it to main CSS file.
 *  'false' - disables component and excludes it from main CSS file.
 *
 * ---------------------------------------------------------------------------- */
/* ------------------------------------------------------------------------------
 *
 *  # Custom Limitless functions
 *
 *  Utility mixins and functions for evalutating source code across our variables, maps, and mixins.
 *
 * ---------------------------------------------------------------------------- */
/* ------------------------------------------------------------------------------
 *
 *  # Custom template mixins
 *
 *  All custom mixins are prefixed with "ll-" to avoid conflicts
 *
 * ---------------------------------------------------------------------------- */
/* ------------------------------------------------------------------------------
 *
 *  # Additional variables
 *
 *  Mainly 3rd party libraries and additional variables for default
 *  Bootstrap components.
 *
 * ---------------------------------------------------------------------------- */
:root,
[data-color-theme=light] {
  --body-font-size-lg: 1rem;
  --body-font-size-sm: 0.75rem;
  --body-font-size-xs: 0.625rem;
  --body-line-height-computed: calc(1375rem / 1000);
  --body-line-height-lg: 1.375;
  --body-line-height-sm: 1.8334;
  --body-line-height-xs: 2.2;
  --component-active-bg: #0c83ff;
  --component-active-bg-rgb: 12, 131, 255;
  --component-active-color: #fff;
  --focus-ring-box-shadow: 0 0 0 0.125rem rgba(12, 131, 255, 0.25);
  --spacer-1: 0.3125rem;
  --spacer-2: 0.625rem;
  --spacer: 1.25rem;
  --spacer-4: 1.875rem;
  --spacer-5: 3.75rem;
  --icon-font-family: Phosphor;
  --icon-font-size: 1.25rem;
  --icon-font-size-lg: 1.5rem;
  --icon-font-size-sm: 1rem;
  --box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.125);
  --box-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --box-shadow-lg: 0 6px 12px rgba(0, 0, 0, 0.15);
  --transition-base-timer: 0.15s;
  --transition-collapse-timer: 0.3s;
  --gray-100: #F9FAFB;
  --gray-200: #F3F4F6;
  --gray-300: #E5E7EB;
  --gray-400: #D1D5DB;
  --gray-500: #9CA3AF;
  --gray-600: #6B7280;
  --gray-700: #4B5563;
  --gray-800: #374151;
  --gray-900: #1F2937;
  --indigo: #5C6BC0;
  --purple: #8e70c1;
  --pink: #f35c86;
  --teal: #26A69A;
  --yellow: #ffd648;
  --primary: #0c83ff;
  --secondary: #247297;
  --success: #059669;
  --info: #049aad;
  --warning: #f58646;
  --danger: #EF4444;
  --light: #F3F4F6;
  --dark: #252b36;
  --black: #000;
  --white: #fff;
  --indigo-rgb: 92, 107, 192;
  --purple-rgb: 142, 112, 193;
  --pink-rgb: 243, 92, 134;
  --teal-rgb: 38, 166, 154;
  --yellow-rgb: 255, 214, 72;
  --primary-rgb: 12, 131, 255;
  --secondary-rgb: 36, 114, 151;
  --success-rgb: 5, 150, 105;
  --info-rgb: 4, 154, 173;
  --warning-rgb: 245, 134, 70;
  --danger-rgb: 239, 68, 68;
  --light-rgb: 243, 244, 246;
  --dark-rgb: 37, 43, 54;
  --black-rgb: 0, 0, 0;
  --white-rgb: 255, 255, 255;
  --body-color-rgb: 31, 41, 55;
  --body-bg-rgb: 241, 244, 249;
  --font-sans-serif: "Inter", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --body-font-family: var(--font-sans-serif);
  --body-font-size: 0.875rem;
  --body-font-weight: 400;
  --body-line-height: 1.5715;
  --body-color: #1F2937;
  --body-bg: #f1f4f9;
  --border-width: 1px;
  --border-style: solid;
  --border-color: #D1D5DB;
  --border-color-translucent: rgba(0, 0, 0, 0.125);
  --border-radius: 0.375rem;
  --border-radius-sm: 0.25rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;
  --border-radius-2xl: 2rem;
  --border-radius-pill: 50rem;
  --link-color: #0c83ff;
  --link-hover-color: #0962bf;
  --code-color: #f35c86;
  --highlight-bg: rgba(0, 0, 0, 0.15);
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}

body {
  margin: 0;
  font-family: var(--body-font-family);
  font-size: var(--body-font-size);
  font-weight: var(--body-font-weight);
  line-height: var(--body-line-height);
  color: var(--body-color);
  text-align: var(--body-text-align);
  background-color: var(--body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

hr {
  margin: var(--spacer) 0;
  color: inherit;
  border: 0;
  border-top: var(--border-width) solid;
  opacity: 0.25;
}

h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  margin-top: 0;
  margin-bottom: calc(var(--spacer) * 0.75);
  font-weight: 600;
  line-height: 1.5715;
}

h1, .h1 {
  font-size: calc(1.2875rem + 0.45vw);
}
@media (min-width: 1200px) {
  h1, .h1 {
    font-size: 1.625rem;
  }
}

h2, .h2 {
  font-size: calc(1.275rem + 0.3vw);
}
@media (min-width: 1200px) {
  h2, .h2 {
    font-size: 1.5rem;
  }
}

h3, .h3 {
  font-size: calc(1.2625rem + 0.15vw);
}
@media (min-width: 1200px) {
  h3, .h3 {
    font-size: 1.375rem;
  }
}

h4, .h4 {
  font-size: 1.25rem;
}

h5, .h5 {
  font-size: 1.125rem;
}

h6, .h6 {
  font-size: 1rem;
}

p {
  margin-top: 0;
  margin-bottom: var(--spacer-2);
}

abbr[title] {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  padding-right: 2rem;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-right: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: 800;
}

small, .small {
  font-size: 0.875em;
}

mark, .mark {
  background-color: var(--highlight-bg);
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: var(--link-color);
  text-decoration: none;
}
a:hover {
  color: var(--link-hover-color);
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: var(--font-monospace);
  font-size: 1em;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.875em;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

code {
  font-size: 0.875em;
  color: var(--code-color);
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 0.875em;
  color: var(--white);
  background-color: var(--black);
  border-radius: 0.25rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  caption-side: bottom;
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: rgba(var(--body-color-rgb), 0.75);
  text-align: right;
}

th {
  font-weight: 600;
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {
  display: none !important;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  float: right;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: right;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: textfield;
}

[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

::file-selector-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}

[hidden] {
  display: none !important;
}

.lead {
  font-size: calc(var(--body-font-size) * 1.25);
  font-weight: 300;
}

.display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 300;
  line-height: 1.5715;
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 5rem;
  }
}

.display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.5715;
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 4.5rem;
  }
}

.display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 300;
  line-height: 1.5715;
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 4rem;
  }
}

.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.5715;
}
@media (min-width: 1200px) {
  .display-4 {
    font-size: 3.5rem;
  }
}

.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.5715;
}
@media (min-width: 1200px) {
  .display-5 {
    font-size: 3rem;
  }
}

.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.5715;
}
@media (min-width: 1200px) {
  .display-6 {
    font-size: 2.5rem;
  }
}

.list-unstyled {
  padding-right: 0;
  list-style: none;
}

.list-inline {
  --list-inline-padding: 1rem;
  padding-right: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-left: var(--list-inline-padding);
}

.initialism {
  font-size: 0.875em;
  text-transform: uppercase;
}

.blockquote {
  --blockquote-margin-y: var(--spacer);
  --blockquote-font-size: 1rem;
  --blockquote-footer-font-size: var(--body-font-size);
  --blockquote-footer-color: var(--gray-500);
  margin-bottom: var(--blockquote-margin-y);
  font-size: var(--blockquote-font-size);
}
.blockquote > :last-child {
  margin-bottom: 0;
}

.blockquote-footer {
  margin-top: calc(var(--blockquote-margin-y) * -1);
  margin-bottom: var(--blockquote-margin-y);
  font-size: var(--blockquote-footer-font-size);
  color: var(--blockquote-footer-color);
}
.blockquote-footer::before {
  content: "— ";
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  --thumbnail-padding: 0.25rem;
  --thumbnail-bg: var(--body-bg);
  --thumbnail-border-width: var(--border-width);
  --thumbnail-border-color: var(--gray-300);
  --thumbnail-border-radius: var(--border-radius);
  --thumbnail-box-shadow: var(--box-shadow-sm);
  padding: var(--thumbnail-padding);
  background-color: var(--thumbnail-bg);
  border: var(--thumbnail-border-width) solid var(--thumbnail-border-color);
  border-radius: var(--thumbnail-border-radius);
  box-shadow: var(--thumbnail-box-shadow);
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.625rem;
  line-height: 1;
}

.figure-caption {
  font-size: 0.875em;
  color: var(--gray-600);
}

.container,
.container-fluid,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm {
  --gutter-x: 1.25rem;
  --gutter-y: 0;
  width: 100%;
  padding-left: calc(var(--gutter-x) * 0.5);
  padding-right: calc(var(--gutter-x) * 0.5);
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 576px) {
  .container-sm, .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container-md, .container-sm, .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1140px;
  }
}
@media (min-width: 1400px) {
  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1320px;
  }
}
.row {
  --gutter-x: 1.25rem;
  --gutter-y: 0;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: calc(-1 * var(--gutter-y));
  margin-left: calc(-0.5 * var(--gutter-x));
  margin-right: calc(-0.5 * var(--gutter-x));
}
.row > * {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-left: calc(var(--gutter-x) * 0.5);
  padding-right: calc(var(--gutter-x) * 0.5);
  margin-top: var(--gutter-y);
}

.col {
  -ms-flex: 1 0 0%;
      flex: 1 0 0%;
}

.row-cols-auto > * {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: auto;
}

.row-cols-1 > * {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 100%;
}

.row-cols-2 > * {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 50%;
}

.row-cols-3 > * {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 33.3333333333%;
}

.row-cols-4 > * {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 25%;
}

.row-cols-5 > * {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 20%;
}

.row-cols-6 > * {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 16.6666666667%;
}

.col-auto {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: auto;
}

.col-1 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 8.33333333%;
}

.col-2 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 16.66666667%;
}

.col-3 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 25%;
}

.col-4 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 33.33333333%;
}

.col-5 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 41.66666667%;
}

.col-6 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 50%;
}

.col-7 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 58.33333333%;
}

.col-8 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 66.66666667%;
}

.col-9 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 75%;
}

.col-10 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 83.33333333%;
}

.col-11 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 91.66666667%;
}

.col-12 {
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 100%;
}

.offset-1 {
  margin-right: 8.33333333%;
}

.offset-2 {
  margin-right: 16.66666667%;
}

.offset-3 {
  margin-right: 25%;
}

.offset-4 {
  margin-right: 33.33333333%;
}

.offset-5 {
  margin-right: 41.66666667%;
}

.offset-6 {
  margin-right: 50%;
}

.offset-7 {
  margin-right: 58.33333333%;
}

.offset-8 {
  margin-right: 66.66666667%;
}

.offset-9 {
  margin-right: 75%;
}

.offset-10 {
  margin-right: 83.33333333%;
}

.offset-11 {
  margin-right: 91.66666667%;
}

.g-0,
.gx-0 {
  --gutter-x: 0;
}

.g-0,
.gy-0 {
  --gutter-y: 0;
}

.g-1,
.gx-1 {
  --gutter-x: 0.3125rem;
}

.g-1,
.gy-1 {
  --gutter-y: 0.3125rem;
}

.g-2,
.gx-2 {
  --gutter-x: 0.625rem;
}

.g-2,
.gy-2 {
  --gutter-y: 0.625rem;
}

.g-3,
.gx-3 {
  --gutter-x: 1.25rem;
}

.g-3,
.gy-3 {
  --gutter-y: 1.25rem;
}

.g-4,
.gx-4 {
  --gutter-x: 1.875rem;
}

.g-4,
.gy-4 {
  --gutter-y: 1.875rem;
}

.g-5,
.gx-5 {
  --gutter-x: 3.75rem;
}

.g-5,
.gy-5 {
  --gutter-y: 3.75rem;
}

@media (min-width: 576px) {
  .col-sm {
    -ms-flex: 1 0 0%;
        flex: 1 0 0%;
  }
  .row-cols-sm-auto > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: auto;
  }
  .row-cols-sm-1 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-sm-2 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-sm-3 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-sm-4 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-sm-5 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-sm-6 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-sm-auto {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: auto;
  }
  .col-sm-1 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-sm-2 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-sm-3 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 25%;
  }
  .col-sm-4 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-sm-5 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-sm-6 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 50%;
  }
  .col-sm-7 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-sm-8 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-sm-9 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 75%;
  }
  .col-sm-10 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-sm-11 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-sm-12 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 100%;
  }
  .offset-sm-0 {
    margin-right: 0;
  }
  .offset-sm-1 {
    margin-right: 8.33333333%;
  }
  .offset-sm-2 {
    margin-right: 16.66666667%;
  }
  .offset-sm-3 {
    margin-right: 25%;
  }
  .offset-sm-4 {
    margin-right: 33.33333333%;
  }
  .offset-sm-5 {
    margin-right: 41.66666667%;
  }
  .offset-sm-6 {
    margin-right: 50%;
  }
  .offset-sm-7 {
    margin-right: 58.33333333%;
  }
  .offset-sm-8 {
    margin-right: 66.66666667%;
  }
  .offset-sm-9 {
    margin-right: 75%;
  }
  .offset-sm-10 {
    margin-right: 83.33333333%;
  }
  .offset-sm-11 {
    margin-right: 91.66666667%;
  }
  .g-sm-0,
.gx-sm-0 {
    --gutter-x: 0;
  }
  .g-sm-0,
.gy-sm-0 {
    --gutter-y: 0;
  }
  .g-sm-1,
.gx-sm-1 {
    --gutter-x: 0.3125rem;
  }
  .g-sm-1,
.gy-sm-1 {
    --gutter-y: 0.3125rem;
  }
  .g-sm-2,
.gx-sm-2 {
    --gutter-x: 0.625rem;
  }
  .g-sm-2,
.gy-sm-2 {
    --gutter-y: 0.625rem;
  }
  .g-sm-3,
.gx-sm-3 {
    --gutter-x: 1.25rem;
  }
  .g-sm-3,
.gy-sm-3 {
    --gutter-y: 1.25rem;
  }
  .g-sm-4,
.gx-sm-4 {
    --gutter-x: 1.875rem;
  }
  .g-sm-4,
.gy-sm-4 {
    --gutter-y: 1.875rem;
  }
  .g-sm-5,
.gx-sm-5 {
    --gutter-x: 3.75rem;
  }
  .g-sm-5,
.gy-sm-5 {
    --gutter-y: 3.75rem;
  }
}
@media (min-width: 768px) {
  .col-md {
    -ms-flex: 1 0 0%;
        flex: 1 0 0%;
  }
  .row-cols-md-auto > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: auto;
  }
  .row-cols-md-1 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-md-2 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-md-3 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-md-4 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-md-5 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-md-6 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-md-auto {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: auto;
  }
  .col-md-1 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-md-2 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-md-3 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 25%;
  }
  .col-md-4 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-md-5 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-md-6 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 50%;
  }
  .col-md-7 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-md-8 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-md-9 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 75%;
  }
  .col-md-10 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-md-11 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-md-12 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 100%;
  }
  .offset-md-0 {
    margin-right: 0;
  }
  .offset-md-1 {
    margin-right: 8.33333333%;
  }
  .offset-md-2 {
    margin-right: 16.66666667%;
  }
  .offset-md-3 {
    margin-right: 25%;
  }
  .offset-md-4 {
    margin-right: 33.33333333%;
  }
  .offset-md-5 {
    margin-right: 41.66666667%;
  }
  .offset-md-6 {
    margin-right: 50%;
  }
  .offset-md-7 {
    margin-right: 58.33333333%;
  }
  .offset-md-8 {
    margin-right: 66.66666667%;
  }
  .offset-md-9 {
    margin-right: 75%;
  }
  .offset-md-10 {
    margin-right: 83.33333333%;
  }
  .offset-md-11 {
    margin-right: 91.66666667%;
  }
  .g-md-0,
.gx-md-0 {
    --gutter-x: 0;
  }
  .g-md-0,
.gy-md-0 {
    --gutter-y: 0;
  }
  .g-md-1,
.gx-md-1 {
    --gutter-x: 0.3125rem;
  }
  .g-md-1,
.gy-md-1 {
    --gutter-y: 0.3125rem;
  }
  .g-md-2,
.gx-md-2 {
    --gutter-x: 0.625rem;
  }
  .g-md-2,
.gy-md-2 {
    --gutter-y: 0.625rem;
  }
  .g-md-3,
.gx-md-3 {
    --gutter-x: 1.25rem;
  }
  .g-md-3,
.gy-md-3 {
    --gutter-y: 1.25rem;
  }
  .g-md-4,
.gx-md-4 {
    --gutter-x: 1.875rem;
  }
  .g-md-4,
.gy-md-4 {
    --gutter-y: 1.875rem;
  }
  .g-md-5,
.gx-md-5 {
    --gutter-x: 3.75rem;
  }
  .g-md-5,
.gy-md-5 {
    --gutter-y: 3.75rem;
  }
}
@media (min-width: 992px) {
  .col-lg {
    -ms-flex: 1 0 0%;
        flex: 1 0 0%;
  }
  .row-cols-lg-auto > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: auto;
  }
  .row-cols-lg-1 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-lg-2 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-lg-3 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-lg-4 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-lg-5 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-lg-6 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-lg-auto {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: auto;
  }
  .col-lg-1 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-lg-2 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-lg-3 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 25%;
  }
  .col-lg-4 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-lg-5 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-lg-6 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 50%;
  }
  .col-lg-7 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-lg-8 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-lg-9 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 75%;
  }
  .col-lg-10 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-lg-11 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-lg-12 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 100%;
  }
  .offset-lg-0 {
    margin-right: 0;
  }
  .offset-lg-1 {
    margin-right: 8.33333333%;
  }
  .offset-lg-2 {
    margin-right: 16.66666667%;
  }
  .offset-lg-3 {
    margin-right: 25%;
  }
  .offset-lg-4 {
    margin-right: 33.33333333%;
  }
  .offset-lg-5 {
    margin-right: 41.66666667%;
  }
  .offset-lg-6 {
    margin-right: 50%;
  }
  .offset-lg-7 {
    margin-right: 58.33333333%;
  }
  .offset-lg-8 {
    margin-right: 66.66666667%;
  }
  .offset-lg-9 {
    margin-right: 75%;
  }
  .offset-lg-10 {
    margin-right: 83.33333333%;
  }
  .offset-lg-11 {
    margin-right: 91.66666667%;
  }
  .g-lg-0,
.gx-lg-0 {
    --gutter-x: 0;
  }
  .g-lg-0,
.gy-lg-0 {
    --gutter-y: 0;
  }
  .g-lg-1,
.gx-lg-1 {
    --gutter-x: 0.3125rem;
  }
  .g-lg-1,
.gy-lg-1 {
    --gutter-y: 0.3125rem;
  }
  .g-lg-2,
.gx-lg-2 {
    --gutter-x: 0.625rem;
  }
  .g-lg-2,
.gy-lg-2 {
    --gutter-y: 0.625rem;
  }
  .g-lg-3,
.gx-lg-3 {
    --gutter-x: 1.25rem;
  }
  .g-lg-3,
.gy-lg-3 {
    --gutter-y: 1.25rem;
  }
  .g-lg-4,
.gx-lg-4 {
    --gutter-x: 1.875rem;
  }
  .g-lg-4,
.gy-lg-4 {
    --gutter-y: 1.875rem;
  }
  .g-lg-5,
.gx-lg-5 {
    --gutter-x: 3.75rem;
  }
  .g-lg-5,
.gy-lg-5 {
    --gutter-y: 3.75rem;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    -ms-flex: 1 0 0%;
        flex: 1 0 0%;
  }
  .row-cols-xl-auto > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xl-1 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xl-2 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xl-3 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xl-4 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xl-5 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xl-6 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-xl-auto {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: auto;
  }
  .col-xl-1 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xl-2 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xl-3 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 25%;
  }
  .col-xl-4 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xl-5 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xl-6 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 50%;
  }
  .col-xl-7 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xl-8 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xl-9 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 75%;
  }
  .col-xl-10 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xl-11 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xl-12 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 100%;
  }
  .offset-xl-0 {
    margin-right: 0;
  }
  .offset-xl-1 {
    margin-right: 8.33333333%;
  }
  .offset-xl-2 {
    margin-right: 16.66666667%;
  }
  .offset-xl-3 {
    margin-right: 25%;
  }
  .offset-xl-4 {
    margin-right: 33.33333333%;
  }
  .offset-xl-5 {
    margin-right: 41.66666667%;
  }
  .offset-xl-6 {
    margin-right: 50%;
  }
  .offset-xl-7 {
    margin-right: 58.33333333%;
  }
  .offset-xl-8 {
    margin-right: 66.66666667%;
  }
  .offset-xl-9 {
    margin-right: 75%;
  }
  .offset-xl-10 {
    margin-right: 83.33333333%;
  }
  .offset-xl-11 {
    margin-right: 91.66666667%;
  }
  .g-xl-0,
.gx-xl-0 {
    --gutter-x: 0;
  }
  .g-xl-0,
.gy-xl-0 {
    --gutter-y: 0;
  }
  .g-xl-1,
.gx-xl-1 {
    --gutter-x: 0.3125rem;
  }
  .g-xl-1,
.gy-xl-1 {
    --gutter-y: 0.3125rem;
  }
  .g-xl-2,
.gx-xl-2 {
    --gutter-x: 0.625rem;
  }
  .g-xl-2,
.gy-xl-2 {
    --gutter-y: 0.625rem;
  }
  .g-xl-3,
.gx-xl-3 {
    --gutter-x: 1.25rem;
  }
  .g-xl-3,
.gy-xl-3 {
    --gutter-y: 1.25rem;
  }
  .g-xl-4,
.gx-xl-4 {
    --gutter-x: 1.875rem;
  }
  .g-xl-4,
.gy-xl-4 {
    --gutter-y: 1.875rem;
  }
  .g-xl-5,
.gx-xl-5 {
    --gutter-x: 3.75rem;
  }
  .g-xl-5,
.gy-xl-5 {
    --gutter-y: 3.75rem;
  }
}
@media (min-width: 1400px) {
  .col-xxl {
    -ms-flex: 1 0 0%;
        flex: 1 0 0%;
  }
  .row-cols-xxl-auto > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xxl-1 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xxl-2 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xxl-3 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xxl-4 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xxl-5 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xxl-6 > * {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-xxl-auto {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: auto;
  }
  .col-xxl-1 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xxl-2 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xxl-3 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 25%;
  }
  .col-xxl-4 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xxl-5 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xxl-6 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 50%;
  }
  .col-xxl-7 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xxl-8 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xxl-9 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 75%;
  }
  .col-xxl-10 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xxl-11 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xxl-12 {
    -ms-flex: 0 0 auto;
        flex: 0 0 auto;
    width: 100%;
  }
  .offset-xxl-0 {
    margin-right: 0;
  }
  .offset-xxl-1 {
    margin-right: 8.33333333%;
  }
  .offset-xxl-2 {
    margin-right: 16.66666667%;
  }
  .offset-xxl-3 {
    margin-right: 25%;
  }
  .offset-xxl-4 {
    margin-right: 33.33333333%;
  }
  .offset-xxl-5 {
    margin-right: 41.66666667%;
  }
  .offset-xxl-6 {
    margin-right: 50%;
  }
  .offset-xxl-7 {
    margin-right: 58.33333333%;
  }
  .offset-xxl-8 {
    margin-right: 66.66666667%;
  }
  .offset-xxl-9 {
    margin-right: 75%;
  }
  .offset-xxl-10 {
    margin-right: 83.33333333%;
  }
  .offset-xxl-11 {
    margin-right: 91.66666667%;
  }
  .g-xxl-0,
.gx-xxl-0 {
    --gutter-x: 0;
  }
  .g-xxl-0,
.gy-xxl-0 {
    --gutter-y: 0;
  }
  .g-xxl-1,
.gx-xxl-1 {
    --gutter-x: 0.3125rem;
  }
  .g-xxl-1,
.gy-xxl-1 {
    --gutter-y: 0.3125rem;
  }
  .g-xxl-2,
.gx-xxl-2 {
    --gutter-x: 0.625rem;
  }
  .g-xxl-2,
.gy-xxl-2 {
    --gutter-y: 0.625rem;
  }
  .g-xxl-3,
.gx-xxl-3 {
    --gutter-x: 1.25rem;
  }
  .g-xxl-3,
.gy-xxl-3 {
    --gutter-y: 1.25rem;
  }
  .g-xxl-4,
.gx-xxl-4 {
    --gutter-x: 1.875rem;
  }
  .g-xxl-4,
.gy-xxl-4 {
    --gutter-y: 1.875rem;
  }
  .g-xxl-5,
.gx-xxl-5 {
    --gutter-x: 3.75rem;
  }
  .g-xxl-5,
.gy-xxl-5 {
    --gutter-y: 3.75rem;
  }
}
.table {
  --table-cell-padding-y: 0.75rem;
  --table-cell-padding-x: 1.25rem;
  --table-bg: transparent;
  --table-color: var(--body-color);
  --table-border-width: var(--border-width);
  --table-border-color: var(--border-color);
  --table-accent-bg: transparent;
  --table-striped-color: var(--body-color);
  --table-striped-bg: rgba(var(--black-rgb), 0.035);
  --table-active-color: var(--body-color);
  --table-active-bg: rgba(var(--black-rgb), 0.1);
  --table-hover-color: var(--body-color);
  --table-hover-bg: rgba(var(--black-rgb), 0.05);
  --table-group-separator-color: var(--gray-500);
  width: 100%;
  margin-bottom: 1.25rem;
  color: var(--table-color);
  vertical-align: middle;
  border-color: var(--table-border-color);
}
.table > :not(caption) > * > * {
  padding: var(--table-cell-padding-y) var(--table-cell-padding-x);
  background-color: var(--table-bg);
  border-bottom-width: var(--table-border-width);
  box-shadow: inset 0 0 0 9999px var(--table-accent-bg);
}
.table > tbody {
  vertical-align: inherit;
}
.table > thead {
  vertical-align: bottom;
}

.table-group-divider {
  border-top: calc(var(--table-border-width) * 2) solid var(--table-group-separator-color);
}

.caption-top {
  caption-side: top;
}

.table-sm {
  --table-cell-padding-y: 0.625rem;
  --table-cell-padding-x: 1.25rem;
}

.table-bordered > :not(caption) > * {
  border-width: var(--table-border-width) 0;
}
.table-bordered > :not(caption) > * > * {
  border-width: 0 var(--table-border-width);
}

.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0;
}
.table-borderless > :not(:first-child) {
  border-top-width: 0;
}

.table-striped > tbody > tr:nth-of-type(odd) > * {
  --table-accent-bg: var(--table-striped-bg);
  color: var(--table-striped-color);
}

.table-striped-columns > :not(caption) > tr > :nth-child(even) {
  --table-accent-bg: var(--table-striped-bg);
  color: var(--table-striped-color);
}

.table-active {
  --table-accent-bg: var(--table-active-bg);
  color: var(--table-active-color);
}

.table-hover > tbody > tr:hover > * {
  --table-accent-bg: var(--table-hover-bg);
  color: var(--table-hover-color);
}

.table-primary {
  --table-color: #000;
  --table-bg: #e7f3ff;
  --table-border-color: #d0dbe6;
  --table-striped-bg: #dfeaf6;
  --table-striped-color: #000;
  --table-active-bg: #d0dbe6;
  --table-active-color: #000;
  --table-hover-bg: #dbe7f2;
  --table-hover-color: #000;
  color: var(--table-color);
  border-color: var(--table-border-color);
}

.table-secondary {
  --table-color: #000;
  --table-bg: #e9f1f5;
  --table-border-color: #d2d9dd;
  --table-striped-bg: #e1e9ec;
  --table-striped-color: #000;
  --table-active-bg: #d2d9dd;
  --table-active-color: #000;
  --table-hover-bg: #dde5e9;
  --table-hover-color: #000;
  color: var(--table-color);
  border-color: var(--table-border-color);
}

.table-success {
  --table-color: #000;
  --table-bg: #e6f5f0;
  --table-border-color: #cfddd8;
  --table-striped-bg: #deece8;
  --table-striped-color: #000;
  --table-active-bg: #cfddd8;
  --table-active-color: #000;
  --table-hover-bg: #dbe9e4;
  --table-hover-color: #000;
  color: var(--table-color);
  border-color: var(--table-border-color);
}

.table-info {
  --table-color: #000;
  --table-bg: #e6f5f7;
  --table-border-color: #cfddde;
  --table-striped-bg: #deecee;
  --table-striped-color: #000;
  --table-active-bg: #cfddde;
  --table-active-color: #000;
  --table-hover-bg: #dbe9eb;
  --table-hover-color: #000;
  color: var(--table-color);
  border-color: var(--table-border-color);
}

.table-warning {
  --table-color: #000;
  --table-bg: #fef3ed;
  --table-border-color: #e5dbd5;
  --table-striped-bg: #f5eae5;
  --table-striped-color: #000;
  --table-active-bg: #e5dbd5;
  --table-active-color: #000;
  --table-hover-bg: #f1e7e1;
  --table-hover-color: #000;
  color: var(--table-color);
  border-color: var(--table-border-color);
}

.table-danger {
  --table-color: #000;
  --table-bg: #fdecec;
  --table-border-color: #e4d4d4;
  --table-striped-bg: #f4e4e4;
  --table-striped-color: #000;
  --table-active-bg: #e4d4d4;
  --table-active-color: #000;
  --table-hover-bg: #f0e0e0;
  --table-hover-color: #000;
  color: var(--table-color);
  border-color: var(--table-border-color);
}

.table-light {
  --table-color: #000;
  --table-bg: #F3F4F6;
  --table-border-color: #dbdcdd;
  --table-striped-bg: #eaebed;
  --table-striped-color: #000;
  --table-active-bg: #dbdcdd;
  --table-active-color: #000;
  --table-hover-bg: #e7e8ea;
  --table-hover-color: #000;
  color: var(--table-color);
  border-color: var(--table-border-color);
}

.table-dark {
  --table-color: #fff;
  --table-bg: #252b36;
  --table-border-color: #3b404a;
  --table-striped-bg: #2d323d;
  --table-striped-color: #fff;
  --table-active-bg: #3b404a;
  --table-active-color: #fff;
  --table-hover-bg: #303640;
  --table-hover-color: #fff;
  color: var(--table-color);
  border-color: var(--table-border-color);
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 575.98px) {
  .table-responsive-sm {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-xl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1399.98px) {
  .table-responsive-xxl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
.form-label {
  --form-label-margin-bottom: 0.5rem;
  margin-bottom: var(--form-label-margin-bottom);
}

.col-form-label {
  --form-label-padding-y: 0.5rem;
  --form-label-font-size: inherit;
  padding-top: calc(var(--form-label-padding-y) + var(--border-width));
  padding-bottom: calc(var(--form-label-padding-y) + var(--border-width));
  margin-bottom: 0;
  font-size: var(--form-label-font-size);
  line-height: var(--body-line-height);
}

.col-form-label-lg {
  --form-label-padding-y: 0.625rem;
  --form-label-font-size: var(--body-font-size-lg);
}

.col-form-label-sm {
  --form-label-padding-y: 0.375rem;
  --form-label-font-size: var(--body-font-size-sm);
}

.form-text {
  --form-text-color: rgba(var(--body-color-rgb), 0.75);
  --form-text-margin-top: 0.25rem;
  --form-text-font-size: var(--body-font-size-sm);
  --form-text-font-style: ;
  --form-text-font-weight: ;
  margin-top: var(--form-text-margin-top);
  font-size: var(--form-text-font-size);
  font-style: var(--form-text-font-style);
  font-weight: var(--form-text-font-weight);
  color: var(--form-text-color);
}

.form-control {
  --input-padding-y: 0.5rem;
  --input-padding-x: 0.875rem;
  --input-height: calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2) + calc(var(--border-width) * 2));
  --input-bg: var(--white);
  --input-color: var(--body-color);
  --input-plaintext-color: var(--body-color);
  --input-placeholder-color: var(--gray-600);
  --input-font-weight: 400;
  --input-font-size: var(--body-font-size);
  --input-line-height: var(--body-line-height);
  --input-border-width: var(--border-width);
  --input-border-color: var(--gray-400);
  --input-border-radius: var(--border-radius);
  --input-box-shadow: 0 0 0 0 transparent;
  --input-focus-bg: var(--white);
  --input-focus-border-color: var(--component-active-bg);
  --input-focus-box-shadow: var(--focus-ring-box-shadow);
  --input-disabled-bg: var(--gray-100);
  --input-disabled-border-color: var(--gray-400);
  display: block;
  width: 100%;
  padding: var(--input-padding-y) var(--input-padding-x);
  font-size: var(--input-font-size);
  font-weight: var(--input-font-weight);
  line-height: var(--input-line-height);
  color: var(--input-color);
  background-color: var(--input-bg);
  background-clip: padding-box;
  border: var(--input-border-width) solid var(--input-border-color);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: var(--input-border-radius);
  box-shadow: var(--input-box-shadow);
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control[type=file] {
  --form-file-button-color: var(--body-color);
  --form-file-button-bg: var(--gray-200);
  --form-file-button-hover-bg: var(--gray-300);
  overflow: hidden;
}
.form-control[type=file]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  background-color: var(--input-focus-bg);
  border-color: var(--input-focus-border-color);
  outline: 0;
  box-shadow: 0 0 0 0 transparent, var(--input-focus-box-shadow);
}
.form-control::-webkit-date-and-time-value {
  height: var(--body-line-height-computed);
}
.form-control::-webkit-input-placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}
.form-control::-moz-placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}
.form-control::-ms-input-placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}
.form-control::placeholder {
  color: var(--input-placeholder-color);
  opacity: 1;
}
.form-control:disabled {
  color: var(--input-disabled-color);
  background-color: var(--input-disabled-bg);
  border-color: var(--input-disabled-border-color);
  opacity: 1;
}
.form-control::-webkit-file-upload-button {
  padding: var(--input-padding-y) var(--input-padding-x);
  margin: calc(var(--input-padding-y) * -1) calc(var(--input-padding-x) * -1);
  -webkit-margin-end: var(--input-padding-x);
          margin-inline-end: var(--input-padding-x);
  color: var(--form-file-button-color);
  background-color: var(--form-file-button-bg);
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: var(--input-border-width);
  border-radius: 0;
  -webkit-transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
.form-control::file-selector-button {
  padding: var(--input-padding-y) var(--input-padding-x);
  margin: calc(var(--input-padding-y) * -1) calc(var(--input-padding-x) * -1);
  -webkit-margin-end: var(--input-padding-x);
     -moz-margin-end: var(--input-padding-x);
          margin-inline-end: var(--input-padding-x);
  color: var(--form-file-button-color);
  background-color: var(--form-file-button-bg);
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: var(--input-border-width);
  border-radius: 0;
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::-webkit-file-upload-button {
    -webkit-transition: none;
    transition: none;
  }
  .form-control::file-selector-button {
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {
  background-color: var(--form-file-button-hover-bg);
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: var(--form-file-button-hover-bg);
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: var(--input-padding-y) 0;
  margin-bottom: 0;
  line-height: var(--input-line-height);
  color: var(--input-plaintext-color);
  background-color: transparent;
  border: solid transparent;
  border-width: var(--input-border-width) 0;
}
.form-control-plaintext:focus {
  outline: 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-left: 0;
  padding-right: 0;
}

.form-control-sm {
  --input-height: calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2) + calc(var(--border-width) * 2));
  --input-padding-y: 0.375rem;
  --input-padding-x: 0.75rem;
  --input-font-size: var(--body-font-size-sm);
  --input-line-height: var(--body-line-height-sm);
  --input-border-radius: var(--border-radius-sm);
}

.form-control-lg {
  --input-height: calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2) + calc(var(--border-width) * 2));
  --input-padding-y: 0.625rem;
  --input-padding-x: 1rem;
  --input-font-size: var(--body-font-size-lg);
  --input-line-height: var(--body-line-height-lg);
  --input-border-radius: var(--border-radius-lg);
}

textarea.form-control {
  min-height: var(--input-height);
}
textarea.form-control-sm {
  --input-height: calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2) + calc(var(--border-width) * 2));
}
textarea.form-control-lg {
  --input-height: calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2) + calc(var(--border-width) * 2));
}

.form-control-color {
  width: 3rem;
  height: auto;
  padding: var(--input-padding-y);
}
.form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control-color::-moz-color-swatch {
  height: var(--body-line-height-computed);
  border-radius: var(--input-border-radius);
}
.form-control-color::-webkit-color-swatch {
  height: var(--body-line-height-computed);
  border-radius: var(--input-border-radius);
}

.form-select {
  --form-select-padding-y: 0.5rem;
  --form-select-padding-x: 0.875rem;
  --form-select-bg: var(--white);
  --form-select-bg-position: left 0.875rem center;;
  --form-select-bg-size: 16px 12px;
  --form-select-color: var(--body-color);
  --form-select-font-family: ;
  --form-select-font-size: var(--body-font-size);
  --form-select-line-height: var(--body-line-height);
  --form-select-border-width: var(--border-width);
  --form-select-border-color: var(--gray-400);
  --form-select-border-radius: var(--border-radius);
  --form-select-box-shadow: 0 0 0 0 transparent;
  --form-select-focus-border-color: var(--component-active-bg);
  --form-select-focus-box-shadow: var(--focus-ring-box-shadow);
  --form-select-disabled-color: var(--body-color);
  --form-select-disabled-bg: var(--gray-100);
  --form-select-disabled-border-color: var(--gray-400);
  --form-select-indicator-padding: 2.625rem;
  display: block;
  width: 100%;
  padding: var(--form-select-padding-y) var(--form-select-padding-x) var(--form-select-padding-y) var(--form-select-indicator-padding);
  -moz-padding-start: calc(var(--form-select-padding-x) - 3px);
  font-family: var(--form-select-font-family);
  font-size: var(--form-select-font-size);
  font-weight: 400;
  line-height: var(--form-select-line-height);
  color: var(--form-select-color);
  background-color: var(--form-select-bg);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23374151' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: var(--form-select-bg-position);
  background-size: var(--form-select-bg-size);
  border: var(--form-select-border-width) solid var(--form-select-border-color);
  border-radius: var(--form-select-border-radius);
  box-shadow: var(--form-select-box-shadow);
  transition: border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    transition: none;
  }
}
.form-select:focus {
  border-color: var(--form-select-focus-border-color);
  outline: 0;
  box-shadow: var(--form-select-box-shadow), var(--form-select-focus-box-shadow);
}
.form-select[multiple], .form-select[size]:not([size="1"]) {
  padding-left: var(--form-select-padding-x);
  background-image: none;
}
.form-select:disabled {
  color: var(--form-select-disabled-color);
  background-color: var(--form-select-disabled-bg);
  border-color: var(--form-select-disabled-border-color);
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 var(--form-select-color);
}

.form-select-sm {
  --form-select-padding-y: 0.375rem;
  --form-select-padding-x: 0.75rem;
  --form-select-font-size: var(--body-font-size-sm);
  --form-select-line-height: var(--body-line-height-sm);
  --form-select-border-radius: var(--border-radius-sm);
}

.form-select-lg {
  --form-select-padding-y: 0.625rem;
  --form-select-padding-x: 1rem;
  --form-select-font-size: var(--body-font-size-lg);
  --form-select-line-height: var(--body-line-height-lg);
  --form-select-border-radius: var(--border-radius-lg);
}

.form-check {
  --form-check-min-height: var(--body-line-height-computed);
  --form-check-padding-start: calc(1.25rem + var(--spacer-2));
  --form-check-margin-bottom: 0;
  display: block;
  min-height: var(--form-check-min-height);
  padding-right: var(--form-check-padding-start);
  margin-bottom: var(--form-check-margin-bottom);
}
.form-check .form-check-input {
  float: right;
  margin-right: calc(var(--form-check-padding-start) * -1);
}

.form-check-reverse {
  padding-left: var(--form-check-padding-start);
  padding-right: 0;
  text-align: left;
}
.form-check-reverse .form-check-input {
  float: left;
  margin-left: calc(var(--form-check-padding-start) * -1);
  margin-right: 0;
}

.form-check-input {
  --form-check-input-width: 1.25rem;
  --form-check-input-height: 1.25rem;
  --form-check-input-bg: var(--white);
  --form-check-input-border: calc(var(--border-width) * 2) solid var(--gray-400);
  --form-check-input-border-radius: 0.1875em;
  --form-check-input-focus-border: var(--component-active-bg);
  --form-check-input-focus-box-shadow: var(--focus-ring-box-shadow);
  --form-check-input-checked-bg-color: var(--component-active-bg);
  --form-check-input-checked-border-color: transparent;
  --form-check-input-disabled-opacity: 0.5;
  --form-check-input-indeterminate-bg-color: var(--component-active-bg);
  --form-check-input-indeterminate-border-color: transparent;
  --form-check-checked-bg-color-rgb: var(--component-active-bg-rgb);
  --form-check-checked-border-color: transparent;
  --form-check-radio-border-radius: 100rem;
  width: var(--form-check-input-width);
  height: var(--form-check-input-height);
  margin-top: calc((var(--body-line-height) - var(--form-check-input-height)) * 0.5);
  vertical-align: top;
  background-color: var(--form-check-input-bg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: var(--form-check-input-border);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-print-color-adjust: exact;
     color-adjust: exact;
          print-color-adjust: exact;
  transition: box-shadow var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-check-input {
    transition: none;
  }
}
.form-check-input[type=checkbox] {
  border-radius: var(--form-check-input-border-radius);
}
.form-check-input[type=radio] {
  border-radius: var(--form-check-radio-border-radius);
}
.form-check-input:focus {
  border-color: var(--form-check-input-focus-border);
  outline: 0;
  box-shadow: var(--form-check-input-focus-box-shadow);
}
.form-check-input:checked {
  background-color: var(--form-check-input-checked-bg-color);
  border-color: var(--form-check-input-checked-border-color);
}
.form-check-input:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2.5' d='M5 10l4 4l6-8'/%3e%3c/svg%3e");
}
.form-check-input:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}
.form-check-input[type=checkbox]:indeterminate {
  background-color: var(--form-check-input-indeterminate-bg-color);
  border-color: var(--form-check-input-indeterminate-border-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2.5' d='M5 10h10'/%3e%3c/svg%3e");
}
.form-check-input:disabled {
  pointer-events: none;
  -webkit-filter: none;
          filter: none;
  opacity: var(--form-check-input-disabled-opacity);
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  cursor: default;
  opacity: var(--form-check-label-disabled-opacity);
}

.form-check-label {
  --form-check-label-disabled-opacity: 0.5;
}

.form-switch {
  --form-check-padding-start: calc(calc(1.25rem * 1.75) + var(--spacer-2));
}
.form-switch .form-check-input {
  --form-check-input-width: calc(1.25rem * 1.75);
  --form-check-input-border-radius: calc(1.25rem * 1.75);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.5%29'/%3e%3c/svg%3e");
  background-position: right center;
  transition: box-shadow var(--transition-base-timer) ease-in-out, background-position var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, background-image var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.5%29'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-position: left center;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-inline {
  --form-check-inline-margin-end: 1rem;
  display: inline-block;
  margin-left: var(--form-check-inline-margin-end);
}

.btn-check {
  --form-check-btn-check-disabled-opacity: 0.65;
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.btn-check[disabled] + .btn, .btn-check:disabled + .btn {
  pointer-events: none;
  -webkit-filter: none;
          filter: none;
  opacity: var(--form-check-btn-check-disabled-opacity);
}

.form-range {
  --form-range-thumb-width: 1rem;
  --form-range-thumb-height: 1rem;
  --form-range-thumb-focus-box-shadow-width: 0.125rem;
  --form-range-thumb-focus-box-shadow: 0 0 0 1px var(--body-bg), var(--focus-ring-box-shadow);
  --form-range-thumb-bg: var(--component-active-bg);
  --form-range-thumb-border: 0;
  --form-range-thumb-border-radius: 1rem;
  --form-range-thumb-box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
  --form-range-thumb-active-bg: var(--component-active-bg);
  --form-range-thumb-disabled-bg: var(--gray-500);
  --form-range-track-width: 100%;
  --form-range-track-height: 0.5rem;
  --form-range-track-bg: var(--gray-300);
  --form-range-track-border-radius: 1rem;
  --form-range-track-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  width: 100%;
  height: calc(--form-range-thumb-height + calc(var(--form-range-thumb-focus-box-shadow-width) * 2));
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.form-range:focus {
  outline: 0;
}
.form-range:focus::-webkit-slider-thumb {
  box-shadow: var(--form-range-thumb-focus-box-shadow);
}
.form-range:focus::-moz-range-thumb {
  box-shadow: var(--form-range-thumb-focus-box-shadow);
}
.form-range::-moz-focus-outer {
  border: 0;
}
.form-range::-webkit-slider-thumb {
  width: var(--form-range-thumb-width);
  height: var(--form-range-thumb-height);
  margin-top: calc((var(--form-range-track-height) - var(--form-range-thumb-height)) * 0.5);
  background-color: var(--form-range-thumb-bg);
  border: var(--form-range-thumb-border);
  border-radius: var(--form-range-thumb-border-radius);
  box-shadow: var(--form-range-thumb-box-shadow);
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.form-range::-webkit-slider-thumb:active {
  background-color: var(--form-range-thumb-active-bg);
}
.form-range::-webkit-slider-runnable-track {
  width: var(--form-range-track-width);
  height: var(--form-range-track-height);
  color: transparent;
  cursor: pointer;
  background-color: var(--form-range-track-bg);
  border-color: transparent;
  border-radius: var(--form-range-track-border-radius);
  box-shadow: var(--form-range-track-box-shadow);
}
.form-range::-moz-range-thumb {
  width: var(--form-range-thumb-width);
  height: var(--form-range-thumb-height);
  background-color: var(--form-range-thumb-bg);
  border: var(--form-range-thumb-border);
  border-radius: var(--form-range-thumb-border-radius);
  box-shadow: var(--form-range-thumb-box-shadow);
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
       appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.form-range::-moz-range-thumb:active {
  background-color: var(--form-range-thumb-active-bg);
}
.form-range::-moz-range-track {
  width: var(--form-range-track-width);
  height: var(--form-range-track-height);
  color: transparent;
  cursor: pointer;
  background-color: var(--form-range-track-bg);
  border-color: transparent;
  border-radius: var(--form-range-track-border-radius);
  box-shadow: var(--form-range-track-box-shadow);
}
.form-range:disabled {
  pointer-events: none;
}
.form-range:disabled::-webkit-slider-thumb {
  background-color: var(--form-range-thumb-disabled-bg);
}
.form-range:disabled::-moz-range-thumb {
  background-color: var(--form-range-thumb-disabled-bg);
}

.form-floating {
  --form-floating-height: calc(3.5rem + calc(var(--border-width) * 2));
  --form-floating-line-height: 1.25;
  --form-floating-padding-y: 1rem;
  --form-floating-padding-x: 0.875rem;
  --form-floating-input-padding-t: 1.625rem;
  --form-floating-input-padding-b: 0.625rem;
  --form-floating-label-opacity: 0.65;
  --form-floating-label-transform: scale(0.85) translateY(-0.4rem) translateX(0.15rem);
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext,
.form-floating > .form-select {
  height: var(--form-floating-height);
  line-height: var(--form-floating-line-height);
}
.form-floating > label {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  padding: var(--form-floating-padding-y) var(--form-floating-padding-x);
  overflow: hidden;
  text-align: start;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: none;
  border: var(--border-width) solid transparent;
  -webkit-transform-origin: 100% 0;
          transform-origin: 100% 0;
  transition: opacity 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-floating > label {
    transition: none;
  }
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext {
  padding: var(--form-floating-padding-y) var(--form-floating-padding-x);
}
.form-floating > .form-control::-webkit-input-placeholder, .form-floating > .form-control-plaintext::-webkit-input-placeholder {
  color: transparent;
}
.form-floating > .form-control::-moz-placeholder, .form-floating > .form-control-plaintext::-moz-placeholder {
  color: transparent;
}
.form-floating > .form-control:-ms-input-placeholder, .form-floating > .form-control-plaintext:-ms-input-placeholder {
  color: transparent;
}
.form-floating > .form-control::-ms-input-placeholder, .form-floating > .form-control-plaintext::-ms-input-placeholder {
  color: transparent;
}
.form-floating > .form-control::placeholder,
.form-floating > .form-control-plaintext::placeholder {
  color: transparent;
}
.form-floating > .form-control:not(:-moz-placeholder-shown), .form-floating > .form-control-plaintext:not(:-moz-placeholder-shown) {
  padding-top: var(--form-floating-input-padding-t);
  padding-bottom: var(--form-floating-input-padding-b);
}
.form-floating > .form-control:not(:-ms-input-placeholder), .form-floating > .form-control-plaintext:not(:-ms-input-placeholder) {
  padding-top: var(--form-floating-input-padding-t);
  padding-bottom: var(--form-floating-input-padding-b);
}
.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown),
.form-floating > .form-control-plaintext:focus,
.form-floating > .form-control-plaintext:not(:placeholder-shown) {
  padding-top: var(--form-floating-input-padding-t);
  padding-bottom: var(--form-floating-input-padding-b);
}
.form-floating > .form-control:-webkit-autofill,
.form-floating > .form-control-plaintext:-webkit-autofill {
  padding-top: var(--form-floating-input-padding-t);
  padding-bottom: var(--form-floating-input-padding-b);
}
.form-floating > .form-select {
  padding-top: var(--form-floating-input-padding-t);
  padding-bottom: var(--form-floating-input-padding-b);
}
.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  opacity: var(--form-floating-label-opacity);
  transform: var(--form-floating-label-transform);
}
.form-floating > .form-control:not(:-ms-input-placeholder) ~ label {
  opacity: var(--form-floating-label-opacity);
  transform: var(--form-floating-label-transform);
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-control-plaintext ~ label,
.form-floating > .form-select ~ label {
  opacity: var(--form-floating-label-opacity);
  -webkit-transform: var(--form-floating-label-transform);
          transform: var(--form-floating-label-transform);
}
.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: var(--form-floating-label-opacity);
  -webkit-transform: var(--form-floating-label-transform);
          transform: var(--form-floating-label-transform);
}
.form-floating > .form-control-plaintext ~ label {
  border-width: var(--border-width) 0;
}

.input-group {
  --input-group-addon-padding-y: 0.5rem;
  --input-group-addon-padding-x: 0.875rem;
  --input-group-addon-bg: var(--gray-100);
  --input-group-addon-color: var(--body-color);
  --input-group-addon-font-size: var(--body-font-size);
  --input-group-addon-font-weight: 400;
  --input-group-addon-line-height: var(--body-line-height);
  --input-group-addon-border-width: var(--border-width);
  --input-group-addon-border-color: var(--gray-400);
  --input-group-addon-border-radius: var(--border-radius);
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -ms-flex-align: stretch;
      align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-select,
.input-group > .form-floating {
  position: relative;
  -ms-flex: 1 1 auto;
      flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .form-control:focus,
.input-group > .form-select:focus,
.input-group > .form-floating:focus-within {
  z-index: 5;
}
.input-group .btn {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus {
  z-index: 5;
}

.input-group-text {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  padding: var(--input-group-addon-padding-y) var(--input-group-addon-padding-x);
  font-size: var(--input-group-addon-font-size);
  font-weight: var(--input-group-addon-font-weight);
  line-height: var(--input-group-addon-line-height);
  color: var(--input-group-addon-color);
  text-align: center;
  white-space: nowrap;
  background-color: var(--input-group-addon-bg);
  border: var(--input-group-addon-border-width) solid var(--input-group-addon-border-color);
  border-radius: var(--input-group-addon-border-radius);
}

.input-group-lg > .form-control {
  --input-padding-y: 0.625rem;
  --input-padding-x: 1rem;
  --input-font-size: var(--body-font-size-lg);
  --input-line-height: var(--body-line-height-lg);
  --input-border-radius: var(--border-radius-lg);
}
.input-group-lg > .form-select {
  --form-select-padding-y: 0.625rem;
  --form-select-padding-x: 1rem;
  --form-select-indicator-padding: 3rem;
  --form-select-font-size: var(--body-font-size-lg);
  --form-select-line-height: var(--body-line-height-lg);
  --form-select-border-radius: var(--border-radius-lg);
}
.input-group-lg > .input-group-text {
  --input-group-addon-padding-y: 0.625rem;
  --input-group-addon-padding-x: 1rem;
  --input-group-addon-font-size: var(--body-font-size-lg);
  --input-group-addon-line-height: var(--body-line-height-lg);
  --input-group-addon-border-radius: var(--border-radius-lg);
}
.input-group-lg > .btn {
  --btn-padding-y: 0.625rem;
  --btn-padding-x: 1rem;
  --btn-font-size: var(--body-font-size-lg);
  --btn-line-height: var(--body-line-height-lg);
  --btn-border-radius: var(--border-radius-lg);
}

.input-group-sm > .form-control {
  --input-padding-y: 0.375rem;
  --input-padding-x: 0.75rem;
  --input-font-size: var(--body-font-size-sm);
  --input-line-height: var(--body-line-height-sm);
  --input-border-radius: var(--border-radius-sm);
}
.input-group-sm > .form-select {
  --form-select-padding-y: 0.375rem;
  --form-select-padding-x: 0.75rem;
  --form-select-indicator-padding: 2.25rem;
  --form-select-font-size: var(--body-font-size-sm);
  --form-select-line-height: var(--body-line-height-sm);
  --form-select-border-radius: var(--border-radius-sm);
}
.input-group-sm > .input-group-text {
  --input-group-addon-padding-y: 0.375rem;
  --input-group-addon-padding-x: 0.75rem;
  --input-group-addon-font-size: var(--body-font-size-sm);
  --input-group-addon-line-height: var(--body-line-height-sm);
  --input-group-addon-border-radius: var(--border-radius-sm);
}
.input-group-sm > .btn {
  --btn-padding-y: 0.375rem;
  --btn-padding-x: 0.75rem;
  --btn-font-size: var(--body-font-size-sm);
  --btn-line-height: var(--body-line-height-sm);
  --btn-border-radius: var(--border-radius-sm);
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3),
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control,
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4),
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-control,
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-right: calc(var(--border-width) * -1);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > .form-floating:not(:first-child) > .form-control,
.input-group > .form-floating:not(:first-child) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.valid-feedback {
  --form-validation-color: #059669;
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: var(--body-font-size-sm);
  color: var(--form-validation-color);
}

.valid-tooltip {
  --form-validation-tooltip-color: #fff;
  --form-validation-tooltip-bg: rgba(5, 150, 105, 0.95);
  --form-validation-tooltip-border-radius: var(--border-radius);
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: calc(var(--spacer) * 0.4) calc(var(--spacer) * 0.6);
  margin-top: 0.1rem;
  font-size: var(--body-font-size);
  color: var(--form-validation-tooltip-color);
  background-color: var(--form-validation-tooltip-bg);
  border-radius: var(--form-validation-tooltip-border-radius);
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  --form-validation-border-color: #059669;
  --form-validation-focus-box-shadow: 0 0 0 0.125rem rgba(5, 150, 105, 0.25);
  border-color: var(--form-validation-border-color);
  padding-left: calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2));
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23059669' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: left calc(calc(var(--input-line-height) * 0.25em) + calc(var(--input-padding-y) * 0.5)) center;
  background-size: calc(calc(var(--input-line-height) * 0.5em) + var(--input-padding-y)) calc(calc(var(--input-line-height) * 0.5em) + var(--input-padding-y));
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  box-shadow: var(--form-validation-focus-box-shadow);
}

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-left: calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2));
  background-position: top calc(calc(var(--input-line-height) * 0.25em) + calc(var(--input-padding-y) * 0.5)) left calc(calc(var(--input-line-height) * 0.25em) + calc(var(--input-padding-y) * 0.5));
}

.was-validated .form-select:valid, .form-select.is-valid {
  --form-validation-border-color: #059669;
  --form-validation-focus-box-shadow: 0 0 0 0.125rem rgba(5, 150, 105, 0.25);
  border-color: var(--form-validation-border-color);
}
.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"] {
  padding-left: 4.8125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23374151' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23059669' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-position: left 0.875rem center;;
  background-size: 16px 12px, calc(calc(var(--form-select-line-height) * 0.5em) + var(--form-select-padding-y)) calc(calc(var(--form-select-line-height) * 0.5em) + var(--form-select-padding-y));
}
[data-color-theme=dark] .was-validated .form-select:valid:not([multiple]):not([size]), [data-color-theme=dark] .was-validated .form-select:valid:not([multiple])[size="1"], [data-color-theme=dark] .form-select.is-valid:not([multiple]):not([size]), [data-color-theme=dark] .form-select.is-valid:not([multiple])[size="1"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23059669' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
}
.was-validated .form-select:valid:focus, .form-select.is-valid:focus {
  border-color: var(--form-validation-border-color);
  box-shadow: var(--form-validation-focus-box-shadow);
}

.was-validated .form-control-color:valid, .form-control-color.is-valid {
  width: calc(3rem + calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2)));
}

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  --focus-ring-box-shadow: 0 0 0 0.125rem rgba(5, 150, 105, 0.25);
  --component-active-bg: #059669;
  border-color: var(--component-active-bg);
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  --form-validation-color: #059669;
  color: var(--form-validation-color);
}

.form-check-inline .form-check-input ~ .valid-feedback {
  margin-right: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):valid, .input-group > .form-control:not(:focus).is-valid,
.was-validated .input-group > .form-select:not(:focus):valid,
.input-group > .form-select:not(:focus).is-valid,
.was-validated .input-group > .form-floating:not(:focus-within):valid,
.input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3;
}

.invalid-feedback {
  --form-validation-color: #EF4444;
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: var(--body-font-size-sm);
  color: var(--form-validation-color);
}

.invalid-tooltip {
  --form-validation-tooltip-color: #fff;
  --form-validation-tooltip-bg: rgba(239, 68, 68, 0.95);
  --form-validation-tooltip-border-radius: var(--border-radius);
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: calc(var(--spacer) * 0.4) calc(var(--spacer) * 0.6);
  margin-top: 0.1rem;
  font-size: var(--body-font-size);
  color: var(--form-validation-tooltip-color);
  background-color: var(--form-validation-tooltip-bg);
  border-radius: var(--form-validation-tooltip-border-radius);
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  --form-validation-border-color: #EF4444;
  --form-validation-focus-box-shadow: 0 0 0 0.125rem rgba(239, 68, 68, 0.25);
  border-color: var(--form-validation-border-color);
  padding-left: calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2));
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23EF4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23EF4444' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: left calc(calc(var(--input-line-height) * 0.25em) + calc(var(--input-padding-y) * 0.5)) center;
  background-size: calc(calc(var(--input-line-height) * 0.5em) + var(--input-padding-y)) calc(calc(var(--input-line-height) * 0.5em) + var(--input-padding-y));
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  box-shadow: var(--form-validation-focus-box-shadow);
}

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-left: calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2));
  background-position: top calc(calc(var(--input-line-height) * 0.25em) + calc(var(--input-padding-y) * 0.5)) left calc(calc(var(--input-line-height) * 0.25em) + calc(var(--input-padding-y) * 0.5));
}

.was-validated .form-select:invalid, .form-select.is-invalid {
  --form-validation-border-color: #EF4444;
  --form-validation-focus-box-shadow: 0 0 0 0.125rem rgba(239, 68, 68, 0.25);
  border-color: var(--form-validation-border-color);
}
.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"] {
  padding-left: 4.8125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23374151' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23EF4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23EF4444' stroke='none'/%3e%3c/svg%3e");
  background-position: left 0.875rem center;;
  background-size: 16px 12px, calc(calc(var(--form-select-line-height) * 0.5em) + var(--form-select-padding-y)) calc(calc(var(--form-select-line-height) * 0.5em) + var(--form-select-padding-y));
}
[data-color-theme=dark] .was-validated .form-select:invalid:not([multiple]):not([size]), [data-color-theme=dark] .was-validated .form-select:invalid:not([multiple])[size="1"], [data-color-theme=dark] .form-select.is-invalid:not([multiple]):not([size]), [data-color-theme=dark] .form-select.is-invalid:not([multiple])[size="1"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23EF4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23EF4444' stroke='none'/%3e%3c/svg%3e");
}
.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
  border-color: var(--form-validation-border-color);
  box-shadow: var(--form-validation-focus-box-shadow);
}

.was-validated .form-control-color:invalid, .form-control-color.is-invalid {
  width: calc(3rem + calc(calc(var(--input-line-height) * 1em) + calc(var(--input-padding-y) * 2)));
}

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  --focus-ring-box-shadow: 0 0 0 0.125rem rgba(239, 68, 68, 0.25);
  --component-active-bg: #EF4444;
  border-color: var(--component-active-bg);
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  --form-validation-color: #EF4444;
  color: var(--form-validation-color);
}

.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-right: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):invalid, .input-group > .form-control:not(:focus).is-invalid,
.was-validated .input-group > .form-select:not(:focus):invalid,
.input-group > .form-select:not(:focus).is-invalid,
.was-validated .input-group > .form-floating:not(:focus-within):invalid,
.input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4;
}

.btn {
  --btn-padding-x: 0.875rem;
  --btn-padding-y: 0.5rem;
  --btn-font-family: ;
  --btn-font-size: var(--body-font-size);
  --btn-font-weight: 400;
  --btn-line-height: var(--body-line-height);
  --btn-color: #1F2937;
  --btn-bg: transparent;
  --btn-border-width: var(--border-width);
  --btn-border-color: transparent;
  --btn-border-radius: var(--border-radius);
  --btn-hover-border-color: transparent;
  --btn-box-shadow: 0 0 0 0 transparent;
  --btn-disabled-opacity: 0.65;
  --btn-focus-box-shadow: 0 0 0 0.125rem rgba(var(--btn-focus-shadow-rgb), .5);
  display: inline-block;
  padding: var(--btn-padding-y) var(--btn-padding-x);
  font-family: var(--btn-font-family);
  font-size: var(--btn-font-size);
  font-weight: var(--btn-font-weight);
  line-height: var(--btn-line-height);
  color: var(--btn-color);
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border: var(--btn-border-width) solid var(--btn-border-color);
  border-radius: var(--btn-border-radius);
  background-color: var(--btn-bg);
  box-shadow: var(--btn-box-shadow);
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}
.btn:hover {
  color: var(--btn-hover-color);
  background-color: var(--btn-hover-bg);
  border-color: var(--btn-hover-border-color);
}
.btn-check + .btn:hover {
  color: var(--btn-color);
  background-color: var(--btn-bg);
  border-color: var(--btn-border-color);
}
.btn:focus-visible {
  color: var(--btn-hover-color);
  background-color: var(--btn-hover-bg);
  border-color: var(--btn-hover-border-color);
  outline: 0;
  box-shadow: var(--btn-box-shadow), var(--btn-focus-box-shadow);
}
.btn-check:focus-visible + .btn {
  border-color: var(--btn-hover-border-color);
  outline: 0;
  box-shadow: var(--btn-box-shadow), var(--btn-focus-box-shadow);
}
.btn-check:checked + .btn, :not(.btn-check) + .btn:active, .btn:first-child:active, .btn.active, .btn.show {
  color: var(--btn-active-color);
  background-color: var(--btn-active-bg);
  border-color: var(--btn-active-border-color);
  box-shadow: var(--btn-active-shadow);
}
.btn-check:checked + .btn:focus-visible, :not(.btn-check) + .btn:active:focus-visible, .btn:first-child:active:focus-visible, .btn.active:focus-visible, .btn.show:focus-visible {
  box-shadow: var(--btn-active-shadow), var(--btn-focus-box-shadow);
}
.btn:disabled, .btn.disabled, fieldset:disabled .btn {
  color: var(--btn-disabled-color);
  pointer-events: none;
  background-color: var(--btn-disabled-bg);
  border-color: var(--btn-disabled-border-color);
  opacity: var(--btn-disabled-opacity);
  box-shadow: none;
}

.btn-indigo {
  --btn-color: #fff;
  --btn-bg: #5C6BC0;
  --btn-border-color: #5C6BC0;
  --btn-hover-color: #fff;
  --btn-hover-bg: #5360ad;
  --btn-hover-border-color: #5360ad;
  --btn-focus-shadow-rgb: 116, 129, 201;
  --btn-active-color: #fff;
  --btn-active-bg: #4e5ba3;
  --btn-active-border-color: #4e5ba3;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #5C6BC0;
  --btn-disabled-border-color: #5C6BC0;
}

.btn-purple {
  --btn-color: #fff;
  --btn-bg: #8e70c1;
  --btn-border-color: #8e70c1;
  --btn-hover-color: #fff;
  --btn-hover-bg: #8065ae;
  --btn-hover-border-color: #8065ae;
  --btn-focus-shadow-rgb: 159, 133, 202;
  --btn-active-color: #fff;
  --btn-active-bg: #795fa4;
  --btn-active-border-color: #795fa4;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #8e70c1;
  --btn-disabled-border-color: #8e70c1;
}

.btn-pink {
  --btn-color: #fff;
  --btn-bg: #f35c86;
  --btn-border-color: #f35c86;
  --btn-hover-color: #fff;
  --btn-hover-bg: #db5379;
  --btn-hover-border-color: #db5379;
  --btn-focus-shadow-rgb: 245, 116, 152;
  --btn-active-color: #fff;
  --btn-active-bg: #cf4e72;
  --btn-active-border-color: #cf4e72;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #f35c86;
  --btn-disabled-border-color: #f35c86;
}

.btn-teal {
  --btn-color: #fff;
  --btn-bg: #26A69A;
  --btn-border-color: #26A69A;
  --btn-hover-color: #fff;
  --btn-hover-bg: #22958b;
  --btn-hover-border-color: #22958b;
  --btn-focus-shadow-rgb: 71, 179, 169;
  --btn-active-color: #fff;
  --btn-active-bg: #208d83;
  --btn-active-border-color: #208d83;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #26A69A;
  --btn-disabled-border-color: #26A69A;
}

.btn-yellow {
  --btn-color: #000;
  --btn-bg: #ffd648;
  --btn-border-color: #ffd648;
  --btn-hover-color: #000;
  --btn-hover-bg: #f9d146;
  --btn-hover-border-color: #f9d146;
  --btn-focus-shadow-rgb: 217, 182, 61;
  --btn-active-color: #000;
  --btn-active-bg: #f2cb44;
  --btn-active-border-color: #f9d146;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #000;
  --btn-disabled-bg: #ffd648;
  --btn-disabled-border-color: #ffd648;
}

.btn-primary {
  --btn-color: #fff;
  --btn-bg: #0c83ff;
  --btn-border-color: #0c83ff;
  --btn-hover-color: #fff;
  --btn-hover-bg: #0b76e6;
  --btn-hover-border-color: #0b76e6;
  --btn-focus-shadow-rgb: 48, 150, 255;
  --btn-active-color: #fff;
  --btn-active-bg: #0a6fd9;
  --btn-active-border-color: #0a6fd9;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #0c83ff;
  --btn-disabled-border-color: #0c83ff;
}

.btn-secondary {
  --btn-color: #fff;
  --btn-bg: #247297;
  --btn-border-color: #247297;
  --btn-hover-color: #fff;
  --btn-hover-bg: #206788;
  --btn-hover-border-color: #206788;
  --btn-focus-shadow-rgb: 69, 135, 167;
  --btn-active-color: #fff;
  --btn-active-bg: #1f6180;
  --btn-active-border-color: #1f6180;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #247297;
  --btn-disabled-border-color: #247297;
}

.btn-success {
  --btn-color: #fff;
  --btn-bg: #059669;
  --btn-border-color: #059669;
  --btn-hover-color: #fff;
  --btn-hover-bg: #05875f;
  --btn-hover-border-color: #05875f;
  --btn-focus-shadow-rgb: 43, 166, 128;
  --btn-active-color: #fff;
  --btn-active-bg: #048059;
  --btn-active-border-color: #048059;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #059669;
  --btn-disabled-border-color: #059669;
}

.btn-info {
  --btn-color: #fff;
  --btn-bg: #049aad;
  --btn-border-color: #049aad;
  --btn-hover-color: #fff;
  --btn-hover-bg: #048b9c;
  --btn-hover-border-color: #048b9c;
  --btn-focus-shadow-rgb: 42, 169, 185;
  --btn-active-color: #fff;
  --btn-active-bg: #038393;
  --btn-active-border-color: #038393;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #049aad;
  --btn-disabled-border-color: #049aad;
}

.btn-warning {
  --btn-color: #fff;
  --btn-bg: #f58646;
  --btn-border-color: #f58646;
  --btn-hover-color: #fff;
  --btn-hover-bg: #dd793f;
  --btn-hover-border-color: #dd793f;
  --btn-focus-shadow-rgb: 247, 152, 98;
  --btn-active-color: #fff;
  --btn-active-bg: #d0723c;
  --btn-active-border-color: #d0723c;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #f58646;
  --btn-disabled-border-color: #f58646;
}

.btn-danger {
  --btn-color: #fff;
  --btn-bg: #EF4444;
  --btn-border-color: #EF4444;
  --btn-hover-color: #fff;
  --btn-hover-bg: #d73d3d;
  --btn-hover-border-color: #d73d3d;
  --btn-focus-shadow-rgb: 241, 96, 96;
  --btn-active-color: #fff;
  --btn-active-bg: #cb3a3a;
  --btn-active-border-color: #cb3a3a;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #EF4444;
  --btn-disabled-border-color: #EF4444;
}

.btn-light {
  --btn-color: var(--body-color);
  --btn-bg: var(--gray-200);
  --btn-border-color: var(--gray-400);
  --btn-hover-color: var(--body-color);
  --btn-hover-bg: var(--gray-300);
  --btn-hover-border-color: var(--gray-500);
  --btn-focus-shadow-rgb: 207, 207, 209;
  --btn-active-color: var(--body-color);
  --btn-active-bg: var(--gray-400);
  --btn-active-border-color: var(--gray-600);
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: var(--body-color);
  --btn-disabled-bg: var(--gray-200);
  --btn-disabled-border-color: var(--gray-400);
}

.btn-dark {
  --btn-color: #fff;
  --btn-bg: #252b36;
  --btn-border-color: #252b36;
  --btn-hover-color: #fff;
  --btn-hover-bg: #2a303b;
  --btn-hover-border-color: #2a303b;
  --btn-focus-shadow-rgb: 70, 75, 84;
  --btn-active-color: #fff;
  --btn-active-bg: #303640;
  --btn-active-border-color: #2a303b;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #252b36;
  --btn-disabled-border-color: #252b36;
}

.btn-black {
  --btn-color: #fff;
  --btn-bg: #000;
  --btn-border-color: #000;
  --btn-hover-color: #fff;
  --btn-hover-bg: black;
  --btn-hover-border-color: black;
  --btn-focus-shadow-rgb: 38, 38, 38;
  --btn-active-color: #fff;
  --btn-active-bg: black;
  --btn-active-border-color: black;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: #000;
  --btn-disabled-border-color: #000;
}

.btn-white {
  --btn-color: #000;
  --btn-bg: #fff;
  --btn-border-color: #fff;
  --btn-hover-color: #000;
  --btn-hover-bg: #f9f9f9;
  --btn-hover-border-color: #f9f9f9;
  --btn-focus-shadow-rgb: 217, 217, 217;
  --btn-active-color: #000;
  --btn-active-bg: #f2f2f2;
  --btn-active-border-color: #f9f9f9;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #000;
  --btn-disabled-bg: #fff;
  --btn-disabled-border-color: #fff;
}

.btn-outline-indigo {
  --btn-color: #5C6BC0;
  --btn-border-color: #5C6BC0;
  --btn-hover-color: #fff;
  --btn-hover-bg: #5C6BC0;
  --btn-hover-border-color: #5C6BC0;
  --btn-focus-shadow-rgb: 92, 107, 192;
  --btn-active-color: #fff;
  --btn-active-bg: #4a5ab9;
  --btn-active-border-color: #5C6BC0;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #5C6BC0;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-purple {
  --btn-color: #8e70c1;
  --btn-border-color: #8e70c1;
  --btn-hover-color: #fff;
  --btn-hover-bg: #8e70c1;
  --btn-hover-border-color: #8e70c1;
  --btn-focus-shadow-rgb: 142, 112, 193;
  --btn-active-color: #fff;
  --btn-active-bg: #805eb9;
  --btn-active-border-color: #8e70c1;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #8e70c1;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-pink {
  --btn-color: #f35c86;
  --btn-border-color: #f35c86;
  --btn-hover-color: #fff;
  --btn-hover-bg: #f35c86;
  --btn-hover-border-color: #f35c86;
  --btn-focus-shadow-rgb: 243, 92, 134;
  --btn-active-color: #fff;
  --btn-active-bg: #f14474;
  --btn-active-border-color: #f35c86;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #f35c86;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-teal {
  --btn-color: #26A69A;
  --btn-border-color: #26A69A;
  --btn-hover-color: #fff;
  --btn-hover-bg: #26A69A;
  --btn-hover-border-color: #26A69A;
  --btn-focus-shadow-rgb: 38, 166, 154;
  --btn-active-color: #fff;
  --btn-active-bg: #219187;
  --btn-active-border-color: #26A69A;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #26A69A;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-yellow {
  --btn-color: #ffd648;
  --btn-border-color: #ffd648;
  --btn-hover-color: #000;
  --btn-hover-bg: #ffd648;
  --btn-hover-border-color: #ffd648;
  --btn-focus-shadow-rgb: 255, 214, 72;
  --btn-active-color: #000;
  --btn-active-bg: #ffd02f;
  --btn-active-border-color: #ffd648;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #ffd648;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-primary {
  --btn-color: #0c83ff;
  --btn-border-color: #0c83ff;
  --btn-hover-color: #fff;
  --btn-hover-bg: #0c83ff;
  --btn-hover-border-color: #0c83ff;
  --btn-focus-shadow-rgb: 12, 131, 255;
  --btn-active-color: #fff;
  --btn-active-bg: #0076f2;
  --btn-active-border-color: #0c83ff;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #0c83ff;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-secondary {
  --btn-color: #247297;
  --btn-border-color: #247297;
  --btn-hover-color: #fff;
  --btn-hover-bg: #247297;
  --btn-hover-border-color: #247297;
  --btn-focus-shadow-rgb: 36, 114, 151;
  --btn-active-color: #fff;
  --btn-active-bg: #1f6282;
  --btn-active-border-color: #247297;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #247297;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-success {
  --btn-color: #059669;
  --btn-border-color: #059669;
  --btn-hover-color: #fff;
  --btn-hover-bg: #059669;
  --btn-hover-border-color: #059669;
  --btn-focus-shadow-rgb: 5, 150, 105;
  --btn-active-color: #fff;
  --btn-active-bg: #047d58;
  --btn-active-border-color: #059669;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #059669;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-info {
  --btn-color: #049aad;
  --btn-border-color: #049aad;
  --btn-hover-color: #fff;
  --btn-hover-bg: #049aad;
  --btn-hover-border-color: #049aad;
  --btn-focus-shadow-rgb: 4, 154, 173;
  --btn-active-color: #fff;
  --btn-active-bg: #038494;
  --btn-active-border-color: #049aad;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #049aad;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-warning {
  --btn-color: #f58646;
  --btn-border-color: #f58646;
  --btn-hover-color: #fff;
  --btn-hover-bg: #f58646;
  --btn-hover-border-color: #f58646;
  --btn-focus-shadow-rgb: 245, 134, 70;
  --btn-active-color: #fff;
  --btn-active-bg: #f4762e;
  --btn-active-border-color: #f58646;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #f58646;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-danger {
  --btn-color: #EF4444;
  --btn-border-color: #EF4444;
  --btn-hover-color: #fff;
  --btn-hover-bg: #EF4444;
  --btn-hover-border-color: #EF4444;
  --btn-focus-shadow-rgb: 239, 68, 68;
  --btn-active-color: #fff;
  --btn-active-bg: #ed2d2d;
  --btn-active-border-color: #EF4444;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #EF4444;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-light {
  --btn-color: var(--body-color);
  --btn-border-color: var(--gray-400);
  --btn-hover-color: var(--body-color);
  --btn-hover-bg: var(--gray-300);
  --btn-hover-border-color: var(--gray-500);
  --btn-focus-shadow-rgb: 243, 244, 246;
  --btn-active-color: var(--body-color);
  --btn-active-bg: var(--gray-400);
  --btn-active-border-color: var(--gray-600);
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: var(--body-color);
  --btn-disabled-bg: var(--gray-200);
}

.btn-outline-dark {
  --btn-color: #252b36;
  --btn-border-color: #252b36;
  --btn-hover-color: #fff;
  --btn-hover-bg: #252b36;
  --btn-hover-border-color: #252b36;
  --btn-focus-shadow-rgb: 37, 43, 54;
  --btn-active-color: #fff;
  --btn-active-bg: #1b1f27;
  --btn-active-border-color: #252b36;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #252b36;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-black {
  --btn-color: #000;
  --btn-border-color: #000;
  --btn-hover-color: #fff;
  --btn-hover-bg: #000;
  --btn-hover-border-color: #000;
  --btn-focus-shadow-rgb: 0, 0, 0;
  --btn-active-color: #fff;
  --btn-active-bg: black;
  --btn-active-border-color: #000;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #000;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-outline-white {
  --btn-color: #fff;
  --btn-border-color: #fff;
  --btn-hover-color: #000;
  --btn-hover-bg: #fff;
  --btn-hover-border-color: #fff;
  --btn-focus-shadow-rgb: 255, 255, 255;
  --btn-active-color: #000;
  --btn-active-bg: #f2f2f2;
  --btn-active-border-color: #fff;
  --btn-active-shadow: inset 0 0 0 0 transparent;
  --btn-disabled-color: #fff;
  --btn-disabled-bg: transparent;
  --gradient: none;
}

.btn-link {
  --btn-font-weight: 400;
  --btn-color: var(--link-color);
  --btn-bg: transparent;
  --btn-border-color: transparent;
  --btn-hover-color: var(--link-hover-color);
  --btn-hover-border-color: transparent;
  --btn-active-color: var(--link-hover-color);
  --btn-active-border-color: transparent;
  --btn-disabled-color: rgba(var(--body-color-rgb), 0.75);
  --btn-disabled-border-color: transparent;
  --btn-box-shadow: none;
  --btn-focus-shadow-rgb: 48, 150, 255;
  text-decoration: none;
}
.btn-link:focus-visible {
  color: var(--btn-color);
}
.btn-link:hover {
  color: var(--btn-hover-color);
}

.btn-lg, .btn-group-lg > .btn {
  --btn-padding-y: 0.625rem;
  --btn-padding-x: 1rem;
  --btn-font-size: var(--body-font-size-lg);
  --btn-border-radius: var(--border-radius-lg);
}

.btn-sm, .btn-group-sm > .btn {
  --btn-padding-y: 0.375rem;
  --btn-padding-x: 0.75rem;
  --btn-font-size: var(--body-font-size-sm);
  --btn-border-radius: var(--border-radius-sm);
}

.fade {
  transition: opacity var(--transition-base-timer) linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height var(--transition-collapse-timer) ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  transition: width var(--transition-collapse-timer) ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal {
    transition: none;
  }
}

.dropup,
.dropend,
.dropdown,
.dropstart,
.dropup-center,
.dropdown-center {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-right: 0.25em;
  vertical-align: -0.125em;
  font-family: var(--icon-font-family);
  font-size: 1em;
  line-height: 1;
  content: "\f31a";
}
.dropdown-toggle:empty::after {
  margin-right: 0;
}

.dropdown-menu {
  --dropdown-zindex: 1000;
  --dropdown-min-width: 13.75rem;
  --dropdown-padding-x: 0;
  --dropdown-padding-y: 0.625rem;
  --dropdown-spacer: 0.125rem;
  --dropdown-font-size: var(--body-font-size);
  --dropdown-color: var(--body-color);
  --dropdown-bg: var(--white);
  --dropdown-border-color: var(--border-color-translucent);
  --dropdown-border-radius: var(--border-radius);
  --dropdown-border-width: var(--border-width);
  --dropdown-inner-border-radius: calc(var(--border-radius) - (var(--border-width)));
  --dropdown-divider-bg: var(--border-color);
  --dropdown-divider-margin-y: 0.625rem;
  --dropdown-box-shadow: var(--box-shadow-lg);
  --dropdown-link-color: var(--body-color);
  --dropdown-link-hover-color: var(--body-color);
  --dropdown-link-hover-bg: var(--gray-200);
  --dropdown-link-active-color: var(--gray-900);
  --dropdown-link-active-bg: var(--gray-300);
  --dropdown-link-disabled-color: var(--gray-500);
  --dropdown-item-padding-x: var(--spacer);
  --dropdown-item-padding-y: calc(var(--spacer) * 0.4);
  --dropdown-header-color: rgba(var(--body-color-rgb), 0.5);
  --dropdown-header-padding-x: var(--spacer);
  --dropdown-header-padding-y: 0.625rem;
  position: absolute;
  z-index: var(--dropdown-zindex);
  display: none;
  min-width: var(--dropdown-min-width);
  padding: var(--dropdown-padding-y) var(--dropdown-padding-x);
  margin: 0;
  font-size: var(--dropdown-font-size);
  color: var(--dropdown-color);
  text-align: right;
  list-style: none;
  background-color: var(--dropdown-bg);
  background-clip: padding-box;
  border: var(--dropdown-border-width) solid var(--dropdown-border-color);
  border-radius: var(--dropdown-border-radius);
  box-shadow: var(--dropdown-box-shadow);
}
.dropdown-menu[data-bs-popper] {
  top: 100%;
  right: 0;
  margin-top: var(--dropdown-spacer);
}

.dropdown-menu-start {
  --bs-position: start;
}
.dropdown-menu-start[data-bs-popper] {
  left: auto;
  right: 0;
}

.dropdown-menu-end {
  --bs-position: end;
}
.dropdown-menu-end[data-bs-popper] {
  left: 0;
  right: auto;
}

@media (min-width: 576px) {
  .dropdown-menu-sm-start {
    --bs-position: start;
  }
  .dropdown-menu-sm-start[data-bs-popper] {
    left: auto;
    right: 0;
  }
  .dropdown-menu-sm-end {
    --bs-position: end;
  }
  .dropdown-menu-sm-end[data-bs-popper] {
    left: 0;
    right: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-start {
    --bs-position: start;
  }
  .dropdown-menu-md-start[data-bs-popper] {
    left: auto;
    right: 0;
  }
  .dropdown-menu-md-end {
    --bs-position: end;
  }
  .dropdown-menu-md-end[data-bs-popper] {
    left: 0;
    right: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-start {
    --bs-position: start;
  }
  .dropdown-menu-lg-start[data-bs-popper] {
    left: auto;
    right: 0;
  }
  .dropdown-menu-lg-end {
    --bs-position: end;
  }
  .dropdown-menu-lg-end[data-bs-popper] {
    left: 0;
    right: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-start {
    --bs-position: start;
  }
  .dropdown-menu-xl-start[data-bs-popper] {
    left: auto;
    right: 0;
  }
  .dropdown-menu-xl-end {
    --bs-position: end;
  }
  .dropdown-menu-xl-end[data-bs-popper] {
    left: 0;
    right: auto;
  }
}
@media (min-width: 1400px) {
  .dropdown-menu-xxl-start {
    --bs-position: start;
  }
  .dropdown-menu-xxl-start[data-bs-popper] {
    left: auto;
    right: 0;
  }
  .dropdown-menu-xxl-end {
    --bs-position: end;
  }
  .dropdown-menu-xxl-end[data-bs-popper] {
    left: 0;
    right: auto;
  }
}
.dropup .dropdown-menu[data-bs-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: var(--dropdown-spacer);
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-right: 0.25em;
  vertical-align: -0.125em;
  font-family: var(--icon-font-family);
  font-size: 1em;
  line-height: 1;
  content: "\f31d";
}
.dropup .dropdown-toggle:empty::after {
  margin-right: 0;
}

.dropend .dropdown-menu[data-bs-popper] {
  top: 0;
  left: auto;
  right: 100%;
  margin-top: 0;
  margin-right: var(--dropdown-spacer);
}
.dropend .dropdown-toggle::after {
  display: inline-block;
  margin-right: 0.25em;
  vertical-align: -0.125em;
  font-family: var(--icon-font-family);
  font-size: 1em;
  line-height: 1;
  content: "\f31c";
}
.dropend .dropdown-toggle:empty::after {
  margin-right: 0;
}
.dropend .dropdown-toggle::after {
  vertical-align: 0;
}

.dropstart .dropdown-menu[data-bs-popper] {
  top: 0;
  left: 100%;
  right: auto;
  margin-top: 0;
  margin-left: var(--dropdown-spacer);
}
.dropstart .dropdown-toggle::after {
  display: inline-block;
  margin-right: 0.25em;
  vertical-align: -0.125em;
  font-family: var(--icon-font-family);
  font-size: 1em;
  line-height: 1;
}
.dropstart .dropdown-toggle::after {
  display: none;
}
.dropstart .dropdown-toggle::before {
  display: inline-block;
  margin-left: 0.25em;
  vertical-align: -0.125em;
  font-family: var(--icon-font-family);
  font-size: 1em;
  line-height: 1;
  content: "\f31b";
}
.dropstart .dropdown-toggle:empty::after {
  margin-right: 0;
}
.dropstart .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-divider {
  height: 0;
  margin: var(--dropdown-divider-margin-y) 0;
  overflow: hidden;
  border-top: 1px solid var(--dropdown-divider-bg);
  opacity: 1;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--dropdown-item-padding-y) var(--dropdown-item-padding-x);
  clear: both;
  font-weight: 400;
  color: var(--dropdown-link-color);
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.dropdown-item:hover, .dropdown-item:focus {
  color: var(--dropdown-link-hover-color);
  background-color: var(--dropdown-link-hover-bg);
}
.dropdown-item.active, .dropdown-item:active {
  color: var(--dropdown-link-active-color);
  text-decoration: none;
  background-color: var(--dropdown-link-active-bg);
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: var(--dropdown-link-disabled-color);
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: var(--dropdown-header-padding-y) var(--dropdown-header-padding-x);
  margin-bottom: 0;
  font-size: 0.75rem;
  color: var(--dropdown-header-color);
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: var(--dropdown-item-padding-y) var(--dropdown-item-padding-x);
  color: var(--dropdown-link-color);
}

.dropdown-menu-dark {
  --dropdown-color: var(--white);
  --dropdown-bg: var(--dark);
  --dropdown-border-color: rgba(var(--black-rgb), 0.15);
  --dropdown-box-shadow: var(--box-shadow);
  --dropdown-link-color: var(--white);
  --dropdown-link-hover-color: var(--white);
  --dropdown-divider-bg: rgba(var(--white-rgb), 0.15);
  --dropdown-link-hover-bg: rgba(var(--white-rgb), 0.15);
  --dropdown-link-active-color: var(--white);
  --dropdown-link-active-bg: rgba(var(--white-rgb), 0.25);
  --dropdown-link-disabled-color: var(--gray-500);
  --dropdown-header-color: var(--gray-500);
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  -ms-flex: 1 1 auto;
      flex: 1 1 auto;
}
.btn-group > .btn-check:checked + .btn,
.btn-group > .btn-check:focus + .btn,
.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn-check:checked + .btn,
.btn-group-vertical > .btn-check:focus + .btn,
.btn-group-vertical > .btn:hover,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
  z-index: 1;
}

.btn-toolbar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -ms-flex-pack: start;
      justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}

.btn-group {
  border-radius: var(--border-radius);
}
.btn-group > :not(.btn-check:first-child) + .btn,
.btn-group > .btn-group:not(:first-child) {
  margin-right: calc(var(--border-width) * -1);
}
.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn.dropdown-toggle-split:first-child,
.btn-group > .btn-group:not(:last-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group > .btn:nth-child(n+3),
.btn-group > :not(.btn-check) + .btn,
.btn-group > .btn-group:not(:first-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.dropdown-toggle-split {
  padding-left: calc(var(--btn-padding-x) * 0.75);
  padding-right: calc(var(--btn-padding-x) * 0.75);
}
.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after {
  margin-right: 0;
}
.dropstart .dropdown-toggle-split::before {
  margin-left: 0;
}

.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-left: calc(var(--btn-padding-x-sm) * 0.75);
  padding-right: calc(var(--btn-padding-x-sm) * 0.75);
}

.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-left: calc(var(--btn-padding-x-lg) * 0.75);
  padding-right: calc(var(--btn-padding-x-lg) * 0.75);
}

.btn-group.show .dropdown-toggle {
  box-shadow: var(--btn-active-shadow);
}
.btn-group.show .dropdown-toggle.btn-link {
  box-shadow: none;
}

.btn-group-vertical {
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-align: start;
      align-items: flex-start;
  -ms-flex-pack: center;
      justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: calc(var(--btn-border-width) * -1);
}
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group-vertical > .btn ~ .btn,
.btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.nav {
  --nav-link-padding-x: 1rem;
  --nav-link-padding-y: 0.625rem;
  --nav-link-font-weight: ;
  --nav-link-color: rgba(var(--body-color-rgb), 0.75);
  --nav-link-hover-color: var(--body-color);
  --nav-link-disabled-color: rgba(var(--body-color-rgb), 0.5);
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding-right: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: var(--nav-link-padding-y) var(--nav-link-padding-x);
  font-size: var(--nav-link-font-size);
  font-weight: var(--nav-link-font-weight);
  color: var(--nav-link-color);
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: var(--nav-link-hover-color);
}
.nav-link.disabled {
  color: var(--nav-link-disabled-color);
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  --nav-tabs-border-width: var(--border-width);
  --nav-tabs-border-color: var(--border-color);
  --nav-tabs-border-radius: var(--border-radius);
  --nav-tabs-link-hover-border-color: transparent;
  --nav-tabs-link-active-color: var(--body-color);
  --nav-tabs-link-active-bg: var(--white);
  --nav-tabs-link-active-border-color: var(--border-color) var(--border-color) var(--white);
  border-bottom: var(--nav-tabs-border-width) solid var(--nav-tabs-border-color);
}
.nav-tabs .nav-link {
  margin-bottom: calc(-1 * var(--nav-tabs-border-width));
  background: none;
  border: var(--nav-tabs-border-width) solid transparent;
  border-top-right-radius: var(--nav-tabs-border-radius);
  border-top-left-radius: var(--nav-tabs-border-radius);
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  isolation: isolate;
  border-color: var(--nav-tabs-link-hover-border-color);
}
.nav-tabs .nav-link.disabled, .nav-tabs .nav-link:disabled {
  color: var(--nav-link-disabled-color);
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: var(--nav-tabs-link-active-color);
  background-color: var(--nav-tabs-link-active-bg);
  border-color: var(--nav-tabs-link-active-border-color);
}
.nav-tabs .dropdown-menu {
  margin-top: calc(-1 * var(--nav-tabs-border-width));
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.nav-pills {
  --nav-pills-border-radius: var(--border-radius);
  --nav-pills-link-active-color: var(--component-active-color);
  --nav-pills-link-active-bg: var(--component-active-bg);
}
.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: var(--nav-pills-border-radius);
}
.nav-pills .nav-link:disabled {
  color: var(--nav-link-disabled-color);
  background-color: transparent;
  border-color: transparent;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: var(--nav-pills-link-active-color);
  background-color: var(--nav-pills-link-active-bg);
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  -ms-flex: 1 1 auto;
      flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  -ms-flex-preferred-size: 0;
      flex-basis: 0;
  -ms-flex-positive: 1;
      flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  --navbar-padding-x: calc(var(--spacer) * 0.5);
  --navbar-padding-y: var(--spacer-2);
  --navbar-color: var(--body-color);
  --navbar-hover-color: var(--body-color);
  --navbar-disabled-color: rgba(var(--body-color-rgb), 0.3);
  --navbar-active-color: var(--link-color);
  --navbar-brand-padding-y: var(--spacer-1);
  --navbar-brand-margin-end: 0;
  --navbar-brand-font-size: var(--body-font-size);
  --navbar-brand-color: var(--link-color);
  --navbar-brand-hover-color: var(--link-color);
  --navbar-nav-link-padding-x: 1rem;
  --navbar-toggler-padding-y: var(--navbar-link-padding-y);
  --navbar-toggler-padding-x: var(--navbar-link-padding-y);
  --navbar-toggler-font-size: ;
  --navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='var%28--body-color%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  --navbar-toggler-border-color: ;
  --navbar-toggler-border-radius: var(--border-radius);
  --navbar-toggler-focus-width: ;
  --navbar-toggler-transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out;
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: justify;
      justify-content: space-between;
  padding: var(--navbar-padding-y) var(--navbar-padding-x);
}
.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-sm,
.navbar > .container-md,
.navbar > .container-lg,
.navbar > .container-xl,
.navbar > .container-xxl {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: inherit;
      flex-wrap: inherit;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: justify;
      justify-content: space-between;
}
.navbar-brand {
  padding-top: var(--navbar-brand-padding-y);
  padding-bottom: var(--navbar-brand-padding-y);
  margin-left: var(--navbar-brand-margin-end);
  font-size: var(--navbar-brand-font-size);
  color: var(--navbar-brand-color);
  white-space: nowrap;
}
.navbar-brand:hover, .navbar-brand:focus {
  color: var(--navbar-brand-hover-color);
}

.navbar-nav {
  --nav-link-padding-x: 0;
  --nav-link-padding-y: 0.625rem;
  --nav-link-font-weight: ;
  --nav-link-color: var(--navbar-color);
  --nav-link-hover-color: var(--navbar-hover-color);
  --nav-link-disabled-color: var(--navbar-disabled-color);
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  padding-right: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link.show,
.navbar-nav .nav-link.active {
  color: var(--navbar-active-color);
}
.navbar-nav .dropdown-menu {
  position: static;
}

.navbar-text {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  color: var(--navbar-color);
}
.navbar-text a,
.navbar-text a:hover,
.navbar-text a:focus {
  color: var(--navbar-active-color);
}

.navbar-collapse {
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
  -ms-flex-positive: 1;
      flex-grow: 1;
  -ms-flex-align: center;
      align-items: center;
}

.navbar-toggler {
  padding: var(--navbar-toggler-padding-y) var(--navbar-toggler-padding-x);
  font-size: var(--navbar-toggler-font-size);
  line-height: 1;
  color: var(--navbar-color);
  background-color: transparent;
  border: var(--border-width) solid var(--navbar-toggler-border-color);
  border-radius: var(--navbar-toggler-border-radius);
  transition: var(--navbar-toggler-transition);
}
@media (prefers-reduced-motion: reduce) {
  .navbar-toggler {
    transition: none;
  }
}
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  box-shadow: 0 0 0 var(--navbar-toggler-focus-width);
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-image: var(--navbar-toggler-icon-bg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.navbar-nav-scroll {
  max-height: var(--scroll-height, 75vh);
  overflow-y: auto;
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    -ms-flex-pack: start;
        justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-left: var(--navbar-nav-link-padding-x);
    padding-right: var(--navbar-nav-link-padding-x);
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
  .navbar-expand-sm .offcanvas {
    position: static;
    z-index: auto;
    -ms-flex-positive: 1;
        flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    -webkit-transform: none !important;
            transform: none !important;
    box-shadow: none;
    transition: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-body {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-positive: 0;
        flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    -ms-flex-pack: start;
        justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-left: var(--navbar-nav-link-padding-x);
    padding-right: var(--navbar-nav-link-padding-x);
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
  .navbar-expand-md .offcanvas {
    position: static;
    z-index: auto;
    -ms-flex-positive: 1;
        flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    -webkit-transform: none !important;
            transform: none !important;
    box-shadow: none;
    transition: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-body {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-positive: 0;
        flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    -ms-flex-pack: start;
        justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-left: var(--navbar-nav-link-padding-x);
    padding-right: var(--navbar-nav-link-padding-x);
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
  .navbar-expand-lg .offcanvas {
    position: static;
    z-index: auto;
    -ms-flex-positive: 1;
        flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    -webkit-transform: none !important;
            transform: none !important;
    box-shadow: none;
    transition: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-body {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-positive: 0;
        flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    -ms-flex-pack: start;
        justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-left: var(--navbar-nav-link-padding-x);
    padding-right: var(--navbar-nav-link-padding-x);
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xl .offcanvas {
    position: static;
    z-index: auto;
    -ms-flex-positive: 1;
        flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    -webkit-transform: none !important;
            transform: none !important;
    box-shadow: none;
    transition: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-body {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-positive: 0;
        flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1400px) {
  .navbar-expand-xxl {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    -ms-flex-pack: start;
        justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-left: var(--navbar-nav-link-padding-x);
    padding-right: var(--navbar-nav-link-padding-x);
  }
  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xxl .offcanvas {
    position: static;
    z-index: auto;
    -ms-flex-positive: 1;
        flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    -webkit-transform: none !important;
            transform: none !important;
    box-shadow: none;
    transition: none;
  }
  .navbar-expand-xxl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xxl .offcanvas .offcanvas-body {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-positive: 0;
        flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
.navbar-expand {
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  -ms-flex-pack: start;
      justify-content: flex-start;
}
.navbar-expand .navbar-nav {
  -ms-flex-direction: row;
      flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-left: var(--navbar-nav-link-padding-x);
  padding-right: var(--navbar-nav-link-padding-x);
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: -ms-flexbox !important;
  display: flex !important;
  -ms-flex-preferred-size: auto;
      flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-expand .offcanvas {
  position: static;
  z-index: auto;
  -ms-flex-positive: 1;
      flex-grow: 1;
  width: auto !important;
  height: auto !important;
  visibility: visible !important;
  background-color: transparent !important;
  border: 0 !important;
  -webkit-transform: none !important;
          transform: none !important;
  box-shadow: none;
  transition: none;
}
.navbar-expand .offcanvas .offcanvas-header {
  display: none;
}
.navbar-expand .offcanvas .offcanvas-body {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-positive: 0;
      flex-grow: 0;
  padding: 0;
  overflow-y: visible;
}

.navbar-dark {
  --navbar-color: #fff;
  --navbar-hover-color: #fff;
  --navbar-disabled-color: rgba(255, 255, 255, 0.25);
  --navbar-active-color: #fff;
  --navbar-brand-color: #fff;
  --navbar-brand-hover-color: #fff;
  --navbar-toggler-border-color: ;
  --navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='%23fff' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.card {
  --card-spacer-y: var(--spacer);
  --card-spacer-x: var(--spacer);
  --card-title-spacer-y: calc(var(--spacer) * 0.75);
  --card-border-width: var(--border-width);
  --card-border-color: var(--border-color-translucent);
  --card-border-radius: var(--border-radius);
  --card-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.075), var(--box-shadow);
  --card-inner-border-radius: calc(var(--border-radius) - (var(--border-width)));
  --card-cap-padding-y: calc(var(--spacer) * 0.75);
  --card-cap-padding-x: var(--spacer);
  --card-cap-bg: ;
  --card-cap-color: ;
  --card-height: ;
  --card-color: ;
  --card-bg: var(--white);
  --card-img-overlay-padding: var(--spacer);
  --card-group-margin: 0.625rem;
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  min-width: 0;
  height: var(--card-height);
  word-wrap: break-word;
  background-color: var(--card-bg);
  background-clip: border-box;
  border: var(--card-border-width) solid var(--card-border-color);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-box-shadow);
}
.card > hr {
  margin-left: 0;
  margin-right: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-right-radius: var(--card-inner-border-radius);
  border-top-left-radius: var(--card-inner-border-radius);
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-left-radius: var(--card-inner-border-radius);
  border-bottom-right-radius: var(--card-inner-border-radius);
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}

.card-body {
  -ms-flex: 1 1 auto;
      flex: 1 1 auto;
  padding: var(--card-spacer-y) var(--card-spacer-x);
  color: var(--card-color);
}

.card-title {
  margin-bottom: var(--card-title-spacer-y);
}

.card-subtitle {
  margin-top: calc(-0.5 * var(--card-title-spacer-y));
  margin-bottom: 0;
}

.card-text:last-child {
  margin-bottom: 0;
}

.card-link + .card-link {
  margin-right: var(--card-spacer-x);
}

.card-header {
  padding: var(--card-cap-padding-y) var(--card-cap-padding-x);
  margin-bottom: 0;
  color: var(--card-cap-color);
  background-color: var(--card-cap-bg);
  border-bottom: var(--card-border-width) solid var(--card-border-color);
}
.card-header:first-child {
  border-radius: var(--card-inner-border-radius) var(--card-inner-border-radius) 0 0;
}

.card-footer {
  padding: var(--card-cap-padding-y) var(--card-cap-padding-x);
  color: var(--card-cap-color);
  background-color: var(--card-cap-bg);
  border-top: var(--card-border-width) solid var(--card-border-color);
}
.card-footer:last-child {
  border-radius: 0 0 var(--card-inner-border-radius) var(--card-inner-border-radius);
}

.card-header-tabs {
  margin-left: calc(-0.5 * var(--card-cap-padding-x));
  margin-bottom: calc(-1 * var(--card-cap-padding-y));
  margin-right: calc(-0.5 * var(--card-cap-padding-x));
  border-bottom: 0;
}
.card-header-tabs .nav-link.active {
  background-color: var(--card-bg);
  border-bottom-color: var(--card-bg);
}

.card-header-pills {
  margin-left: calc(-0.5 * var(--card-cap-padding-x));
  margin-right: calc(-0.5 * var(--card-cap-padding-x));
}

.card-img-overlay {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  padding: var(--card-img-overlay-padding);
  border-radius: var(--card-inner-border-radius);
}

.card-img,
.card-img-top,
.card-img-bottom {
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-right-radius: var(--card-inner-border-radius);
  border-top-left-radius: var(--card-inner-border-radius);
}

.card-img,
.card-img-bottom {
  border-bottom-left-radius: var(--card-inner-border-radius);
  border-bottom-right-radius: var(--card-inner-border-radius);
}

.card-group > .card {
  margin-bottom: var(--card-group-margin);
}
@media (min-width: 576px) {
  .card-group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-flow: row wrap;
        flex-flow: row wrap;
  }
  .card-group > .card {
    -ms-flex: 1 0 0%;
        flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-right: 0;
    border-right: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
.card-group > .card:not(:last-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
.card-group > .card:not(:last-child) .card-footer {
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
.card-group > .card:not(:first-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
.card-group > .card:not(:first-child) .card-footer {
    border-bottom-right-radius: 0;
  }
}

.accordion {
  --accordion-color: var(--body-color);
  --accordion-bg: transparent;
  --accordion-transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out, border-radius var(--transition-base-timer) ease;
  --accordion-border-color: var(--border-color);
  --accordion-border-width: var(--border-width);
  --accordion-border-radius: var(--border-radius);
  --accordion-inner-border-radius: calc(var(--border-radius) - (var(--border-width)));
  --accordion-btn-padding-x: 1.25rem;
  --accordion-btn-padding-y: 1rem;
  --accordion-btn-color: var(--body-color);
  --accordion-btn-bg: transparent;
  --accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%231F2937'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --accordion-btn-icon-width: 1rem;
  --accordion-btn-icon-transform: rotate(-180deg);
  --accordion-btn-icon-transition: transform var(--transition-collapse-timer) ease-in-out;
  --accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%231F2937'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --accordion-btn-focus-border-color: var(--component-active-bg);
  --accordion-btn-focus-box-shadow: ;
  --accordion-body-padding-x: 1.25rem;
  --accordion-body-padding-y: 1rem;
  --accordion-active-color: var(--link-color);
  --accordion-active-bg: transparent;
}

.accordion-button {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  width: 100%;
  padding: var(--accordion-btn-padding-y) var(--accordion-btn-padding-x);
  font-size: 0.875rem;
  color: var(--accordion-btn-color);
  text-align: right;
  background-color: var(--accordion-btn-bg);
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: var(--accordion-transition);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button {
    transition: none;
  }
}
.accordion-button:not(.collapsed) {
  color: var(--accordion-active-color);
  background-color: var(--accordion-active-bg);
  box-shadow: inset 0 calc(-1 * var(--accordion-border-width)) 0 var(--accordion-border-color);
}
.accordion-button:not(.collapsed)::after {
  background-image: var(--accordion-btn-active-icon);
  -webkit-transform: var(--accordion-btn-icon-transform);
          transform: var(--accordion-btn-icon-transform);
}
.accordion-button::after {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  width: var(--accordion-btn-icon-width);
  height: var(--accordion-btn-icon-width);
  margin-right: auto;
  content: "";
  background-image: var(--accordion-btn-icon);
  background-repeat: no-repeat;
  background-size: var(--accordion-btn-icon-width);
  transition: var(--accordion-btn-icon-transition);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button::after {
    transition: none;
  }
}
.accordion-button:hover {
  z-index: 2;
}
.accordion-button:focus {
  z-index: 3;
  border-color: var(--accordion-btn-focus-border-color);
  outline: 0;
  box-shadow: var(--accordion-btn-focus-box-shadow);
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-item {
  color: var(--accordion-color);
  background-color: var(--accordion-bg);
  border: var(--accordion-border-width) solid var(--accordion-border-color);
}
.accordion-item:first-of-type {
  border-top-right-radius: var(--accordion-border-radius);
  border-top-left-radius: var(--accordion-border-radius);
}
.accordion-item:first-of-type .accordion-button {
  border-top-right-radius: var(--accordion-inner-border-radius);
  border-top-left-radius: var(--accordion-inner-border-radius);
}
.accordion-item:not(:first-of-type) {
  border-top: 0;
}
.accordion-item:last-of-type {
  border-bottom-left-radius: var(--accordion-border-radius);
  border-bottom-right-radius: var(--accordion-border-radius);
}
.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-left-radius: var(--accordion-inner-border-radius);
  border-bottom-right-radius: var(--accordion-inner-border-radius);
}
.accordion-item:last-of-type .accordion-collapse {
  border-bottom-left-radius: var(--accordion-border-radius);
  border-bottom-right-radius: var(--accordion-border-radius);
}

.accordion-body {
  padding: var(--accordion-body-padding-y) var(--accordion-body-padding-x);
}

.accordion-flush .accordion-collapse {
  border-width: 0;
}
.accordion-flush .accordion-item {
  border-left: 0;
  border-right: 0;
  border-radius: 0;
}
.accordion-flush .accordion-item:first-child {
  border-top: 0;
}
.accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}
.accordion-flush .accordion-item .accordion-button, .accordion-flush .accordion-item .accordion-button.collapsed {
  border-radius: 0;
}

.breadcrumb {
  --breadcrumb-padding-x: 0;
  --breadcrumb-padding-y: 0;
  --breadcrumb-margin-bottom: ;
  --breadcrumb-bg: ;
  --breadcrumb-border-radius: ;
  --breadcrumb-divider-color: var(--gray-600);
  --breadcrumb-item-padding-x: 0.5rem;
  --breadcrumb-item-active-color: var(--gray-600);
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding: var(--breadcrumb-padding-y) var(--breadcrumb-padding-x);
  margin-bottom: var(--breadcrumb-margin-bottom);
  font-size: var(--breadcrumb-font-size);
  list-style: none;
  background-color: var(--breadcrumb-bg);
  border-radius: var(--breadcrumb-border-radius);
}

.breadcrumb-item + .breadcrumb-item {
  padding-right: var(--breadcrumb-item-padding-x);
}
.breadcrumb-item + .breadcrumb-item::before {
  float: right;
  padding-left: var(--breadcrumb-item-padding-x);
  color: var(--breadcrumb-divider-color);
  content:  var(--breadcrumb-divider, "/") ;
}
.breadcrumb-item.active {
  color: var(--breadcrumb-item-active-color);
}

.pagination {
  --pagination-padding-x: 0.875rem;
  --pagination-padding-y: 0.5rem;
  --pagination-font-size: var(--body-font-size);
  --pagination-margin-start: calc(var(--border-width) * -1);
  --pagination-color: var(--body-color);
  --pagination-bg: var(--white);
  --pagination-border-width: var(--border-width);
  --pagination-border-color: var(--border-color);
  --pagination-border-radius: var(--border-radius);
  --pagination-hover-color: var(--body-color);
  --pagination-hover-bg: var(--gray-200);
  --pagination-hover-border-color: var(--gray-400);
  --pagination-focus-color: var(--body-color);
  --pagination-focus-bg: var(--gray-200);
  --pagination-focus-box-shadow: ;
  --pagination-active-color: var(--component-active-color);
  --pagination-active-bg: var(--component-active-bg);
  --pagination-active-border-color: var(--component-active-bg);
  --pagination-disabled-color: var(--gray-500);
  --pagination-disabled-bg: var(--white);
  --pagination-disabled-border-color: var(--border-color);
  display: -ms-flexbox;
  display: flex;
  padding-right: 0;
  list-style: none;
}

.page-link {
  position: relative;
  display: block;
  padding: var(--pagination-padding-y) var(--pagination-padding-x);
  font-size: var(--pagination-font-size);
  color: var(--pagination-color);
  background-color: var(--pagination-bg);
  border: var(--pagination-border-width) solid var(--pagination-border-color);
  transition: color var(--transition-base-timer) ease-in-out, background-color var(--transition-base-timer) ease-in-out, border-color var(--transition-base-timer) ease-in-out, box-shadow var(--transition-base-timer) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .page-link {
    transition: none;
  }
}
.page-link:hover {
  z-index: 2;
  color: var(--pagination-hover-color);
  background-color: var(--pagination-hover-bg);
  border-color: var(--pagination-hover-border-color);
}
.page-link:focus {
  z-index: 3;
  color: var(--pagination-focus-color);
  background-color: var(--pagination-focus-bg);
  outline: 0;
  box-shadow: var(--pagination-focus-box-shadow);
}
.page-link.active, .active > .page-link {
  z-index: 3;
  color: var(--pagination-active-color);
  background-color: var(--pagination-active-bg);
  border-color: var(--pagination-active-border-color);
}
.page-link.disabled, .disabled > .page-link {
  color: var(--pagination-disabled-color);
  pointer-events: none;
  background-color: var(--pagination-disabled-bg);
  border-color: var(--pagination-disabled-border-color);
}

.page-item:not(:first-child) .page-link {
  margin-right: var(--pagination-margin-start);
}
.page-item:first-child .page-link {
  border-top-right-radius: var(--pagination-border-radius);
  border-bottom-right-radius: var(--pagination-border-radius);
}
.page-item:last-child .page-link {
  border-top-left-radius: var(--pagination-border-radius);
  border-bottom-left-radius: var(--pagination-border-radius);
}

.pagination-lg {
  --pagination-padding-x: 1rem;
  --pagination-padding-y: 0.625rem;
  --pagination-font-size: 1rem;
  --pagination-border-radius: var(--border-radius-lg);
}

.pagination-sm {
  --pagination-padding-x: 0.75rem;
  --pagination-padding-y: 0.375rem;
  --pagination-font-size: 0.75rem;
  --pagination-border-radius: var(--border-radius-sm);
}

.badge {
  --badge-padding-x: 0.4375rem;
  --badge-padding-y: 0.3125rem;
  --badge-font-size: 0.75rem;
  --badge-font-weight: 600;
  --badge-color: #fff;
  --badge-border-radius: var(--border-radius-sm);
  display: inline-block;
  padding: var(--badge-padding-y) var(--badge-padding-x);
  font-size: var(--badge-font-size);
  font-weight: var(--badge-font-weight);
  line-height: 1;
  color: var(--badge-color);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--badge-border-radius);
}
.badge:empty {
  display: none;
}

.btn .badge {
  position: relative;
  top: -1px;
}

.alert {
  --alert-bg: transparent;
  --alert-padding-x: var(--spacer);
  --alert-padding-y: calc(var(--spacer) * 0.8);
  --alert-dismissible-padding-r: calc(var(--spacer) * 3);
  --alert-margin-bottom: var(--spacer);
  --alert-color: inherit;
  --alert-border-width: var(--border-width);
  --alert-border-color: transparent;
  --alert-border-radius: var(--border-radius);
  position: relative;
  padding: var(--alert-padding-y) var(--alert-padding-x);
  margin-bottom: var(--alert-margin-bottom);
  color: var(--alert-color);
  background-color: var(--alert-bg);
  border: var(--alert-border-width) solid var(--alert-border-color);
  border-radius: var(--alert-border-radius);
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 600;
}

.alert-dismissible {
  padding-left: var(--alert-dismissible-padding-r);
}
.alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  padding: calc(var(--alert-padding-y) * 1.25) var(--alert-padding-x);
}

.alert-indigo {
  --alert-color: #404b86;
  --alert-bg: #eff0f9;
  --alert-border-color: #aeb5e0;
  --alert-link-color: #333c6b;
  --alert-link-hover-color: #2d355e;
}
.alert-indigo .alert-link {
  color: var(--alert-link-color);
}
.alert-indigo .alert-link:hover, .alert-indigo .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-purple {
  --alert-color: #634e87;
  --alert-bg: #f4f1f9;
  --alert-border-color: #c7b8e0;
  --alert-link-color: #4f3e6c;
  --alert-link-hover-color: #45375f;
}
.alert-purple .alert-link {
  color: var(--alert-link-color);
}
.alert-purple .alert-link:hover, .alert-purple .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-pink {
  --alert-color: #aa405e;
  --alert-bg: #feeff3;
  --alert-border-color: #f9aec3;
  --alert-link-color: #88334b;
  --alert-link-hover-color: #772d42;
}
.alert-pink .alert-link {
  color: var(--alert-link-color);
}
.alert-pink .alert-link:hover, .alert-pink .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-teal {
  --alert-color: #1b746c;
  --alert-bg: #e9f6f5;
  --alert-border-color: #93d3cd;
  --alert-link-color: #165d56;
  --alert-link-hover-color: #13514c;
}
.alert-teal .alert-link {
  color: var(--alert-link-color);
}
.alert-teal .alert-link:hover, .alert-teal .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-yellow {
  --alert-color: #b39632;
  --alert-bg: #fffbed;
  --alert-border-color: #ffeba4;
  --alert-link-color: #8f7828;
  --alert-link-hover-color: #7d6923;
}
.alert-yellow .alert-link {
  color: var(--alert-link-color);
}
.alert-yellow .alert-link:hover, .alert-yellow .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-primary {
  --alert-color: #085cb3;
  --alert-bg: #e7f3ff;
  --alert-border-color: #86c1ff;
  --alert-link-color: #064a8f;
  --alert-link-hover-color: #06407d;
}
.alert-primary .alert-link {
  color: var(--alert-link-color);
}
.alert-primary .alert-link:hover, .alert-primary .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-secondary {
  --alert-color: #19506a;
  --alert-bg: #e9f1f5;
  --alert-border-color: #92b9cb;
  --alert-link-color: #144055;
  --alert-link-hover-color: #12384a;
}
.alert-secondary .alert-link {
  color: var(--alert-link-color);
}
.alert-secondary .alert-link:hover, .alert-secondary .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-success {
  --alert-color: #04694a;
  --alert-bg: #e6f5f0;
  --alert-border-color: #82cbb4;
  --alert-link-color: #03543b;
  --alert-link-hover-color: #034a34;
}
.alert-success .alert-link {
  color: var(--alert-link-color);
}
.alert-success .alert-link:hover, .alert-success .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-info {
  --alert-color: #036c79;
  --alert-bg: #e6f5f7;
  --alert-border-color: #82cdd6;
  --alert-link-color: #025661;
  --alert-link-hover-color: #024c55;
}
.alert-info .alert-link {
  color: var(--alert-link-color);
}
.alert-info .alert-link:hover, .alert-info .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-warning {
  --alert-color: #ac5e31;
  --alert-bg: #fef3ed;
  --alert-border-color: #fac3a3;
  --alert-link-color: #8a4b27;
  --alert-link-hover-color: #784222;
}
.alert-warning .alert-link {
  color: var(--alert-link-color);
}
.alert-warning .alert-link:hover, .alert-warning .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-danger {
  --alert-color: #a73030;
  --alert-bg: #fdecec;
  --alert-border-color: #f7a2a2;
  --alert-link-color: #862626;
  --alert-link-hover-color: #752222;
}
.alert-danger .alert-link {
  color: var(--alert-link-color);
}
.alert-danger .alert-link:hover, .alert-danger .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-light {
  --alert-color: #49494a;
  --alert-bg: #fefefe;
  --alert-border-color: #f9fafb;
  --alert-link-color: #3a3a3b;
  --alert-link-hover-color: #333334;
}
.alert-light .alert-link {
  color: var(--alert-link-color);
}
.alert-light .alert-link:hover, .alert-light .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-dark {
  --alert-color: #1a1e26;
  --alert-bg: #e9eaeb;
  --alert-border-color: #92959b;
  --alert-link-color: #15181e;
  --alert-link-hover-color: #12151b;
}
.alert-dark .alert-link {
  color: var(--alert-link-color);
}
.alert-dark .alert-link:hover, .alert-dark .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-black {
  --alert-color: black;
  --alert-bg: #e6e6e6;
  --alert-border-color: gray;
  --alert-link-color: black;
  --alert-link-hover-color: black;
}
.alert-black .alert-link {
  color: var(--alert-link-color);
}
.alert-black .alert-link:hover, .alert-black .alert-link:focus {
  color: var(--alert-link-hover-color);
}

.alert-white {
  --alert-color: #4d4d4d;
  --alert-bg: white;
  --alert-border-color: white;
  --alert-link-color: #3e3e3e;
  --alert-link-hover-color: #363636;
}
.alert-white .alert-link {
  color: var(--alert-link-color);
}
.alert-white .alert-link:hover, .alert-white .alert-link:focus {
  color: var(--alert-link-hover-color);
}

@-webkit-keyframes progress-bar-stripes {
  0% {
    background-position-x: 1.25rem;
  }
}

@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1.25rem;
  }
}
.progress {
  --progress-height: 1.25rem;
  --progress-font-size: calc(var(--body-font-size) * 0.85);
  --progress-bg: var(--gray-200);
  --progress-border-radius: var(--border-radius);
  --progress-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --progress-bar-color: var(--white);
  --progress-bar-bg: var(--primary);
  --progress-bar-transition: width 0.6s ease;
  display: -ms-flexbox;
  display: flex;
  height: var(--progress-height);
  overflow: hidden;
  font-size: var(--progress-font-size);
  background-color: var(--progress-bg);
  border-radius: var(--progress-border-radius);
  box-shadow: var(--progress-box-shadow);
}

.progress-bar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-pack: center;
      justify-content: center;
  overflow: hidden;
  color: var(--progress-bar-color);
  text-align: center;
  white-space: nowrap;
  background-color: var(--progress-bar-bg);
  transition: var(--progress-bar-transition);
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.progress-bar-striped {
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: var(--progress-height) var(--progress-height);
}

.progress-bar-animated {
  -webkit-animation: 1s linear infinite progress-bar-stripes;
          animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    -webkit-animation: none;
            animation: none;
  }
}

.list-group {
  --list-group-color: var(--body-color);
  --list-group-bg: transparent;
  --list-group-border-color: var(--border-color);
  --list-group-border-width: var(--border-width);
  --list-group-border-radius: var(--border-radius);
  --list-group-item-padding-x: var(--spacer);
  --list-group-item-padding-y: calc(var(--spacer) * 0.5);
  --list-group-action-color: var(--body-color);
  --list-group-action-hover-color: var(--body-color);
  --list-group-action-hover-bg: var(--gray-200);
  --list-group-action-active-color: var(--body-color);
  --list-group-action-active-bg: var(--gray-300);
  --list-group-disabled-color: var(--gray-600);
  --list-group-disabled-bg: transparent;
  --list-group-active-color: var(--component-active-color);
  --list-group-active-bg: var(--component-active-bg);
  --list-group-active-border-color: var(--component-active-bg);
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  padding-right: 0;
  margin-bottom: 0;
  border-radius: var(--list-group-border-radius);
}

.list-group-numbered {
  list-style-type: none;
  counter-reset: section;
}
.list-group-numbered > .list-group-item::before {
  content: counters(section, ".") ". ";
  counter-increment: section;
}

.list-group-item-action {
  width: 100%;
  color: var(--list-group-action-color);
  text-align: inherit;
}
.list-group-item-action:hover, .list-group-item-action:focus {
  z-index: 1;
  color: var(--list-group-action-hover-color);
  text-decoration: none;
  background-color: var(--list-group-action-hover-bg);
}
.list-group-item-action:active {
  color: var(--list-group-action-active-color);
  background-color: var(--list-group-action-active-bg);
}

.list-group-item {
  position: relative;
  display: block;
  padding: var(--list-group-item-padding-y) var(--list-group-item-padding-x);
  color: var(--list-group-color);
  background-color: var(--list-group-bg);
  border: var(--list-group-border-width) solid var(--list-group-border-color);
}
.list-group-item:first-child {
  border-top-right-radius: inherit;
  border-top-left-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
}
.list-group-item.disabled, .list-group-item:disabled {
  color: var(--list-group-disabled-color);
  pointer-events: none;
  background-color: var(--list-group-disabled-bg);
}
.list-group-item.active {
  z-index: 2;
  color: var(--list-group-active-color);
  background-color: var(--list-group-active-bg);
  border-color: var(--list-group-active-border-color);
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  margin-top: calc(-1 * var(--list-group-border-width));
  border-top-width: var(--list-group-border-width);
}

.list-group-horizontal {
  -ms-flex-direction: row;
      flex-direction: row;
}
.list-group-horizontal > .list-group-item:first-child:not(:last-child) {
  border-bottom-right-radius: var(--list-group-border-radius);
  border-top-left-radius: 0;
}
.list-group-horizontal > .list-group-item:last-child:not(:first-child) {
  border-top-left-radius: var(--list-group-border-radius);
  border-bottom-right-radius: 0;
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: var(--list-group-border-width);
  border-right-width: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-right: calc(-1 * var(--list-group-border-width));
  border-right-width: var(--list-group-border-width);
}

@media (min-width: 576px) {
  .list-group-horizontal-sm {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .list-group-horizontal-sm > .list-group-item:first-child:not(:last-child) {
    border-bottom-right-radius: var(--list-group-border-radius);
    border-top-left-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item:last-child:not(:first-child) {
    border-top-left-radius: var(--list-group-border-radius);
    border-bottom-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: var(--list-group-border-width);
    border-right-width: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-right: calc(-1 * var(--list-group-border-width));
    border-right-width: var(--list-group-border-width);
  }
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .list-group-horizontal-md > .list-group-item:first-child:not(:last-child) {
    border-bottom-right-radius: var(--list-group-border-radius);
    border-top-left-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item:last-child:not(:first-child) {
    border-top-left-radius: var(--list-group-border-radius);
    border-bottom-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: var(--list-group-border-width);
    border-right-width: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-right: calc(-1 * var(--list-group-border-width));
    border-right-width: var(--list-group-border-width);
  }
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .list-group-horizontal-lg > .list-group-item:first-child:not(:last-child) {
    border-bottom-right-radius: var(--list-group-border-radius);
    border-top-left-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item:last-child:not(:first-child) {
    border-top-left-radius: var(--list-group-border-radius);
    border-bottom-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: var(--list-group-border-width);
    border-right-width: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-right: calc(-1 * var(--list-group-border-width));
    border-right-width: var(--list-group-border-width);
  }
}
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .list-group-horizontal-xl > .list-group-item:first-child:not(:last-child) {
    border-bottom-right-radius: var(--list-group-border-radius);
    border-top-left-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item:last-child:not(:first-child) {
    border-top-left-radius: var(--list-group-border-radius);
    border-bottom-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: var(--list-group-border-width);
    border-right-width: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-right: calc(-1 * var(--list-group-border-width));
    border-right-width: var(--list-group-border-width);
  }
}
@media (min-width: 1400px) {
  .list-group-horizontal-xxl {
    -ms-flex-direction: row;
        flex-direction: row;
  }
  .list-group-horizontal-xxl > .list-group-item:first-child:not(:last-child) {
    border-bottom-right-radius: var(--list-group-border-radius);
    border-top-left-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item:last-child:not(:first-child) {
    border-top-left-radius: var(--list-group-border-radius);
    border-bottom-right-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item {
    border-top-width: var(--list-group-border-width);
    border-right-width: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    margin-right: calc(-1 * var(--list-group-border-width));
    border-right-width: var(--list-group-border-width);
  }
}
.list-group-flush {
  border-radius: 0;
}
.list-group-flush > .list-group-item {
  border-width: 0 0 var(--list-group-border-width);
}
.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}

.list-group-item-indigo {
  color: #374073;
  background-color: #dee1f2;
}
.list-group-item-indigo.list-group-item-action:hover, .list-group-item-indigo.list-group-item-action:focus {
  color: #374073;
  background-color: #c8cbda;
}
.list-group-item-indigo.list-group-item-action.active {
  color: #fff;
  background-color: #374073;
  border-color: #374073;
}

.list-group-item-purple {
  color: #554374;
  background-color: #e8e2f3;
}
.list-group-item-purple.list-group-item-action:hover, .list-group-item-purple.list-group-item-action:focus {
  color: #554374;
  background-color: #d1cbdb;
}
.list-group-item-purple.list-group-item-action.active {
  color: #fff;
  background-color: #554374;
  border-color: #554374;
}

.list-group-item-pink {
  color: #923750;
  background-color: #fddee7;
}
.list-group-item-pink.list-group-item-action:hover, .list-group-item-pink.list-group-item-action:focus {
  color: #923750;
  background-color: #e4c8d0;
}
.list-group-item-pink.list-group-item-action.active {
  color: #fff;
  background-color: #923750;
  border-color: #923750;
}

.list-group-item-teal {
  color: #17645c;
  background-color: #d4edeb;
}
.list-group-item-teal.list-group-item-action:hover, .list-group-item-teal.list-group-item-action:focus {
  color: #17645c;
  background-color: #bfd5d4;
}
.list-group-item-teal.list-group-item-action.active {
  color: #fff;
  background-color: #17645c;
  border-color: #17645c;
}

.list-group-item-yellow {
  color: #99802b;
  background-color: #fff7da;
}
.list-group-item-yellow.list-group-item-action:hover, .list-group-item-yellow.list-group-item-action:focus {
  color: #99802b;
  background-color: #e6dec4;
}
.list-group-item-yellow.list-group-item-action.active {
  color: #fff;
  background-color: #99802b;
  border-color: #99802b;
}

.list-group-item-primary {
  color: #074f99;
  background-color: #cee6ff;
}
.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {
  color: #074f99;
  background-color: #b9cfe6;
}
.list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #074f99;
  border-color: #074f99;
}

.list-group-item-secondary {
  color: #16445b;
  background-color: #d3e3ea;
}
.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus {
  color: #16445b;
  background-color: #beccd3;
}
.list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #16445b;
  border-color: #16445b;
}

.list-group-item-success {
  color: #035a3f;
  background-color: #cdeae1;
}
.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus {
  color: #035a3f;
  background-color: #b9d3cb;
}
.list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #035a3f;
  border-color: #035a3f;
}

.list-group-item-info {
  color: #025c68;
  background-color: #cdebef;
}
.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus {
  color: #025c68;
  background-color: #b9d4d7;
}
.list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #025c68;
  border-color: #025c68;
}

.list-group-item-warning {
  color: #93502a;
  background-color: #fde7da;
}
.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus {
  color: #93502a;
  background-color: #e4d0c4;
}
.list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #93502a;
  border-color: #93502a;
}

.list-group-item-danger {
  color: #8f2929;
  background-color: #fcdada;
}
.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus {
  color: #8f2929;
  background-color: #e3c4c4;
}
.list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #8f2929;
  border-color: #8f2929;
}

.list-group-item-light {
  color: #929294;
  background-color: #fdfdfd;
}
.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus {
  color: #929294;
  background-color: #e4e4e4;
}
.list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #929294;
  border-color: #929294;
}

.list-group-item-dark {
  color: #161a20;
  background-color: #d3d5d7;
}
.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus {
  color: #161a20;
  background-color: #bec0c2;
}
.list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #161a20;
  border-color: #161a20;
}

.list-group-item-black {
  color: black;
  background-color: #cccccc;
}
.list-group-item-black.list-group-item-action:hover, .list-group-item-black.list-group-item-action:focus {
  color: black;
  background-color: #b8b8b8;
}
.list-group-item-black.list-group-item-action.active {
  color: #fff;
  background-color: black;
  border-color: black;
}

.list-group-item-white {
  color: #999999;
  background-color: white;
}
.list-group-item-white.list-group-item-action:hover, .list-group-item-white.list-group-item-action:focus {
  color: #999999;
  background-color: #e6e6e6;
}
.list-group-item-white.list-group-item-action.active {
  color: #fff;
  background-color: #999999;
  border-color: #999999;
}

.btn-close {
  --btn-close-width: 1em;
  --btn-close-height: 1em;
  --btn-close-padding-y: 0.25em;
  --btn-close-padding-x: 0.25em;
  --btn-close-color: #000;
  --btn-close-bg: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-2 -2 20 20' fill='#000'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>");
  --btn-close-opacity: 0.65;
  --btn-close-hover-opacity: 1;
  --btn-close-focus-opacity: 1;
  --btn-close-disabled-opacity: 0.25;
  box-sizing: content-box;
  width: var(--btn-close-width);
  height: var(--btn-close-height);
  padding: var(--btn-close-padding-y) var(--btn-close-padding-x);
  color: var(--btn-close-color);
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-2 -2 20 20' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/var(--btn-close-width) auto no-repeat;
  border: 0;
  border-radius: 0.375rem;
  opacity: var(--btn-close-opacity);
}
.btn-close:hover {
  color: var(--btn-close-color);
  text-decoration: none;
  opacity: var(--btn-close-hover-opacity);
}
.btn-close:focus {
  outline: 0;
  opacity: var(--btn-close-focus-opacity);
}
.btn-close:disabled, .btn-close.disabled {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  opacity: var(--btn-close-disabled-opacity);
}

.btn-close-white {
  -webkit-filter: invert(1) grayscale(100%) brightness(200%);
          filter: invert(1) grayscale(100%) brightness(200%);
}

.toast {
  --toast-zindex: 1090;
  --toast-padding-x: 1rem;
  --toast-padding-y: 0.75rem;
  --toast-spacing: var(--spacer);
  --toast-max-width: 350px;
  --toast-font-size: var(--body-font-size);
  --toast-color: ;
  --toast-bg: var(--white);
  --toast-border-width: var(--border-width);
  --toast-border-color: var(--border-color-translucent);
  --toast-border-radius: var(--border-radius);
  --toast-box-shadow: var(--box-shadow);
  --toast-header-color: var(--body-color);
  --toast-header-bg: var(--gray-100);
  --toast-header-border-color: var(--border-color-translucent);
  width: var(--toast-max-width);
  max-width: 100%;
  font-size: var(--toast-font-size);
  color: var(--toast-color);
  pointer-events: auto;
  background-color: var(--toast-bg);
  background-clip: padding-box;
  border: var(--toast-border-width) solid var(--toast-border-color);
  box-shadow: var(--toast-box-shadow);
  border-radius: var(--toast-border-radius);
}
.toast.showing {
  opacity: 0;
}
.toast:not(.show) {
  display: none;
}

.toast-container {
  --toast-zindex: 1090;
  position: absolute;
  z-index: var(--toast-zindex);
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: 100%;
  pointer-events: none;
}
.toast-container > :not(:last-child) {
  margin-bottom: var(--toast-spacing);
}

.toast-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  padding: var(--toast-padding-y) var(--toast-padding-x);
  color: var(--toast-header-color);
  background-color: var(--toast-header-bg);
  background-clip: padding-box;
  border-bottom: var(--toast-border-width) solid var(--toast-header-border-color);
  border-top-right-radius: calc(var(--toast-border-radius) - var(--toast-border-width));
  border-top-left-radius: calc(var(--toast-border-radius) - var(--toast-border-width));
}
.toast-header .btn-close {
  margin-left: calc(-0.5 * var(--toast-padding-x));
  margin-right: var(--toast-padding-x);
}

.toast-body {
  padding: var(--toast-padding-x);
  word-wrap: break-word;
}

.modal {
  --modal-zindex: 1055;
  --modal-width: 600px;
  --modal-padding: var(--spacer);
  --modal-margin: 0.5rem;
  --modal-color: ;
  --modal-bg: var(--white);
  --modal-border-color: var(--border-color-translucent);
  --modal-border-width: var(--border-width);
  --modal-border-radius: var(--border-radius-lg);
  --modal-box-shadow: var(--box-shadow-sm);
  --modal-inner-border-radius: calc(var(--modal-border-radius) - (var(--modal-border-width)));
  --modal-header-padding-x: var(--spacer);
  --modal-header-padding-y: var(--spacer);
  --modal-header-padding: var(--spacer) var(--spacer);
  --modal-header-border-color: var(--border-color);
  --modal-header-border-width: var(--border-width);
  --modal-title-line-height: var(--body-line-height);
  --modal-footer-gap: var(--spacer-2);
  --modal-footer-bg: ;
  --modal-footer-border-color: var(--border-color);
  --modal-footer-border-width: var(--border-width);
  position: fixed;
  top: 0;
  right: 0;
  z-index: var(--modal-zindex);
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: var(--modal-margin);
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: -webkit-transform calc(var(--transition-base-timer) * 2) ease-out;
  transition: transform calc(var(--transition-base-timer) * 2) ease-out;
  transition: transform calc(var(--transition-base-timer) * 2) ease-out, -webkit-transform calc(var(--transition-base-timer) * 2) ease-out;
  -webkit-transform: translate(0, -50px);
          transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  -webkit-transform: none;
          transform: none;
}
.modal.modal-static .modal-dialog {
  -webkit-transform: scale(1.02);
          transform: scale(1.02);
}

.modal-dialog-scrollable {
  height: calc(100% - var(--modal-margin) * 2);
}
.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  min-height: calc(100% - var(--modal-margin) * 2);
}

.modal-content {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  width: 100%;
  color: var(--modal-color);
  pointer-events: auto;
  background-color: var(--modal-bg);
  background-clip: padding-box;
  border: var(--modal-border-width) solid var(--modal-border-color);
  border-radius: var(--modal-border-radius);
  box-shadow: var(--modal-box-shadow);
  outline: 0;
}

.modal-backdrop {
  --backdrop-zindex: 1050;
  --backdrop-bg: var(--black);
  --backdrop-opacity: 0.35;
  position: fixed;
  top: 0;
  right: 0;
  z-index: var(--backdrop-zindex);
  width: 100vw;
  height: 100vh;
  background-color: var(--backdrop-bg);
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: var(--backdrop-opacity);
}

.modal-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: justify;
      justify-content: space-between;
  padding: var(--modal-header-padding);
  border-bottom: var(--modal-header-border-width) solid var(--modal-header-border-color);
  border-top-right-radius: var(--modal-inner-border-radius);
  border-top-left-radius: var(--modal-inner-border-radius);
}
.modal-header .btn-close {
  padding: calc(var(--modal-header-padding-y) * 0.5) calc(var(--modal-header-padding-x) * 0.5);
  margin: calc(-0.5 * var(--modal-header-padding-y)) auto calc(-0.5 * var(--modal-header-padding-y)) calc(-0.5 * var(--modal-header-padding-x));
}

.modal-title {
  margin-bottom: 0;
  line-height: var(--modal-title-line-height);
}

.modal-body {
  position: relative;
  -ms-flex: 1 1 auto;
      flex: 1 1 auto;
  padding: var(--modal-padding);
}

.modal-footer {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: end;
      justify-content: flex-end;
  padding: calc(var(--modal-padding) - var(--modal-footer-gap) * 0.5);
  background-color: var(--modal-footer-bg);
  border-top: var(--modal-footer-border-width) solid var(--modal-footer-border-color);
  border-bottom-left-radius: var(--modal-inner-border-radius);
  border-bottom-right-radius: var(--modal-inner-border-radius);
}
.modal-footer > * {
  margin: calc(var(--modal-footer-gap) * 0.5);
}

@media (min-width: 576px) {
  .modal {
    --modal-margin: 1.75rem;
    --modal-box-shadow: var(--box-shadow);
  }
  .modal-dialog {
    max-width: var(--modal-width);
    margin-left: auto;
    margin-right: auto;
  }
  .modal-sm {
    --modal-width: 400px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
.modal-xl {
    --modal-width: 900px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    --modal-width: 1140px;
  }
}
.modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
.modal-fullscreen .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}
.modal-fullscreen .modal-header,
.modal-fullscreen .modal-footer {
  border-radius: 0;
}
.modal-fullscreen .modal-body {
  overflow-y: auto;
}

@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-header,
.modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 767.98px) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-header,
.modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-header,
.modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1199.98px) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-header,
.modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1399.98px) {
  .modal-fullscreen-xxl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xxl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-header,
.modal-fullscreen-xxl-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-body {
    overflow-y: auto;
  }
}
.tooltip {
  --tooltip-zindex: 1080;
  --tooltip-max-width: 200px;
  --tooltip-padding-x: calc(var(--spacer) * 0.6);
  --tooltip-padding-y: calc(var(--spacer) * 0.4);
  --tooltip-margin: ;
  --tooltip-font-size: var(--body-font-size);
  --tooltip-color: var(--white);
  --tooltip-bg: var(--black);
  --tooltip-border-radius: var(--border-radius);
  --tooltip-opacity: 0.95;
  --tooltip-arrow-width: 0.8rem;
  --tooltip-arrow-height: 0.4rem;
  z-index: var(--tooltip-zindex);
  display: block;
  padding: var(--tooltip-arrow-height);
  margin: var(--tooltip-margin);
  font-family: var(--font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5715;
  text-align: right;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--tooltip-font-size);
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: var(--tooltip-opacity);
}
.tooltip .tooltip-arrow {
  display: block;
  width: var(--tooltip-arrow-width);
  height: var(--tooltip-arrow-height);
}
.tooltip .tooltip-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow {
  bottom: 0;
}
.bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before {
  top: -1px;
  border-width: var(--tooltip-arrow-height) calc(var(--tooltip-arrow-width) * 0.5) 0;
  border-top-color: var(--tooltip-bg);
}
.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow {
  left: 0;
  width: var(--tooltip-arrow-height);
  height: var(--tooltip-arrow-width);
}
.bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before {
  right: -1px;
  border-width: calc(var(--tooltip-arrow-width) * 0.5) var(--tooltip-arrow-height) calc(var(--tooltip-arrow-width) * 0.5) 0;
  border-right-color: var(--tooltip-bg);
}
.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow {
  top: 0;
}
.bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before {
  bottom: -1px;
  border-width: 0 calc(var(--tooltip-arrow-width) * 0.5) var(--tooltip-arrow-height);
  border-bottom-color: var(--tooltip-bg);
}
.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow {
  right: 0;
  width: var(--tooltip-arrow-height);
  height: var(--tooltip-arrow-width);
}
.bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before {
  left: -1px;
  border-width: calc(var(--tooltip-arrow-width) * 0.5) 0 calc(var(--tooltip-arrow-width) * 0.5) var(--tooltip-arrow-height);
  border-left-color: var(--tooltip-bg);
}
.tooltip-inner {
  max-width: var(--tooltip-max-width);
  padding: var(--tooltip-padding-y) var(--tooltip-padding-x);
  color: var(--tooltip-color);
  text-align: center;
  background-color: var(--tooltip-bg);
  border-radius: var(--tooltip-border-radius);
}

.popover {
  --popover-zindex: 1070;
  --popover-max-width: 276px;
  --popover-font-size: var(--body-font-size);
  --popover-bg: var(--white);
  --popover-border-width: var(--border-width);
  --popover-border-color: var(--border-color-translucent);
  --popover-border-radius: var(--border-radius);
  --popover-inner-border-radius: calc(var(--border-radius) - (var(--border-width)));
  --popover-box-shadow: var(--box-shadow);
  --popover-header-padding-x: var(--spacer);
  --popover-header-padding-y: 0.75rem;
  --popover-header-font-size: var(--body-font-size);
  --popover-header-color: var(--body-color);
  --popover-header-bg: var(--gray-100);
  --popover-body-padding-x: var(--spacer);
  --popover-body-padding-y: calc(var(--spacer) * 0.75);
  --popover-body-color: var(--body-color);
  --popover-arrow-width: 1rem;
  --popover-arrow-height: 0.5rem;
  --popover-arrow-border: var(--popover-border-color);
  z-index: var(--popover-zindex);
  display: block;
  max-width: var(--popover-max-width);
  font-family: var(--font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5715;
  text-align: right;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--popover-font-size);
  word-wrap: break-word;
  background-color: var(--popover-bg);
  background-clip: padding-box;
  border: var(--popover-border-width) solid var(--popover-border-color);
  border-radius: var(--popover-border-radius);
  box-shadow: var(--popover-box-shadow);
}
.popover .popover-arrow {
  display: block;
  width: var(--popover-arrow-width);
  height: var(--popover-arrow-height);
}
.popover .popover-arrow::before, .popover .popover-arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
  border-width: 0;
}

.bs-popover-top > .popover-arrow, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow {
  bottom: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width));
}
.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before, .bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
  border-width: var(--popover-arrow-height) calc(var(--popover-arrow-width) * 0.5) 0;
}
.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before {
  bottom: 0;
  border-top-color: var(--popover-arrow-border);
}
.bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
  bottom: var(--popover-border-width);
  border-top-color: var(--popover-bg);
}
.bs-popover-end > .popover-arrow, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow {
  left: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width));
  width: var(--popover-arrow-height);
  height: var(--popover-arrow-width);
}
.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before, .bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
  border-width: calc(var(--popover-arrow-width) * 0.5) var(--popover-arrow-height) calc(var(--popover-arrow-width) * 0.5) 0;
}
.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before {
  left: 0;
  border-right-color: var(--popover-arrow-border);
}
.bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
  left: var(--popover-border-width);
  border-right-color: var(--popover-bg);
}
.bs-popover-bottom > .popover-arrow, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow {
  top: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width));
}
.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before, .bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
  border-width: 0 calc(var(--popover-arrow-width) * 0.5) var(--popover-arrow-height);
}
.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before {
  top: 0;
  border-bottom-color: var(--popover-arrow-border);
}
.bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
  top: var(--popover-border-width);
  border-bottom-color: var(--popover-bg);
}
.bs-popover-bottom .popover-header::before, .bs-popover-auto[data-popper-placement^=bottom] .popover-header::before {
  position: absolute;
  top: 0;
  right: 50%;
  display: block;
  width: var(--popover-arrow-width);
  margin-right: calc(-0.5 * var(--popover-arrow-width));
  content: "";
  border-bottom: var(--popover-border-width) solid var(--popover-header-bg);
}
.bs-popover-start > .popover-arrow, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow {
  right: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width));
  width: var(--popover-arrow-height);
  height: var(--popover-arrow-width);
}
.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before, .bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
  border-width: calc(var(--popover-arrow-width) * 0.5) 0 calc(var(--popover-arrow-width) * 0.5) var(--popover-arrow-height);
}
.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before {
  right: 0;
  border-left-color: var(--popover-arrow-border);
}
.bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
  right: var(--popover-border-width);
  border-left-color: var(--popover-bg);
}
.popover-header {
  padding: var(--popover-header-padding-y) var(--popover-header-padding-x);
  margin-bottom: 0;
  font-size: var(--popover-header-font-size);
  color: var(--popover-header-color);
  background-color: var(--popover-header-bg);
  border-bottom: var(--popover-border-width) solid var(--popover-border-color);
  border-top-right-radius: var(--popover-inner-border-radius);
  border-top-left-radius: var(--popover-inner-border-radius);
}
.popover-header:empty {
  display: none;
}

.popover-body {
  padding: var(--popover-body-padding-y) var(--popover-body-padding-x);
  color: var(--popover-body-color);
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  -ms-touch-action: pan-y;
      touch-action: pan-y;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}

.carousel-item {
  position: relative;
  display: none;
  float: right;
  width: 100%;
  margin-left: -100%;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  transition: -webkit-transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}
.carousel-item-next:not(.carousel-item-start),
.active.carousel-item-end {
  -webkit-transform: translateX(100%);
          transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-end),
.active.carousel-item-start {
  -webkit-transform: translateX(-100%);
          transform: translateX(-100%);
}
.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  -webkit-transform: none;
          transform: none;
}
.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
    transition: none;
  }
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  width: 6.25rem;
  padding: 0;
  color: #fff;
  text-align: center;
  background: none;
  border: 0;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
.carousel-control-next {
    transition: none;
  }
}
.carousel-control-prev:hover, .carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  right: 0;
}

.carousel-control-next {
  left: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%;
}
.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.carousel-indicators {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 2;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
      justify-content: center;
  padding: 0;
  margin-left: 6.25rem;
  margin-bottom: 1rem;
  margin-right: 6.25rem;
  list-style: none;
}
.carousel-indicators [data-bs-target] {
  box-sizing: content-box;
  -ms-flex: 0 1 auto;
      flex: 0 1 auto;
  width: 30px;
  height: 3px;
  padding: 0;
  margin-left: 3px;
  margin-right: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators [data-bs-target] {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}

.carousel-caption {
  position: absolute;
  left: 15%;
  bottom: 1.25rem;
  right: 15%;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  color: #fff;
  text-align: center;
}

.carousel-dark .carousel-control-next-icon,
.carousel-dark .carousel-control-prev-icon {
  -webkit-filter: invert(1) grayscale(100);
          filter: invert(1) grayscale(100);
}
.carousel-dark .carousel-indicators [data-bs-target] {
  background-color: #000;
}
.carousel-dark .carousel-caption {
  color: #000;
}

.spinner-grow,
.spinner-border {
  display: inline-block;
  width: var(--spinner-width);
  height: var(--spinner-height);
  vertical-align: var(--spinner-vertical-align);
  border-radius: 50%;
  -webkit-animation: var(--spinner-animation-speed) linear infinite var(--spinner-animation-name);
          animation: var(--spinner-animation-speed) linear infinite var(--spinner-animation-name);
}

@-webkit-keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg) ;
            transform: rotate(360deg) ;
  }
}

@keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg) ;
            transform: rotate(360deg) ;
  }
}
.spinner-border {
  --spinner-width: var(--icon-font-size);
  --spinner-height: var(--icon-font-size);
  --spinner-vertical-align: -0.125em;
  --spinner-border-width: 0.15em;
  --spinner-animation-speed: 1s;
  --spinner-animation-name: spinner-border;
  border: var(--spinner-border-width) solid currentcolor;
  border-left-color: transparent;
}

.spinner-border-sm {
  --spinner-width: var(--icon-font-size-sm);
  --spinner-height: var(--icon-font-size-sm);
  --spinner-border-width: 0.1em;
}

@-webkit-keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
            transform: scale(0);
  }
  50% {
    opacity: 1;
    -webkit-transform: none;
            transform: none;
  }
}

@keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
            transform: scale(0);
  }
  50% {
    opacity: 1;
    -webkit-transform: none;
            transform: none;
  }
}
.spinner-grow {
  --spinner-width: var(--icon-font-size);
  --spinner-height: var(--icon-font-size);
  --spinner-vertical-align: -0.125em;
  --spinner-animation-speed: 1s;
  --spinner-animation-name: spinner-grow;
  background-color: currentcolor;
  opacity: 0;
}

.spinner-grow-sm {
  --spinner-width: var(--icon-font-size-sm);
  --spinner-height: var(--icon-font-size-sm);
}

@media (prefers-reduced-motion: reduce) {
  .spinner-border,
.spinner-grow {
    --spinner-animation-speed: 2s;
  }
}
.offcanvas, .offcanvas-xxl, .offcanvas-xl, .offcanvas-lg, .offcanvas-md, .offcanvas-sm {
  --offcanvas-zindex: 1045;
  --offcanvas-width: 380px;
  --offcanvas-height: auto;
  --offcanvas-padding-x: var(--spacer);
  --offcanvas-padding-y: var(--spacer);
  --offcanvas-color: ;
  --offcanvas-bg: var(--white);
  --offcanvas-border-width: ;
  --offcanvas-border-color: ;
  --offcanvas-box-shadow: var(--box-shadow);
}

@media (max-width: 575.98px) {
  .offcanvas-sm {
    position: fixed;
    bottom: 0;
    z-index: var(--offcanvas-zindex);
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
        flex-direction: column;
    max-width: 100%;
    color: var(--offcanvas-color);
    visibility: hidden;
    background-color: var(--offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    box-shadow: var(--offcanvas-box-shadow);
    transition: -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
    transition: transform calc(var(--transition-base-timer) * 2) ease-in-out;
    transition: transform calc(var(--transition-base-timer) * 2) ease-in-out, -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
  }
}
@media (max-width: 575.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-sm {
    transition: none;
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-start {
    top: 0;
    right: 0;
    width: var(--offcanvas-width);
    border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-end {
    top: 0;
    left: 0;
    width: var(--offcanvas-width);
    border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-top {
    top: 0;
    left: 0;
    right: 0;
    height: var(--offcanvas-height);
    max-height: 100%;
    border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-bottom {
    left: 0;
    right: 0;
    height: var(--offcanvas-height);
    max-height: 100%;
    border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.showing, .offcanvas-sm.show:not(.hiding) {
    -webkit-transform: none;
            transform: none;
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.showing, .offcanvas-sm.hiding, .offcanvas-sm.show {
    visibility: visible;
  }
}
@media (min-width: 576px) {
  .offcanvas-sm {
    --offcanvas-height: auto;
    --offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-sm .offcanvas-header {
    display: none;
  }
  .offcanvas-sm .offcanvas-body {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-positive: 0;
        flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 767.98px) {
  .offcanvas-md {
    position: fixed;
    bottom: 0;
    z-index: var(--offcanvas-zindex);
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
        flex-direction: column;
    max-width: 100%;
    color: var(--offcanvas-color);
    visibility: hidden;
    background-color: var(--offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    box-shadow: var(--offcanvas-box-shadow);
    transition: -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
    transition: transform calc(var(--transition-base-timer) * 2) ease-in-out;
    transition: transform calc(var(--transition-base-timer) * 2) ease-in-out, -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
  }
}
@media (max-width: 767.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-md {
    transition: none;
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-start {
    top: 0;
    right: 0;
    width: var(--offcanvas-width);
    border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-end {
    top: 0;
    left: 0;
    width: var(--offcanvas-width);
    border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-top {
    top: 0;
    left: 0;
    right: 0;
    height: var(--offcanvas-height);
    max-height: 100%;
    border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-bottom {
    left: 0;
    right: 0;
    height: var(--offcanvas-height);
    max-height: 100%;
    border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.showing, .offcanvas-md.show:not(.hiding) {
    -webkit-transform: none;
            transform: none;
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.showing, .offcanvas-md.hiding, .offcanvas-md.show {
    visibility: visible;
  }
}
@media (min-width: 768px) {
  .offcanvas-md {
    --offcanvas-height: auto;
    --offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-md .offcanvas-header {
    display: none;
  }
  .offcanvas-md .offcanvas-body {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-positive: 0;
        flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 991.98px) {
  .offcanvas-lg {
    position: fixed;
    bottom: 0;
    z-index: var(--offcanvas-zindex);
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
        flex-direction: column;
    max-width: 100%;
    color: var(--offcanvas-color);
    visibility: hidden;
    background-color: var(--offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    box-shadow: var(--offcanvas-box-shadow);
    transition: -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
    transition: transform calc(var(--transition-base-timer) * 2) ease-in-out;
    transition: transform calc(var(--transition-base-timer) * 2) ease-in-out, -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
  }
}
@media (max-width: 991.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-lg {
    transition: none;
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-start {
    top: 0;
    right: 0;
    width: var(--offcanvas-width);
    border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-end {
    top: 0;
    left: 0;
    width: var(--offcanvas-width);
    border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-top {
    top: 0;
    left: 0;
    right: 0;
    height: var(--offcanvas-height);
    max-height: 100%;
    border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-bottom {
    left: 0;
    right: 0;
    height: var(--offcanvas-height);
    max-height: 100%;
    border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.showing, .offcanvas-lg.show:not(.hiding) {
    -webkit-transform: none;
            transform: none;
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.showing, .offcanvas-lg.hiding, .offcanvas-lg.show {
    visibility: visible;
  }
}
@media (min-width: 992px) {
  .offcanvas-lg {
    --offcanvas-height: auto;
    --offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-lg .offcanvas-header {
    display: none;
  }
  .offcanvas-lg .offcanvas-body {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-positive: 0;
        flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 1199.98px) {
  .offcanvas-xl {
    position: fixed;
    bottom: 0;
    z-index: var(--offcanvas-zindex);
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
        flex-direction: column;
    max-width: 100%;
    color: var(--offcanvas-color);
    visibility: hidden;
    background-color: var(--offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    box-shadow: var(--offcanvas-box-shadow);
    transition: -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
    transition: transform calc(var(--transition-base-timer) * 2) ease-in-out;
    transition: transform calc(var(--transition-base-timer) * 2) ease-in-out, -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
  }
}
@media (max-width: 1199.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xl {
    transition: none;
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-start {
    top: 0;
    right: 0;
    width: var(--offcanvas-width);
    border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-end {
    top: 0;
    left: 0;
    width: var(--offcanvas-width);
    border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-top {
    top: 0;
    left: 0;
    right: 0;
    height: var(--offcanvas-height);
    max-height: 100%;
    border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-bottom {
    left: 0;
    right: 0;
    height: var(--offcanvas-height);
    max-height: 100%;
    border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.showing, .offcanvas-xl.show:not(.hiding) {
    -webkit-transform: none;
            transform: none;
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.showing, .offcanvas-xl.hiding, .offcanvas-xl.show {
    visibility: visible;
  }
}
@media (min-width: 1200px) {
  .offcanvas-xl {
    --offcanvas-height: auto;
    --offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xl .offcanvas-header {
    display: none;
  }
  .offcanvas-xl .offcanvas-body {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-positive: 0;
        flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 1399.98px) {
  .offcanvas-xxl {
    position: fixed;
    bottom: 0;
    z-index: var(--offcanvas-zindex);
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
        flex-direction: column;
    max-width: 100%;
    color: var(--offcanvas-color);
    visibility: hidden;
    background-color: var(--offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    box-shadow: var(--offcanvas-box-shadow);
    transition: -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
    transition: transform calc(var(--transition-base-timer) * 2) ease-in-out;
    transition: transform calc(var(--transition-base-timer) * 2) ease-in-out, -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
  }
}
@media (max-width: 1399.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xxl {
    transition: none;
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-start {
    top: 0;
    right: 0;
    width: var(--offcanvas-width);
    border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-end {
    top: 0;
    left: 0;
    width: var(--offcanvas-width);
    border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-top {
    top: 0;
    left: 0;
    right: 0;
    height: var(--offcanvas-height);
    max-height: 100%;
    border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.offcanvas-bottom {
    left: 0;
    right: 0;
    height: var(--offcanvas-height);
    max-height: 100%;
    border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.showing, .offcanvas-xxl.show:not(.hiding) {
    -webkit-transform: none;
            transform: none;
  }
}
@media (max-width: 1399.98px) {
  .offcanvas-xxl.showing, .offcanvas-xxl.hiding, .offcanvas-xxl.show {
    visibility: visible;
  }
}
@media (min-width: 1400px) {
  .offcanvas-xxl {
    --offcanvas-height: auto;
    --offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xxl .offcanvas-header {
    display: none;
  }
  .offcanvas-xxl .offcanvas-body {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-positive: 0;
        flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: var(--offcanvas-zindex);
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  max-width: 100%;
  color: var(--offcanvas-color);
  visibility: hidden;
  background-color: var(--offcanvas-bg);
  background-clip: padding-box;
  outline: 0;
  box-shadow: var(--offcanvas-box-shadow);
  transition: -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
  transition: transform calc(var(--transition-base-timer) * 2) ease-in-out;
  transition: transform calc(var(--transition-base-timer) * 2) ease-in-out, -webkit-transform calc(var(--transition-base-timer) * 2) ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    transition: none;
  }
}
.offcanvas.offcanvas-start {
  top: 0;
  right: 0;
  width: var(--offcanvas-width);
  border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
  -webkit-transform: translateX(100%);
          transform: translateX(100%);
}
.offcanvas.offcanvas-end {
  top: 0;
  left: 0;
  width: var(--offcanvas-width);
  border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
  -webkit-transform: translateX(-100%);
          transform: translateX(-100%);
}
.offcanvas.offcanvas-top {
  top: 0;
  left: 0;
  right: 0;
  height: var(--offcanvas-height);
  max-height: 100%;
  border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
  -webkit-transform: translateY(-100%);
          transform: translateY(-100%);
}
.offcanvas.offcanvas-bottom {
  left: 0;
  right: 0;
  height: var(--offcanvas-height);
  max-height: 100%;
  border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color);
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
}
.offcanvas.showing, .offcanvas.show:not(.hiding) {
  -webkit-transform: none;
          transform: none;
}
.offcanvas.showing, .offcanvas.hiding, .offcanvas.show {
  visibility: visible;
}

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: var(--black);
}
.offcanvas-backdrop.fade {
  opacity: 0;
}
.offcanvas-backdrop.show {
  opacity: 0.25;
}

.offcanvas-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: justify;
      justify-content: space-between;
  padding: var(--offcanvas-padding-y) var(--offcanvas-padding-x);
}
.offcanvas-header .btn-close {
  padding: calc(var(--offcanvas-padding-y) * 0.5) calc(var(--offcanvas-padding-x) * 0.5);
  margin-top: calc(-0.5 * var(--offcanvas-padding-y));
  margin-left: calc(-0.5 * var(--offcanvas-padding-x));
  margin-bottom: calc(-0.5 * var(--offcanvas-padding-y));
}

.offcanvas-title {
  margin-bottom: 0;
  line-height: var(--body-line-height);
}

.offcanvas-body {
  -ms-flex-positive: 1;
      flex-grow: 1;
  padding: var(--offcanvas-padding-y) var(--offcanvas-padding-x);
  overflow-y: auto;
}

.placeholder {
  display: inline-block;
  min-height: 1em;
  vertical-align: middle;
  cursor: wait;
  background-color: currentcolor;
  opacity: 0.5;
}
.placeholder.btn::before {
  display: inline-block;
  content: "";
}

.placeholder-xs {
  min-height: 0.6em;
}

.placeholder-sm {
  min-height: 0.8em;
}

.placeholder-lg {
  min-height: 1.2em;
}

.placeholder-glow .placeholder {
  -webkit-animation: placeholder-glow 2s ease-in-out infinite;
          animation: placeholder-glow 2s ease-in-out infinite;
}

@-webkit-keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}

@keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}
.placeholder-wave {
  -webkit-mask-image: linear-gradient(130deg, #000 55%, rgba(0, 0, 0, 0.8) 75%, #000 95%);
          mask-image: linear-gradient(130deg, #000 55%, rgba(0, 0, 0, 0.8) 75%, #000 95%);
  -webkit-mask-size: 200% 100%;
          mask-size: 200% 100%;
  -webkit-animation: placeholder-wave 2s linear infinite;
          animation: placeholder-wave 2s linear infinite;
}

@-webkit-keyframes placeholder-wave {
  100% {
    -webkit-mask-position: -200% 0%;
            mask-position: -200% 0%;
  }
}

@keyframes placeholder-wave {
  100% {
    -webkit-mask-position: -200% 0%;
            mask-position: -200% 0%;
  }
}
.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.text-bg-indigo {
  color: #fff !important;
  background-color: RGBA(92, 107, 192, var(--bg-opacity, 1)) !important;
}

.text-bg-purple {
  color: #fff !important;
  background-color: RGBA(142, 112, 193, var(--bg-opacity, 1)) !important;
}

.text-bg-pink {
  color: #fff !important;
  background-color: RGBA(243, 92, 134, var(--bg-opacity, 1)) !important;
}

.text-bg-teal {
  color: #fff !important;
  background-color: RGBA(38, 166, 154, var(--bg-opacity, 1)) !important;
}

.text-bg-yellow {
  color: #000 !important;
  background-color: RGBA(255, 214, 72, var(--bg-opacity, 1)) !important;
}

.text-bg-primary {
  color: #fff !important;
  background-color: RGBA(12, 131, 255, var(--bg-opacity, 1)) !important;
}

.text-bg-secondary {
  color: #fff !important;
  background-color: RGBA(36, 114, 151, var(--bg-opacity, 1)) !important;
}

.text-bg-success {
  color: #fff !important;
  background-color: RGBA(5, 150, 105, var(--bg-opacity, 1)) !important;
}

.text-bg-info {
  color: #fff !important;
  background-color: RGBA(4, 154, 173, var(--bg-opacity, 1)) !important;
}

.text-bg-warning {
  color: #fff !important;
  background-color: RGBA(245, 134, 70, var(--bg-opacity, 1)) !important;
}

.text-bg-danger {
  color: #fff !important;
  background-color: RGBA(239, 68, 68, var(--bg-opacity, 1)) !important;
}

.text-bg-light {
  color: #000 !important;
  background-color: RGBA(243, 244, 246, var(--bg-opacity, 1)) !important;
}

.text-bg-dark {
  color: #fff !important;
  background-color: RGBA(37, 43, 54, var(--bg-opacity, 1)) !important;
}

.text-bg-black {
  color: #fff !important;
  background-color: RGBA(0, 0, 0, var(--bg-opacity, 1)) !important;
}

.text-bg-white {
  color: #000 !important;
  background-color: RGBA(255, 255, 255, var(--bg-opacity, 1)) !important;
}

.link-indigo {
  color: #5C6BC0 !important;
}
.link-indigo:hover, .link-indigo:focus {
  color: #455090 !important;
}

.link-purple {
  color: #8e70c1 !important;
}
.link-purple:hover, .link-purple:focus {
  color: #6b5491 !important;
}

.link-pink {
  color: #f35c86 !important;
}
.link-pink:hover, .link-pink:focus {
  color: #b64565 !important;
}

.link-teal {
  color: #26A69A !important;
}
.link-teal:hover, .link-teal:focus {
  color: #1d7d74 !important;
}

.link-yellow {
  color: #ffd648 !important;
}
.link-yellow:hover, .link-yellow:focus {
  color: #ffe076 !important;
}

.link-primary {
  color: #0c83ff !important;
}
.link-primary:hover, .link-primary:focus {
  color: #0962bf !important;
}

.link-secondary {
  color: #247297 !important;
}
.link-secondary:hover, .link-secondary:focus {
  color: #1b5671 !important;
}

.link-success {
  color: #059669 !important;
}
.link-success:hover, .link-success:focus {
  color: #04714f !important;
}

.link-info {
  color: #049aad !important;
}
.link-info:hover, .link-info:focus {
  color: #037482 !important;
}

.link-warning {
  color: #f58646 !important;
}
.link-warning:hover, .link-warning:focus {
  color: #b86535 !important;
}

.link-danger {
  color: #EF4444 !important;
}
.link-danger:hover, .link-danger:focus {
  color: #b33333 !important;
}

.link-light {
  color: #F3F4F6 !important;
}
.link-light:hover, .link-light:focus {
  color: #f6f7f8 !important;
}

.link-dark {
  color: #252b36 !important;
}
.link-dark:hover, .link-dark:focus {
  color: #1c2029 !important;
}

.link-black {
  color: #000 !important;
}
.link-black:hover, .link-black:focus {
  color: black !important;
}

.link-white {
  color: #fff !important;
}
.link-white:hover, .link-white:focus {
  color: white !important;
}

.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
}

.ratio-1x1 {
  --aspect-ratio: 100%;
}

.ratio-4x3 {
  --aspect-ratio: 75%;
}

.ratio-16x9 {
  --aspect-ratio: 56.25%;
}

.ratio-21x9 {
  --aspect-ratio: 42.8571428571%;
}

.fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 1030;
}

.sticky-top {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1020;
}

.sticky-bottom {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  z-index: 1020;
}

@media (min-width: 576px) {
  .sticky-sm-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-sm-bottom {
    position: -webkit-sticky;
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .sticky-md-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-md-bottom {
    position: -webkit-sticky;
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .sticky-lg-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-lg-bottom {
    position: -webkit-sticky;
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1200px) {
  .sticky-xl-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-xl-bottom {
    position: -webkit-sticky;
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1400px) {
  .sticky-xxl-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-xxl-bottom {
    position: -webkit-sticky;
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
.hstack {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
      flex-direction: row;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-item-align: stretch;
      align-self: stretch;
}

.vstack {
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 1 1 auto;
      flex: 1 1 auto;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-item-align: stretch;
      align-self: stretch;
}

.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.stretched-link::after {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 1;
  content: "";
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vr {
  display: inline-block;
  -ms-flex-item-align: stretch;
      align-self: stretch;
  width: 1px;
  min-height: 1em;
  background-color: currentcolor;
  opacity: 0.25;
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.float-start {
  float: right !important;
}

.float-end {
  float: left !important;
}

.float-none {
  float: none !important;
}

.opacity-0 {
  opacity: 0 !important;
}

.opacity-25 {
  opacity: 0.25 !important;
}

.opacity-50 {
  opacity: 0.5 !important;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.opacity-100 {
  opacity: 1 !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-grid {
  display: grid !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: -ms-flexbox !important;
  display: flex !important;
}

.d-inline-flex {
  display: -ms-inline-flexbox !important;
  display: inline-flex !important;
}

.d-none {
  display: none !important;
}

.shadow {
  box-shadow: var(--box-shadow) !important;
}

.shadow-sm {
  box-shadow: var(--box-shadow-sm) !important;
}

.shadow-lg {
  box-shadow: var(--box-shadow-lg) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}

.top-0 {
  top: 0 !important;
}

.top-50 {
  top: 50% !important;
}

.top-100 {
  top: 100% !important;
}

.top-auto {
  top: "auto" !important;
}

.bottom-0 {
  bottom: 0 !important;
}

.bottom-50 {
  bottom: 50% !important;
}

.bottom-100 {
  bottom: 100% !important;
}

.bottom-auto {
  bottom: "auto" !important;
}

.start-0 {
  right: 0 !important;
}

.start-50 {
  right: 50% !important;
}

.start-100 {
  right: 100% !important;
}

.start-auto {
  right: "auto" !important;
}

.end-0 {
  left: 0 !important;
}

.end-50 {
  left: 50% !important;
}

.end-100 {
  left: 100% !important;
}

.end-auto {
  left: "auto" !important;
}

.translate-middle {
  -webkit-transform: translate(50%, -50%) !important;
          transform: translate(50%, -50%) !important;
}

.translate-middle-x {
  -webkit-transform: translateX(50%) !important;
          transform: translateX(50%) !important;
}

.translate-middle-y {
  -webkit-transform: translateY(-50%) !important;
          transform: translateY(-50%) !important;
}

.translate-middle-start {
  -webkit-transform: translate(50%, 50%) !important;
          transform: translate(50%, 50%) !important;
}

.translate-middle-top {
  -webkit-transform: translate(-50%, -50%) !important;
          transform: translate(-50%, -50%) !important;
}

.border {
  border: var(--border-width) var(--border-style) var(--border-color) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: var(--border-width) var(--border-style) var(--border-color) !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-end {
  border-left: var(--border-width) var(--border-style) var(--border-color) !important;
}

.border-end-0 {
  border-left: 0 !important;
}

.border-bottom {
  border-bottom: var(--border-width) var(--border-style) var(--border-color) !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-start {
  border-right: var(--border-width) var(--border-style) var(--border-color) !important;
}

.border-start-0 {
  border-right: 0 !important;
}

.border-indigo {
  --border-opacity: 1;
  border-color: rgba(var(--indigo-rgb), var(--border-opacity)) !important;
}

.border-purple {
  --border-opacity: 1;
  border-color: rgba(var(--purple-rgb), var(--border-opacity)) !important;
}

.border-pink {
  --border-opacity: 1;
  border-color: rgba(var(--pink-rgb), var(--border-opacity)) !important;
}

.border-teal {
  --border-opacity: 1;
  border-color: rgba(var(--teal-rgb), var(--border-opacity)) !important;
}

.border-yellow {
  --border-opacity: 1;
  border-color: rgba(var(--yellow-rgb), var(--border-opacity)) !important;
}

.border-primary {
  --border-opacity: 1;
  border-color: rgba(var(--primary-rgb), var(--border-opacity)) !important;
}

.border-secondary {
  --border-opacity: 1;
  border-color: rgba(var(--secondary-rgb), var(--border-opacity)) !important;
}

.border-success {
  --border-opacity: 1;
  border-color: rgba(var(--success-rgb), var(--border-opacity)) !important;
}

.border-info {
  --border-opacity: 1;
  border-color: rgba(var(--info-rgb), var(--border-opacity)) !important;
}

.border-warning {
  --border-opacity: 1;
  border-color: rgba(var(--warning-rgb), var(--border-opacity)) !important;
}

.border-danger {
  --border-opacity: 1;
  border-color: rgba(var(--danger-rgb), var(--border-opacity)) !important;
}

.border-light {
  --border-opacity: 1;
  border-color: rgba(var(--light-rgb), var(--border-opacity)) !important;
}

.border-dark {
  --border-opacity: 1;
  border-color: rgba(var(--dark-rgb), var(--border-opacity)) !important;
}

.border-black {
  --border-opacity: 1;
  border-color: rgba(var(--black-rgb), var(--border-opacity)) !important;
}

.border-white {
  --border-opacity: 1;
  border-color: rgba(var(--white-rgb), var(--border-opacity)) !important;
}

.border-transparent {
  --border-opacity: 1;
  border-color: transparent !important;
}

.border-top-indigo {
  --border-opacity: 1;
  border-top-color: rgba(var(--indigo-rgb), var(--border-opacity)) !important;
}

.border-top-purple {
  --border-opacity: 1;
  border-top-color: rgba(var(--purple-rgb), var(--border-opacity)) !important;
}

.border-top-pink {
  --border-opacity: 1;
  border-top-color: rgba(var(--pink-rgb), var(--border-opacity)) !important;
}

.border-top-teal {
  --border-opacity: 1;
  border-top-color: rgba(var(--teal-rgb), var(--border-opacity)) !important;
}

.border-top-yellow {
  --border-opacity: 1;
  border-top-color: rgba(var(--yellow-rgb), var(--border-opacity)) !important;
}

.border-top-primary {
  --border-opacity: 1;
  border-top-color: rgba(var(--primary-rgb), var(--border-opacity)) !important;
}

.border-top-secondary {
  --border-opacity: 1;
  border-top-color: rgba(var(--secondary-rgb), var(--border-opacity)) !important;
}

.border-top-success {
  --border-opacity: 1;
  border-top-color: rgba(var(--success-rgb), var(--border-opacity)) !important;
}

.border-top-info {
  --border-opacity: 1;
  border-top-color: rgba(var(--info-rgb), var(--border-opacity)) !important;
}

.border-top-warning {
  --border-opacity: 1;
  border-top-color: rgba(var(--warning-rgb), var(--border-opacity)) !important;
}

.border-top-danger {
  --border-opacity: 1;
  border-top-color: rgba(var(--danger-rgb), var(--border-opacity)) !important;
}

.border-top-light {
  --border-opacity: 1;
  border-top-color: rgba(var(--light-rgb), var(--border-opacity)) !important;
}

.border-top-dark {
  --border-opacity: 1;
  border-top-color: rgba(var(--dark-rgb), var(--border-opacity)) !important;
}

.border-top-black {
  --border-opacity: 1;
  border-top-color: rgba(var(--black-rgb), var(--border-opacity)) !important;
}

.border-top-white {
  --border-opacity: 1;
  border-top-color: rgba(var(--white-rgb), var(--border-opacity)) !important;
}

.border-top-transparent {
  --border-opacity: 1;
  border-top-color: transparent !important;
}

.border-bottom-indigo {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--indigo-rgb), var(--border-opacity)) !important;
}

.border-bottom-purple {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--purple-rgb), var(--border-opacity)) !important;
}

.border-bottom-pink {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--pink-rgb), var(--border-opacity)) !important;
}

.border-bottom-teal {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--teal-rgb), var(--border-opacity)) !important;
}

.border-bottom-yellow {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--yellow-rgb), var(--border-opacity)) !important;
}

.border-bottom-primary {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--primary-rgb), var(--border-opacity)) !important;
}

.border-bottom-secondary {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--secondary-rgb), var(--border-opacity)) !important;
}

.border-bottom-success {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--success-rgb), var(--border-opacity)) !important;
}

.border-bottom-info {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--info-rgb), var(--border-opacity)) !important;
}

.border-bottom-warning {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--warning-rgb), var(--border-opacity)) !important;
}

.border-bottom-danger {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--danger-rgb), var(--border-opacity)) !important;
}

.border-bottom-light {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--light-rgb), var(--border-opacity)) !important;
}

.border-bottom-dark {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--dark-rgb), var(--border-opacity)) !important;
}

.border-bottom-black {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--black-rgb), var(--border-opacity)) !important;
}

.border-bottom-white {
  --border-opacity: 1;
  border-bottom-color: rgba(var(--white-rgb), var(--border-opacity)) !important;
}

.border-bottom-transparent {
  --border-opacity: 1;
  border-bottom-color: transparent !important;
}

.border-start-indigo {
  --border-opacity: 1;
  border-right-color: rgba(var(--indigo-rgb), var(--border-opacity)) !important;
}

.border-start-purple {
  --border-opacity: 1;
  border-right-color: rgba(var(--purple-rgb), var(--border-opacity)) !important;
}

.border-start-pink {
  --border-opacity: 1;
  border-right-color: rgba(var(--pink-rgb), var(--border-opacity)) !important;
}

.border-start-teal {
  --border-opacity: 1;
  border-right-color: rgba(var(--teal-rgb), var(--border-opacity)) !important;
}

.border-start-yellow {
  --border-opacity: 1;
  border-right-color: rgba(var(--yellow-rgb), var(--border-opacity)) !important;
}

.border-start-primary {
  --border-opacity: 1;
  border-right-color: rgba(var(--primary-rgb), var(--border-opacity)) !important;
}

.border-start-secondary {
  --border-opacity: 1;
  border-right-color: rgba(var(--secondary-rgb), var(--border-opacity)) !important;
}

.border-start-success {
  --border-opacity: 1;
  border-right-color: rgba(var(--success-rgb), var(--border-opacity)) !important;
}

.border-start-info {
  --border-opacity: 1;
  border-right-color: rgba(var(--info-rgb), var(--border-opacity)) !important;
}

.border-start-warning {
  --border-opacity: 1;
  border-right-color: rgba(var(--warning-rgb), var(--border-opacity)) !important;
}

.border-start-danger {
  --border-opacity: 1;
  border-right-color: rgba(var(--danger-rgb), var(--border-opacity)) !important;
}

.border-start-light {
  --border-opacity: 1;
  border-right-color: rgba(var(--light-rgb), var(--border-opacity)) !important;
}

.border-start-dark {
  --border-opacity: 1;
  border-right-color: rgba(var(--dark-rgb), var(--border-opacity)) !important;
}

.border-start-black {
  --border-opacity: 1;
  border-right-color: rgba(var(--black-rgb), var(--border-opacity)) !important;
}

.border-start-white {
  --border-opacity: 1;
  border-right-color: rgba(var(--white-rgb), var(--border-opacity)) !important;
}

.border-start-transparent {
  --border-opacity: 1;
  border-right-color: transparent !important;
}

.border-end-indigo {
  --border-opacity: 1;
  border-left-color: rgba(var(--indigo-rgb), var(--border-opacity)) !important;
}

.border-end-purple {
  --border-opacity: 1;
  border-left-color: rgba(var(--purple-rgb), var(--border-opacity)) !important;
}

.border-end-pink {
  --border-opacity: 1;
  border-left-color: rgba(var(--pink-rgb), var(--border-opacity)) !important;
}

.border-end-teal {
  --border-opacity: 1;
  border-left-color: rgba(var(--teal-rgb), var(--border-opacity)) !important;
}

.border-end-yellow {
  --border-opacity: 1;
  border-left-color: rgba(var(--yellow-rgb), var(--border-opacity)) !important;
}

.border-end-primary {
  --border-opacity: 1;
  border-left-color: rgba(var(--primary-rgb), var(--border-opacity)) !important;
}

.border-end-secondary {
  --border-opacity: 1;
  border-left-color: rgba(var(--secondary-rgb), var(--border-opacity)) !important;
}

.border-end-success {
  --border-opacity: 1;
  border-left-color: rgba(var(--success-rgb), var(--border-opacity)) !important;
}

.border-end-info {
  --border-opacity: 1;
  border-left-color: rgba(var(--info-rgb), var(--border-opacity)) !important;
}

.border-end-warning {
  --border-opacity: 1;
  border-left-color: rgba(var(--warning-rgb), var(--border-opacity)) !important;
}

.border-end-danger {
  --border-opacity: 1;
  border-left-color: rgba(var(--danger-rgb), var(--border-opacity)) !important;
}

.border-end-light {
  --border-opacity: 1;
  border-left-color: rgba(var(--light-rgb), var(--border-opacity)) !important;
}

.border-end-dark {
  --border-opacity: 1;
  border-left-color: rgba(var(--dark-rgb), var(--border-opacity)) !important;
}

.border-end-black {
  --border-opacity: 1;
  border-left-color: rgba(var(--black-rgb), var(--border-opacity)) !important;
}

.border-end-white {
  --border-opacity: 1;
  border-left-color: rgba(var(--white-rgb), var(--border-opacity)) !important;
}

.border-end-transparent {
  --border-opacity: 1;
  border-left-color: transparent !important;
}

.border-width-0 {
  border-width: 0 !important;
}

.border-width-1 {
  border-width: 1px !important;
}

.border-width-2 {
  border-width: 2px !important;
}

.border-width-3 {
  border-width: 3px !important;
}

.border-width-4 {
  border-width: 4px !important;
}

.border-width-5 {
  border-width: 5px !important;
}

.border-top-width-0 {
  border-top-width: 0 !important;
}

.border-top-width-1 {
  border-top-width: 1px !important;
}

.border-top-width-2 {
  border-top-width: 2px !important;
}

.border-top-width-3 {
  border-top-width: 3px !important;
}

.border-top-width-4 {
  border-top-width: 4px !important;
}

.border-top-width-5 {
  border-top-width: 5px !important;
}

.border-bottom-width-0 {
  border-bottom-width: 0 !important;
}

.border-bottom-width-1 {
  border-bottom-width: 1px !important;
}

.border-bottom-width-2 {
  border-bottom-width: 2px !important;
}

.border-bottom-width-3 {
  border-bottom-width: 3px !important;
}

.border-bottom-width-4 {
  border-bottom-width: 4px !important;
}

.border-bottom-width-5 {
  border-bottom-width: 5px !important;
}

.border-start-width-0 {
  border-right-width: 0 !important;
}

.border-start-width-1 {
  border-right-width: 1px !important;
}

.border-start-width-2 {
  border-right-width: 2px !important;
}

.border-start-width-3 {
  border-right-width: 3px !important;
}

.border-start-width-4 {
  border-right-width: 4px !important;
}

.border-start-width-5 {
  border-right-width: 5px !important;
}

.border-end-width-0 {
  border-left-width: 0 !important;
}

.border-end-width-1 {
  border-left-width: 1px !important;
}

.border-end-width-2 {
  border-left-width: 2px !important;
}

.border-end-width-3 {
  border-left-width: 3px !important;
}

.border-end-width-4 {
  border-left-width: 4px !important;
}

.border-end-width-5 {
  border-left-width: 5px !important;
}

.border-opacity-10 {
  --border-opacity: 0.1;
}

.border-opacity-15 {
  --border-opacity: 0.15;
}

.border-opacity-20 {
  --border-opacity: 0.2;
}

.border-opacity-25 {
  --border-opacity: 0.25;
}

.border-opacity-50 {
  --border-opacity: 0.5;
}

.border-opacity-75 {
  --border-opacity: 0.75;
}

.border-opacity-100 {
  --border-opacity: 1;
}

.border-style-dashed {
  border-style: "dashed" !important;
}

.border-style-dotted {
  border-style: "dotted" !important;
}

.border-top-style-dashed {
  border-top-style: "dashed" !important;
}

.border-top-style-dotted {
  border-top-style: "dotted" !important;
}

.border-bottom-style-dashed {
  border-bottom-style: "dashed" !important;
}

.border-bottom-style-dotted {
  border-bottom-style: "dotted" !important;
}

.w-16px {
  width: 1rem !important;
}

.w-24px {
  width: 1.5rem !important;
}

.w-32px {
  width: 2rem !important;
}

.w-40px {
  width: 2.5rem !important;
}

.w-48px {
  width: 3rem !important;
}

.w-56px {
  width: 3.5rem !important;
}

.w-64px {
  width: 4rem !important;
}

.w-72px {
  width: 4.5rem !important;
}

.w-80px {
  width: 5rem !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.wmin-0 {
  min-width: 0 !important;
}

.wmin-200 {
  min-width: 200px !important;
}

.wmin-250 {
  min-width: 250px !important;
}

.wmin-300 {
  min-width: 300px !important;
}

.wmin-350 {
  min-width: 350px !important;
}

.wmin-400 {
  min-width: 400px !important;
}

.wmin-450 {
  min-width: 450px !important;
}

.wmin-500 {
  min-width: 500px !important;
}

.wmin-550 {
  min-width: 550px !important;
}

.wmin-600 {
  min-width: 600px !important;
}

.mw-100 {
  max-width: 100% !important;
}

.vw-100 {
  width: 100vw !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.h-16px {
  height: 1rem !important;
}

.h-24px {
  height: 1.5rem !important;
}

.h-32px {
  height: 2rem !important;
}

.h-40px {
  height: 2.5rem !important;
}

.h-48px {
  height: 3rem !important;
}

.h-56px {
  height: 3.5rem !important;
}

.h-64px {
  height: 4rem !important;
}

.h-72px {
  height: 4.5rem !important;
}

.h-80px {
  height: 5rem !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mh-100 {
  max-height: 100% !important;
}

.vh-100 {
  height: 100vh !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.flex-0 {
  -ms-flex: 0 1 auto !important;
      flex: 0 1 auto !important;
}

.flex-1 {
  -ms-flex: 1 !important;
      flex: 1 !important;
}

.flex-fill {
  -ms-flex: 1 1 auto !important;
      flex: 1 1 auto !important;
}

.flex-row {
  -ms-flex-direction: row !important;
      flex-direction: row !important;
}

.flex-column {
  -ms-flex-direction: column !important;
      flex-direction: column !important;
}

.flex-row-reverse {
  -ms-flex-direction: row-reverse !important;
      flex-direction: row-reverse !important;
}

.flex-column-reverse {
  -ms-flex-direction: column-reverse !important;
      flex-direction: column-reverse !important;
}

.flex-grow-0 {
  -ms-flex-positive: 0 !important;
      flex-grow: 0 !important;
}

.flex-grow-1 {
  -ms-flex-positive: 1 !important;
      flex-grow: 1 !important;
}

.flex-grow-2 {
  -ms-flex-positive: 2 !important;
      flex-grow: 2 !important;
}

.flex-grow-3 {
  -ms-flex-positive: 3 !important;
      flex-grow: 3 !important;
}

.flex-shrink-0 {
  -ms-flex-negative: 0 !important;
      flex-shrink: 0 !important;
}

.flex-shrink-1 {
  -ms-flex-negative: 1 !important;
      flex-shrink: 1 !important;
}

.flex-wrap {
  -ms-flex-wrap: wrap !important;
      flex-wrap: wrap !important;
}

.flex-nowrap {
  -ms-flex-wrap: nowrap !important;
      flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse !important;
      flex-wrap: wrap-reverse !important;
}

.justify-content-start {
  -ms-flex-pack: start !important;
      justify-content: flex-start !important;
}

.justify-content-end {
  -ms-flex-pack: end !important;
      justify-content: flex-end !important;
}

.justify-content-center {
  -ms-flex-pack: center !important;
      justify-content: center !important;
}

.justify-content-between {
  -ms-flex-pack: justify !important;
      justify-content: space-between !important;
}

.justify-content-around {
  -ms-flex-pack: distribute !important;
      justify-content: space-around !important;
}

.justify-content-evenly {
  -ms-flex-pack: space-evenly !important;
      justify-content: space-evenly !important;
}

.align-items-start {
  -ms-flex-align: start !important;
      align-items: flex-start !important;
}

.align-items-end {
  -ms-flex-align: end !important;
      align-items: flex-end !important;
}

.align-items-center {
  -ms-flex-align: center !important;
      align-items: center !important;
}

.align-items-baseline {
  -ms-flex-align: baseline !important;
      align-items: baseline !important;
}

.align-items-stretch {
  -ms-flex-align: stretch !important;
      align-items: stretch !important;
}

.align-content-start {
  -ms-flex-line-pack: start !important;
      align-content: flex-start !important;
}

.align-content-end {
  -ms-flex-line-pack: end !important;
      align-content: flex-end !important;
}

.align-content-center {
  -ms-flex-line-pack: center !important;
      align-content: center !important;
}

.align-content-between {
  -ms-flex-line-pack: justify !important;
      align-content: space-between !important;
}

.align-content-around {
  -ms-flex-line-pack: distribute !important;
      align-content: space-around !important;
}

.align-content-stretch {
  -ms-flex-line-pack: stretch !important;
      align-content: stretch !important;
}

.align-self-auto {
  -ms-flex-item-align: auto !important;
      align-self: auto !important;
}

.align-self-start {
  -ms-flex-item-align: start !important;
      align-self: flex-start !important;
}

.align-self-end {
  -ms-flex-item-align: end !important;
      align-self: flex-end !important;
}

.align-self-center {
  -ms-flex-item-align: center !important;
      align-self: center !important;
}

.align-self-baseline {
  -ms-flex-item-align: baseline !important;
      align-self: baseline !important;
}

.align-self-stretch {
  -ms-flex-item-align: stretch !important;
      align-self: stretch !important;
}

.order-first {
  -ms-flex-order: -1 !important;
      order: -1 !important;
}

.order-0 {
  -ms-flex-order: 0 !important;
      order: 0 !important;
}

.order-1 {
  -ms-flex-order: 1 !important;
      order: 1 !important;
}

.order-2 {
  -ms-flex-order: 2 !important;
      order: 2 !important;
}

.order-3 {
  -ms-flex-order: 3 !important;
      order: 3 !important;
}

.order-4 {
  -ms-flex-order: 4 !important;
      order: 4 !important;
}

.order-5 {
  -ms-flex-order: 5 !important;
      order: 5 !important;
}

.order-last {
  -ms-flex-order: 6 !important;
      order: 6 !important;
}

.m-0 {
  margin: 0 !important;
}

.m-1 {
  margin: 0.3125rem !important;
}

.m-2 {
  margin: 0.625rem !important;
}

.m-3 {
  margin: 1.25rem !important;
}

.m-4 {
  margin: 1.875rem !important;
}

.m-5 {
  margin: 3.75rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-0 {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.mx-1 {
  margin-left: 0.3125rem !important;
  margin-right: 0.3125rem !important;
}

.mx-2 {
  margin-left: 0.625rem !important;
  margin-right: 0.625rem !important;
}

.mx-3 {
  margin-left: 1.25rem !important;
  margin-right: 1.25rem !important;
}

.mx-4 {
  margin-left: 1.875rem !important;
  margin-right: 1.875rem !important;
}

.mx-5 {
  margin-left: 3.75rem !important;
  margin-right: 3.75rem !important;
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.my-1 {
  margin-top: 0.3125rem !important;
  margin-bottom: 0.3125rem !important;
}

.my-2 {
  margin-top: 0.625rem !important;
  margin-bottom: 0.625rem !important;
}

.my-3 {
  margin-top: 1.25rem !important;
  margin-bottom: 1.25rem !important;
}

.my-4 {
  margin-top: 1.875rem !important;
  margin-bottom: 1.875rem !important;
}

.my-5 {
  margin-top: 3.75rem !important;
  margin-bottom: 3.75rem !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-1 {
  margin-top: 0.3125rem !important;
}

.mt-2 {
  margin-top: 0.625rem !important;
}

.mt-3 {
  margin-top: 1.25rem !important;
}

.mt-4 {
  margin-top: 1.875rem !important;
}

.mt-5 {
  margin-top: 3.75rem !important;
}

.mt-auto {
  margin-top: auto !important;
}

.me-0 {
  margin-left: 0 !important;
}

.me-1 {
  margin-left: 0.3125rem !important;
}

.me-2 {
  margin-left: 0.625rem !important;
}

.me-3 {
  margin-left: 1.25rem !important;
}

.me-4 {
  margin-left: 1.875rem !important;
}

.me-5 {
  margin-left: 3.75rem !important;
}

.me-auto {
  margin-left: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: 0.3125rem !important;
}

.mb-2 {
  margin-bottom: 0.625rem !important;
}

.mb-3 {
  margin-bottom: 1.25rem !important;
}

.mb-4 {
  margin-bottom: 1.875rem !important;
}

.mb-5 {
  margin-bottom: 3.75rem !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

.ms-0 {
  margin-right: 0 !important;
}

.ms-1 {
  margin-right: 0.3125rem !important;
}

.ms-2 {
  margin-right: 0.625rem !important;
}

.ms-3 {
  margin-right: 1.25rem !important;
}

.ms-4 {
  margin-right: 1.875rem !important;
}

.ms-5 {
  margin-right: 3.75rem !important;
}

.ms-auto {
  margin-right: auto !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 0.3125rem !important;
}

.p-2 {
  padding: 0.625rem !important;
}

.p-3 {
  padding: 1.25rem !important;
}

.p-4 {
  padding: 1.875rem !important;
}

.p-5 {
  padding: 3.75rem !important;
}

.px-0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.px-1 {
  padding-left: 0.3125rem !important;
  padding-right: 0.3125rem !important;
}

.px-2 {
  padding-left: 0.625rem !important;
  padding-right: 0.625rem !important;
}

.px-3 {
  padding-left: 1.25rem !important;
  padding-right: 1.25rem !important;
}

.px-4 {
  padding-left: 1.875rem !important;
  padding-right: 1.875rem !important;
}

.px-5 {
  padding-left: 3.75rem !important;
  padding-right: 3.75rem !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.py-1 {
  padding-top: 0.3125rem !important;
  padding-bottom: 0.3125rem !important;
}

.py-2 {
  padding-top: 0.625rem !important;
  padding-bottom: 0.625rem !important;
}

.py-3 {
  padding-top: 1.25rem !important;
  padding-bottom: 1.25rem !important;
}

.py-4 {
  padding-top: 1.875rem !important;
  padding-bottom: 1.875rem !important;
}

.py-5 {
  padding-top: 3.75rem !important;
  padding-bottom: 3.75rem !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-1 {
  padding-top: 0.3125rem !important;
}

.pt-2 {
  padding-top: 0.625rem !important;
}

.pt-3 {
  padding-top: 1.25rem !important;
}

.pt-4 {
  padding-top: 1.875rem !important;
}

.pt-5 {
  padding-top: 3.75rem !important;
}

.pe-0 {
  padding-left: 0 !important;
}

.pe-1 {
  padding-left: 0.3125rem !important;
}

.pe-2 {
  padding-left: 0.625rem !important;
}

.pe-3 {
  padding-left: 1.25rem !important;
}

.pe-4 {
  padding-left: 1.875rem !important;
}

.pe-5 {
  padding-left: 3.75rem !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-1 {
  padding-bottom: 0.3125rem !important;
}

.pb-2 {
  padding-bottom: 0.625rem !important;
}

.pb-3 {
  padding-bottom: 1.25rem !important;
}

.pb-4 {
  padding-bottom: 1.875rem !important;
}

.pb-5 {
  padding-bottom: 3.75rem !important;
}

.ps-0 {
  padding-right: 0 !important;
}

.ps-1 {
  padding-right: 0.3125rem !important;
}

.ps-2 {
  padding-right: 0.625rem !important;
}

.ps-3 {
  padding-right: 1.25rem !important;
}

.ps-4 {
  padding-right: 1.875rem !important;
}

.ps-5 {
  padding-right: 3.75rem !important;
}

.gap-0 {
  gap: 0 !important;
}

.gap-1 {
  gap: 0.3125rem !important;
}

.gap-2 {
  gap: 0.625rem !important;
}

.gap-3 {
  gap: 1.25rem !important;
}

.gap-4 {
  gap: 1.875rem !important;
}

.gap-5 {
  gap: 3.75rem !important;
}

.font-monospace {
  font-family: var(--font-monospace) !important;
}

.fs-1 {
  font-size: calc(1.2875rem + 0.45vw) !important;
}

.fs-2 {
  font-size: calc(1.275rem + 0.3vw) !important;
}

.fs-3 {
  font-size: calc(1.2625rem + 0.15vw) !important;
}

.fs-4 {
  font-size: 1.25rem !important;
}

.fs-5 {
  font-size: 1.125rem !important;
}

.fs-6 {
  font-size: 1rem !important;
}

.fs-base {
  font-size: var(--body-font-size) !important;
}

.fs-lg {
  font-size: var(--body-font-size-lg) !important;
}

.fs-sm {
  font-size: var(--body-font-size-sm) !important;
}

.fs-xs {
  font-size: var(--body-font-size-xs) !important;
}

.fst-italic {
  font-style: italic !important;
}

.fst-normal {
  font-style: normal !important;
}

.fw-light {
  font-weight: 300 !important;
}

.fw-lighter {
  font-weight: 200 !important;
}

.fw-normal {
  font-weight: 400 !important;
}

.fw-medium {
  font-weight: 500 !important;
}

.fw-semibold {
  font-weight: 600 !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.fw-bolder {
  font-weight: 800 !important;
}

.lh-1 {
  line-height: 1 !important;
}

.lh-xs {
  line-height: var(--body-line-height-xs) !important;
}

.lh-sm {
  line-height: var(--body-line-height-sm) !important;
}

.lh-base {
  line-height: var(--body-line-height) !important;
}

.lh-lg {
  line-height: var(--body-line-height-lg) !important;
}

.text-start {
  text-align: right !important;
}

.text-end {
  text-align: left !important;
}

.text-center {
  text-align: center !important;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}
.text-indigo {
  --text-opacity: 1;
  color: rgba(var(--indigo-rgb), var(--text-opacity)) !important;
}

.text-purple {
  --text-opacity: 1;
  color: rgba(var(--purple-rgb), var(--text-opacity)) !important;
}

.text-pink {
  --text-opacity: 1;
  color: rgba(var(--pink-rgb), var(--text-opacity)) !important;
}

.text-teal {
  --text-opacity: 1;
  color: rgba(var(--teal-rgb), var(--text-opacity)) !important;
}

.text-yellow {
  --text-opacity: 1;
  color: rgba(var(--yellow-rgb), var(--text-opacity)) !important;
}

.text-primary {
  --text-opacity: 1;
  color: rgba(var(--primary-rgb), var(--text-opacity)) !important;
}

.text-secondary {
  --text-opacity: 1;
  color: rgba(var(--secondary-rgb), var(--text-opacity)) !important;
}

.text-success {
  --text-opacity: 1;
  color: rgba(var(--success-rgb), var(--text-opacity)) !important;
}

.text-info {
  --text-opacity: 1;
  color: rgba(var(--info-rgb), var(--text-opacity)) !important;
}

.text-warning {
  --text-opacity: 1;
  color: rgba(var(--warning-rgb), var(--text-opacity)) !important;
}

.text-danger {
  --text-opacity: 1;
  color: rgba(var(--danger-rgb), var(--text-opacity)) !important;
}

.text-light {
  --text-opacity: 1;
  color: rgba(var(--light-rgb), var(--text-opacity)) !important;
}

.text-dark {
  --text-opacity: 1;
  color: rgba(var(--dark-rgb), var(--text-opacity)) !important;
}

.text-black {
  --text-opacity: 1;
  color: rgba(var(--black-rgb), var(--text-opacity)) !important;
}

.text-white {
  --text-opacity: 1;
  color: rgba(var(--white-rgb), var(--text-opacity)) !important;
}

.text-body {
  --text-opacity: 1;
  color: rgba(var(--body-color-rgb), var(--text-opacity)) !important;
}

.text-muted {
  --text-opacity: 1;
  color: rgba(var(--body-color-rgb), 0.75) !important;
}

.text-reset {
  --text-opacity: 1;
  color: inherit !important;
}

.text-opacity-25 {
  --text-opacity: 0.25;
}

.text-opacity-50 {
  --text-opacity: 0.5;
}

.text-opacity-75 {
  --text-opacity: 0.75;
}

.text-opacity-100 {
  --text-opacity: 1;
}

.bg-indigo {
  --bg-opacity: 1;
  background-color: rgba(var(--indigo-rgb), var(--bg-opacity)) !important;
}

.bg-purple {
  --bg-opacity: 1;
  background-color: rgba(var(--purple-rgb), var(--bg-opacity)) !important;
}

.bg-pink {
  --bg-opacity: 1;
  background-color: rgba(var(--pink-rgb), var(--bg-opacity)) !important;
}

.bg-teal {
  --bg-opacity: 1;
  background-color: rgba(var(--teal-rgb), var(--bg-opacity)) !important;
}

.bg-yellow {
  --bg-opacity: 1;
  background-color: rgba(var(--yellow-rgb), var(--bg-opacity)) !important;
}

.bg-primary {
  --bg-opacity: 1;
  background-color: rgba(var(--primary-rgb), var(--bg-opacity)) !important;
}

.bg-secondary {
  --bg-opacity: 1;
  background-color: rgba(var(--secondary-rgb), var(--bg-opacity)) !important;
}

.bg-success {
  --bg-opacity: 1;
  background-color: rgba(var(--success-rgb), var(--bg-opacity)) !important;
}

.bg-info {
  --bg-opacity: 1;
  background-color: rgba(var(--info-rgb), var(--bg-opacity)) !important;
}

.bg-warning {
  --bg-opacity: 1;
  background-color: rgba(var(--warning-rgb), var(--bg-opacity)) !important;
}

.bg-danger {
  --bg-opacity: 1;
  background-color: rgba(var(--danger-rgb), var(--bg-opacity)) !important;
}

.bg-light {
  --bg-opacity: 1;
  background-color: rgba(var(--light-rgb), var(--bg-opacity)) !important;
}

.bg-dark {
  --bg-opacity: 1;
  background-color: rgba(var(--dark-rgb), var(--bg-opacity)) !important;
}

.bg-black {
  --bg-opacity: 1;
  background-color: rgba(var(--black-rgb), var(--bg-opacity)) !important;
}

.bg-white {
  --bg-opacity: 1;
  background-color: rgba(var(--white-rgb), var(--bg-opacity)) !important;
}

.bg-body {
  --bg-opacity: 1;
  background-color: rgba(var(--body-bg-rgb), var(--bg-opacity)) !important;
}

.bg-transparent {
  --bg-opacity: 1;
  background-color: transparent !important;
}

.bg-card {
  --bg-opacity: 1;
  background-color: var(--white) !important;
}

.bg-opacity-10 {
  --bg-opacity: 0.1;
}

.bg-opacity-20 {
  --bg-opacity: 0.2;
}

.bg-opacity-25 {
  --bg-opacity: 0.25;
}

.bg-opacity-50 {
  --bg-opacity: 0.5;
}

.bg-opacity-75 {
  --bg-opacity: 0.75;
}

.bg-opacity-100 {
  --bg-opacity: 1;
}

.bg-gradient {
  background-image: var(--gradient) !important;
}

.user-select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
          user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
      -ms-user-select: auto !important;
          user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
      -ms-user-select: none !important;
          user-select: none !important;
}

.pe-none {
  pointer-events: none !important;
}

.pe-auto {
  pointer-events: auto !important;
}

.rounded {
  border-radius: var(--border-radius) !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-1 {
  border-radius: var(--border-radius-sm) !important;
}

.rounded-2 {
  border-radius: var(--border-radius) !important;
}

.rounded-3 {
  border-radius: var(--border-radius-lg) !important;
}

.rounded-4 {
  border-radius: var(--border-radius-xl) !important;
}

.rounded-5 {
  border-radius: var(--border-radius-2xl) !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: var(--border-radius-pill) !important;
}

.rounded-top {
  border-top-right-radius: var(--border-radius) !important;
  border-top-left-radius: var(--border-radius) !important;
}

.rounded-top-0 {
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important;
}

.rounded-top-1 {
  border-top-right-radius: var(--border-radius-sm) !important;
  border-top-left-radius: var(--border-radius-sm) !important;
}

.rounded-top-2 {
  border-top-right-radius: var(--border-radius) !important;
  border-top-left-radius: var(--border-radius) !important;
}

.rounded-top-pill {
  border-top-right-radius: var(--border-radius-pill) !important;
  border-top-left-radius: var(--border-radius-pill) !important;
}

.rounded-end {
  border-top-left-radius: var(--border-radius) !important;
  border-bottom-left-radius: var(--border-radius) !important;
}

.rounded-end-0 {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.rounded-end-1 {
  border-top-left-radius: var(--border-radius-sm) !important;
  border-bottom-left-radius: var(--border-radius-sm) !important;
}

.rounded-end-2 {
  border-top-left-radius: var(--border-radius) !important;
  border-bottom-left-radius: var(--border-radius) !important;
}

.rounded-end-pill {
  border-top-left-radius: var(--border-radius-pill) !important;
  border-bottom-left-radius: var(--border-radius-pill) !important;
}

.rounded-bottom {
  border-bottom-left-radius: var(--border-radius) !important;
  border-bottom-right-radius: var(--border-radius) !important;
}

.rounded-bottom-0 {
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.rounded-bottom-1 {
  border-bottom-left-radius: var(--border-radius-sm) !important;
  border-bottom-right-radius: var(--border-radius-sm) !important;
}

.rounded-bottom-2 {
  border-bottom-left-radius: var(--border-radius) !important;
  border-bottom-right-radius: var(--border-radius) !important;
}

.rounded-bottom-pill {
  border-bottom-left-radius: var(--border-radius-pill) !important;
  border-bottom-right-radius: var(--border-radius-pill) !important;
}

.rounded-start {
  border-bottom-right-radius: var(--border-radius) !important;
  border-top-right-radius: var(--border-radius) !important;
}

.rounded-start-0 {
  border-bottom-right-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.rounded-start-1 {
  border-bottom-right-radius: var(--border-radius-sm) !important;
  border-top-right-radius: var(--border-radius-sm) !important;
}

.rounded-start-2 {
  border-bottom-right-radius: var(--border-radius) !important;
  border-top-right-radius: var(--border-radius) !important;
}

.rounded-start-pill {
  border-bottom-right-radius: var(--border-radius-pill) !important;
  border-top-right-radius: var(--border-radius-pill) !important;
}

.rounded-top-start {
  border-top-right-radius: 0.375rem !important;
}

.rounded-top-start-0 {
  border-top-right-radius: 0 !important;
}

.rounded-top-end {
  border-top-left-radius: 0.375rem !important;
}

.rounded-top-end-0 {
  border-top-left-radius: 0 !important;
}

.rounded-bottom-start {
  border-bottom-right-radius: 0.375rem !important;
}

.rounded-bottom-start-0 {
  border-bottom-right-radius: 0 !important;
}

.rounded-bottom-end {
  border-bottom-left-radius: 0.375rem !important;
}

.rounded-bottom-end-0 {
  border-bottom-left-radius: 0 !important;
}

.filter-none {
  -webkit-filter: none !important;
          filter: none !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.cursor-move {
  cursor: move !important;
}

.cursor-default {
  cursor: default !important;
}

.rotate-cw-45 {
  -webkit-transform: rotate(-45deg) !important;
          transform: rotate(-45deg) !important;
}

.rotate-cw-90 {
  -webkit-transform: rotate(-90deg) !important;
          transform: rotate(-90deg) !important;
}

.rotate-cw-180 {
  -webkit-transform: rotate(-180deg) !important;
          transform: rotate(-180deg) !important;
}

.rotate-ccw-45 {
  -webkit-transform: rotate(45deg) !important;
          transform: rotate(45deg) !important;
}

.rotate-ccw-90 {
  -webkit-transform: rotate(90deg) !important;
          transform: rotate(90deg) !important;
}

.rotate-ccw-180 {
  -webkit-transform: rotate(180deg) !important;
          transform: rotate(180deg) !important;
}

.transition-none {
  transition: none !important;
}

.zindex-auto {
  z-index: auto !important;
}

.zindex-1 {
  z-index: 1 !important;
}

@media (min-width: 576px) {
  .float-sm-start {
    float: right !important;
  }
  .float-sm-end {
    float: left !important;
  }
  .float-sm-none {
    float: none !important;
  }
  .overflow-sm-auto {
    overflow: auto !important;
  }
  .overflow-sm-hidden {
    overflow: hidden !important;
  }
  .overflow-sm-visible {
    overflow: visible !important;
  }
  .overflow-sm-scroll {
    overflow: scroll !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-grid {
    display: grid !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
  .d-sm-none {
    display: none !important;
  }
  .border-sm {
    border: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-sm-0 {
    border: 0 !important;
  }
  .border-top-sm {
    border-top: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-top-sm-0 {
    border-top: 0 !important;
  }
  .border-end-sm {
    border-left: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-end-sm-0 {
    border-left: 0 !important;
  }
  .border-bottom-sm {
    border-bottom: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-bottom-sm-0 {
    border-bottom: 0 !important;
  }
  .border-start-sm {
    border-right: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-start-sm-0 {
    border-right: 0 !important;
  }
  .w-sm-25 {
    width: 25% !important;
  }
  .w-sm-50 {
    width: 50% !important;
  }
  .w-sm-75 {
    width: 75% !important;
  }
  .w-sm-100 {
    width: 100% !important;
  }
  .w-sm-auto {
    width: auto !important;
  }
  .wmin-sm-0 {
    min-width: 0 !important;
  }
  .wmin-sm-200 {
    min-width: 200px !important;
  }
  .wmin-sm-250 {
    min-width: 250px !important;
  }
  .wmin-sm-300 {
    min-width: 300px !important;
  }
  .wmin-sm-350 {
    min-width: 350px !important;
  }
  .wmin-sm-400 {
    min-width: 400px !important;
  }
  .wmin-sm-450 {
    min-width: 450px !important;
  }
  .wmin-sm-500 {
    min-width: 500px !important;
  }
  .wmin-sm-550 {
    min-width: 550px !important;
  }
  .wmin-sm-600 {
    min-width: 600px !important;
  }
  .h-sm-25 {
    height: 25% !important;
  }
  .h-sm-50 {
    height: 50% !important;
  }
  .h-sm-75 {
    height: 75% !important;
  }
  .h-sm-100 {
    height: 100% !important;
  }
  .h-sm-auto {
    height: auto !important;
  }
  .flex-sm-0 {
    -ms-flex: 0 1 auto !important;
        flex: 0 1 auto !important;
  }
  .flex-sm-1 {
    -ms-flex: 1 !important;
        flex: 1 !important;
  }
  .flex-sm-fill {
    -ms-flex: 1 1 auto !important;
        flex: 1 1 auto !important;
  }
  .flex-sm-row {
    -ms-flex-direction: row !important;
        flex-direction: row !important;
  }
  .flex-sm-column {
    -ms-flex-direction: column !important;
        flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important;
  }
  .flex-sm-grow-0 {
    -ms-flex-positive: 0 !important;
        flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    -ms-flex-positive: 1 !important;
        flex-grow: 1 !important;
  }
  .flex-sm-grow-2 {
    -ms-flex-positive: 2 !important;
        flex-grow: 2 !important;
  }
  .flex-sm-grow-3 {
    -ms-flex-positive: 3 !important;
        flex-grow: 3 !important;
  }
  .flex-sm-shrink-0 {
    -ms-flex-negative: 0 !important;
        flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    -ms-flex-negative: 1 !important;
        flex-shrink: 1 !important;
  }
  .flex-sm-wrap {
    -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important;
  }
  .justify-content-sm-start {
    -ms-flex-pack: start !important;
        justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    -ms-flex-pack: end !important;
        justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    -ms-flex-pack: center !important;
        justify-content: center !important;
  }
  .justify-content-sm-between {
    -ms-flex-pack: justify !important;
        justify-content: space-between !important;
  }
  .justify-content-sm-around {
    -ms-flex-pack: distribute !important;
        justify-content: space-around !important;
  }
  .justify-content-sm-evenly {
    -ms-flex-pack: space-evenly !important;
        justify-content: space-evenly !important;
  }
  .align-items-sm-start {
    -ms-flex-align: start !important;
        align-items: flex-start !important;
  }
  .align-items-sm-end {
    -ms-flex-align: end !important;
        align-items: flex-end !important;
  }
  .align-items-sm-center {
    -ms-flex-align: center !important;
        align-items: center !important;
  }
  .align-items-sm-baseline {
    -ms-flex-align: baseline !important;
        align-items: baseline !important;
  }
  .align-items-sm-stretch {
    -ms-flex-align: stretch !important;
        align-items: stretch !important;
  }
  .align-content-sm-start {
    -ms-flex-line-pack: start !important;
        align-content: flex-start !important;
  }
  .align-content-sm-end {
    -ms-flex-line-pack: end !important;
        align-content: flex-end !important;
  }
  .align-content-sm-center {
    -ms-flex-line-pack: center !important;
        align-content: center !important;
  }
  .align-content-sm-between {
    -ms-flex-line-pack: justify !important;
        align-content: space-between !important;
  }
  .align-content-sm-around {
    -ms-flex-line-pack: distribute !important;
        align-content: space-around !important;
  }
  .align-content-sm-stretch {
    -ms-flex-line-pack: stretch !important;
        align-content: stretch !important;
  }
  .align-self-sm-auto {
    -ms-flex-item-align: auto !important;
        align-self: auto !important;
  }
  .align-self-sm-start {
    -ms-flex-item-align: start !important;
        align-self: flex-start !important;
  }
  .align-self-sm-end {
    -ms-flex-item-align: end !important;
        align-self: flex-end !important;
  }
  .align-self-sm-center {
    -ms-flex-item-align: center !important;
        align-self: center !important;
  }
  .align-self-sm-baseline {
    -ms-flex-item-align: baseline !important;
        align-self: baseline !important;
  }
  .align-self-sm-stretch {
    -ms-flex-item-align: stretch !important;
        align-self: stretch !important;
  }
  .order-sm-first {
    -ms-flex-order: -1 !important;
        order: -1 !important;
  }
  .order-sm-0 {
    -ms-flex-order: 0 !important;
        order: 0 !important;
  }
  .order-sm-1 {
    -ms-flex-order: 1 !important;
        order: 1 !important;
  }
  .order-sm-2 {
    -ms-flex-order: 2 !important;
        order: 2 !important;
  }
  .order-sm-3 {
    -ms-flex-order: 3 !important;
        order: 3 !important;
  }
  .order-sm-4 {
    -ms-flex-order: 4 !important;
        order: 4 !important;
  }
  .order-sm-5 {
    -ms-flex-order: 5 !important;
        order: 5 !important;
  }
  .order-sm-last {
    -ms-flex-order: 6 !important;
        order: 6 !important;
  }
  .m-sm-0 {
    margin: 0 !important;
  }
  .m-sm-1 {
    margin: 0.3125rem !important;
  }
  .m-sm-2 {
    margin: 0.625rem !important;
  }
  .m-sm-3 {
    margin: 1.25rem !important;
  }
  .m-sm-4 {
    margin: 1.875rem !important;
  }
  .m-sm-5 {
    margin: 3.75rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mx-sm-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .mx-sm-1 {
    margin-left: 0.3125rem !important;
    margin-right: 0.3125rem !important;
  }
  .mx-sm-2 {
    margin-left: 0.625rem !important;
    margin-right: 0.625rem !important;
  }
  .mx-sm-3 {
    margin-left: 1.25rem !important;
    margin-right: 1.25rem !important;
  }
  .mx-sm-4 {
    margin-left: 1.875rem !important;
    margin-right: 1.875rem !important;
  }
  .mx-sm-5 {
    margin-left: 3.75rem !important;
    margin-right: 3.75rem !important;
  }
  .mx-sm-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-sm-1 {
    margin-top: 0.3125rem !important;
    margin-bottom: 0.3125rem !important;
  }
  .my-sm-2 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }
  .my-sm-3 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }
  .my-sm-4 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }
  .my-sm-5 {
    margin-top: 3.75rem !important;
    margin-bottom: 3.75rem !important;
  }
  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-sm-0 {
    margin-top: 0 !important;
  }
  .mt-sm-1 {
    margin-top: 0.3125rem !important;
  }
  .mt-sm-2 {
    margin-top: 0.625rem !important;
  }
  .mt-sm-3 {
    margin-top: 1.25rem !important;
  }
  .mt-sm-4 {
    margin-top: 1.875rem !important;
  }
  .mt-sm-5 {
    margin-top: 3.75rem !important;
  }
  .mt-sm-auto {
    margin-top: auto !important;
  }
  .me-sm-0 {
    margin-left: 0 !important;
  }
  .me-sm-1 {
    margin-left: 0.3125rem !important;
  }
  .me-sm-2 {
    margin-left: 0.625rem !important;
  }
  .me-sm-3 {
    margin-left: 1.25rem !important;
  }
  .me-sm-4 {
    margin-left: 1.875rem !important;
  }
  .me-sm-5 {
    margin-left: 3.75rem !important;
  }
  .me-sm-auto {
    margin-left: auto !important;
  }
  .mb-sm-0 {
    margin-bottom: 0 !important;
  }
  .mb-sm-1 {
    margin-bottom: 0.3125rem !important;
  }
  .mb-sm-2 {
    margin-bottom: 0.625rem !important;
  }
  .mb-sm-3 {
    margin-bottom: 1.25rem !important;
  }
  .mb-sm-4 {
    margin-bottom: 1.875rem !important;
  }
  .mb-sm-5 {
    margin-bottom: 3.75rem !important;
  }
  .mb-sm-auto {
    margin-bottom: auto !important;
  }
  .ms-sm-0 {
    margin-right: 0 !important;
  }
  .ms-sm-1 {
    margin-right: 0.3125rem !important;
  }
  .ms-sm-2 {
    margin-right: 0.625rem !important;
  }
  .ms-sm-3 {
    margin-right: 1.25rem !important;
  }
  .ms-sm-4 {
    margin-right: 1.875rem !important;
  }
  .ms-sm-5 {
    margin-right: 3.75rem !important;
  }
  .ms-sm-auto {
    margin-right: auto !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .p-sm-1 {
    padding: 0.3125rem !important;
  }
  .p-sm-2 {
    padding: 0.625rem !important;
  }
  .p-sm-3 {
    padding: 1.25rem !important;
  }
  .p-sm-4 {
    padding: 1.875rem !important;
  }
  .p-sm-5 {
    padding: 3.75rem !important;
  }
  .px-sm-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-sm-1 {
    padding-left: 0.3125rem !important;
    padding-right: 0.3125rem !important;
  }
  .px-sm-2 {
    padding-left: 0.625rem !important;
    padding-right: 0.625rem !important;
  }
  .px-sm-3 {
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
  }
  .px-sm-4 {
    padding-left: 1.875rem !important;
    padding-right: 1.875rem !important;
  }
  .px-sm-5 {
    padding-left: 3.75rem !important;
    padding-right: 3.75rem !important;
  }
  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-sm-1 {
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
  }
  .py-sm-2 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }
  .py-sm-3 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }
  .py-sm-4 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }
  .py-sm-5 {
    padding-top: 3.75rem !important;
    padding-bottom: 3.75rem !important;
  }
  .pt-sm-0 {
    padding-top: 0 !important;
  }
  .pt-sm-1 {
    padding-top: 0.3125rem !important;
  }
  .pt-sm-2 {
    padding-top: 0.625rem !important;
  }
  .pt-sm-3 {
    padding-top: 1.25rem !important;
  }
  .pt-sm-4 {
    padding-top: 1.875rem !important;
  }
  .pt-sm-5 {
    padding-top: 3.75rem !important;
  }
  .pe-sm-0 {
    padding-left: 0 !important;
  }
  .pe-sm-1 {
    padding-left: 0.3125rem !important;
  }
  .pe-sm-2 {
    padding-left: 0.625rem !important;
  }
  .pe-sm-3 {
    padding-left: 1.25rem !important;
  }
  .pe-sm-4 {
    padding-left: 1.875rem !important;
  }
  .pe-sm-5 {
    padding-left: 3.75rem !important;
  }
  .pb-sm-0 {
    padding-bottom: 0 !important;
  }
  .pb-sm-1 {
    padding-bottom: 0.3125rem !important;
  }
  .pb-sm-2 {
    padding-bottom: 0.625rem !important;
  }
  .pb-sm-3 {
    padding-bottom: 1.25rem !important;
  }
  .pb-sm-4 {
    padding-bottom: 1.875rem !important;
  }
  .pb-sm-5 {
    padding-bottom: 3.75rem !important;
  }
  .ps-sm-0 {
    padding-right: 0 !important;
  }
  .ps-sm-1 {
    padding-right: 0.3125rem !important;
  }
  .ps-sm-2 {
    padding-right: 0.625rem !important;
  }
  .ps-sm-3 {
    padding-right: 1.25rem !important;
  }
  .ps-sm-4 {
    padding-right: 1.875rem !important;
  }
  .ps-sm-5 {
    padding-right: 3.75rem !important;
  }
  .gap-sm-0 {
    gap: 0 !important;
  }
  .gap-sm-1 {
    gap: 0.3125rem !important;
  }
  .gap-sm-2 {
    gap: 0.625rem !important;
  }
  .gap-sm-3 {
    gap: 1.25rem !important;
  }
  .gap-sm-4 {
    gap: 1.875rem !important;
  }
  .gap-sm-5 {
    gap: 3.75rem !important;
  }
  .text-sm-start {
    text-align: right !important;
  }
  .text-sm-end {
    text-align: left !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
  .rounded-sm {
    border-radius: var(--border-radius) !important;
  }
  .rounded-sm-0 {
    border-radius: 0 !important;
  }
  .rounded-sm-1 {
    border-radius: var(--border-radius-sm) !important;
  }
  .rounded-sm-2 {
    border-radius: var(--border-radius) !important;
  }
  .rounded-sm-3 {
    border-radius: var(--border-radius-lg) !important;
  }
  .rounded-sm-4 {
    border-radius: var(--border-radius-xl) !important;
  }
  .rounded-sm-5 {
    border-radius: var(--border-radius-2xl) !important;
  }
  .rounded-sm-circle {
    border-radius: 50% !important;
  }
  .rounded-sm-pill {
    border-radius: var(--border-radius-pill) !important;
  }
  .rounded-top-sm {
    border-top-right-radius: var(--border-radius) !important;
    border-top-left-radius: var(--border-radius) !important;
  }
  .rounded-top-sm-0 {
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }
  .rounded-top-sm-1 {
    border-top-right-radius: var(--border-radius-sm) !important;
    border-top-left-radius: var(--border-radius-sm) !important;
  }
  .rounded-top-sm-2 {
    border-top-right-radius: var(--border-radius) !important;
    border-top-left-radius: var(--border-radius) !important;
  }
  .rounded-top-sm-pill {
    border-top-right-radius: var(--border-radius-pill) !important;
    border-top-left-radius: var(--border-radius-pill) !important;
  }
  .rounded-end-sm {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
  }
  .rounded-end-sm-0 {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  .rounded-end-sm-1 {
    border-top-left-radius: var(--border-radius-sm) !important;
    border-bottom-left-radius: var(--border-radius-sm) !important;
  }
  .rounded-end-sm-2 {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
  }
  .rounded-end-sm-pill {
    border-top-left-radius: var(--border-radius-pill) !important;
    border-bottom-left-radius: var(--border-radius-pill) !important;
  }
  .rounded-bottom-sm {
    border-bottom-left-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
  }
  .rounded-bottom-sm-0 {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  .rounded-bottom-sm-1 {
    border-bottom-left-radius: var(--border-radius-sm) !important;
    border-bottom-right-radius: var(--border-radius-sm) !important;
  }
  .rounded-bottom-sm-2 {
    border-bottom-left-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
  }
  .rounded-bottom-sm-pill {
    border-bottom-left-radius: var(--border-radius-pill) !important;
    border-bottom-right-radius: var(--border-radius-pill) !important;
  }
  .rounded-start-sm {
    border-bottom-right-radius: var(--border-radius) !important;
    border-top-right-radius: var(--border-radius) !important;
  }
  .rounded-start-sm-0 {
    border-bottom-right-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }
  .rounded-start-sm-1 {
    border-bottom-right-radius: var(--border-radius-sm) !important;
    border-top-right-radius: var(--border-radius-sm) !important;
  }
  .rounded-start-sm-2 {
    border-bottom-right-radius: var(--border-radius) !important;
    border-top-right-radius: var(--border-radius) !important;
  }
  .rounded-start-sm-pill {
    border-bottom-right-radius: var(--border-radius-pill) !important;
    border-top-right-radius: var(--border-radius-pill) !important;
  }
}
@media (min-width: 768px) {
  .float-md-start {
    float: right !important;
  }
  .float-md-end {
    float: left !important;
  }
  .float-md-none {
    float: none !important;
  }
  .overflow-md-auto {
    overflow: auto !important;
  }
  .overflow-md-hidden {
    overflow: hidden !important;
  }
  .overflow-md-visible {
    overflow: visible !important;
  }
  .overflow-md-scroll {
    overflow: scroll !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-grid {
    display: grid !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-md-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
  .d-md-none {
    display: none !important;
  }
  .border-md {
    border: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-md-0 {
    border: 0 !important;
  }
  .border-top-md {
    border-top: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-top-md-0 {
    border-top: 0 !important;
  }
  .border-end-md {
    border-left: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-end-md-0 {
    border-left: 0 !important;
  }
  .border-bottom-md {
    border-bottom: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-bottom-md-0 {
    border-bottom: 0 !important;
  }
  .border-start-md {
    border-right: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-start-md-0 {
    border-right: 0 !important;
  }
  .w-md-25 {
    width: 25% !important;
  }
  .w-md-50 {
    width: 50% !important;
  }
  .w-md-75 {
    width: 75% !important;
  }
  .w-md-100 {
    width: 100% !important;
  }
  .w-md-auto {
    width: auto !important;
  }
  .wmin-md-0 {
    min-width: 0 !important;
  }
  .wmin-md-200 {
    min-width: 200px !important;
  }
  .wmin-md-250 {
    min-width: 250px !important;
  }
  .wmin-md-300 {
    min-width: 300px !important;
  }
  .wmin-md-350 {
    min-width: 350px !important;
  }
  .wmin-md-400 {
    min-width: 400px !important;
  }
  .wmin-md-450 {
    min-width: 450px !important;
  }
  .wmin-md-500 {
    min-width: 500px !important;
  }
  .wmin-md-550 {
    min-width: 550px !important;
  }
  .wmin-md-600 {
    min-width: 600px !important;
  }
  .h-md-25 {
    height: 25% !important;
  }
  .h-md-50 {
    height: 50% !important;
  }
  .h-md-75 {
    height: 75% !important;
  }
  .h-md-100 {
    height: 100% !important;
  }
  .h-md-auto {
    height: auto !important;
  }
  .flex-md-0 {
    -ms-flex: 0 1 auto !important;
        flex: 0 1 auto !important;
  }
  .flex-md-1 {
    -ms-flex: 1 !important;
        flex: 1 !important;
  }
  .flex-md-fill {
    -ms-flex: 1 1 auto !important;
        flex: 1 1 auto !important;
  }
  .flex-md-row {
    -ms-flex-direction: row !important;
        flex-direction: row !important;
  }
  .flex-md-column {
    -ms-flex-direction: column !important;
        flex-direction: column !important;
  }
  .flex-md-row-reverse {
    -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important;
  }
  .flex-md-grow-0 {
    -ms-flex-positive: 0 !important;
        flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    -ms-flex-positive: 1 !important;
        flex-grow: 1 !important;
  }
  .flex-md-grow-2 {
    -ms-flex-positive: 2 !important;
        flex-grow: 2 !important;
  }
  .flex-md-grow-3 {
    -ms-flex-positive: 3 !important;
        flex-grow: 3 !important;
  }
  .flex-md-shrink-0 {
    -ms-flex-negative: 0 !important;
        flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    -ms-flex-negative: 1 !important;
        flex-shrink: 1 !important;
  }
  .flex-md-wrap {
    -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important;
  }
  .justify-content-md-start {
    -ms-flex-pack: start !important;
        justify-content: flex-start !important;
  }
  .justify-content-md-end {
    -ms-flex-pack: end !important;
        justify-content: flex-end !important;
  }
  .justify-content-md-center {
    -ms-flex-pack: center !important;
        justify-content: center !important;
  }
  .justify-content-md-between {
    -ms-flex-pack: justify !important;
        justify-content: space-between !important;
  }
  .justify-content-md-around {
    -ms-flex-pack: distribute !important;
        justify-content: space-around !important;
  }
  .justify-content-md-evenly {
    -ms-flex-pack: space-evenly !important;
        justify-content: space-evenly !important;
  }
  .align-items-md-start {
    -ms-flex-align: start !important;
        align-items: flex-start !important;
  }
  .align-items-md-end {
    -ms-flex-align: end !important;
        align-items: flex-end !important;
  }
  .align-items-md-center {
    -ms-flex-align: center !important;
        align-items: center !important;
  }
  .align-items-md-baseline {
    -ms-flex-align: baseline !important;
        align-items: baseline !important;
  }
  .align-items-md-stretch {
    -ms-flex-align: stretch !important;
        align-items: stretch !important;
  }
  .align-content-md-start {
    -ms-flex-line-pack: start !important;
        align-content: flex-start !important;
  }
  .align-content-md-end {
    -ms-flex-line-pack: end !important;
        align-content: flex-end !important;
  }
  .align-content-md-center {
    -ms-flex-line-pack: center !important;
        align-content: center !important;
  }
  .align-content-md-between {
    -ms-flex-line-pack: justify !important;
        align-content: space-between !important;
  }
  .align-content-md-around {
    -ms-flex-line-pack: distribute !important;
        align-content: space-around !important;
  }
  .align-content-md-stretch {
    -ms-flex-line-pack: stretch !important;
        align-content: stretch !important;
  }
  .align-self-md-auto {
    -ms-flex-item-align: auto !important;
        align-self: auto !important;
  }
  .align-self-md-start {
    -ms-flex-item-align: start !important;
        align-self: flex-start !important;
  }
  .align-self-md-end {
    -ms-flex-item-align: end !important;
        align-self: flex-end !important;
  }
  .align-self-md-center {
    -ms-flex-item-align: center !important;
        align-self: center !important;
  }
  .align-self-md-baseline {
    -ms-flex-item-align: baseline !important;
        align-self: baseline !important;
  }
  .align-self-md-stretch {
    -ms-flex-item-align: stretch !important;
        align-self: stretch !important;
  }
  .order-md-first {
    -ms-flex-order: -1 !important;
        order: -1 !important;
  }
  .order-md-0 {
    -ms-flex-order: 0 !important;
        order: 0 !important;
  }
  .order-md-1 {
    -ms-flex-order: 1 !important;
        order: 1 !important;
  }
  .order-md-2 {
    -ms-flex-order: 2 !important;
        order: 2 !important;
  }
  .order-md-3 {
    -ms-flex-order: 3 !important;
        order: 3 !important;
  }
  .order-md-4 {
    -ms-flex-order: 4 !important;
        order: 4 !important;
  }
  .order-md-5 {
    -ms-flex-order: 5 !important;
        order: 5 !important;
  }
  .order-md-last {
    -ms-flex-order: 6 !important;
        order: 6 !important;
  }
  .m-md-0 {
    margin: 0 !important;
  }
  .m-md-1 {
    margin: 0.3125rem !important;
  }
  .m-md-2 {
    margin: 0.625rem !important;
  }
  .m-md-3 {
    margin: 1.25rem !important;
  }
  .m-md-4 {
    margin: 1.875rem !important;
  }
  .m-md-5 {
    margin: 3.75rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mx-md-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .mx-md-1 {
    margin-left: 0.3125rem !important;
    margin-right: 0.3125rem !important;
  }
  .mx-md-2 {
    margin-left: 0.625rem !important;
    margin-right: 0.625rem !important;
  }
  .mx-md-3 {
    margin-left: 1.25rem !important;
    margin-right: 1.25rem !important;
  }
  .mx-md-4 {
    margin-left: 1.875rem !important;
    margin-right: 1.875rem !important;
  }
  .mx-md-5 {
    margin-left: 3.75rem !important;
    margin-right: 3.75rem !important;
  }
  .mx-md-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-md-1 {
    margin-top: 0.3125rem !important;
    margin-bottom: 0.3125rem !important;
  }
  .my-md-2 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }
  .my-md-3 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }
  .my-md-4 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }
  .my-md-5 {
    margin-top: 3.75rem !important;
    margin-bottom: 3.75rem !important;
  }
  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-md-0 {
    margin-top: 0 !important;
  }
  .mt-md-1 {
    margin-top: 0.3125rem !important;
  }
  .mt-md-2 {
    margin-top: 0.625rem !important;
  }
  .mt-md-3 {
    margin-top: 1.25rem !important;
  }
  .mt-md-4 {
    margin-top: 1.875rem !important;
  }
  .mt-md-5 {
    margin-top: 3.75rem !important;
  }
  .mt-md-auto {
    margin-top: auto !important;
  }
  .me-md-0 {
    margin-left: 0 !important;
  }
  .me-md-1 {
    margin-left: 0.3125rem !important;
  }
  .me-md-2 {
    margin-left: 0.625rem !important;
  }
  .me-md-3 {
    margin-left: 1.25rem !important;
  }
  .me-md-4 {
    margin-left: 1.875rem !important;
  }
  .me-md-5 {
    margin-left: 3.75rem !important;
  }
  .me-md-auto {
    margin-left: auto !important;
  }
  .mb-md-0 {
    margin-bottom: 0 !important;
  }
  .mb-md-1 {
    margin-bottom: 0.3125rem !important;
  }
  .mb-md-2 {
    margin-bottom: 0.625rem !important;
  }
  .mb-md-3 {
    margin-bottom: 1.25rem !important;
  }
  .mb-md-4 {
    margin-bottom: 1.875rem !important;
  }
  .mb-md-5 {
    margin-bottom: 3.75rem !important;
  }
  .mb-md-auto {
    margin-bottom: auto !important;
  }
  .ms-md-0 {
    margin-right: 0 !important;
  }
  .ms-md-1 {
    margin-right: 0.3125rem !important;
  }
  .ms-md-2 {
    margin-right: 0.625rem !important;
  }
  .ms-md-3 {
    margin-right: 1.25rem !important;
  }
  .ms-md-4 {
    margin-right: 1.875rem !important;
  }
  .ms-md-5 {
    margin-right: 3.75rem !important;
  }
  .ms-md-auto {
    margin-right: auto !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .p-md-1 {
    padding: 0.3125rem !important;
  }
  .p-md-2 {
    padding: 0.625rem !important;
  }
  .p-md-3 {
    padding: 1.25rem !important;
  }
  .p-md-4 {
    padding: 1.875rem !important;
  }
  .p-md-5 {
    padding: 3.75rem !important;
  }
  .px-md-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-md-1 {
    padding-left: 0.3125rem !important;
    padding-right: 0.3125rem !important;
  }
  .px-md-2 {
    padding-left: 0.625rem !important;
    padding-right: 0.625rem !important;
  }
  .px-md-3 {
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
  }
  .px-md-4 {
    padding-left: 1.875rem !important;
    padding-right: 1.875rem !important;
  }
  .px-md-5 {
    padding-left: 3.75rem !important;
    padding-right: 3.75rem !important;
  }
  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-md-1 {
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
  }
  .py-md-2 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }
  .py-md-3 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }
  .py-md-4 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }
  .py-md-5 {
    padding-top: 3.75rem !important;
    padding-bottom: 3.75rem !important;
  }
  .pt-md-0 {
    padding-top: 0 !important;
  }
  .pt-md-1 {
    padding-top: 0.3125rem !important;
  }
  .pt-md-2 {
    padding-top: 0.625rem !important;
  }
  .pt-md-3 {
    padding-top: 1.25rem !important;
  }
  .pt-md-4 {
    padding-top: 1.875rem !important;
  }
  .pt-md-5 {
    padding-top: 3.75rem !important;
  }
  .pe-md-0 {
    padding-left: 0 !important;
  }
  .pe-md-1 {
    padding-left: 0.3125rem !important;
  }
  .pe-md-2 {
    padding-left: 0.625rem !important;
  }
  .pe-md-3 {
    padding-left: 1.25rem !important;
  }
  .pe-md-4 {
    padding-left: 1.875rem !important;
  }
  .pe-md-5 {
    padding-left: 3.75rem !important;
  }
  .pb-md-0 {
    padding-bottom: 0 !important;
  }
  .pb-md-1 {
    padding-bottom: 0.3125rem !important;
  }
  .pb-md-2 {
    padding-bottom: 0.625rem !important;
  }
  .pb-md-3 {
    padding-bottom: 1.25rem !important;
  }
  .pb-md-4 {
    padding-bottom: 1.875rem !important;
  }
  .pb-md-5 {
    padding-bottom: 3.75rem !important;
  }
  .ps-md-0 {
    padding-right: 0 !important;
  }
  .ps-md-1 {
    padding-right: 0.3125rem !important;
  }
  .ps-md-2 {
    padding-right: 0.625rem !important;
  }
  .ps-md-3 {
    padding-right: 1.25rem !important;
  }
  .ps-md-4 {
    padding-right: 1.875rem !important;
  }
  .ps-md-5 {
    padding-right: 3.75rem !important;
  }
  .gap-md-0 {
    gap: 0 !important;
  }
  .gap-md-1 {
    gap: 0.3125rem !important;
  }
  .gap-md-2 {
    gap: 0.625rem !important;
  }
  .gap-md-3 {
    gap: 1.25rem !important;
  }
  .gap-md-4 {
    gap: 1.875rem !important;
  }
  .gap-md-5 {
    gap: 3.75rem !important;
  }
  .text-md-start {
    text-align: right !important;
  }
  .text-md-end {
    text-align: left !important;
  }
  .text-md-center {
    text-align: center !important;
  }
  .rounded-md {
    border-radius: var(--border-radius) !important;
  }
  .rounded-md-0 {
    border-radius: 0 !important;
  }
  .rounded-md-1 {
    border-radius: var(--border-radius-sm) !important;
  }
  .rounded-md-2 {
    border-radius: var(--border-radius) !important;
  }
  .rounded-md-3 {
    border-radius: var(--border-radius-lg) !important;
  }
  .rounded-md-4 {
    border-radius: var(--border-radius-xl) !important;
  }
  .rounded-md-5 {
    border-radius: var(--border-radius-2xl) !important;
  }
  .rounded-md-circle {
    border-radius: 50% !important;
  }
  .rounded-md-pill {
    border-radius: var(--border-radius-pill) !important;
  }
  .rounded-top-md {
    border-top-right-radius: var(--border-radius) !important;
    border-top-left-radius: var(--border-radius) !important;
  }
  .rounded-top-md-0 {
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }
  .rounded-top-md-1 {
    border-top-right-radius: var(--border-radius-sm) !important;
    border-top-left-radius: var(--border-radius-sm) !important;
  }
  .rounded-top-md-2 {
    border-top-right-radius: var(--border-radius) !important;
    border-top-left-radius: var(--border-radius) !important;
  }
  .rounded-top-md-pill {
    border-top-right-radius: var(--border-radius-pill) !important;
    border-top-left-radius: var(--border-radius-pill) !important;
  }
  .rounded-end-md {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
  }
  .rounded-end-md-0 {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  .rounded-end-md-1 {
    border-top-left-radius: var(--border-radius-sm) !important;
    border-bottom-left-radius: var(--border-radius-sm) !important;
  }
  .rounded-end-md-2 {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
  }
  .rounded-end-md-pill {
    border-top-left-radius: var(--border-radius-pill) !important;
    border-bottom-left-radius: var(--border-radius-pill) !important;
  }
  .rounded-bottom-md {
    border-bottom-left-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
  }
  .rounded-bottom-md-0 {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  .rounded-bottom-md-1 {
    border-bottom-left-radius: var(--border-radius-sm) !important;
    border-bottom-right-radius: var(--border-radius-sm) !important;
  }
  .rounded-bottom-md-2 {
    border-bottom-left-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
  }
  .rounded-bottom-md-pill {
    border-bottom-left-radius: var(--border-radius-pill) !important;
    border-bottom-right-radius: var(--border-radius-pill) !important;
  }
  .rounded-start-md {
    border-bottom-right-radius: var(--border-radius) !important;
    border-top-right-radius: var(--border-radius) !important;
  }
  .rounded-start-md-0 {
    border-bottom-right-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }
  .rounded-start-md-1 {
    border-bottom-right-radius: var(--border-radius-sm) !important;
    border-top-right-radius: var(--border-radius-sm) !important;
  }
  .rounded-start-md-2 {
    border-bottom-right-radius: var(--border-radius) !important;
    border-top-right-radius: var(--border-radius) !important;
  }
  .rounded-start-md-pill {
    border-bottom-right-radius: var(--border-radius-pill) !important;
    border-top-right-radius: var(--border-radius-pill) !important;
  }
}
@media (min-width: 992px) {
  .float-lg-start {
    float: right !important;
  }
  .float-lg-end {
    float: left !important;
  }
  .float-lg-none {
    float: none !important;
  }
  .overflow-lg-auto {
    overflow: auto !important;
  }
  .overflow-lg-hidden {
    overflow: hidden !important;
  }
  .overflow-lg-visible {
    overflow: visible !important;
  }
  .overflow-lg-scroll {
    overflow: scroll !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-grid {
    display: grid !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
  .d-lg-none {
    display: none !important;
  }
  .border-lg {
    border: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-lg-0 {
    border: 0 !important;
  }
  .border-top-lg {
    border-top: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-top-lg-0 {
    border-top: 0 !important;
  }
  .border-end-lg {
    border-left: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-end-lg-0 {
    border-left: 0 !important;
  }
  .border-bottom-lg {
    border-bottom: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-bottom-lg-0 {
    border-bottom: 0 !important;
  }
  .border-start-lg {
    border-right: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-start-lg-0 {
    border-right: 0 !important;
  }
  .w-lg-25 {
    width: 25% !important;
  }
  .w-lg-50 {
    width: 50% !important;
  }
  .w-lg-75 {
    width: 75% !important;
  }
  .w-lg-100 {
    width: 100% !important;
  }
  .w-lg-auto {
    width: auto !important;
  }
  .wmin-lg-0 {
    min-width: 0 !important;
  }
  .wmin-lg-200 {
    min-width: 200px !important;
  }
  .wmin-lg-250 {
    min-width: 250px !important;
  }
  .wmin-lg-300 {
    min-width: 300px !important;
  }
  .wmin-lg-350 {
    min-width: 350px !important;
  }
  .wmin-lg-400 {
    min-width: 400px !important;
  }
  .wmin-lg-450 {
    min-width: 450px !important;
  }
  .wmin-lg-500 {
    min-width: 500px !important;
  }
  .wmin-lg-550 {
    min-width: 550px !important;
  }
  .wmin-lg-600 {
    min-width: 600px !important;
  }
  .h-lg-25 {
    height: 25% !important;
  }
  .h-lg-50 {
    height: 50% !important;
  }
  .h-lg-75 {
    height: 75% !important;
  }
  .h-lg-100 {
    height: 100% !important;
  }
  .h-lg-auto {
    height: auto !important;
  }
  .flex-lg-0 {
    -ms-flex: 0 1 auto !important;
        flex: 0 1 auto !important;
  }
  .flex-lg-1 {
    -ms-flex: 1 !important;
        flex: 1 !important;
  }
  .flex-lg-fill {
    -ms-flex: 1 1 auto !important;
        flex: 1 1 auto !important;
  }
  .flex-lg-row {
    -ms-flex-direction: row !important;
        flex-direction: row !important;
  }
  .flex-lg-column {
    -ms-flex-direction: column !important;
        flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important;
  }
  .flex-lg-grow-0 {
    -ms-flex-positive: 0 !important;
        flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    -ms-flex-positive: 1 !important;
        flex-grow: 1 !important;
  }
  .flex-lg-grow-2 {
    -ms-flex-positive: 2 !important;
        flex-grow: 2 !important;
  }
  .flex-lg-grow-3 {
    -ms-flex-positive: 3 !important;
        flex-grow: 3 !important;
  }
  .flex-lg-shrink-0 {
    -ms-flex-negative: 0 !important;
        flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    -ms-flex-negative: 1 !important;
        flex-shrink: 1 !important;
  }
  .flex-lg-wrap {
    -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important;
  }
  .justify-content-lg-start {
    -ms-flex-pack: start !important;
        justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    -ms-flex-pack: end !important;
        justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    -ms-flex-pack: center !important;
        justify-content: center !important;
  }
  .justify-content-lg-between {
    -ms-flex-pack: justify !important;
        justify-content: space-between !important;
  }
  .justify-content-lg-around {
    -ms-flex-pack: distribute !important;
        justify-content: space-around !important;
  }
  .justify-content-lg-evenly {
    -ms-flex-pack: space-evenly !important;
        justify-content: space-evenly !important;
  }
  .align-items-lg-start {
    -ms-flex-align: start !important;
        align-items: flex-start !important;
  }
  .align-items-lg-end {
    -ms-flex-align: end !important;
        align-items: flex-end !important;
  }
  .align-items-lg-center {
    -ms-flex-align: center !important;
        align-items: center !important;
  }
  .align-items-lg-baseline {
    -ms-flex-align: baseline !important;
        align-items: baseline !important;
  }
  .align-items-lg-stretch {
    -ms-flex-align: stretch !important;
        align-items: stretch !important;
  }
  .align-content-lg-start {
    -ms-flex-line-pack: start !important;
        align-content: flex-start !important;
  }
  .align-content-lg-end {
    -ms-flex-line-pack: end !important;
        align-content: flex-end !important;
  }
  .align-content-lg-center {
    -ms-flex-line-pack: center !important;
        align-content: center !important;
  }
  .align-content-lg-between {
    -ms-flex-line-pack: justify !important;
        align-content: space-between !important;
  }
  .align-content-lg-around {
    -ms-flex-line-pack: distribute !important;
        align-content: space-around !important;
  }
  .align-content-lg-stretch {
    -ms-flex-line-pack: stretch !important;
        align-content: stretch !important;
  }
  .align-self-lg-auto {
    -ms-flex-item-align: auto !important;
        align-self: auto !important;
  }
  .align-self-lg-start {
    -ms-flex-item-align: start !important;
        align-self: flex-start !important;
  }
  .align-self-lg-end {
    -ms-flex-item-align: end !important;
        align-self: flex-end !important;
  }
  .align-self-lg-center {
    -ms-flex-item-align: center !important;
        align-self: center !important;
  }
  .align-self-lg-baseline {
    -ms-flex-item-align: baseline !important;
        align-self: baseline !important;
  }
  .align-self-lg-stretch {
    -ms-flex-item-align: stretch !important;
        align-self: stretch !important;
  }
  .order-lg-first {
    -ms-flex-order: -1 !important;
        order: -1 !important;
  }
  .order-lg-0 {
    -ms-flex-order: 0 !important;
        order: 0 !important;
  }
  .order-lg-1 {
    -ms-flex-order: 1 !important;
        order: 1 !important;
  }
  .order-lg-2 {
    -ms-flex-order: 2 !important;
        order: 2 !important;
  }
  .order-lg-3 {
    -ms-flex-order: 3 !important;
        order: 3 !important;
  }
  .order-lg-4 {
    -ms-flex-order: 4 !important;
        order: 4 !important;
  }
  .order-lg-5 {
    -ms-flex-order: 5 !important;
        order: 5 !important;
  }
  .order-lg-last {
    -ms-flex-order: 6 !important;
        order: 6 !important;
  }
  .m-lg-0 {
    margin: 0 !important;
  }
  .m-lg-1 {
    margin: 0.3125rem !important;
  }
  .m-lg-2 {
    margin: 0.625rem !important;
  }
  .m-lg-3 {
    margin: 1.25rem !important;
  }
  .m-lg-4 {
    margin: 1.875rem !important;
  }
  .m-lg-5 {
    margin: 3.75rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mx-lg-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .mx-lg-1 {
    margin-left: 0.3125rem !important;
    margin-right: 0.3125rem !important;
  }
  .mx-lg-2 {
    margin-left: 0.625rem !important;
    margin-right: 0.625rem !important;
  }
  .mx-lg-3 {
    margin-left: 1.25rem !important;
    margin-right: 1.25rem !important;
  }
  .mx-lg-4 {
    margin-left: 1.875rem !important;
    margin-right: 1.875rem !important;
  }
  .mx-lg-5 {
    margin-left: 3.75rem !important;
    margin-right: 3.75rem !important;
  }
  .mx-lg-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-lg-1 {
    margin-top: 0.3125rem !important;
    margin-bottom: 0.3125rem !important;
  }
  .my-lg-2 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }
  .my-lg-3 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }
  .my-lg-4 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }
  .my-lg-5 {
    margin-top: 3.75rem !important;
    margin-bottom: 3.75rem !important;
  }
  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-lg-0 {
    margin-top: 0 !important;
  }
  .mt-lg-1 {
    margin-top: 0.3125rem !important;
  }
  .mt-lg-2 {
    margin-top: 0.625rem !important;
  }
  .mt-lg-3 {
    margin-top: 1.25rem !important;
  }
  .mt-lg-4 {
    margin-top: 1.875rem !important;
  }
  .mt-lg-5 {
    margin-top: 3.75rem !important;
  }
  .mt-lg-auto {
    margin-top: auto !important;
  }
  .me-lg-0 {
    margin-left: 0 !important;
  }
  .me-lg-1 {
    margin-left: 0.3125rem !important;
  }
  .me-lg-2 {
    margin-left: 0.625rem !important;
  }
  .me-lg-3 {
    margin-left: 1.25rem !important;
  }
  .me-lg-4 {
    margin-left: 1.875rem !important;
  }
  .me-lg-5 {
    margin-left: 3.75rem !important;
  }
  .me-lg-auto {
    margin-left: auto !important;
  }
  .mb-lg-0 {
    margin-bottom: 0 !important;
  }
  .mb-lg-1 {
    margin-bottom: 0.3125rem !important;
  }
  .mb-lg-2 {
    margin-bottom: 0.625rem !important;
  }
  .mb-lg-3 {
    margin-bottom: 1.25rem !important;
  }
  .mb-lg-4 {
    margin-bottom: 1.875rem !important;
  }
  .mb-lg-5 {
    margin-bottom: 3.75rem !important;
  }
  .mb-lg-auto {
    margin-bottom: auto !important;
  }
  .ms-lg-0 {
    margin-right: 0 !important;
  }
  .ms-lg-1 {
    margin-right: 0.3125rem !important;
  }
  .ms-lg-2 {
    margin-right: 0.625rem !important;
  }
  .ms-lg-3 {
    margin-right: 1.25rem !important;
  }
  .ms-lg-4 {
    margin-right: 1.875rem !important;
  }
  .ms-lg-5 {
    margin-right: 3.75rem !important;
  }
  .ms-lg-auto {
    margin-right: auto !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .p-lg-1 {
    padding: 0.3125rem !important;
  }
  .p-lg-2 {
    padding: 0.625rem !important;
  }
  .p-lg-3 {
    padding: 1.25rem !important;
  }
  .p-lg-4 {
    padding: 1.875rem !important;
  }
  .p-lg-5 {
    padding: 3.75rem !important;
  }
  .px-lg-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-lg-1 {
    padding-left: 0.3125rem !important;
    padding-right: 0.3125rem !important;
  }
  .px-lg-2 {
    padding-left: 0.625rem !important;
    padding-right: 0.625rem !important;
  }
  .px-lg-3 {
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
  }
  .px-lg-4 {
    padding-left: 1.875rem !important;
    padding-right: 1.875rem !important;
  }
  .px-lg-5 {
    padding-left: 3.75rem !important;
    padding-right: 3.75rem !important;
  }
  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-lg-1 {
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
  }
  .py-lg-2 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }
  .py-lg-3 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }
  .py-lg-4 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }
  .py-lg-5 {
    padding-top: 3.75rem !important;
    padding-bottom: 3.75rem !important;
  }
  .pt-lg-0 {
    padding-top: 0 !important;
  }
  .pt-lg-1 {
    padding-top: 0.3125rem !important;
  }
  .pt-lg-2 {
    padding-top: 0.625rem !important;
  }
  .pt-lg-3 {
    padding-top: 1.25rem !important;
  }
  .pt-lg-4 {
    padding-top: 1.875rem !important;
  }
  .pt-lg-5 {
    padding-top: 3.75rem !important;
  }
  .pe-lg-0 {
    padding-left: 0 !important;
  }
  .pe-lg-1 {
    padding-left: 0.3125rem !important;
  }
  .pe-lg-2 {
    padding-left: 0.625rem !important;
  }
  .pe-lg-3 {
    padding-left: 1.25rem !important;
  }
  .pe-lg-4 {
    padding-left: 1.875rem !important;
  }
  .pe-lg-5 {
    padding-left: 3.75rem !important;
  }
  .pb-lg-0 {
    padding-bottom: 0 !important;
  }
  .pb-lg-1 {
    padding-bottom: 0.3125rem !important;
  }
  .pb-lg-2 {
    padding-bottom: 0.625rem !important;
  }
  .pb-lg-3 {
    padding-bottom: 1.25rem !important;
  }
  .pb-lg-4 {
    padding-bottom: 1.875rem !important;
  }
  .pb-lg-5 {
    padding-bottom: 3.75rem !important;
  }
  .ps-lg-0 {
    padding-right: 0 !important;
  }
  .ps-lg-1 {
    padding-right: 0.3125rem !important;
  }
  .ps-lg-2 {
    padding-right: 0.625rem !important;
  }
  .ps-lg-3 {
    padding-right: 1.25rem !important;
  }
  .ps-lg-4 {
    padding-right: 1.875rem !important;
  }
  .ps-lg-5 {
    padding-right: 3.75rem !important;
  }
  .gap-lg-0 {
    gap: 0 !important;
  }
  .gap-lg-1 {
    gap: 0.3125rem !important;
  }
  .gap-lg-2 {
    gap: 0.625rem !important;
  }
  .gap-lg-3 {
    gap: 1.25rem !important;
  }
  .gap-lg-4 {
    gap: 1.875rem !important;
  }
  .gap-lg-5 {
    gap: 3.75rem !important;
  }
  .text-lg-start {
    text-align: right !important;
  }
  .text-lg-end {
    text-align: left !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
  .rounded-lg {
    border-radius: var(--border-radius) !important;
  }
  .rounded-lg-0 {
    border-radius: 0 !important;
  }
  .rounded-lg-1 {
    border-radius: var(--border-radius-sm) !important;
  }
  .rounded-lg-2 {
    border-radius: var(--border-radius) !important;
  }
  .rounded-lg-3 {
    border-radius: var(--border-radius-lg) !important;
  }
  .rounded-lg-4 {
    border-radius: var(--border-radius-xl) !important;
  }
  .rounded-lg-5 {
    border-radius: var(--border-radius-2xl) !important;
  }
  .rounded-lg-circle {
    border-radius: 50% !important;
  }
  .rounded-lg-pill {
    border-radius: var(--border-radius-pill) !important;
  }
  .rounded-top-lg {
    border-top-right-radius: var(--border-radius) !important;
    border-top-left-radius: var(--border-radius) !important;
  }
  .rounded-top-lg-0 {
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }
  .rounded-top-lg-1 {
    border-top-right-radius: var(--border-radius-sm) !important;
    border-top-left-radius: var(--border-radius-sm) !important;
  }
  .rounded-top-lg-2 {
    border-top-right-radius: var(--border-radius) !important;
    border-top-left-radius: var(--border-radius) !important;
  }
  .rounded-top-lg-pill {
    border-top-right-radius: var(--border-radius-pill) !important;
    border-top-left-radius: var(--border-radius-pill) !important;
  }
  .rounded-end-lg {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
  }
  .rounded-end-lg-0 {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  .rounded-end-lg-1 {
    border-top-left-radius: var(--border-radius-sm) !important;
    border-bottom-left-radius: var(--border-radius-sm) !important;
  }
  .rounded-end-lg-2 {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
  }
  .rounded-end-lg-pill {
    border-top-left-radius: var(--border-radius-pill) !important;
    border-bottom-left-radius: var(--border-radius-pill) !important;
  }
  .rounded-bottom-lg {
    border-bottom-left-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
  }
  .rounded-bottom-lg-0 {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  .rounded-bottom-lg-1 {
    border-bottom-left-radius: var(--border-radius-sm) !important;
    border-bottom-right-radius: var(--border-radius-sm) !important;
  }
  .rounded-bottom-lg-2 {
    border-bottom-left-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
  }
  .rounded-bottom-lg-pill {
    border-bottom-left-radius: var(--border-radius-pill) !important;
    border-bottom-right-radius: var(--border-radius-pill) !important;
  }
  .rounded-start-lg {
    border-bottom-right-radius: var(--border-radius) !important;
    border-top-right-radius: var(--border-radius) !important;
  }
  .rounded-start-lg-0 {
    border-bottom-right-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }
  .rounded-start-lg-1 {
    border-bottom-right-radius: var(--border-radius-sm) !important;
    border-top-right-radius: var(--border-radius-sm) !important;
  }
  .rounded-start-lg-2 {
    border-bottom-right-radius: var(--border-radius) !important;
    border-top-right-radius: var(--border-radius) !important;
  }
  .rounded-start-lg-pill {
    border-bottom-right-radius: var(--border-radius-pill) !important;
    border-top-right-radius: var(--border-radius-pill) !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-start {
    float: right !important;
  }
  .float-xl-end {
    float: left !important;
  }
  .float-xl-none {
    float: none !important;
  }
  .overflow-xl-auto {
    overflow: auto !important;
  }
  .overflow-xl-hidden {
    overflow: hidden !important;
  }
  .overflow-xl-visible {
    overflow: visible !important;
  }
  .overflow-xl-scroll {
    overflow: scroll !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-grid {
    display: grid !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
  .d-xl-none {
    display: none !important;
  }
  .border-xl {
    border: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-xl-0 {
    border: 0 !important;
  }
  .border-top-xl {
    border-top: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-top-xl-0 {
    border-top: 0 !important;
  }
  .border-end-xl {
    border-left: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-end-xl-0 {
    border-left: 0 !important;
  }
  .border-bottom-xl {
    border-bottom: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-bottom-xl-0 {
    border-bottom: 0 !important;
  }
  .border-start-xl {
    border-right: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-start-xl-0 {
    border-right: 0 !important;
  }
  .w-xl-25 {
    width: 25% !important;
  }
  .w-xl-50 {
    width: 50% !important;
  }
  .w-xl-75 {
    width: 75% !important;
  }
  .w-xl-100 {
    width: 100% !important;
  }
  .w-xl-auto {
    width: auto !important;
  }
  .wmin-xl-0 {
    min-width: 0 !important;
  }
  .wmin-xl-200 {
    min-width: 200px !important;
  }
  .wmin-xl-250 {
    min-width: 250px !important;
  }
  .wmin-xl-300 {
    min-width: 300px !important;
  }
  .wmin-xl-350 {
    min-width: 350px !important;
  }
  .wmin-xl-400 {
    min-width: 400px !important;
  }
  .wmin-xl-450 {
    min-width: 450px !important;
  }
  .wmin-xl-500 {
    min-width: 500px !important;
  }
  .wmin-xl-550 {
    min-width: 550px !important;
  }
  .wmin-xl-600 {
    min-width: 600px !important;
  }
  .h-xl-25 {
    height: 25% !important;
  }
  .h-xl-50 {
    height: 50% !important;
  }
  .h-xl-75 {
    height: 75% !important;
  }
  .h-xl-100 {
    height: 100% !important;
  }
  .h-xl-auto {
    height: auto !important;
  }
  .flex-xl-0 {
    -ms-flex: 0 1 auto !important;
        flex: 0 1 auto !important;
  }
  .flex-xl-1 {
    -ms-flex: 1 !important;
        flex: 1 !important;
  }
  .flex-xl-fill {
    -ms-flex: 1 1 auto !important;
        flex: 1 1 auto !important;
  }
  .flex-xl-row {
    -ms-flex-direction: row !important;
        flex-direction: row !important;
  }
  .flex-xl-column {
    -ms-flex-direction: column !important;
        flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important;
  }
  .flex-xl-grow-0 {
    -ms-flex-positive: 0 !important;
        flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    -ms-flex-positive: 1 !important;
        flex-grow: 1 !important;
  }
  .flex-xl-grow-2 {
    -ms-flex-positive: 2 !important;
        flex-grow: 2 !important;
  }
  .flex-xl-grow-3 {
    -ms-flex-positive: 3 !important;
        flex-grow: 3 !important;
  }
  .flex-xl-shrink-0 {
    -ms-flex-negative: 0 !important;
        flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    -ms-flex-negative: 1 !important;
        flex-shrink: 1 !important;
  }
  .flex-xl-wrap {
    -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important;
  }
  .justify-content-xl-start {
    -ms-flex-pack: start !important;
        justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    -ms-flex-pack: end !important;
        justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    -ms-flex-pack: center !important;
        justify-content: center !important;
  }
  .justify-content-xl-between {
    -ms-flex-pack: justify !important;
        justify-content: space-between !important;
  }
  .justify-content-xl-around {
    -ms-flex-pack: distribute !important;
        justify-content: space-around !important;
  }
  .justify-content-xl-evenly {
    -ms-flex-pack: space-evenly !important;
        justify-content: space-evenly !important;
  }
  .align-items-xl-start {
    -ms-flex-align: start !important;
        align-items: flex-start !important;
  }
  .align-items-xl-end {
    -ms-flex-align: end !important;
        align-items: flex-end !important;
  }
  .align-items-xl-center {
    -ms-flex-align: center !important;
        align-items: center !important;
  }
  .align-items-xl-baseline {
    -ms-flex-align: baseline !important;
        align-items: baseline !important;
  }
  .align-items-xl-stretch {
    -ms-flex-align: stretch !important;
        align-items: stretch !important;
  }
  .align-content-xl-start {
    -ms-flex-line-pack: start !important;
        align-content: flex-start !important;
  }
  .align-content-xl-end {
    -ms-flex-line-pack: end !important;
        align-content: flex-end !important;
  }
  .align-content-xl-center {
    -ms-flex-line-pack: center !important;
        align-content: center !important;
  }
  .align-content-xl-between {
    -ms-flex-line-pack: justify !important;
        align-content: space-between !important;
  }
  .align-content-xl-around {
    -ms-flex-line-pack: distribute !important;
        align-content: space-around !important;
  }
  .align-content-xl-stretch {
    -ms-flex-line-pack: stretch !important;
        align-content: stretch !important;
  }
  .align-self-xl-auto {
    -ms-flex-item-align: auto !important;
        align-self: auto !important;
  }
  .align-self-xl-start {
    -ms-flex-item-align: start !important;
        align-self: flex-start !important;
  }
  .align-self-xl-end {
    -ms-flex-item-align: end !important;
        align-self: flex-end !important;
  }
  .align-self-xl-center {
    -ms-flex-item-align: center !important;
        align-self: center !important;
  }
  .align-self-xl-baseline {
    -ms-flex-item-align: baseline !important;
        align-self: baseline !important;
  }
  .align-self-xl-stretch {
    -ms-flex-item-align: stretch !important;
        align-self: stretch !important;
  }
  .order-xl-first {
    -ms-flex-order: -1 !important;
        order: -1 !important;
  }
  .order-xl-0 {
    -ms-flex-order: 0 !important;
        order: 0 !important;
  }
  .order-xl-1 {
    -ms-flex-order: 1 !important;
        order: 1 !important;
  }
  .order-xl-2 {
    -ms-flex-order: 2 !important;
        order: 2 !important;
  }
  .order-xl-3 {
    -ms-flex-order: 3 !important;
        order: 3 !important;
  }
  .order-xl-4 {
    -ms-flex-order: 4 !important;
        order: 4 !important;
  }
  .order-xl-5 {
    -ms-flex-order: 5 !important;
        order: 5 !important;
  }
  .order-xl-last {
    -ms-flex-order: 6 !important;
        order: 6 !important;
  }
  .m-xl-0 {
    margin: 0 !important;
  }
  .m-xl-1 {
    margin: 0.3125rem !important;
  }
  .m-xl-2 {
    margin: 0.625rem !important;
  }
  .m-xl-3 {
    margin: 1.25rem !important;
  }
  .m-xl-4 {
    margin: 1.875rem !important;
  }
  .m-xl-5 {
    margin: 3.75rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mx-xl-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .mx-xl-1 {
    margin-left: 0.3125rem !important;
    margin-right: 0.3125rem !important;
  }
  .mx-xl-2 {
    margin-left: 0.625rem !important;
    margin-right: 0.625rem !important;
  }
  .mx-xl-3 {
    margin-left: 1.25rem !important;
    margin-right: 1.25rem !important;
  }
  .mx-xl-4 {
    margin-left: 1.875rem !important;
    margin-right: 1.875rem !important;
  }
  .mx-xl-5 {
    margin-left: 3.75rem !important;
    margin-right: 3.75rem !important;
  }
  .mx-xl-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-xl-1 {
    margin-top: 0.3125rem !important;
    margin-bottom: 0.3125rem !important;
  }
  .my-xl-2 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }
  .my-xl-3 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }
  .my-xl-4 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }
  .my-xl-5 {
    margin-top: 3.75rem !important;
    margin-bottom: 3.75rem !important;
  }
  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xl-0 {
    margin-top: 0 !important;
  }
  .mt-xl-1 {
    margin-top: 0.3125rem !important;
  }
  .mt-xl-2 {
    margin-top: 0.625rem !important;
  }
  .mt-xl-3 {
    margin-top: 1.25rem !important;
  }
  .mt-xl-4 {
    margin-top: 1.875rem !important;
  }
  .mt-xl-5 {
    margin-top: 3.75rem !important;
  }
  .mt-xl-auto {
    margin-top: auto !important;
  }
  .me-xl-0 {
    margin-left: 0 !important;
  }
  .me-xl-1 {
    margin-left: 0.3125rem !important;
  }
  .me-xl-2 {
    margin-left: 0.625rem !important;
  }
  .me-xl-3 {
    margin-left: 1.25rem !important;
  }
  .me-xl-4 {
    margin-left: 1.875rem !important;
  }
  .me-xl-5 {
    margin-left: 3.75rem !important;
  }
  .me-xl-auto {
    margin-left: auto !important;
  }
  .mb-xl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xl-1 {
    margin-bottom: 0.3125rem !important;
  }
  .mb-xl-2 {
    margin-bottom: 0.625rem !important;
  }
  .mb-xl-3 {
    margin-bottom: 1.25rem !important;
  }
  .mb-xl-4 {
    margin-bottom: 1.875rem !important;
  }
  .mb-xl-5 {
    margin-bottom: 3.75rem !important;
  }
  .mb-xl-auto {
    margin-bottom: auto !important;
  }
  .ms-xl-0 {
    margin-right: 0 !important;
  }
  .ms-xl-1 {
    margin-right: 0.3125rem !important;
  }
  .ms-xl-2 {
    margin-right: 0.625rem !important;
  }
  .ms-xl-3 {
    margin-right: 1.25rem !important;
  }
  .ms-xl-4 {
    margin-right: 1.875rem !important;
  }
  .ms-xl-5 {
    margin-right: 3.75rem !important;
  }
  .ms-xl-auto {
    margin-right: auto !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .p-xl-1 {
    padding: 0.3125rem !important;
  }
  .p-xl-2 {
    padding: 0.625rem !important;
  }
  .p-xl-3 {
    padding: 1.25rem !important;
  }
  .p-xl-4 {
    padding: 1.875rem !important;
  }
  .p-xl-5 {
    padding: 3.75rem !important;
  }
  .px-xl-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-xl-1 {
    padding-left: 0.3125rem !important;
    padding-right: 0.3125rem !important;
  }
  .px-xl-2 {
    padding-left: 0.625rem !important;
    padding-right: 0.625rem !important;
  }
  .px-xl-3 {
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
  }
  .px-xl-4 {
    padding-left: 1.875rem !important;
    padding-right: 1.875rem !important;
  }
  .px-xl-5 {
    padding-left: 3.75rem !important;
    padding-right: 3.75rem !important;
  }
  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-xl-1 {
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
  }
  .py-xl-2 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }
  .py-xl-3 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }
  .py-xl-4 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }
  .py-xl-5 {
    padding-top: 3.75rem !important;
    padding-bottom: 3.75rem !important;
  }
  .pt-xl-0 {
    padding-top: 0 !important;
  }
  .pt-xl-1 {
    padding-top: 0.3125rem !important;
  }
  .pt-xl-2 {
    padding-top: 0.625rem !important;
  }
  .pt-xl-3 {
    padding-top: 1.25rem !important;
  }
  .pt-xl-4 {
    padding-top: 1.875rem !important;
  }
  .pt-xl-5 {
    padding-top: 3.75rem !important;
  }
  .pe-xl-0 {
    padding-left: 0 !important;
  }
  .pe-xl-1 {
    padding-left: 0.3125rem !important;
  }
  .pe-xl-2 {
    padding-left: 0.625rem !important;
  }
  .pe-xl-3 {
    padding-left: 1.25rem !important;
  }
  .pe-xl-4 {
    padding-left: 1.875rem !important;
  }
  .pe-xl-5 {
    padding-left: 3.75rem !important;
  }
  .pb-xl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xl-1 {
    padding-bottom: 0.3125rem !important;
  }
  .pb-xl-2 {
    padding-bottom: 0.625rem !important;
  }
  .pb-xl-3 {
    padding-bottom: 1.25rem !important;
  }
  .pb-xl-4 {
    padding-bottom: 1.875rem !important;
  }
  .pb-xl-5 {
    padding-bottom: 3.75rem !important;
  }
  .ps-xl-0 {
    padding-right: 0 !important;
  }
  .ps-xl-1 {
    padding-right: 0.3125rem !important;
  }
  .ps-xl-2 {
    padding-right: 0.625rem !important;
  }
  .ps-xl-3 {
    padding-right: 1.25rem !important;
  }
  .ps-xl-4 {
    padding-right: 1.875rem !important;
  }
  .ps-xl-5 {
    padding-right: 3.75rem !important;
  }
  .gap-xl-0 {
    gap: 0 !important;
  }
  .gap-xl-1 {
    gap: 0.3125rem !important;
  }
  .gap-xl-2 {
    gap: 0.625rem !important;
  }
  .gap-xl-3 {
    gap: 1.25rem !important;
  }
  .gap-xl-4 {
    gap: 1.875rem !important;
  }
  .gap-xl-5 {
    gap: 3.75rem !important;
  }
  .text-xl-start {
    text-align: right !important;
  }
  .text-xl-end {
    text-align: left !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
  .rounded-xl {
    border-radius: var(--border-radius) !important;
  }
  .rounded-xl-0 {
    border-radius: 0 !important;
  }
  .rounded-xl-1 {
    border-radius: var(--border-radius-sm) !important;
  }
  .rounded-xl-2 {
    border-radius: var(--border-radius) !important;
  }
  .rounded-xl-3 {
    border-radius: var(--border-radius-lg) !important;
  }
  .rounded-xl-4 {
    border-radius: var(--border-radius-xl) !important;
  }
  .rounded-xl-5 {
    border-radius: var(--border-radius-2xl) !important;
  }
  .rounded-xl-circle {
    border-radius: 50% !important;
  }
  .rounded-xl-pill {
    border-radius: var(--border-radius-pill) !important;
  }
  .rounded-top-xl {
    border-top-right-radius: var(--border-radius) !important;
    border-top-left-radius: var(--border-radius) !important;
  }
  .rounded-top-xl-0 {
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }
  .rounded-top-xl-1 {
    border-top-right-radius: var(--border-radius-sm) !important;
    border-top-left-radius: var(--border-radius-sm) !important;
  }
  .rounded-top-xl-2 {
    border-top-right-radius: var(--border-radius) !important;
    border-top-left-radius: var(--border-radius) !important;
  }
  .rounded-top-xl-pill {
    border-top-right-radius: var(--border-radius-pill) !important;
    border-top-left-radius: var(--border-radius-pill) !important;
  }
  .rounded-end-xl {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
  }
  .rounded-end-xl-0 {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  .rounded-end-xl-1 {
    border-top-left-radius: var(--border-radius-sm) !important;
    border-bottom-left-radius: var(--border-radius-sm) !important;
  }
  .rounded-end-xl-2 {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
  }
  .rounded-end-xl-pill {
    border-top-left-radius: var(--border-radius-pill) !important;
    border-bottom-left-radius: var(--border-radius-pill) !important;
  }
  .rounded-bottom-xl {
    border-bottom-left-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
  }
  .rounded-bottom-xl-0 {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  .rounded-bottom-xl-1 {
    border-bottom-left-radius: var(--border-radius-sm) !important;
    border-bottom-right-radius: var(--border-radius-sm) !important;
  }
  .rounded-bottom-xl-2 {
    border-bottom-left-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
  }
  .rounded-bottom-xl-pill {
    border-bottom-left-radius: var(--border-radius-pill) !important;
    border-bottom-right-radius: var(--border-radius-pill) !important;
  }
  .rounded-start-xl {
    border-bottom-right-radius: var(--border-radius) !important;
    border-top-right-radius: var(--border-radius) !important;
  }
  .rounded-start-xl-0 {
    border-bottom-right-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }
  .rounded-start-xl-1 {
    border-bottom-right-radius: var(--border-radius-sm) !important;
    border-top-right-radius: var(--border-radius-sm) !important;
  }
  .rounded-start-xl-2 {
    border-bottom-right-radius: var(--border-radius) !important;
    border-top-right-radius: var(--border-radius) !important;
  }
  .rounded-start-xl-pill {
    border-bottom-right-radius: var(--border-radius-pill) !important;
    border-top-right-radius: var(--border-radius-pill) !important;
  }
}
@media (min-width: 1400px) {
  .float-xxl-start {
    float: right !important;
  }
  .float-xxl-end {
    float: left !important;
  }
  .float-xxl-none {
    float: none !important;
  }
  .overflow-xxl-auto {
    overflow: auto !important;
  }
  .overflow-xxl-hidden {
    overflow: hidden !important;
  }
  .overflow-xxl-visible {
    overflow: visible !important;
  }
  .overflow-xxl-scroll {
    overflow: scroll !important;
  }
  .d-xxl-inline {
    display: inline !important;
  }
  .d-xxl-inline-block {
    display: inline-block !important;
  }
  .d-xxl-block {
    display: block !important;
  }
  .d-xxl-grid {
    display: grid !important;
  }
  .d-xxl-table {
    display: table !important;
  }
  .d-xxl-table-row {
    display: table-row !important;
  }
  .d-xxl-table-cell {
    display: table-cell !important;
  }
  .d-xxl-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-xxl-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
  .d-xxl-none {
    display: none !important;
  }
  .border-xxl {
    border: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-xxl-0 {
    border: 0 !important;
  }
  .border-top-xxl {
    border-top: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-top-xxl-0 {
    border-top: 0 !important;
  }
  .border-end-xxl {
    border-left: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-end-xxl-0 {
    border-left: 0 !important;
  }
  .border-bottom-xxl {
    border-bottom: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-bottom-xxl-0 {
    border-bottom: 0 !important;
  }
  .border-start-xxl {
    border-right: var(--border-width) var(--border-style) var(--border-color) !important;
  }
  .border-start-xxl-0 {
    border-right: 0 !important;
  }
  .w-xxl-25 {
    width: 25% !important;
  }
  .w-xxl-50 {
    width: 50% !important;
  }
  .w-xxl-75 {
    width: 75% !important;
  }
  .w-xxl-100 {
    width: 100% !important;
  }
  .w-xxl-auto {
    width: auto !important;
  }
  .wmin-xxl-0 {
    min-width: 0 !important;
  }
  .wmin-xxl-200 {
    min-width: 200px !important;
  }
  .wmin-xxl-250 {
    min-width: 250px !important;
  }
  .wmin-xxl-300 {
    min-width: 300px !important;
  }
  .wmin-xxl-350 {
    min-width: 350px !important;
  }
  .wmin-xxl-400 {
    min-width: 400px !important;
  }
  .wmin-xxl-450 {
    min-width: 450px !important;
  }
  .wmin-xxl-500 {
    min-width: 500px !important;
  }
  .wmin-xxl-550 {
    min-width: 550px !important;
  }
  .wmin-xxl-600 {
    min-width: 600px !important;
  }
  .h-xxl-25 {
    height: 25% !important;
  }
  .h-xxl-50 {
    height: 50% !important;
  }
  .h-xxl-75 {
    height: 75% !important;
  }
  .h-xxl-100 {
    height: 100% !important;
  }
  .h-xxl-auto {
    height: auto !important;
  }
  .flex-xxl-0 {
    -ms-flex: 0 1 auto !important;
        flex: 0 1 auto !important;
  }
  .flex-xxl-1 {
    -ms-flex: 1 !important;
        flex: 1 !important;
  }
  .flex-xxl-fill {
    -ms-flex: 1 1 auto !important;
        flex: 1 1 auto !important;
  }
  .flex-xxl-row {
    -ms-flex-direction: row !important;
        flex-direction: row !important;
  }
  .flex-xxl-column {
    -ms-flex-direction: column !important;
        flex-direction: column !important;
  }
  .flex-xxl-row-reverse {
    -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important;
  }
  .flex-xxl-column-reverse {
    -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important;
  }
  .flex-xxl-grow-0 {
    -ms-flex-positive: 0 !important;
        flex-grow: 0 !important;
  }
  .flex-xxl-grow-1 {
    -ms-flex-positive: 1 !important;
        flex-grow: 1 !important;
  }
  .flex-xxl-grow-2 {
    -ms-flex-positive: 2 !important;
        flex-grow: 2 !important;
  }
  .flex-xxl-grow-3 {
    -ms-flex-positive: 3 !important;
        flex-grow: 3 !important;
  }
  .flex-xxl-shrink-0 {
    -ms-flex-negative: 0 !important;
        flex-shrink: 0 !important;
  }
  .flex-xxl-shrink-1 {
    -ms-flex-negative: 1 !important;
        flex-shrink: 1 !important;
  }
  .flex-xxl-wrap {
    -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important;
  }
  .flex-xxl-nowrap {
    -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important;
  }
  .flex-xxl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important;
  }
  .justify-content-xxl-start {
    -ms-flex-pack: start !important;
        justify-content: flex-start !important;
  }
  .justify-content-xxl-end {
    -ms-flex-pack: end !important;
        justify-content: flex-end !important;
  }
  .justify-content-xxl-center {
    -ms-flex-pack: center !important;
        justify-content: center !important;
  }
  .justify-content-xxl-between {
    -ms-flex-pack: justify !important;
        justify-content: space-between !important;
  }
  .justify-content-xxl-around {
    -ms-flex-pack: distribute !important;
        justify-content: space-around !important;
  }
  .justify-content-xxl-evenly {
    -ms-flex-pack: space-evenly !important;
        justify-content: space-evenly !important;
  }
  .align-items-xxl-start {
    -ms-flex-align: start !important;
        align-items: flex-start !important;
  }
  .align-items-xxl-end {
    -ms-flex-align: end !important;
        align-items: flex-end !important;
  }
  .align-items-xxl-center {
    -ms-flex-align: center !important;
        align-items: center !important;
  }
  .align-items-xxl-baseline {
    -ms-flex-align: baseline !important;
        align-items: baseline !important;
  }
  .align-items-xxl-stretch {
    -ms-flex-align: stretch !important;
        align-items: stretch !important;
  }
  .align-content-xxl-start {
    -ms-flex-line-pack: start !important;
        align-content: flex-start !important;
  }
  .align-content-xxl-end {
    -ms-flex-line-pack: end !important;
        align-content: flex-end !important;
  }
  .align-content-xxl-center {
    -ms-flex-line-pack: center !important;
        align-content: center !important;
  }
  .align-content-xxl-between {
    -ms-flex-line-pack: justify !important;
        align-content: space-between !important;
  }
  .align-content-xxl-around {
    -ms-flex-line-pack: distribute !important;
        align-content: space-around !important;
  }
  .align-content-xxl-stretch {
    -ms-flex-line-pack: stretch !important;
        align-content: stretch !important;
  }
  .align-self-xxl-auto {
    -ms-flex-item-align: auto !important;
        align-self: auto !important;
  }
  .align-self-xxl-start {
    -ms-flex-item-align: start !important;
        align-self: flex-start !important;
  }
  .align-self-xxl-end {
    -ms-flex-item-align: end !important;
        align-self: flex-end !important;
  }
  .align-self-xxl-center {
    -ms-flex-item-align: center !important;
        align-self: center !important;
  }
  .align-self-xxl-baseline {
    -ms-flex-item-align: baseline !important;
        align-self: baseline !important;
  }
  .align-self-xxl-stretch {
    -ms-flex-item-align: stretch !important;
        align-self: stretch !important;
  }
  .order-xxl-first {
    -ms-flex-order: -1 !important;
        order: -1 !important;
  }
  .order-xxl-0 {
    -ms-flex-order: 0 !important;
        order: 0 !important;
  }
  .order-xxl-1 {
    -ms-flex-order: 1 !important;
        order: 1 !important;
  }
  .order-xxl-2 {
    -ms-flex-order: 2 !important;
        order: 2 !important;
  }
  .order-xxl-3 {
    -ms-flex-order: 3 !important;
        order: 3 !important;
  }
  .order-xxl-4 {
    -ms-flex-order: 4 !important;
        order: 4 !important;
  }
  .order-xxl-5 {
    -ms-flex-order: 5 !important;
        order: 5 !important;
  }
  .order-xxl-last {
    -ms-flex-order: 6 !important;
        order: 6 !important;
  }
  .m-xxl-0 {
    margin: 0 !important;
  }
  .m-xxl-1 {
    margin: 0.3125rem !important;
  }
  .m-xxl-2 {
    margin: 0.625rem !important;
  }
  .m-xxl-3 {
    margin: 1.25rem !important;
  }
  .m-xxl-4 {
    margin: 1.875rem !important;
  }
  .m-xxl-5 {
    margin: 3.75rem !important;
  }
  .m-xxl-auto {
    margin: auto !important;
  }
  .mx-xxl-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .mx-xxl-1 {
    margin-left: 0.3125rem !important;
    margin-right: 0.3125rem !important;
  }
  .mx-xxl-2 {
    margin-left: 0.625rem !important;
    margin-right: 0.625rem !important;
  }
  .mx-xxl-3 {
    margin-left: 1.25rem !important;
    margin-right: 1.25rem !important;
  }
  .mx-xxl-4 {
    margin-left: 1.875rem !important;
    margin-right: 1.875rem !important;
  }
  .mx-xxl-5 {
    margin-left: 3.75rem !important;
    margin-right: 3.75rem !important;
  }
  .mx-xxl-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-xxl-1 {
    margin-top: 0.3125rem !important;
    margin-bottom: 0.3125rem !important;
  }
  .my-xxl-2 {
    margin-top: 0.625rem !important;
    margin-bottom: 0.625rem !important;
  }
  .my-xxl-3 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
  }
  .my-xxl-4 {
    margin-top: 1.875rem !important;
    margin-bottom: 1.875rem !important;
  }
  .my-xxl-5 {
    margin-top: 3.75rem !important;
    margin-bottom: 3.75rem !important;
  }
  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xxl-0 {
    margin-top: 0 !important;
  }
  .mt-xxl-1 {
    margin-top: 0.3125rem !important;
  }
  .mt-xxl-2 {
    margin-top: 0.625rem !important;
  }
  .mt-xxl-3 {
    margin-top: 1.25rem !important;
  }
  .mt-xxl-4 {
    margin-top: 1.875rem !important;
  }
  .mt-xxl-5 {
    margin-top: 3.75rem !important;
  }
  .mt-xxl-auto {
    margin-top: auto !important;
  }
  .me-xxl-0 {
    margin-left: 0 !important;
  }
  .me-xxl-1 {
    margin-left: 0.3125rem !important;
  }
  .me-xxl-2 {
    margin-left: 0.625rem !important;
  }
  .me-xxl-3 {
    margin-left: 1.25rem !important;
  }
  .me-xxl-4 {
    margin-left: 1.875rem !important;
  }
  .me-xxl-5 {
    margin-left: 3.75rem !important;
  }
  .me-xxl-auto {
    margin-left: auto !important;
  }
  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xxl-1 {
    margin-bottom: 0.3125rem !important;
  }
  .mb-xxl-2 {
    margin-bottom: 0.625rem !important;
  }
  .mb-xxl-3 {
    margin-bottom: 1.25rem !important;
  }
  .mb-xxl-4 {
    margin-bottom: 1.875rem !important;
  }
  .mb-xxl-5 {
    margin-bottom: 3.75rem !important;
  }
  .mb-xxl-auto {
    margin-bottom: auto !important;
  }
  .ms-xxl-0 {
    margin-right: 0 !important;
  }
  .ms-xxl-1 {
    margin-right: 0.3125rem !important;
  }
  .ms-xxl-2 {
    margin-right: 0.625rem !important;
  }
  .ms-xxl-3 {
    margin-right: 1.25rem !important;
  }
  .ms-xxl-4 {
    margin-right: 1.875rem !important;
  }
  .ms-xxl-5 {
    margin-right: 3.75rem !important;
  }
  .ms-xxl-auto {
    margin-right: auto !important;
  }
  .p-xxl-0 {
    padding: 0 !important;
  }
  .p-xxl-1 {
    padding: 0.3125rem !important;
  }
  .p-xxl-2 {
    padding: 0.625rem !important;
  }
  .p-xxl-3 {
    padding: 1.25rem !important;
  }
  .p-xxl-4 {
    padding: 1.875rem !important;
  }
  .p-xxl-5 {
    padding: 3.75rem !important;
  }
  .px-xxl-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-xxl-1 {
    padding-left: 0.3125rem !important;
    padding-right: 0.3125rem !important;
  }
  .px-xxl-2 {
    padding-left: 0.625rem !important;
    padding-right: 0.625rem !important;
  }
  .px-xxl-3 {
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
  }
  .px-xxl-4 {
    padding-left: 1.875rem !important;
    padding-right: 1.875rem !important;
  }
  .px-xxl-5 {
    padding-left: 3.75rem !important;
    padding-right: 3.75rem !important;
  }
  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-xxl-1 {
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
  }
  .py-xxl-2 {
    padding-top: 0.625rem !important;
    padding-bottom: 0.625rem !important;
  }
  .py-xxl-3 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
  }
  .py-xxl-4 {
    padding-top: 1.875rem !important;
    padding-bottom: 1.875rem !important;
  }
  .py-xxl-5 {
    padding-top: 3.75rem !important;
    padding-bottom: 3.75rem !important;
  }
  .pt-xxl-0 {
    padding-top: 0 !important;
  }
  .pt-xxl-1 {
    padding-top: 0.3125rem !important;
  }
  .pt-xxl-2 {
    padding-top: 0.625rem !important;
  }
  .pt-xxl-3 {
    padding-top: 1.25rem !important;
  }
  .pt-xxl-4 {
    padding-top: 1.875rem !important;
  }
  .pt-xxl-5 {
    padding-top: 3.75rem !important;
  }
  .pe-xxl-0 {
    padding-left: 0 !important;
  }
  .pe-xxl-1 {
    padding-left: 0.3125rem !important;
  }
  .pe-xxl-2 {
    padding-left: 0.625rem !important;
  }
  .pe-xxl-3 {
    padding-left: 1.25rem !important;
  }
  .pe-xxl-4 {
    padding-left: 1.875rem !important;
  }
  .pe-xxl-5 {
    padding-left: 3.75rem !important;
  }
  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xxl-1 {
    padding-bottom: 0.3125rem !important;
  }
  .pb-xxl-2 {
    padding-bottom: 0.625rem !important;
  }
  .pb-xxl-3 {
    padding-bottom: 1.25rem !important;
  }
  .pb-xxl-4 {
    padding-bottom: 1.875rem !important;
  }
  .pb-xxl-5 {
    padding-bottom: 3.75rem !important;
  }
  .ps-xxl-0 {
    padding-right: 0 !important;
  }
  .ps-xxl-1 {
    padding-right: 0.3125rem !important;
  }
  .ps-xxl-2 {
    padding-right: 0.625rem !important;
  }
  .ps-xxl-3 {
    padding-right: 1.25rem !important;
  }
  .ps-xxl-4 {
    padding-right: 1.875rem !important;
  }
  .ps-xxl-5 {
    padding-right: 3.75rem !important;
  }
  .gap-xxl-0 {
    gap: 0 !important;
  }
  .gap-xxl-1 {
    gap: 0.3125rem !important;
  }
  .gap-xxl-2 {
    gap: 0.625rem !important;
  }
  .gap-xxl-3 {
    gap: 1.25rem !important;
  }
  .gap-xxl-4 {
    gap: 1.875rem !important;
  }
  .gap-xxl-5 {
    gap: 3.75rem !important;
  }
  .text-xxl-start {
    text-align: right !important;
  }
  .text-xxl-end {
    text-align: left !important;
  }
  .text-xxl-center {
    text-align: center !important;
  }
  .rounded-xxl {
    border-radius: var(--border-radius) !important;
  }
  .rounded-xxl-0 {
    border-radius: 0 !important;
  }
  .rounded-xxl-1 {
    border-radius: var(--border-radius-sm) !important;
  }
  .rounded-xxl-2 {
    border-radius: var(--border-radius) !important;
  }
  .rounded-xxl-3 {
    border-radius: var(--border-radius-lg) !important;
  }
  .rounded-xxl-4 {
    border-radius: var(--border-radius-xl) !important;
  }
  .rounded-xxl-5 {
    border-radius: var(--border-radius-2xl) !important;
  }
  .rounded-xxl-circle {
    border-radius: 50% !important;
  }
  .rounded-xxl-pill {
    border-radius: var(--border-radius-pill) !important;
  }
  .rounded-top-xxl {
    border-top-right-radius: var(--border-radius) !important;
    border-top-left-radius: var(--border-radius) !important;
  }
  .rounded-top-xxl-0 {
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }
  .rounded-top-xxl-1 {
    border-top-right-radius: var(--border-radius-sm) !important;
    border-top-left-radius: var(--border-radius-sm) !important;
  }
  .rounded-top-xxl-2 {
    border-top-right-radius: var(--border-radius) !important;
    border-top-left-radius: var(--border-radius) !important;
  }
  .rounded-top-xxl-pill {
    border-top-right-radius: var(--border-radius-pill) !important;
    border-top-left-radius: var(--border-radius-pill) !important;
  }
  .rounded-end-xxl {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
  }
  .rounded-end-xxl-0 {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  .rounded-end-xxl-1 {
    border-top-left-radius: var(--border-radius-sm) !important;
    border-bottom-left-radius: var(--border-radius-sm) !important;
  }
  .rounded-end-xxl-2 {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
  }
  .rounded-end-xxl-pill {
    border-top-left-radius: var(--border-radius-pill) !important;
    border-bottom-left-radius: var(--border-radius-pill) !important;
  }
  .rounded-bottom-xxl {
    border-bottom-left-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
  }
  .rounded-bottom-xxl-0 {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  .rounded-bottom-xxl-1 {
    border-bottom-left-radius: var(--border-radius-sm) !important;
    border-bottom-right-radius: var(--border-radius-sm) !important;
  }
  .rounded-bottom-xxl-2 {
    border-bottom-left-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
  }
  .rounded-bottom-xxl-pill {
    border-bottom-left-radius: var(--border-radius-pill) !important;
    border-bottom-right-radius: var(--border-radius-pill) !important;
  }
  .rounded-start-xxl {
    border-bottom-right-radius: var(--border-radius) !important;
    border-top-right-radius: var(--border-radius) !important;
  }
  .rounded-start-xxl-0 {
    border-bottom-right-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }
  .rounded-start-xxl-1 {
    border-bottom-right-radius: var(--border-radius-sm) !important;
    border-top-right-radius: var(--border-radius-sm) !important;
  }
  .rounded-start-xxl-2 {
    border-bottom-right-radius: var(--border-radius) !important;
    border-top-right-radius: var(--border-radius) !important;
  }
  .rounded-start-xxl-pill {
    border-bottom-right-radius: var(--border-radius-pill) !important;
    border-top-right-radius: var(--border-radius-pill) !important;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 1.625rem !important;
  }
  .fs-2 {
    font-size: 1.5rem !important;
  }
  .fs-3 {
    font-size: 1.375rem !important;
  }
}
@media print {
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-grid {
    display: grid !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-print-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
  .d-print-none {
    display: none !important;
  }
}