<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<title>Limitless - Responsive Web Application Kit by <PERSON></title>

	<!-- Global stylesheets -->
	<link href="../../../assets/fonts/inter/inter.css" rel="stylesheet" type="text/css">
	<link href="../../../assets/icons/phosphor/styles.min.css" rel="stylesheet" type="text/css">
	<link href="assets/css/ltr/all.min.css" id="stylesheet" rel="stylesheet" type="text/css">
	<!-- /global stylesheets -->

	<!-- Core JS files -->
	<script src="../../../assets/demo/demo_configurator.js"></script>
	<script src="../../../assets/js/bootstrap/bootstrap.bundle.min.js"></script>
	<!-- /core JS files -->

	<!-- Theme JS files -->
	<script src="../../../assets/js/vendor/ui/prism.min.js"></script>

	<script src="assets/js/app.js"></script>
	<!-- /theme JS files -->

</head>

<body>

	<!-- Main navbar -->
	<div class="navbar navbar-dark navbar-expand-lg navbar-static">
		<div class="container-fluid">
			<div class="navbar-brand flex-1 flex-lg-0">
				<a href="index.html" class="d-inline-flex align-items-center">
					<img src="../../../assets/images/logo_icon.svg" alt="">
					<img src="../../../assets/images/logo_text_light.svg" class="d-none d-sm-inline-block h-16px ms-3" alt="">
				</a>
			</div>

			<ul class="nav flex-row">
				<li class="nav-item d-lg-none">
					<a href="#navbar_search" class="navbar-nav-link navbar-nav-link-icon rounded-pill" data-bs-toggle="collapse">
						<i class="ph-magnifying-glass"></i>
					</a>
				</li>

				<li class="nav-item nav-item-dropdown-lg dropdown">
					<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded-pill" data-bs-toggle="dropdown">
						<i class="ph-squares-four"></i>
					</a>

					<div class="dropdown-menu dropdown-menu-scrollable-sm wmin-lg-600 p-0">
						<div class="d-flex align-items-center border-bottom p-3">
							<h6 class="mb-0">Browse apps</h6>
							<a href="#" class="ms-auto">
								View all
								<i class="ph-arrow-circle-right ms-1"></i>
							</a>
						</div>

						<div class="row row-cols-1 row-cols-sm-2 g-0">
							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start border-end-sm border-bottom p-3">
									<div>
										<img src="../../../assets/images/demo/logos/1.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Customer data platform</div>
										<div class="text-muted">Unify customer data from multiple sources</div>
									</div>
								</button>
							</div>

							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start border-bottom p-3">
									<div>
										<img src="../../../assets/images/demo/logos/2.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Data catalog</div>
										<div class="text-muted">Discover, inventory, and organize data assets</div>
									</div>
								</button>
							</div>

							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start border-end-sm border-bottom border-bottom-sm-0 rounded-bottom-start p-3">
									<div>
										<img src="../../../assets/images/demo/logos/3.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Data governance</div>
										<div class="text-muted">The collaboration hub and data marketplace</div>
									</div>
								</button>
							</div>

							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start rounded-bottom-end p-3">
									<div>
										<img src="../../../assets/images/demo/logos/4.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Data privacy</div>
										<div class="text-muted">Automated provisioning of non-production datasets</div>
									</div>
								</button>
							</div>
						</div>
					</div>
				</li>

				<li class="nav-item nav-item-dropdown-lg dropdown ms-lg-2">
					<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded-pill" data-bs-toggle="dropdown" data-bs-auto-close="outside">
						<i class="ph-chats"></i>
						<span class="badge bg-yellow text-black position-absolute top-0 end-0 translate-middle-top zindex-1 rounded-pill mt-1 me-1">8</span>
					</a>

					<div class="dropdown-menu wmin-lg-400 p-0">
						<div class="d-flex align-items-center p-3">
							<h6 class="mb-0">Messages</h6>
							<div class="ms-auto">
								<a href="#" class="text-body">
									<i class="ph-plus-circle"></i>
								</a>
								<a href="#search_messages" class="collapsed text-body ms-2" data-bs-toggle="collapse">
									<i class="ph-magnifying-glass"></i>
								</a>
							</div>
						</div>

						<div class="collapse" id="search_messages">
							<div class="px-3 mb-2">
								<div class="form-control-feedback form-control-feedback-start">
									<input type="text" class="form-control" placeholder="Search messages">
									<div class="form-control-feedback-icon">
										<i class="ph-magnifying-glass"></i>
									</div>
								</div>
							</div>
						</div>

						<div class="dropdown-menu-scrollable pb-2">
							<a href="#" class="dropdown-item align-items-start text-wrap py-2">
								<div class="status-indicator-container me-3">
									<img src="../../../assets/images/demo/users/face10.jpg" class="w-40px h-40px rounded-pill" alt="">
									<span class="status-indicator bg-warning"></span>
								</div>

								<div class="flex-1">
									<span class="fw-semibold">James Alexander</span>
									<span class="text-muted float-end fs-sm">04:58</span>
									<div class="text-muted">who knows, maybe that would be the best thing for me...</div>
								</div>
							</a>

							<a href="#" class="dropdown-item align-items-start text-wrap py-2">
								<div class="status-indicator-container me-3">
									<img src="../../../assets/images/demo/users/face3.jpg" class="w-40px h-40px rounded-pill" alt="">
									<span class="status-indicator bg-success"></span>
								</div>

								<div class="flex-1">
									<span class="fw-semibold">Margo Baker</span>
									<span class="text-muted float-end fs-sm">12:16</span>
									<div class="text-muted">That was something he was unable to do because...</div>
								</div>
							</a>

							<a href="#" class="dropdown-item align-items-start text-wrap py-2">
								<div class="status-indicator-container me-3">
									<img src="../../../assets/images/demo/users/face24.jpg" class="w-40px h-40px rounded-pill" alt="">
									<span class="status-indicator bg-success"></span>
								</div>
								<div class="flex-1">
									<span class="fw-semibold">Jeremy Victorino</span>
									<span class="text-muted float-end fs-sm">22:48</span>
									<div class="text-muted">But that would be extremely strained and suspicious...</div>
								</div>
							</a>

							<a href="#" class="dropdown-item align-items-start text-wrap py-2">
								<div class="status-indicator-container me-3">
									<img src="../../../assets/images/demo/users/face4.jpg" class="w-40px h-40px rounded-pill" alt="">
									<span class="status-indicator bg-grey"></span>
								</div>
								<div class="flex-1">
									<span class="fw-semibold">Beatrix Diaz</span>
									<span class="text-muted float-end fs-sm">Tue</span>
									<div class="text-muted">What a strenuous career it is that I've chosen...</div>
								</div>
							</a>

							<a href="#" class="dropdown-item align-items-start text-wrap py-2">
								<div class="status-indicator-container me-3">
									<img src="../../../assets/images/demo/users/face25.jpg" class="w-40px h-40px rounded-pill" alt="">
									<span class="status-indicator bg-danger"></span>
								</div>
								<div class="flex-1">
									<span class="fw-semibold">Richard Vango</span>
									<span class="text-muted float-end fs-sm">Mon</span>
									<div class="text-muted">Other travelling salesmen live a life of luxury...</div>
								</div>
							</a>
						</div>

						<div class="d-flex border-top py-2 px-3">
							<a href="#" class="text-body">
								<i class="ph-checks me-1"></i>
								Dismiss all
							</a>
							<a href="#" class="text-body ms-auto">
								View all
								<i class="ph-arrow-circle-right ms-1"></i>
							</a>
						</div>
					</div>
				</li>
			</ul>

			<div class="navbar-collapse justify-content-center flex-lg-1 order-2 order-lg-1 collapse" id="navbar_search">
				<div class="navbar-search flex-fill position-relative mt-2 mt-lg-0 mx-lg-3">
					<div class="form-control-feedback form-control-feedback-start flex-grow-1" data-color-theme="dark">
						<input type="text" class="form-control bg-transparent rounded-pill" placeholder="Search" data-bs-toggle="dropdown">
						<div class="form-control-feedback-icon">
							<i class="ph-magnifying-glass"></i>
						</div>
						<div class="dropdown-menu w-100" data-color-theme="light">
							<button type="button" class="dropdown-item">
								<div class="text-center w-32px me-3">
									<i class="ph-magnifying-glass"></i>
								</div>
								<span>Search <span class="fw-bold">"in"</span> everywhere</span>
							</button>

							<div class="dropdown-divider"></div>

							<div class="dropdown-menu-scrollable-lg">
								<div class="dropdown-header">
									Contacts
									<a href="#" class="float-end">
										See all
										<i class="ph-arrow-circle-right ms-1"></i>
									</a>
								</div>

								<div class="dropdown-item cursor-pointer">
									<div class="me-3">
										<img src="../../../assets/images/demo/users/face3.jpg" class="w-32px h-32px rounded-pill" alt="">
									</div>

									<div class="d-flex flex-column flex-grow-1">
										<div class="fw-semibold">Christ<mark>in</mark>e Johnson</div>
										<span class="fs-sm text-muted"><EMAIL></span>
									</div>

									<div class="d-inline-flex">
										<a href="#" class="text-body ms-2">
											<i class="ph-user-circle"></i>
										</a>
									</div>
								</div>

								<div class="dropdown-item cursor-pointer">
									<div class="me-3">
										<img src="../../../assets/images/demo/users/face24.jpg" class="w-32px h-32px rounded-pill" alt="">
									</div>

									<div class="d-flex flex-column flex-grow-1">
										<div class="fw-semibold">Cl<mark>in</mark>ton Sparks</div>
										<span class="fs-sm text-muted"><EMAIL></span>
									</div>

									<div class="d-inline-flex">
										<a href="#" class="text-body ms-2">
											<i class="ph-user-circle"></i>
										</a>
									</div>
								</div>

								<div class="dropdown-divider"></div>

								<div class="dropdown-header">
									Clients
									<a href="#" class="float-end">
										See all
										<i class="ph-arrow-circle-right ms-1"></i>
									</a>
								</div>

								<div class="dropdown-item cursor-pointer">
									<div class="me-3">
										<img src="../../../assets/images/brands/adobe.svg" class="w-32px h-32px rounded-pill" alt="">
									</div>

									<div class="d-flex flex-column flex-grow-1">
										<div class="fw-semibold">Adobe <mark>In</mark>c.</div>
										<span class="fs-sm text-muted">Enterprise license</span>
									</div>

									<div class="d-inline-flex">
										<a href="#" class="text-body ms-2">
											<i class="ph-briefcase"></i>
										</a>
									</div>
								</div>

								<div class="dropdown-item cursor-pointer">
									<div class="me-3">
										<img src="../../../assets/images/brands/holiday-inn.svg" class="w-32px h-32px rounded-pill" alt="">
									</div>

									<div class="d-flex flex-column flex-grow-1">
										<div class="fw-semibold">Holiday-<mark>In</mark>n</div>
										<span class="fs-sm text-muted">On-premise license</span>
									</div>

									<div class="d-inline-flex">
										<a href="#" class="text-body ms-2">
											<i class="ph-briefcase"></i>
										</a>
									</div>
								</div>

								<div class="dropdown-item cursor-pointer">
									<div class="me-3">
										<img src="../../../assets/images/brands/ing.svg" class="w-32px h-32px rounded-pill" alt="">
									</div>

									<div class="d-flex flex-column flex-grow-1">
										<div class="fw-semibold"><mark>IN</mark>G Group</div>
										<span class="fs-sm text-muted">Perpetual license</span>
									</div>

									<div class="d-inline-flex">
										<a href="#" class="text-body ms-2">
											<i class="ph-briefcase"></i>
										</a>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div>
						<a href="#" class="navbar-nav-link align-items-center justify-content-center w-40px h-32px rounded-pill position-absolute end-0 top-50 translate-middle-y p-0 me-1" data-bs-toggle="dropdown" data-bs-auto-close="outside">
							<i class="ph-faders-horizontal"></i>
						</a>

						<div class="dropdown-menu w-100 p-3">
							<div class="d-flex align-items-center mb-3">
								<h6 class="mb-0">Search options</h6>
								<a href="#" class="text-body rounded-pill ms-auto">
									<i class="ph-clock-counter-clockwise"></i>
								</a>
							</div>

							<div class="mb-3">
								<label class="d-block form-label">Category</label>
								<label class="form-check form-check-inline">
									<input type="checkbox" class="form-check-input" checked>
									<span class="form-check-label">Invoices</span>
								</label>
								<label class="form-check form-check-inline">
									<input type="checkbox" class="form-check-input">
									<span class="form-check-label">Files</span>
								</label>
								<label class="form-check form-check-inline">
									<input type="checkbox" class="form-check-input">
									<span class="form-check-label">Users</span>
								</label>
							</div>

							<div class="mb-3">
								<label class="form-label">Addition</label>
								<div class="input-group">
									<select class="form-select w-auto flex-grow-0">
										<option value="1" selected>has</option>
										<option value="2">has not</option>
									</select>
									<input type="text" class="form-control" placeholder="Enter the word(s)">
								</div>
							</div>

							<div class="mb-3">
								<label class="form-label">Status</label>
								<div class="input-group">
									<select class="form-select w-auto flex-grow-0">
										<option value="1" selected>is</option>
										<option value="2">is not</option>
									</select>
									<select class="form-select">
										<option value="1" selected>Active</option>
										<option value="2">Inactive</option>
										<option value="3">New</option>
										<option value="4">Expired</option>
										<option value="5">Pending</option>
									</select>
								</div>
							</div>

							<div class="d-flex">
								<button type="button" class="btn btn-light">Reset</button>

								<div class="ms-auto">
									<button type="button" class="btn btn-light">Cancel</button>
									<button type="button" class="btn btn-primary ms-2">Apply</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<ul class="nav flex-row justify-content-end order-1 order-lg-2">
				<li class="nav-item ms-lg-2">
					<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded-pill" data-bs-toggle="offcanvas" data-bs-target="#notifications">
						<i class="ph-bell"></i>
						<span class="badge bg-yellow text-black position-absolute top-0 end-0 translate-middle-top zindex-1 rounded-pill mt-1 me-1">2</span>
					</a>
				</li>

				<li class="nav-item nav-item-dropdown-lg dropdown ms-lg-2">
					<a href="#" class="navbar-nav-link align-items-center rounded-pill p-1" data-bs-toggle="dropdown">
						<div class="status-indicator-container">
							<img src="../../../assets/images/demo/users/face11.jpg" class="w-32px h-32px rounded-pill" alt="">
							<span class="status-indicator bg-success"></span>
						</div>
						<span class="d-none d-lg-inline-block mx-lg-2">Victoria</span>
					</a>

					<div class="dropdown-menu dropdown-menu-end">
						<a href="#" class="dropdown-item">
							<i class="ph-user-circle me-2"></i>
							My profile
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-currency-circle-dollar me-2"></i>
							My subscription
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-shopping-cart me-2"></i>
							My orders
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-envelope-open me-2"></i>
							My inbox
							<span class="badge bg-primary rounded-pill ms-auto">26</span>
						</a>
						<div class="dropdown-divider"></div>
						<a href="#" class="dropdown-item">
							<i class="ph-gear me-2"></i>
							Account settings
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-sign-out me-2"></i>
							Logout
						</a>
					</div>
				</li>
			</ul>
		</div>
	</div>
	<!-- /main navbar -->


	<!-- Navigation -->
	<div class="navbar navbar-sm shadow">
		<div class="container-fluid">
			<div class="flex-fill overflow-auto overflow-lg-visible scrollbar-hidden">
				<ul class="nav gap-1 flex-nowrap flex-lg-wrap">
					<li class="nav-item">
						<a href="index.html" class="navbar-nav-link rounded">
							<i class="ph-house me-2"></i>
							Home
						</a>
					</li>
					<li class="nav-item">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded" data-bs-toggle="dropdown">
							<i class="ph-layout me-2"></i>
							Page
						</a>

						<div class="dropdown-menu start-0 end-0 p-3 mx-md-3">
							<div class="row">
								<div class="col-md-4 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Navbars</div>
									<div class="mb-3 mb-md-0">
										<a href="layout_navbar_fixed.html" class="dropdown-item rounded">Fixed navbar</a>
										<a href="layout_navbar_hideable.html" class="dropdown-item rounded">Hideable navbar</a>
										<a href="layout_navbar_sticky.html" class="dropdown-item rounded">Sticky navbar</a>
										<a href="layout_fixed_footer.html" class="dropdown-item rounded">Fixed footer</a>
									</div>
								</div>
								<div class="col-md-4 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Sidebars</div>
									<div class="mb-3 mb-md-0">
										<a href="layout_2_sidebars_1_side.html" class="dropdown-item rounded">2 sidebars on 1 side</a>
										<a href="layout_2_sidebars_2_sides.html" class="dropdown-item rounded">2 sidebars on 2 sides</a>
										<a href="layout_3_sidebars.html" class="dropdown-item rounded">3 sidebars</a>
									</div>
								</div>
								<div class="col-md-4 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Sections</div>
									<div class="mb-3 mb-md-0">
										<a href="layout_no_header.html" class="dropdown-item rounded">No header</a>
										<a href="layout_no_footer.html" class="dropdown-item rounded">No footer</a>
										<a href="layout_boxed_page.html" class="dropdown-item rounded">Boxed page</a>
										<a href="layout_boxed_content.html" class="dropdown-item rounded">Boxed content</a>
									</div>
								</div>
							</div>
						</div>
					</li>
					<li class="nav-item">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded" data-bs-toggle="dropdown">
							<i class="ph-columns me-2"></i>
							Sidebars
						</a>

						<div class="dropdown-menu start-0 end-0 p-3 mx-md-3">
							<div class="row">
								<div class="col-md-3 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Main</div>
									<div class="mb-3 mb-md-0">
										<a href="sidebar_default_resizable.html" class="dropdown-item rounded">Resizable</a>
										<a href="sidebar_default_resized.html" class="dropdown-item rounded">Resized</a>
										<a href="sidebar_default_hideable.html" class="dropdown-item rounded">Hideable</a>
										<a href="sidebar_default_hidden.html" class="dropdown-item rounded">Hidden</a>
										<a href="sidebar_default_stretched.html" class="dropdown-item rounded">Stretched</a>
										<a href="sidebar_default_color_dark.html" class="dropdown-item rounded">Dark color</a>
									</div>
								</div>
								<div class="col-md-3 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Secondary</div>
									<div class="mb-3 mb-md-0">
										<a href="sidebar_secondary_hideable.html" class="dropdown-item rounded">Hideable</a>
										<a href="sidebar_secondary_hidden.html" class="dropdown-item rounded">Hidden</a>
										<a href="sidebar_secondary_stretched.html" class="dropdown-item rounded">Stretched</a>
										<a href="sidebar_secondary_color_dark.html" class="dropdown-item rounded">Dark color</a>
									</div>
								</div>
								<div class="col-md-3 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Right</div>
									<div class="mb-3 mb-md-0">
										<a href="sidebar_right_hideable.html" class="dropdown-item rounded">Hideable</a>
										<a href="sidebar_right_hidden.html" class="dropdown-item rounded">Hidden</a>
										<a href="sidebar_right_stretched.html" class="dropdown-item rounded">Stretched</a>
										<a href="sidebar_right_color_dark.html" class="dropdown-item rounded">Dark color</a>
									</div>
								</div>
								<div class="col-md-3 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Other</div>
									<div class="mb-3 mb-md-0">
										<a href="sidebar_components.html" class="dropdown-item rounded">Sidebar components</a>
									</div>
								</div>
							</div>
						</div>
					</li>
					<li class="nav-item">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded" data-bs-toggle="dropdown">
							<i class="ph-rows me-2"></i>
							Navbars
						</a>

						<div class="dropdown-menu start-0 end-0 p-3 mx-md-3">
							<div class="row">
								<div class="col-md-3 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Single</div>
									<div class="mb-3 mb-md-0">
										<a href="navbar_single_top_static.html" class="dropdown-item rounded">Top static</a>
										<a href="navbar_single_top_fixed.html" class="dropdown-item rounded">Top fixed</a>
										<a href="navbar_single_bottom_static.html" class="dropdown-item rounded">Bottom static</a>
										<a href="navbar_single_bottom_fixed.html" class="dropdown-item rounded">Bottom fixed</a>
									</div>
								</div>
								<div class="col-md-3 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Multiple</div>
									<div class="mb-3 mb-md-0">
										<a href="navbar_multiple_top_static.html" class="dropdown-item rounded">Top static</a>
										<a href="navbar_multiple_top_fixed.html" class="dropdown-item rounded">Top fixed</a>
										<a href="navbar_multiple_bottom_static.html" class="dropdown-item rounded">Bottom static</a>
										<a href="navbar_multiple_bottom_fixed.html" class="dropdown-item rounded">Bottom fixed</a>
										<a href="navbar_multiple_top_bottom_fixed.html" class="dropdown-item rounded">Top and bottom fixed</a>
										<a href="navbar_multiple_secondary_sticky.html" class="dropdown-item rounded">Secondary sticky</a>
									</div>
								</div>
								<div class="col-md-3 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Content</div>
									<div class="mb-3 mb-md-0">
										<a href="navbar_component_single.html" class="dropdown-item rounded">Single</a>
										<a href="navbar_component_multiple.html" class="dropdown-item rounded">Multiple</a>
									</div>
								</div>
								<div class="col-md-3 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Other</div>
									<div class="mb-3 mb-md-0">
										<a href="navbar_colors.html" class="dropdown-item rounded">Color options</a>
										<a href="navbar_sizes.html" class="dropdown-item rounded">Sizing options</a>
										<a href="navbar_components.html" class="dropdown-item rounded">Navbar components</a>
									</div>
								</div>
							</div>
						</div>
					</li>
					<li class="nav-item">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded active" data-bs-toggle="dropdown">
							<i class="ph-list me-2"></i>
							Navigation
						</a>

						<div class="dropdown-menu start-0 end-0 p-3 mx-md-3">
							<div class="row">
								<div class="col-md-6 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Vertical</div>
									<div class="mb-3 mb-md-0">
										<a href="navigation_vertical_styles.html" class="dropdown-item rounded">Navigation styles</a>
										<a href="navigation_vertical_collapsible.html" class="dropdown-item rounded">Collapsible menu</a>
										<a href="navigation_vertical_accordion.html" class="dropdown-item rounded">Accordion menu</a>
										<a href="navigation_vertical_bordered.html" class="dropdown-item rounded">Bordered navigation</a>
										<a href="navigation_vertical_right_icons.html" class="dropdown-item rounded">Right icons</a>
										<a href="navigation_vertical_badges.html" class="dropdown-item rounded">Badges</a>
										<a href="navigation_vertical_disabled.html" class="dropdown-item rounded active">Disabled items</a>
									</div>
								</div>
								<div class="col-md-6 mb-3 mb-md-0">
									<div class="fw-bold border-bottom pb-2 mb-2">Horizontal</div>
									<div class="mb-3 mb-md-0">
										<a href="navigation_horizontal_styles.html" class="dropdown-item rounded">Navigation styles</a>
										<a href="navigation_horizontal_elements.html" class="dropdown-item rounded">Navigation elements</a>
										<a href="navigation_horizontal_tabs.html" class="dropdown-item rounded">Tabbed navigation</a>
										<a href="navigation_horizontal_disabled.html" class="dropdown-item rounded">Disabled items</a>
										<a href="navigation_horizontal_mega.html" class="dropdown-item rounded">Mega menu</a>
									</div>
								</div>
							</div>
						</div>
					</li>
					<li class="nav-item nav-item-dropdown-lg dropdown">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded" data-bs-toggle="dropdown">
							<i class="ph-note-blank me-2"></i>
							Starter kit
						</a>

						<div class="dropdown-menu">
							<div class="dropdown-header">Basic layouts</div>
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-columns me-2"></i>
									Sidebars
								</a>
								<div class="dropdown-menu">
									<a href="../seed/layout_2_sidebars_1_side.html" class="dropdown-item rounded">2 sidebars on 1 side</a>
									<a href="../seed/layout_2_sidebars_2_sides.html" class="dropdown-item rounded">2 sidebars on 2 sides</a>
									<a href="../seed/layout_3_sidebars.html" class="dropdown-item rounded">3 sidebars</a>
								</div>
							</div>
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-rows me-2"></i>
									Navbars
								</a>
								<div class="dropdown-menu">
									<a href="../seed/layout_navbar_fixed.html" class="dropdown-item rounded">Fixed navbar</a>
									<a href="../seed/layout_navbar_hideable.html" class="dropdown-item rounded">Hideable navbar</a>
									<a href="../seed/layout_navbar_sticky.html" class="dropdown-item rounded">Sticky navbar</a>
									<a href="../seed/layout_fixed_footer.html" class="dropdown-item rounded">Fixed footer</a>
								</div>
							</div>
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-squares-four me-2"></i>
									Boxed
								</a>
								<div class="dropdown-menu">
									<a href="../seed/layout_boxed_page.html" class="dropdown-item rounded">Boxed page</a>
									<a href="../seed/layout_boxed_content.html" class="dropdown-item rounded">Boxed content</a>
								</div>
							</div>
							<div class="dropdown-header">Others</div>
							<a href="../seed/layout_no_header.html" class="dropdown-item rounded">No header</a>
							<a href="../seed/layout_no_footer.html" class="dropdown-item rounded">No footer</a>
						</div>
					</li>

					<li class="nav-item nav-item-dropdown-lg dropdown ms-lg-auto">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded" data-bs-toggle="dropdown">
							<i class="ph-arrows-clockwise me-2"></i>
							Switch
						</a>

						<div class="dropdown-menu dropdown-menu-end">
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-layout me-2"></i>
									Layouts
								</a>
								<div class="dropdown-menu">
									<a href="../../layout_1/full/index.html" class="dropdown-item">Default layout</a>
									<a href="../../layout_2/full/index.html" class="dropdown-item">Layout 2</a>
									<a href="../../layout_3/full/index.html" class="dropdown-item">Layout 3</a>
									<a href="index.html" class="dropdown-item active">Layout 4</a>
									<a href="../../layout_5/full/index.html" class="dropdown-item">Layout 5</a>
									<a href="../../layout_6/full/index.html" class="dropdown-item">Layout 6</a>
									<a href="../../layout_7/full/index.html" class="dropdown-item disabled">
										Layout 7
										<span class="opacity-75 fs-sm ms-auto">Coming soon</span>
									</a>
								</div>
							</div>
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-swatches me-2"></i>
									Themes
								</a>
								<div class="dropdown-menu">
									<a href="index.html" class="dropdown-item active">Default</a>
									<a href="../../../LTR/material/full/index.html" class="dropdown-item disabled">
										Material
										<span class="opacity-75 fs-sm ms-auto">Coming soon</span>
									</a>
									<a href="../../../LTR/clean/full/index.html" class="dropdown-item disabled">
										Clean
										<span class="opacity-75 fs-sm ms-auto">Coming soon</span>
									</a>
								</div>
							</div>
						</div>
					</li>
				</ul>
			</div>
		</div>
	</div>
	<!-- /navigation -->


	<!-- Page header -->
	<div class="page-header">
		<div class="page-header-content d-lg-flex">
			<div class="d-flex">
				<h4 class="page-title mb-0">
					Vertical Nav - <span class="fw-normal">Disabled</span>
				</h4>

				<a href="#page_header" class="btn btn-light align-self-center collapsed d-lg-none border-transparent rounded-pill p-0 ms-auto" data-bs-toggle="collapse">
					<i class="ph-caret-down collapsible-indicator ph-sm m-1"></i>
				</a>
			</div>

			<div class="collapse d-lg-block my-lg-auto ms-lg-auto" id="page_header">
				<div class="d-sm-flex align-items-center mb-3 mb-lg-0 ms-lg-3">
					<div class="dropdown w-100 w-sm-auto">
						<a href="#" class="d-flex align-items-center text-body lh-1 dropdown-toggle py-sm-2" data-bs-toggle="dropdown" data-bs-display="static">
							<img src="../../../assets/images/brands/tesla.svg" class="w-32px h-32px me-2" alt="">
							<div class="me-auto me-lg-1">
								<div class="fs-sm text-muted mb-1">Customer</div>
								<div class="fw-semibold">Tesla Motors Inc</div>
							</div>
						</a>

						<div class="dropdown-menu dropdown-menu-lg-end w-100 w-lg-auto wmin-300 wmin-sm-350 pt-0">
							<div class="d-flex align-items-center p-3">
								<h6 class="fw-semibold mb-0">Customers</h6>
								<a href="#" class="ms-auto">
									View all
									<i class="ph-arrow-circle-right ms-1"></i>
								</a>
							</div>
							<a href="#" class="dropdown-item active py-2">
								<img src="../../../assets/images/brands/tesla.svg" class="w-32px h-32px me-2" alt="">
								<div>
									<div class="fw-semibold">Tesla Motors Inc</div>
									<div class="fs-sm text-muted">42 users</div>
								</div>
							</a>
							<a href="#" class="dropdown-item py-2">
								<img src="../../../assets/images/brands/debijenkorf.svg" class="w-32px h-32px me-2" alt="">
								<div>
									<div class="fw-semibold">De Bijenkorf</div>
									<div class="fs-sm text-muted">49 users</div>
								</div>
							</a>
							<a href="#" class="dropdown-item py-2">
								<img src="../../../assets/images/brands/klm.svg" class="w-32px h-32px me-2" alt="">
								<div>
									<div class="fw-semibold">Royal Dutch Airlines</div>
									<div class="fs-sm text-muted">18 users</div>
								</div>
							</a>
							<a href="#" class="dropdown-item py-2">
								<img src="../../../assets/images/brands/shell.svg" class="w-32px h-32px me-2" alt="">
								<div>
									<div class="fw-semibold">Royal Dutch Shell</div>
									<div class="fs-sm text-muted">54 users</div>
								</div>
							</a>
							<a href="#" class="dropdown-item py-2">
								<img src="../../../assets/images/brands/bp.svg" class="w-32px h-32px me-2" alt="">
								<div>
									<div class="fw-semibold">BP plc</div>
									<div class="fs-sm text-muted">23 users</div>
								</div>
							</a>
						</div>
					</div>

					<div class="vr d-none d-sm-block flex-shrink-0 my-2 mx-3"></div>

					<div class="d-inline-flex mt-3 mt-sm-0">
						<a href="#" class="status-indicator-container ms-1">
							<img src="../../../assets/images/demo/users/face24.jpg" class="w-32px h-32px rounded-pill" alt="">
							<span class="status-indicator bg-warning"></span>
						</a>
						<a href="#" class="status-indicator-container ms-1">
							<img src="../../../assets/images/demo/users/face1.jpg" class="w-32px h-32px rounded-pill" alt="">
							<span class="status-indicator bg-success"></span>
						</a>
						<a href="#" class="status-indicator-container ms-1">
							<img src="../../../assets/images/demo/users/face3.jpg" class="w-32px h-32px rounded-pill" alt="">
							<span class="status-indicator bg-danger"></span>
						</a>
						<a href="#" class="btn btn-outline-primary btn-icon w-32px h-32px rounded-pill ms-3">
							<i class="ph-plus"></i>
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- /page header -->


	<!-- Page content -->
	<div class="page-content pt-0">

		<!-- Main content -->
		<div class="content-wrapper">

			<!-- Content area -->
			<div class="content">

				<!-- Disabled items -->
				<div class="card">
					<div class="card-header">
						<h5 class="mb-0">Disabled items</h5>
					</div>

					<div class="card-body">
						<p class="mb-3">Disabled state of vertical sidebar navigation is controlled by JS code located in <code>app.js</code> file - it disables click action in single nav item and submenu. Styles of disabled state are defined in SASS and can be easily changed in a set of variables. To disable single item or submenu with icons, add <code>.disabled</code> to any <code>.nav-link</code> element. If this element is inside <code>.nav-item-submenu</code> container, children list of nav items that comes right after nav link won't be collapsible.</p>

						<div class="row">
							<div class="col-lg-6">
								<p class="fw-semibold">In light sidebar:</p>
								<div class="sidebar sidebar-expand w-100 border shadow-none rounded mb-lg-4">
									<div class="sidebar-content">

										<!-- User menu -->
										<div class="sidebar-section sidebar-section-body">
											<div class="d-flex justify-content-center">
												<a href="#">
													<img src="../../../assets/images/demo/users/face11.jpg" width="40" height="40" class="rounded-pill" alt="">
												</a>

												<div class="flex-fill ms-3">
													<div class="fw-semibold">Victoria Baker</div>
													<div class="fs-sm lh-1 opacity-50 mt-1">
														Senior developer
													</div>
												</div>

												<div class="ms-3 align-self-center">
													<button type="button" class="btn btn-light border-transparent btn-icon btn-sm rounded-pill">
														<i class="ph-gear"></i>
													</button>
												</div>
											</div>
										</div>
										<!-- /user menu -->


										<!-- Navigation -->
										<div class="sidebar-section">
											<ul class="nav nav-sidebar" data-nav-type="accordion">
												<li class="nav-item-header">
													<div class="text-uppercase fs-sm lh-sm opacity-50">Navigation header</div>
												</li>

												<li class="nav-item">
													<a href="#" class="nav-link active">
														<i class="ph-plus-circle"></i>
														Active link
													</a>
												</li>

												<li class="nav-item">
													<a href="#" class="nav-link disabled">
														<i class="ph-circles-three-plus"></i>
														Disabled top level link
													</a>
												</li>

												<li class="nav-item nav-item-submenu">
													<a href="#" class="nav-link">
														<i class="ph-pencil"></i>
														With children
													</a>

													<ul class="nav-group-sub collapse">
														<li class="nav-item">
															<a href="#" class="nav-link disabled">Disabled second level link</a>
														</li>
														<li class="nav-item">
															<a href="#" class="nav-link">Second level link</a>
														</li>
													</ul>
												</li>

												<li class="nav-item nav-item-submenu">
													<a href="#" class="nav-link">
														<i class="ph-user-plus"></i>
														Multiple levels
													</a>

													<ul class="nav-group-sub collapse">
														<li class="nav-item">
															<a href="#" class="nav-link">Second level</a>
														</li>
														<li class="nav-item nav-item-submenu">
															<a href="#" class="nav-link disabled">Disabled second level with child</a>
															<ul class="nav-group-sub collapse">
																<li class="nav-item">
																	<a href="#" class="nav-link">Third level</a>
																</li>
																<li class="nav-item">
																	<a href="#" class="nav-link">Third level</a>
																</li>
															</ul>
														</li>
														<li class="nav-item">
															<a href="#" class="nav-link">Second level</a>
														</li>
													</ul>
												</li>

												<li class="nav-item nav-item-submenu">
													<a href="#" class="nav-link disabled">
														<i class="ph-users-three"></i>
														Disabled levels
													</a>

													<ul class="nav-group-sub collapse">
														<li class="nav-item">
															<a href="#" class="nav-link">Second level link</a>
														</li>
														<li class="nav-item">
															<a href="#" class="nav-link">Second level link</a>
														</li>
													</ul>
												</li>
											</ul>
										</div>
										<!-- /navigation -->

									</div>
								</div>
							</div>

							<div class="col-lg-6">
								<p class="fw-semibold">In dark sidebar:</p>
								<div class="sidebar sidebar-dark sidebar-expand w-100 rounded mb-lg-4">
									<div class="sidebar-content">

										<!-- User menu -->
										<div class="sidebar-section sidebar-section-body">
											<div class="d-flex justify-content-center">
												<a href="#">
													<img src="../../../assets/images/demo/users/face11.jpg" width="40" height="40" class="rounded-pill" alt="">
												</a>

												<div class="flex-fill ms-3">
													<div class="fw-semibold">Victoria Baker</div>
													<div class="fs-sm lh-1 opacity-50 mt-1">
														Senior developer
													</div>
												</div>

												<div class="ms-3 align-self-center">
													<button type="button" class="btn btn-flat-white border-transparent btn-icon btn-sm rounded-pill">
														<i class="ph-gear"></i>
													</button>
												</div>
											</div>
										</div>
										<!-- /user menu -->


										<!-- Navigation -->
										<div class="sidebar-section">
											<ul class="nav nav-sidebar" data-nav-type="accordion">
												<li class="nav-item-header">
													<div class="text-uppercase fs-sm lh-sm opacity-50">Navigation header</div>
												</li>

												<li class="nav-item">
													<a href="#" class="nav-link active">
														<i class="ph-plus-circle"></i>
														Active link
													</a>
												</li>

												<li class="nav-item">
													<a href="#" class="nav-link disabled">
														<i class="ph-circles-three-plus"></i>
														Disabled top level link
													</a>
												</li>

												<li class="nav-item nav-item-submenu">
													<a href="#" class="nav-link">
														<i class="ph-pencil"></i>
														With children
													</a>

													<ul class="nav-group-sub collapse">
														<li class="nav-item">
															<a href="#" class="nav-link disabled">Disabled second level link</a>
														</li>
														<li class="nav-item">
															<a href="#" class="nav-link">Second level link</a>
														</li>
													</ul>
												</li>

												<li class="nav-item nav-item-submenu">
													<a href="#" class="nav-link">
														<i class="ph-user-plus"></i>
														Multiple levels
													</a>

													<ul class="nav-group-sub collapse">
														<li class="nav-item">
															<a href="#" class="nav-link">Second level</a>
														</li>
														<li class="nav-item nav-item-submenu">
															<a href="#" class="nav-link disabled">Disabled second level with child</a>
															<ul class="nav-group-sub collapse">
																<li class="nav-item">
																	<a href="#" class="nav-link">Third level</a>
																</li>
																<li class="nav-item">
																	<a href="#" class="nav-link">Third level</a>
																</li>
															</ul>
														</li>
														<li class="nav-item">
															<a href="#" class="nav-link">Second level</a>
														</li>
													</ul>
												</li>

												<li class="nav-item nav-item-submenu">
													<a href="#" class="nav-link disabled">
														<i class="ph-users-three"></i>
														Disabled levels
													</a>

													<ul class="nav-group-sub collapse">
														<li class="nav-item">
															<a href="#" class="nav-link">Second level link</a>
														</li>
														<li class="nav-item">
															<a href="#" class="nav-link">Second level link</a>
														</li>
													</ul>
												</li>
											</ul>
										</div>
										<!-- /navigation -->

									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /disabled items -->


				<!-- Navigation classes -->
				<div class="card">
					<div class="card-header">
						<h5 class="mb-0">Navigation classes</h5>
					</div>

					<div class="card-body">
						This table contains all classes related to the vertical sidebar navigation. Vertical navigation is a custom layout element that uses global <code>nav</code> component classes and adapted for main navigation purposes. Depending on the needs, it supports different options such as collapsing type, icons, badges, combination with other sidebar components etc. This list explains the logic and purpose:
					</div>

					<div class="table-responsive">
						<table class="table">
							<thead class="table-light">
								<tr>
									<th style="width: 20%;">Class</th>
									<th>Description</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td><code>.nav</code></td>
									<td>Global wrapper for navigation list. It uses default Bootstrap's styles of <code>.nav</code> component and similar markup options.</td>
								</tr>
								<tr>
									<td><code>.nav-sidebar</code></td>
									<td>Default navigation class, must be used with any navigation type and color. Responsible for targeting specific style changes and basic styling of item colors, borders and icons.</td>
								</tr>
								<tr>
									<td><code>.nav-item-header</code></td>
									<td>Targets optional <code>header</code> element for separating group of nav items. In main sidebar this class is responsible for toggling text in folded sidebar mode. Use text utility classes to style inner content.</td>
								</tr>
								<tr>
									<td><code>.nav-item-divider</code></td>
									<td>Navigation item with this class separates groups of related menu items - it adds thick light (dark in light sidebar) line with extra vertical spacing. Useful in long lists.</td>
								</tr>
								<tr>
									<td><code>.nav-item</code></td>
									<td>This class is required as an immediate nav link parent in any <code>.nav</code> container. Since active link color is different from hover and initial states color, <code>.nav-item</code> has 1px vertical spacing to separate items that have same background color. Dropdown menu items have same logic.</td>
								</tr>
								<tr>
									<td><code>.nav-link</code></td>
									<td>This class is responsible for navigation link styling and is also required as a part of nav list class structure. It's also a target for <code>.active</code> and <code>disabled</code> states. Please note - this class doesn't reset <code>&lt;button></code> styles, so make sure you use <code>&lt;a></code> instead.</td>
								</tr>
								<tr>
									<td><code>.nav-link.active</code></td>
									<td>Combination of these classes in a single <code>&lt;a></code> element highlights nav item link and indicates the current page. By default, expanded submenu and <code>active</code> item state color is the same in each level.</td>
								</tr>
								<tr>
									<td><code>.nav-link.disabled</code></td>
									<td>Combination of these classes in a single <code>&lt;a></code> element disables all pointer and click events and mutes out nav link text and background colors.</td>
								</tr>

								<tr>
									<td><code>.nav-item-submenu</code></td>
									<td>This class indicates nav list item with children menu levels and needs to be added to <code>.nav-item</code> container.</td>
								</tr>
								<tr>
									<td><code>.nav-item-expanded</code></td>
									<td>Responsible for <strong>expanding</strong> submenu on page load - this class should be added to <code>.nav-item</code> container in all levels.</td>
								</tr>
								<tr>
									<td><code>.nav-item-open</code></td>
									<td>This class also should be used in combination with <code>.nav-item</code> and/or <code>.nav-item-expanded</code> classes, since it's responsible for some styling and toggles dynamically.</td>
								</tr>
								<tr>
									<td><code>.nav-group-sub</code></td>
									<td>Navigation submenu class - should be used with <code>.nav</code> class in <code>&lt;ul></code> element in all menu levels.</td>
								</tr>

								<tr>
									<td><code>.nav-sidebar-icons-reverse</code></td>
									<td>Add this class to <code>.nav-sidebar</code> container to change icons alignment from <code>left</code> to <code>right</code>.</td>
								</tr>
								<tr>
									<td><code>.nav-sidebar-bordered</code></td>
									<td>Add this class to <code>.nav-sidebar</code> container if you want to add horizontal borders to all navigation links on the first level.</td>
								</tr>

								<tr>
									<td><code>[data-nav-type="collapsible"]</code></td>
									<td>Default navigation behaviour - when new item is expanded, current level remains opened. Actually navigation container doesn't require this, but is used for the sake of semantic naming.</td>
								</tr>
								<tr>
									<td><code>[data-nav-type="accordion"]</code></td>
									<td>Changes default <code>collapsible</code> navigation type to <code>accordion</code> type - when new item is expanded, current level is collapsed.</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- /navigation classes -->


				<!-- Navigation markup -->
				<div class="card">
					<div class="card-header">
						<h5 class="mb-0">Navigation markup</h5>
					</div>

					<div class="card-body">
						<h6>Collapsible and accordion types</h6>
						<p class="mb-3">Flexible template functionality offers 2 different kinds of navigation: collapsible and accordion. Both have custom JS code and provide extended control over the child nav items. Default type is <code>collapsible</code> navigation: open as many child levels as you want, they all will be visible all the time. Second type is <code>accordion</code> navigation: allows to display only 1 parent level, collapsing all others. Both navigation types have hidden 2nd level in mini sidebar that opens on hover/focus.</p>

						<div class="row">
							<div class="col-lg-6">
								<p class="fw-semibold">Collapsible navigation markup</p>
								<pre class="language-markup mb-3 mb-lg-4" data-line="2">
									<code>
										&lt;!-- Collapsible navigation markup -->
										&lt;ul class="nav nav-sidebar" data-nav-type="collapsible">
											&lt;li class="nav-item-header">
												&lt;div class="text-uppercase fs-sm lh-sm opacity-50">Group title&lt;/div>
											&lt;/li>

											&lt;li class="nav-item">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-plus-circle">&lt;/i>
													Top level link
												&lt;/a>
											&lt;/li>

											&lt;li class="nav-item nav-item-submenu">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-circles-three-plus">&lt;/i>
													&lt;span>Top level with subnav&lt;/span>
												&lt;/a>

												&lt;ul class="nav-group-sub collapse">
													&lt;li class="nav-item">
														&lt;a href="#" class="nav-link">2nd level item&lt;/a>
													&lt;/li>
													...
												&lt;/ul>
											&lt;/li>
											...
										&lt;/ul>
										&lt;!-- /collapsible navigation markup -->										
									</code>
								</pre>
							</div>

							<div class="col-lg-6">
								<p class="fw-semibold">Accordion navigation markup</p>
								<pre class="language-markup mb-3 mb-lg-4" data-line="2">
									<code>
										&lt;!-- Accordion navigation markup -->
										&lt;ul class="nav nav-sidebar" data-nav-type="accordion">
											&lt;li class="nav-item-header">
												&lt;div class="text-uppercase fs-sm lh-sm opacity-50">Group title&lt;/div>
											&lt;/li>

											&lt;li class="nav-item">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-plus-circle">&lt;/i>
													Top level link
												&lt;/a>
											&lt;/li>

											&lt;li class="nav-item nav-item-submenu">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-circles-three-plus">&lt;/i>
													&lt;span>Top level with subnav&lt;/span>
												&lt;/a>

												&lt;ul class="nav-group-sub collapse">
													&lt;li class="nav-item">
														&lt;a href="#" class="nav-link">2nd level item&lt;/a>
													&lt;/li>
													...
												&lt;/ul>
											&lt;/li>
											...
										&lt;/ul>
										&lt;!-- /accordion navigation markup -->										
									</code>
								</pre>
							</div>
						</div>


						<h6>Icons alignment</h6>
						<p class="mb-3">Sidebar navigation supports icons in all menu levels. Default icons alignment is left, default visual hierarchy is: icon > text > arrow, adding <code>.nav-sidebar-icons-reverse</code> class to <code>.nav-sidebar</code> container changes it to text > icon > arrow. But despite the default layout, icons are completely optional, so feel free to remove icons markup from the nav links if for some reason you don't need them.</p>

						<div class="row">
							<div class="col-lg-6">
								<p class="fw-semibold">Default left icons position</p>
								<pre class="language-markup mb-3 mb-lg-4" data-line="2, 6, 13">
									<code>
										&lt;!-- Left icons -->
										&lt;ul class="nav nav-sidebar" data-nav-type="accordion">
											...
											&lt;li class="nav-item">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-plus-circle">&lt;/i>
													Navigation link #1
												&lt;/a>
											&lt;/li>

											&lt;li class="nav-item">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-circles-three-plus">&lt;/i>
													Navigation link #2
												&lt;/a>
											&lt;/li>
											...
										&lt;/ul>
										&lt;!-- /left icons -->										
									</code>
								</pre>
							</div>

							<div class="col-lg-6">
								<p class="fw-semibold">Optional right icons position</p>
								<pre class="language-markup mb-3 mb-lg-4" data-line="2, 6, 13">
									<code>
										&lt;!-- Right icons -->
										&lt;ul class="nav nav-sidebar nav-sidebar-icons-reverse" data-nav-type="accordion">
											...
											&lt;li class="nav-item">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-plus-circle">&lt;/i>
													Navigation link #1
												&lt;/a>
											&lt;/li>

											&lt;li class="nav-item">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-circles-three-plus">&lt;/i>
													Navigation link #2
												&lt;/a>
											&lt;/li>
											...
										&lt;/ul>
										&lt;!-- /right icons -->										
									</code>
								</pre>
							</div>
						</div>


						<h6>Other options</h6>
						<p class="mb-3">Default navigation menu doesn't have any horizontal borders. But you can easily change this behaviour just by adding <code>.nav-sidebar-bordered</code> class to the main <code>.nav-sidebar</code> container. You can also use additional components - such as badges, badge pills, extra text or icons - within <code>.nav-link</code>. But for proper vertical and horizontal alignment you need to use flexbox utility classes and extra containers. See the example with badges.</p>

						<div class="row">
							<div class="col-lg-6">
								<p class="fw-semibold">Bordered navigation markup</p>
								<pre class="language-markup mb-3 mb-lg-0" data-line="2">
									<code>
										&lt;!-- Bordered navigation -->
										&lt;ul class="nav nav-sidebar nav-sidebar-bordered" data-nav-type="accordion">
											&lt;li class="nav-item-header">
												&lt;div class="text-uppercase fs-sm lh-sm opacity-50">Group title&lt;/div>
											&lt;/li>

											&lt;li class="nav-item">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-plus-circle">&lt;/i>
													Navigation link #1
												&lt;/a>
											&lt;/li>

											&lt;li class="nav-item">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-circles-three-plus">&lt;/i>
													Navigation link #2
												&lt;/a>
											&lt;/li>
											...
										&lt;/ul>
										&lt;!-- /bordered navigation -->										
									</code>
								</pre>
							</div>

							<div class="col-lg-6">
								<p class="fw-semibold">Navigation with badges</p>
								<pre class="language-markup mb-3 mb-lg-0" data-line="7, 15">
									<code>
										&lt;!-- Badges -->
										&lt;ul class="nav nav-sidebar" data-nav-type="accordion">
											&lt;li class="nav-item">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-plus-circle">&lt;/i>
													Nav link with badge
													&lt;span class="badge bg-danger ms-auto">New&lt;/span>
												&lt;/a>
											&lt;/li>

											&lt;li class="nav-item">
												&lt;a href="#" class="nav-link">
													&lt;i class="ph-circles-three-plus">&lt;/i>
													Nav link with badge pill
													&lt;span class="badge bg-primary rounded-pill ms-auto">32&lt;/span>
												&lt;/a>
											&lt;/li>
										&lt;/ul>
										&lt;!-- /badges -->										
									</code>
								</pre>
							</div>
						</div>
					</div>
				</div>
				<!-- /navigation markup -->

			</div>
			<!-- /content area -->

		</div>
		<!-- /main content -->

	</div>
	<!-- /page content -->


	<!-- Footer -->
	<div class="navbar navbar-sm navbar-footer border-top">
		<div class="container-fluid">
			<span>&copy; 2022 <a href="https://themeforest.net/item/limitless-responsive-web-application-kit/13080328">Limitless Web App Kit</a></span>

			<ul class="nav">
				<li class="nav-item">
					<a href="https://kopyov.ticksy.com/" class="navbar-nav-link navbar-nav-link-icon rounded" target="_blank">
						<div class="d-flex align-items-center mx-md-1">
							<i class="ph-lifebuoy"></i>
							<span class="d-none d-md-inline-block ms-2">Support</span>
						</div>
					</a>
				</li>
				<li class="nav-item ms-md-1">
					<a href="https://demo.interface.club/limitless/demo/Documentation/index.html" class="navbar-nav-link navbar-nav-link-icon rounded" target="_blank">
						<div class="d-flex align-items-center mx-md-1">
							<i class="ph-file-text"></i>
							<span class="d-none d-md-inline-block ms-2">Docs</span>
						</div>
					</a>
				</li>
				<li class="nav-item ms-md-1">
					<a href="https://themeforest.net/item/limitless-responsive-web-application-kit/13080328?ref=kopyov" class="navbar-nav-link navbar-nav-link-icon text-primary bg-primary bg-opacity-10 fw-semibold rounded" target="_blank">
						<div class="d-flex align-items-center mx-md-1">
							<i class="ph-shopping-cart"></i>
							<span class="d-none d-md-inline-block ms-2">Purchase</span>
						</div>
					</a>
				</li>
			</ul>
		</div>
	</div>
	<!-- /footer -->


	<!-- Notifications -->
	<div class="offcanvas offcanvas-end" tabindex="-1" id="notifications">
		<div class="offcanvas-header py-0">
			<h5 class="offcanvas-title py-3">Activity</h5>
			<button type="button" class="btn btn-light btn-sm btn-icon border-transparent rounded-pill" data-bs-dismiss="offcanvas">
				<i class="ph-x"></i>
			</button>
		</div>

		<div class="offcanvas-body p-0">
			<div class="bg-light fw-medium py-2 px-3">New notifications</div>
			<div class="p-3">
				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face1.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-success"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">James</a> has completed the task <a href="#">Submit documents</a> from <a href="#">Onboarding</a> list

						<div class="bg-light rounded p-2 my-2">
							<label class="form-check ms-1">
								<input type="checkbox" class="form-check-input" checked disabled>
								<del class="form-check-label">Submit personal documents</del>
							</label>
						</div>

						<div class="fs-sm text-muted mt-1">2 hours ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face3.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-warning"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Margo</a> has added 4 users to <span class="fw-semibold">Customer enablement</span> channel

						<div class="d-flex my-2">
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face10.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-danger"></span>
							</a>
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face11.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-success"></span>
							</a>
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face12.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-success"></span>
							</a>
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face13.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-success"></span>
							</a>
							<button type="button" class="btn btn-light btn-icon d-inline-flex align-items-center justify-content-center w-32px h-32px rounded-pill p-0">
								<i class="ph-plus ph-sm"></i>
							</button>
						</div>

						<div class="fs-sm text-muted mt-1">3 hours ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start">
					<div class="me-3">
						<div class="bg-warning bg-opacity-10 text-warning rounded-pill">
							<i class="ph-warning p-2"></i>
						</div>
					</div>
					<div class="flex-1">
						Subscription <a href="#">#466573</a> from 10.12.2021 has been cancelled. Refund case <a href="#">#4492</a> created
						<div class="fs-sm text-muted mt-1">4 hours ago</div>
					</div>
				</div>
			</div>

			<div class="bg-light fw-medium py-2 px-3">Older notifications</div>
			<div class="p-3">
				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face25.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-success"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Nick</a> requested your feedback and approval in support request <a href="#">#458</a>

						<div class="my-2">
							<a href="#" class="btn btn-success btn-sm me-1">
								<i class="ph-checks ph-sm me-1"></i>
								Approve
							</a>
							<a href="#" class="btn btn-light btn-sm">
								Review
							</a>
						</div>

						<div class="fs-sm text-muted mt-1">3 days ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face24.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-grey"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Mike</a> added 1 new file(s) to <a href="#">Product management</a> project

						<div class="bg-light rounded p-2 my-2">
							<div class="d-flex align-items-center">
								<div class="me-2">
									<img src="../../../assets/images/icons/pdf.svg" width="34" height="34" alt="">
								</div>
								<div class="flex-fill">
									new_contract.pdf
									<div class="fs-sm text-muted">112KB</div>
								</div>
								<div class="ms-2">
									<button type="button" class="btn btn-flat-dark text-body btn-icon btn-sm border-transparent rounded-pill">
										<i class="ph-arrow-down"></i>
									</button>
								</div>
							</div>
						</div>

						<div class="fs-sm text-muted mt-1">1 day ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<div class="me-3">
						<div class="bg-success bg-opacity-10 text-success rounded-pill">
							<i class="ph-calendar-plus p-2"></i>
						</div>
					</div>
					<div class="flex-fill">
						All hands meeting will take place coming Thursday at 13:45.

						<div class="my-2">
							<a href="#" class="btn btn-primary btn-sm">
								<i class="ph-calendar-plus ph-sm me-1"></i>
								Add to calendar
							</a>
						</div>

						<div class="fs-sm text-muted mt-1">2 days ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face4.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-danger"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Christine</a> commented on your community <a href="#">post</a> from 10.12.2021

						<div class="fs-sm text-muted mt-1">2 days ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<div class="me-3">
						<div class="bg-primary bg-opacity-10 text-primary rounded-pill">
							<i class="ph-users-four p-2"></i>
						</div>
					</div>
					<div class="flex-fill">
						<span class="fw-semibold">HR department</span> requested you to complete internal survey by Friday

						<div class="fs-sm text-muted mt-1">3 days ago</div>
					</div>
				</div>

				<div class="text-center">
					<div class="spinner-border" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- /notifications -->


	<!-- Demo config -->
	<div class="offcanvas offcanvas-end" tabindex="-1" id="demo_config">
		<div class="position-absolute top-50 end-100 visible">
			<button type="button" class="btn btn-primary btn-icon translate-middle-y rounded-end-0" data-bs-toggle="offcanvas" data-bs-target="#demo_config">
				<i class="ph-gear"></i>
			</button>
		</div>

		<div class="offcanvas-header border-bottom py-0">
			<h5 class="offcanvas-title py-3">Demo configuration</h5>
			<button type="button" class="btn btn-light btn-sm btn-icon border-transparent rounded-pill" data-bs-dismiss="offcanvas">
				<i class="ph-x"></i>
			</button>
		</div>

		<div class="offcanvas-body">
			<div class="fw-semibold mb-2">Color mode</div>
			<div class="list-group mb-3">
				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-2">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-sun ph-lg me-3"></i>
							<div>
								<span class="fw-bold">Light theme</span>
								<div class="fs-sm text-muted">Set light theme or reset to default</div>
							</div>
						</div>
						<input type="radio" class="form-check-input cursor-pointer ms-auto" name="main-theme" value="light" checked>
					</div>
				</label>

				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-2">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-moon ph-lg me-3"></i>
							<div>
								<span class="fw-bold">Dark theme</span>
								<div class="fs-sm text-muted">Switch to dark theme</div>
							</div>
						</div>
						<input type="radio" class="form-check-input cursor-pointer ms-auto" name="main-theme" value="dark">
					</div>
				</label>

				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-0">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-translate ph-lg me-3"></i>
							<div>
								<span class="fw-bold">Auto theme</span>
								<div class="fs-sm text-muted">Set theme based on system mode</div>
							</div>
						</div>
						<input type="radio" class="form-check-input cursor-pointer ms-auto" name="main-theme" value="auto">
					</div>
				</label>
			</div>

			<div class="fw-semibold mb-2">Direction</div>
			<div class="list-group mb-3">
				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-0">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-translate ph-lg me-3"></i>
							<div>
								<span class="fw-bold">RTL direction</span>
								<div class="text-muted">Toggle between LTR and RTL</div>
							</div>
						</div>
						<input type="checkbox" name="layout-direction" value="rtl" class="form-check-input cursor-pointer m-0 ms-auto">
					</div>
				</label>
			</div>

			<div class="fw-semibold mb-2">Layouts</div>
			<div class="row">
				<div class="col-12">
					<a href="../../layout_1/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_1.png" class="img-fluid img-thumbnail" alt="">
					</a>				</div>
				<div class="col-12">
					<a href="../../layout_2/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_2.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="../../layout_3/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_3.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_4.png" class="img-fluid img-thumbnail bg-primary bg-opacity-20 border-primary" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="../../layout_5/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_5.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="../../layout_6/full/index.html" class="d-block">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_6.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
			</div>
		</div>

		<div class="border-top text-center py-2 px-3">
			<a href="https://themeforest.net/item/limitless-responsive-web-application-kit/13080328?ref=kopyov" class="btn btn-yellow fw-semibold w-100 my-1" target="_blank">
				<i class="ph-shopping-cart me-2"></i>
				Purchase Limitless
			</a>
		</div>
	</div>
	<!-- /demo config -->

</body>
</html>
