"Ask me" = "Consultarme";
"Same folder as input files" = "La misma carpeta de la fuente";
"Send anonymous system profile" = "Enviar perfil anónimo de sistema";
"Other..." = "Otro...";
"Check Now" = "Chequear";
"Inspect application bundles" = "Revisar contenido de paquetes";
"When extracting multiple archives only the last extracted archive will be shown." = "Al extraer varios archivos, solo se mostrara el último.";
"Move archive to Trash" = "Enviar archivo a la papelera";
"What to do after extracting archives that were dragged onto the Dock icon or opened by double-clicking. Ignored if Archiver is already running." = "Como proceder luego de abrir archivos con Archiver. Ignorado si Archiver está andando.";
"When Archiver is started it will automatically look for a newer version." = "Archiver buscará una actualización al iniciar.";
"Browsing extracted archives:" = "Navegar archivos extraídos:";
"Output files to:" = "Guardar archivos:";
"Play sound" = "Reproducir sonido";
"Deleting archives immediately cannot be undone." = "Eliminar directamente no se puede deshacer.";
"Delete archive immediately" = "Eliminar directamente";
"After extracting an archive:" = "Al extraer:";
"Current" = "Corriente";
"Ignore Mac-specific files" = "Ignorar archivos de sistema";
"When a command completes:" = "Al terminar una tarea:";
"Look inside application bundles when browsing extracted archives" = "Navegar los contenidos de un paquete (por ejemplo, programas).";
"When creating archives ignore Mac OS X hidden files and directories such as .DS_Store and resource forks." = "Ignorar archivos escondidos de Mac OS X, como .DS_Store al archivar.";
"Radio" = "Radio";
"NotUsed" = "No usado";
"OtherViews" = "Otra visión";
"Automatically check for updates" = "Actualizar automáticamente";
"Keep archive" = "Mantener el archivo";
"Select all" = "Seleccionar todos";
"Select the file types you want Archiver to handle" = "Formatos que Archiver debe manejar";
"Show output files in Finder" = "Mostrar archivos completados en Finder";
"Deselect all" = "Anular la selección de todos";

"Maximum Simultaneous Tasks:" = "Máximas Tareas Simúltaneas:";

"Go back to start" = "Comenzar de nuevo";

"Drag and drop extract:" = "Extraer con Drag & Drop:";
"Quit when done" = "Salir al terminar";
"Skip preview" = "No mostrar vista preliminar";
"Skip previewing the contents of archives and extract immediately. Press the Shift key while adding files to temporarily invert this feature." = "Extraer archivo sin ver su contenido. Mantenga Shift oprimido al añadir archivos para invertir esta selección temporalmente.";

"Creating archives:" = "Creando archivos:";
"Skip the option to add more files and go straight to the create options. Press the Shift key while adding files to temporarily invert this feature." = "Ir en seguida a archivar. Mantenga Shift oprimido al añadir archivos para invertir esta selección temporalmente.";

/* RAR */
"Archiver will offer RAR as a format when creating Archives. If the required third party RAR utility is not yet installed, Archiver will provide instructions to do so." = "Archiver ofrecerá el formato RAR para archivar. Si el software adicional RAR no está instalado, Archiver le dará instrucciones para hacerlo.";
"Create RAR archives" = "Archivar con RAR";
"RAR archives:" = "Archivos RAR:";

/* Formats -- no need to localize */
"zip" = "zip";
"7z" = "7z";
"rar" = "rar";
"sitx" = "sitx";
"tbz" = "tbz";
"arj" = "arj";
"adf" = "adf";
"Z" = "Z";
"bz2" = "bz2";
"cab" = "cab";
"rpm" = "rpm";
"sea" = "sea";
"hqx" = "hqx";
"tar" = "tar";
"lha" = "lha";
"jar" = "jar";
"pax" = "pax";
"exe" = "exe";
"tgz" = "tgz";
"cpio" = "cpio";
"lza" = "lza";
"sit" = "sit";
"gz" = "gz";
"chksplit" = "chksplit";
"xar" = "xar";
"zsplit" = "zsplit";
"split" = "split";
"archiver" = "archiver";
