{"name": "flare", "children": [{"name": "animate", "children": [{"name": "Pause", "size": 449}, {"name": "FunctionSequence", "size": 5842}, {"name": "interpolate", "children": [{"name": "ArrayInterpolator", "size": 1983}, {"name": "ColorInterpolator", "size": 2047}, {"name": "DateInterpolator", "size": 1375}, {"name": "Interpolator", "size": 8746}, {"name": "PointInterpolator", "size": 1675}, {"name": "RectangleInterpolator", "size": 2042}]}, {"name": "ISchedulable", "size": 1041}, {"name": "Tween", "size": 6006}]}, {"name": "data", "children": [{"name": "DataSchema", "size": 2165}, {"name": "DataSet", "size": 586}, {"name": "converters", "children": [{"name": "Converters", "size": 721}, {"name": "DelimitedTextConverter", "size": 4294}, {"name": "GraphMLConverter", "size": 9800}, {"name": "IDataConverter", "size": 1314}, {"name": "JSONConverter", "size": 2220}]}, {"name": "DataSource", "size": 3331}, {"name": "DataUtil", "size": 3322}]}, {"name": "physics", "children": [{"name": "DragF<PERSON><PERSON>", "size": 1082}, {"name": "GravityForce", "size": 1336}, {"name": "Spring", "size": 2213}, {"name": "SpringForce", "size": 1681}]}, {"name": "query", "children": [{"name": "If", "size": 2732}, {"name": "Match", "size": 3748}, {"name": "Maximum", "size": 843}, {"name": "methods", "children": [{"name": "count", "size": 277}, {"name": "distinct", "size": 292}, {"name": "div", "size": 595}, {"name": "eq", "size": 594}, {"name": "fn", "size": 460}, {"name": "xor", "size": 354}]}, {"name": "Query", "size": 13896}, {"name": "Range", "size": 1594}, {"name": "<PERSON><PERSON>", "size": 1101}]}, {"name": "scale", "children": [{"name": "IScaleMap", "size": 2105}, {"name": "ScaleType", "size": 1821}, {"name": "TimeScale", "size": 5833}]}]}