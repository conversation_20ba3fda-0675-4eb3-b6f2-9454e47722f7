<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Autoupdate.app/Contents/Info.plist</key>
		<data>
		rNoq+Nb5v48z9OpO3tBAveS2cxY=
		</data>
		<key>Resources/Autoupdate.app/Contents/MacOS/Autoupdate</key>
		<data>
		lF+sWXqYf8/Immp3ik0dcuyyPeE=
		</data>
		<key>Resources/Autoupdate.app/Contents/MacOS/fileop</key>
		<data>
		MnUjNv+qsxVYsQM0hfFc9OH4VCg=
		</data>
		<key>Resources/Autoupdate.app/Contents/PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Resources/Autoupdate.app/Contents/Resources/AppIcon.icns</key>
		<data>
		4McwRDEss5BzWwUMG2Xf93+ze08=
		</data>
		<key>Resources/Autoupdate.app/Contents/Resources/SUStatus.nib</key>
		<data>
		/rAEo1B1IMuT++QH9jb1wz+oCHg=
		</data>
		<key>Resources/Autoupdate.app/Contents/Resources/ar.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LukjJpJ5Ww35Ag4hcLjpp0+enfo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ca.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			K1BEF6sG2vXMLgibwfo3j2h588E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/cs.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			K3PTl8P/irY7ZWO2p6MvO45bWTQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/da.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x947GM5tK1TxpTTBIivrZ0hzzdg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/de.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PROMgX5xg/5euK6ivHo9XYy3SRs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/el.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			duTlFgNXuv3YIQQIUZRKHb3wmsQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/en.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9kxQXywmbUqq7wX1l9/Qua08n8U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/es.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BkNyE3AEFF2wzrzH0A+g1ShVRTo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/fi.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1+xo6Bhub9myF0XkuYJ+w/G2gKI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/fr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B6LBDFZkJ1nv9bqmxZOdZN3Q86M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/he.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ONZyQ7mMihp025wvYCm+YH5p9t8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/is.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bKE7f6KUVWbXzh+cBrwa31j6sXU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/it.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			41By231TKY1pZWvN6ejI5PBZyN4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ja.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LoERNTtLfUOLrz6GUgXAD+ckfe8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ko.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			z1E0HjfGWgDUJtc+f6n7zqTt7eQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/nb.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Pe1adGG0WImrk0VUTGX+6pXMU6U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/nl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NpzxskoanVwd0mOw/Br19YDZ8K8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/pl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			G4wjYFMxWGPOAAxaNjrZ34Rr8KE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/pt_BR.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CvPfb4BD5Yb2Z/MRGxWXQLBROMs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/pt_PT.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+h+hb4+Oe6R6b+U99eVgo+0eijc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ro.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aDd+e0uLA25hDNK6uEx5GzEUmK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ru.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			J4SsMdgzSxKnVlUcZ15e4/0m1XE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/sk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BUXoufCSHk9Lsuh4kYM0qdhyfEM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/sl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			806UZzqlNQvFgNgfsWGSoax+oiQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/sv.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			W7ZVinq4r8EIhBlOCaXXDfzrCBI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/th.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zy0oe+6xz2inIt5LXCfsOKXlcNo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/tr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9LhFHtUDPwFhHC4cUovRTM7PzB0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/uk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5poRW70Xm793iN7MM1w+hqvbFRo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/zh_CN.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v4hee4Qg4FAkc3/LGqGeftwD0w4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/zh_TW.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			W3MpUPpc5MOWi5ZqLimlPxjMZc4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/_CodeSignature/CodeResources</key>
		<data>
		c+xoiOZXoPvGZHNH7Ex6KqLqoD4=
		</data>
		<key>Resources/Info.plist</key>
		<data>
		K9mwcqGHgQogDM9MO77ABvORVUY=
		</data>
		<key>Resources/SUModelTranslation.plist</key>
		<data>
		iD2Ex40Usc4ZE6IAhRePqgwK/xw=
		</data>
		<key>Resources/SUStatus.nib</key>
		<data>
		/rAEo1B1IMuT++QH9jb1wz+oCHg=
		</data>
		<key>Resources/ar.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			LjsB+a97kw2kuwwp4ZZfVTffiMg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Inpmlj5/u9tuBjfOmrTXkgEFkd8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			gqp/+/v6CfkXYwtpkYVZ47VeHHU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LukjJpJ5Ww35Ag4hcLjpp0+enfo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			K1BEF6sG2vXMLgibwfo3j2h588E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Yn6yAw8DcU8ltjgH4wQCjb+kvwc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			VyYSIadU4sMkwn1Ep7tS0Xt7sv0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			VGpfcUZQthTxOHoNOK5zfjuns54=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			K3PTl8P/irY7ZWO2p6MvO45bWTQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			MDOPDGBA3JIz6wn3iahDfYjWvto=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			CSLAri+ZhDKHzkbxaAAoPJgefrI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			6qn8ikylze1qeptdZcHit0SU0cU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x947GM5tK1TxpTTBIivrZ0hzzdg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			U5+DemDa1YPiwbA3Jw+T/3SmLIQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			B163XwJDevU0/ZW4x4g3wYTHu7g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			kKN9HB9yBw+mh8eSuYREKyjbfC4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PROMgX5xg/5euK6ivHo9XYy3SRs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			VBQujTnUvJtHpJthekrU2pcv+q4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			qCREcBlT9rkR2YSGX4RIaGKX0W0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			fD0S6Jvjm2hYIfR3O2TTyEwohbQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			duTlFgNXuv3YIQQIUZRKHb3wmsQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			WFZuLuciMPGXBBlKSDzt1d0Q1VI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			8yThlSplK5lvKh7CX47HQ1In8pc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0pZ3I8hY1zjVitHiSKvsIdvbuic=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9kxQXywmbUqq7wX1l9/Qua08n8U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			c77H0XvWPCCll9s6Z32dNNctV5g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			LG2GisB484k0nk82yPD+tYiJR4c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			K+RHs24bJFELtompaBjaD+GNAkc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BkNyE3AEFF2wzrzH0A+g1ShVRTo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1+xo6Bhub9myF0XkuYJ+w/G2gKI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ujbeXtCdAsigpyu8Tm+NbTzpJAk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			UYYkHaVct1FxvllqabT4BH+F2z0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			xyhwhRGsosArMI7Hhj+IOn2Vny8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B6LBDFZkJ1nv9bqmxZOdZN3Q86M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ONZyQ7mMihp025wvYCm+YH5p9t8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			izmjd/hPRos7fvpLop0Y8fc5mrM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			HZYvvHJeh+8OMzEcIosln1kW3Vg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			qk2a9xsaf3+ubAMlVu86HVTLDpE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bKE7f6KUVWbXzh+cBrwa31j6sXU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			MZK9K4OnapwCSZZYLzTsg6iAC1c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			WVKjsvdqLwkSGAhS/gy+OLxOXhI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			hKNvNBHIOjuc9aVlxrxR3Hwc7No=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			41By231TKY1pZWvN6ejI5PBZyN4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			kXW9ELhvTAj3pCXIn5XFH//lQKY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			xeJzTdAFQJUbXdxLx+Rz54dqEI8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			bl6jvi1L7R7TXRS0IjGnJOKzk+g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LoERNTtLfUOLrz6GUgXAD+ckfe8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			6l1DucFF2JERXSDVdkiPWUwCu7U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			4uTBP41Lj3+nF8d17fgbtqxvauU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			WnVaNsBiTWSop4kDdE3p9adsyvg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			z1E0HjfGWgDUJtc+f6n7zqTt7eQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			gXPgT+7xsRWO6R1Y2BvyJlRTIAg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Gn7hKFlX/2PGKE3hgzU6SvexAjo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			lyc6IRJmDa7Qrq7dUoWghFphPaM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Pe1adGG0WImrk0VUTGX+6pXMU6U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			edGVXExHEEaYt9Xxm6TI+JkoBak=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			e3tv+uOheXPhq3/Yq7F6PJ4VLCs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			wv4y8OjJ+vuqc0Cr+Vv/55tV3TA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NpzxskoanVwd0mOw/Br19YDZ8K8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			CrvWow//0sxv9woizofpc3/E5Mo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ECDLv6G9oBvS9UB5kR90dmQSYaQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			fSyVd+KpHFbBVCpGinuicabhmgY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			G4wjYFMxWGPOAAxaNjrZ34Rr8KE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			z5RMIEvlgieBIsC0kWImPabISx4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			9NvlKf7FoZlx8jhKZQidxtSx020=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			S8mAG7FcN4v83Dt07PTpCv/RbIA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CvPfb4BD5Yb2Z/MRGxWXQLBROMs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			BRC0yH0pw2HVsKlqSJYAL104cao=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0/OOE4kDHjd4qILL+PODojtinfI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			IKdTpm3/E4tflcSB6lk5dsnqqHU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+h+hb4+Oe6R6b+U99eVgo+0eijc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Bclp+Kqrgg6Ad0QPdI8PJg448co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			FFahGw0h17Ql8FJesjKuTSSYWFo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ULdp9acSpkVLsusrUcYtg4WPrCs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aDd+e0uLA25hDNK6uEx5GzEUmK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Wjkurdecv4Hc//vsCO6vlHzGtKY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			bI3/Z6vKKuka5C0cbEZuCJXhXRA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			xbNdo+MhYiZVoQClL6S+q0Ehy+4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			J4SsMdgzSxKnVlUcZ15e4/0m1XE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			1NjsVqPDABIbkiMNmO2rtCP/3ko=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			aRQxITWMNwZiSqXxyMc1Qkua/hg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			C62ekplIxgFa+md8y56ePbjk5N0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BUXoufCSHk9Lsuh4kYM0qdhyfEM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			f7xE8gxZOxIrrhOj609ehdmngeA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			8IUAq8EmwO+ilTKkrXiePkW7y4o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			sH/lhHarRW2RzC987343k7M+WXQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			806UZzqlNQvFgNgfsWGSoax+oiQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			qXdNKjcjKUs+KI1OnXLXBkmvwso=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			QyfMAQBTDVsJGK0Vq+rVo++gQUg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Np7sH1s1dbZ4+S3pKMzZ/klfV6o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			W7ZVinq4r8EIhBlOCaXXDfzrCBI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			KZCQE/WypaXp5WKGv17xYq4+9RA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			GuOCDSR2b0WoVHGq4PDQGbtrYpA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			1tSc7NMNF5H3FntBN7t/fZqMC70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zy0oe+6xz2inIt5LXCfsOKXlcNo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			4FIFA4OGFQn46fApVM28wUyMEp8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			JD1mpe0x3/qfBM2J6AZolzyDJoQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			a1eOVFR7mc3ZtpsLbu8vwPiahGs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9LhFHtUDPwFhHC4cUovRTM7PzB0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			pVA4XrF+trImmyKRDMKBmuE8+ps=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			yV4vVaBRhWzd43UsA/h2jSi9t6A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			RGwwckdwHoOSpoklcnjRd7zTh2s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5poRW70Xm793iN7MM1w+hqvbFRo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			OhxPBKoj5+qOzDZft65PwfxZVxg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			CyVgZGkwNU9r6XnOcftmry8gujc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			nSRScJsjD9xHwcZ2lZxh2u4FBYo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v4hee4Qg4FAkc3/LGqGeftwD0w4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0RuBjp5r99vOLjgt62x7aOhivZE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			RAbhaIVp1QOTLjNJMddZ066Dwgk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash</key>
			<data>
			8zvAqhrhrPvxsyjKKoKKWIuleM8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			W3MpUPpc5MOWi5ZqLimlPxjMZc4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SUAppcast.h</key>
		<dict>
			<key>hash2</key>
			<data>
			J43GI/y6LGfqO/b7Y3AVaVkswdRJYBaj5tKItGaRAGI=
			</data>
		</dict>
		<key>Headers/SUAppcastItem.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rdX7gH+ZB6Mu11JdS8uoy503EVC21cS6SjB5cOBd7Ag=
			</data>
		</dict>
		<key>Headers/SUErrors.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Qw7fGzvKT7wlFaz42s6i2JEQvsRx3cgOvhYxeyWnKz0=
			</data>
		</dict>
		<key>Headers/SUExport.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XO8CQmbFThLbYg949NEGhg3g+iouIw3/3+BCCLtEdFE=
			</data>
		</dict>
		<key>Headers/SUStandardVersionComparator.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5HrjOlX+uJHOw1ma+1BtG5tlYx/75N/gVzt+BX/GOxg=
			</data>
		</dict>
		<key>Headers/SUUpdater.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kQULzqzIMflW3nx8JxL7Pclz+WXUXmV+E3IUDGrsgdM=
			</data>
		</dict>
		<key>Headers/SUUpdaterDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			iZ6DAWe0qGBXL2f2xWheesmpZDfJ2m97u9gqznzpWnM=
			</data>
		</dict>
		<key>Headers/SUVersionComparisonProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rsm3T+GsIhDgSqY8EtkBpIxYgSZCZxf4HE9a/FcTRCc=
			</data>
		</dict>
		<key>Headers/SUVersionDisplayProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			AQITUMp8aq1UAOrmksAKmGFpgI24u9rDSBBZrgDqdN4=
			</data>
		</dict>
		<key>Headers/Sparkle.h</key>
		<dict>
			<key>hash2</key>
			<data>
			P//3APfMBIKF06jWtnwmnNgoXHiKFtKFfKmCXSjLSfw=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			1TF+JZkzFr6n8oH4WItto+C5Vf3K12f0H9KjqD0A5QU=
			</data>
		</dict>
		<key>PrivateHeaders/SUUnarchiver.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SQYAanTtlyX15CJapj5tDbhBEMtgQ7ZNdmpSij0+tD4=
			</data>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			4/stg0B7yh0/y73s1FeloFlH2kIAo+BiLfe90yAOaSA=
			</data>
		</dict>
		<key>Resources/Autoupdate.app/Contents/MacOS/Autoupdate</key>
		<dict>
			<key>hash2</key>
			<data>
			TayltD3IkGndo93yH0Jb0KwRn5AORlLtyz78RKcwpPY=
			</data>
		</dict>
		<key>Resources/Autoupdate.app/Contents/MacOS/fileop</key>
		<dict>
			<key>hash2</key>
			<data>
			d20Diy8AfSyTm6sOAmkkv7GdlC76CLxSpHznskolxTo=
			</data>
		</dict>
		<key>Resources/Autoupdate.app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/AppIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			nq7j0ugQwyNbJn/7zGFwxIR0njwU3i7hAYKEyZhvUfE=
			</data>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/SUStatus.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			d7FfkKD70vVGVvQvinxa69JCFWtPzijc+MYFHT6ig/k=
			</data>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ar.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Dgin+ZREzb70OO9DhCRbC8RJip9EjdSbdg+8/CWxNNs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ca.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			D01nO0KWUvaVR/PR0E95dLAlJCYEKPRh858t+lcxFto=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/cs.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nN+yr+WdHMponOCHmp0/5vE/fsQc/r0nzJtjYZ09UX4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/da.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/79yLHmHUPRJ8KawslNb3cq8a9CTBGlm1OSWwfwU7Q8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/de.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			99X6YkkbUI0BJBZMR1XNLxBwadYXGEF357yvZFCXA3Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/el.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			i//7POJ7aTgQGk7M8b1GXfbefWDTF82TNTpSRMrEjrk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/en.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ufLMidrngAmf9liRSaXVZMkCdQR3HRqNyaIoqj0Yl/U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/es.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3Le/akW48w54tf03qiJqSkRB1wvjcGqqL8XqaZNdtBQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/fi.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k/p41P/YRgBzKHE6v8Mxfdz8aDQdpwkaj50LXrEfPEM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/fr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1C1hgSmLW5shH6DR9fh/PY2koJIm1ibUOhLI1vXdSGQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/he.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			35ECtsAW7lQQpZTAtYBIKgel5ItYO6FvWJaSueWWqVU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/is.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Dh4VgRSkntzRdCDvUFT0O91wxRUTyfKmsonwoD8JO3s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/it.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tjMnwfwU5Igglr94Dv24Nt3O31hM7fZvYJnDfQW9NEk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ja.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+xaM4bwL38YJxASbDBXz+2fnds+FPoG4G/YcZrPWSNE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ko.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aP8grl5Ry4ImijxP8wX9MzNx1GKWKVmxMfS3cTODDlU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/nb.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			oBWzWKqVlCPzKWCBvsUtJ6H13MchuyEwWhKMM4kjOq4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/nl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			r9KbetMXEQrV1+bL5yEjqMkzzuuTlk4RlZbaGVfWBDo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/pl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HwsNLLSPg/K/JbI2yvmgSPTYXM5P9gfaJgS8wR2OPog=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/pt_BR.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zjRkd49gU9VUXAcyE8ZVSAyfskOtw6/Mg+Z+iKkQ8jw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/pt_PT.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			BSmBV0e2NO4PefCA5dL+UrqMJg/DOi6olm+mr7GP1Jw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ro.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1NtaWe5xkasH4P/Ma1tHG844ydllawrSRpD908/lpP4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/ru.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			odUHmWqOA4ZSC7wDIUpFD1A6ChR4WqcR37/w30rq0dQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/sk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6YvPwbtzTbM9UWXZEGjmGWcX0TQjzVNSM+0RiSQ9jSE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/sl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VXO4X+l+7gWZhzMi9NPZkSqFKYlEwUZPBzLv1BFD5XM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/sv.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			JALChfnJN5R/5rlXw6ae6Eq7un2U5AhnkTyNN1sDMRw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/th.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CaxhpacDM/KeDEPdb/N/ESYd0O74lFj9fE4qHPeW3Rk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/tr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vSNCRrDPoFdPKuxOf007CU/HS0gzIWVA9CDyTMq6iLU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/uk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			K/Pq3GisQjEQVUdR5sQRWueQzNtIbhfodBw0oGV6hKs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/zh_CN.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			38g3cjpb97nVKPi6AKZ9ntlt7blXmByBK0beg8bwu2E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/Resources/zh_TW.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AqfSJvdrBIJoZ1I1440ZV9CbltpbJ1i1+ofre48GpO4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/Autoupdate.app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			41sNVtLN2eUUVHfmUjSDNCD6n99BJ3yQjZ4y/NRIWzE=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CppTvNyKt+DXarqFMyimZm6r+SQCAyNxlXmYnW2cTmE=
			</data>
		</dict>
		<key>Resources/SUModelTranslation.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			bxnpRQhROJXTL2xuIeffR3p+hOuuji7eOoDeNqSYqTg=
			</data>
		</dict>
		<key>Resources/SUStatus.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			d7FfkKD70vVGVvQvinxa69JCFWtPzijc+MYFHT6ig/k=
			</data>
		</dict>
		<key>Resources/ar.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			PBJY/dlmanXidoFxrEHeNXzkOcuWVD7UUxi3/EejP14=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			RATW+mrRZGtN/zM3w4EYmps1TR4GCtTV95TXhp/pRhg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			CfIRBcaA4Z+L5irvQKGAD+h9MezeW39oW5adz5bsQtQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Dgin+ZREzb70OO9DhCRbC8RJip9EjdSbdg+8/CWxNNs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			D01nO0KWUvaVR/PR0E95dLAlJCYEKPRh858t+lcxFto=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			cKaGvNfy1dgIXqNn+6BCH6bf8J6Y//qE/qWGJqv8ZGQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BbdpgkH18nAjRe2ZeYW3PbFGxwXlDiS5FO2V74QEcnQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			mhWH98VyJHU/0L1YO02MnjPYnoZeMHOA20wliqX+v/4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nN+yr+WdHMponOCHmp0/5vE/fsQc/r0nzJtjYZ09UX4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			vb47ELcdaWYIRGXs9dI1Kojq1UkndAwzcr1TUz6/7GU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			DIBWgSVXvTX6rWFf6Liob+30HTBK9uPb1iY+PqULhzM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			MDhnKLCACfNBezdRcRKALaWnj06vtG/qQn2sld8yNi4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/79yLHmHUPRJ8KawslNb3cq8a9CTBGlm1OSWwfwU7Q8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			mOOMNYPWwWHFfd9cqTXc+vPq+kjRodwHOqDyCgkwH3A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			mD2Qaff26/KCNKtCbicbqKUXjH+K5CesekqrR0J3ehs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			MwtzyQWxa08vpt1zG5BcN5t+amFxxdvoFJi8k+IUnaM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			99X6YkkbUI0BJBZMR1XNLxBwadYXGEF357yvZFCXA3Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			f0PomS+0kvJ4Wvgave6qcucGU8NiuF9684wfn0KpDd8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ixieJ0+syCNka4+wdRwPBPlhPldmqZ/WACfGDe1aSEQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			HIw5bCDPB47iq+3+aDWj2/IasL6sYA2Uc1OJ1MMYIWE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			i//7POJ7aTgQGk7M8b1GXfbefWDTF82TNTpSRMrEjrk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			7RfAZoGdWgRwb4nKBQyD2BpxTjee2f2peKL7XoRc2pM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			vAwWT0WNmhvxVhmBY8lBabjhJSDV7Q2reHKQ7s1hKqs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			uuFWDHQqfqnZlvGHX7uAMGGLwngoclXDQgNA7dsNs3M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ufLMidrngAmf9liRSaXVZMkCdQR3HRqNyaIoqj0Yl/U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			nRjEFE6qBHP8qFAZRQoI4EV7ZDnhUAZpq1iQ+cgh+K8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0eqOJZDhOkHFwGt7Vous9mClRH0OzgsiybXBGT3WQ3c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KANyoLj4WrDb5mrwObBGitSzXMWvXeD11Um4rMu53zM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3Le/akW48w54tf03qiJqSkRB1wvjcGqqL8XqaZNdtBQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k/p41P/YRgBzKHE6v8Mxfdz8aDQdpwkaj50LXrEfPEM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			T+HB/8qzY5vAWz1Qn8Eg/T7j6T/M5kcMqTeXr0jGems=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			PUFF9J0EaUUaluNLHL3tMpDrfU6s+FmdnlldrXOz0kw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ymJoJBs6zJBCJZc4rqS0sge1xU7U+x1yv02mwcYDW5k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1C1hgSmLW5shH6DR9fh/PY2koJIm1ibUOhLI1vXdSGQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr_CA.lproj</key>
		<dict>
			<key>symlink</key>
			<string>fr.lproj</string>
		</dict>
		<key>Resources/he.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			35ECtsAW7lQQpZTAtYBIKgel5ItYO6FvWJaSueWWqVU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			8J1Sp8nrNtZGvYku2nI9NolRtG5VuWmnW6YAFKV13hw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			XZzR/UxerKhK3aEdUMzI8JabTO55ATIpGSKGjnr9j0Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			THcrHX/rM5qHt+AVrAOj0HpF3NwSlaydM1B0DozfQEo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Dh4VgRSkntzRdCDvUFT0O91wxRUTyfKmsonwoD8JO3s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			nJEWpUskTwDAGD2CncoGYL/2GwzRY0UY4TygxawdTxY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			chgUNvZvUIuHhKzwm6uea91uzpojKjKFlO4BqUnpYX4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			anmR3yw7X4nMtY1al1a7wp1QVacmVUn/3pcyCSgJzrU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tjMnwfwU5Igglr94Dv24Nt3O31hM7fZvYJnDfQW9NEk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			J72+j1QxGUsm6+f27cMgbNbglaIwLA16Wnt2A/iH/mY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KSFUyyM9MeIZBqvL9nKauh7tjCMO9frSszTcGalX5lk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Dc/fju4q4Y3SjDM+07ZdITaYRf4oyGN0fc1aouu+4DU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+xaM4bwL38YJxASbDBXz+2fnds+FPoG4G/YcZrPWSNE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			LivKaeRpkD6ci1Rk5MKyZm6F3kVhl5gDJpWnaXswE9s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			5WjmqJ2TWHL+/vhwtUqgW25dWpVJcLGR+32XrLkei2c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ZTkFEPf8jF0x6LblOyxyvVlOkgN63ztFKMhqDNpnEiU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aP8grl5Ry4ImijxP8wX9MzNx1GKWKVmxMfS3cTODDlU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			tHMHEGgzigSmCrW2qecmoQBr4XBphbhMbZGeA29sL9U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			eQoD1fh2DfUGtN012G1NumtD672yovhnRMPqtfRj9tw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			f6htYoPTah5i/lmEsYHDKGU3x+POCMrZxK6v1cSubfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			oBWzWKqVlCPzKWCBvsUtJ6H13MchuyEwWhKMM4kjOq4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KdEN79bpGABW1i1WUT3ev2+htoYtkSvsZgcAMj35nuA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			IbkbBuFZCyQX/ivTn3DL6g5p9iqYTYrlaU0DdH8E7wg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			3csGBXUn8j9otrH1PzoxHiqtCWNUJCE8qOOxSV8BKHY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			r9KbetMXEQrV1+bL5yEjqMkzzuuTlk4RlZbaGVfWBDo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ldKIXFXeVIF9vPFvETpnOUgSRetVzFENcIDBbZZxvx0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			IoKFaPHFFhQIccavL2pAt5xmPXvG1ypSmYcNw84q4rw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			FeifkURPZaiNIEvFN3ZShW7Gf5KdyDVcwBw1kMC3fFM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HwsNLLSPg/K/JbI2yvmgSPTYXM5P9gfaJgS8wR2OPog=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt.lproj</key>
		<dict>
			<key>symlink</key>
			<string>pt_BR.lproj</string>
		</dict>
		<key>Resources/pt_BR.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			1zGugd8Gr2kLZ4q0eE62t40YVYd5+SZMo2cbYhW3b2c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ls/RX9Ljt2+Psi1BwcjF0IKUYSyhiiaI56e8/jQrHcs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			9TOgSv3k9pBylGCMyYQMnhI+JQII+B2NEK1MEjkNVHI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zjRkd49gU9VUXAcyE8ZVSAyfskOtw6/Mg+Z+iKkQ8jw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			1EyFmncvXO7vF2d+jERb2lMrhz6DHxQ68wEPBdUgdh8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			pJEE8PhYDut/zuAqWB4c7fYCiMg9aUz/KzDNc5xG0kM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			m4tYmXaVgVJbmuSgMvR9LL+WEcHwBwEkukx4/M8Rif8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			BSmBV0e2NO4PefCA5dL+UrqMJg/DOi6olm+mr7GP1Jw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			zLlzVTLmMa/CKfnkyjvsNdQEXDb3yyfE4oUHPRpNFwE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			8rC5h8jkFvGHp5fJiGiHoBbueEh5Ebuo7d2GvUnMNEM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			vRL+kzGu72lVve7inhE0gIAIU8HNxwr8xm78R0DuXLA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1NtaWe5xkasH4P/Ma1tHG844ydllawrSRpD908/lpP4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			p0HZcvgZW5wWrz58Ec94LZKCOWX43aDkTgTuOj8un2A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			E7W3E6qGqfHl7EkUld4TfEJuxJVLEPPSI14j5XySdGk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			uTz0r0WsOLj+MUTU1rTFEjsF9fUu0m8bCuCyD70xg0g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			odUHmWqOA4ZSC7wDIUpFD1A6ChR4WqcR37/w30rq0dQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			8FzVpJmyvPi+nvbtA+6BasijCqtdxV0g8r9L7kiT+wE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0kWq6kOWr8Nd4IGsZSwcGkM4BYMOQyE9mC9tODtGNeA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			XSYDWSdjyXwkLbWVV9Akk2h1GtI/rOtk8VArgIOoV0A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6YvPwbtzTbM9UWXZEGjmGWcX0TQjzVNSM+0RiSQ9jSE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			f38VMgcjKHiogdjUw6ORWHeFPPm0AenQMTSZJtwHwiw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KuS2pPe8SrEtsu2YTjDnH99+cMml1ki7MXOX783m9sA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			4o4U/LArtH3SQJpOKpA91tIlsHbPMLx5nDVP8A6ypGY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VXO4X+l+7gWZhzMi9NPZkSqFKYlEwUZPBzLv1BFD5XM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			/+0aXKVBfZyP+QeEzZz0f9Pr1Nta8X0C1hauCu/K2jI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			21oZL2QDv3q5koAR0vVLli3SuTKP8y4OSsdToPz5+j8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Xaqsiyw3W4zAvsp1W7CuKcaVZ3i/V4EV7q/sHmX062s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			JALChfnJN5R/5rlXw6ae6Eq7un2U5AhnkTyNN1sDMRw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ZAfjewk7W0z4tXBaQEAvJpdKIprcubgq+XmU2PZh04k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			jfMMl84Vr5FIWFvNSgLY+FvAiNoZx84kV9x7LTD7LF8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Q6X7vlxK5jEtl2jQmyMjmkJ4VxnlQfVnV9wW8M2QJAY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CaxhpacDM/KeDEPdb/N/ESYd0O74lFj9fE4qHPeW3Rk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			aKDmp+/p5oKx1n9jjk519pAa6Sa0P5c2vLLIkPkp/4g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			HljPB2xhzYDSxCNiA1hMYhtBH8W9u7NjnUmVz+uoWe4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			SKduxDarg7xeuibr2TizMi5KVrj8LlDEyg5ANBDCbY8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vSNCRrDPoFdPKuxOf007CU/HS0gzIWVA9CDyTMq6iLU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			QzjJscdNndpMcNi+JL1l3cFEgjY2UU07vU62jRH8iqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			SvtQymhMjSuSAzBcS9FSgtJbk19JyySqmfTOOzLeasg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			IdMDDEKKTPd2lg/oscQAGUG/oI+2A+l1/KXqddYilRc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			K/Pq3GisQjEQVUdR5sQRWueQzNtIbhfodBw0oGV6hKs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Ah1Uago9NvnL67CeSH/JJkES9IRy7tF5KokDA95l0sQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			rQI+uQNZ4Phnfpr2yGSsI4qVTV6vITO9+JwKvKWbAt8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			8opG+27v6dNKWxV8AyoUd9bgAw5UTpDcY7jgtpIGtn4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			38g3cjpb97nVKPi6AKZ9ntlt7blXmByBK0beg8bwu2E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUAutomaticUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			aO0gQfQcLmFwanviMUINh0as3tbqYTWRJeAXuBf/gOE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			hWcSGK3pQNsA8fSIbxNrrC32PHU1GZ9GvOm8KHNUTnI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdatePermissionPrompt.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			oOCdtzC+fL32gflAEcJRNni6dA12rKGJx1pKhfmIjDI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AqfSJvdrBIJoZ1I1440ZV9CbltpbJ1i1+ofre48GpO4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
