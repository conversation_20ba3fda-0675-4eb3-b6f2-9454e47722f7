/*!
 * Library to detect file mime type of a Uint8Array.
 *
 * Modified from https://github.com/sindresorhus/file-type to be used standalone on browser based apps.
 *
 * This library requires Node "buffer" module as a pre-requisite. The "buffer" module is made available in this repo
 * for standalone use via the `buffer.js` script which needs to be loaded before this file on the page.
 *
 * Author: <PERSON><PERSON><PERSON>, Krajee.com
 */
var KrajeeFileTypeConfig={minimumBytes:4100,defaultMessages:"End-Of-Stream",tarHeaderChecksumMatches:function(e,i=0){var t=Number.parseInt(e.toString("utf8",148,154).replace(/\0.*$/,"").trim(),8);if(Number.isNaN(t))return!1;var r=256;for(let t=i;t<i+148;t++)r+=e[t];for(let t=i+156;t<i+512;t++)r+=e[t];return t===r},uint32SyncSafeToken:{get:function(e,i){return 127&e[i+3]|e[i+2]<<7|e[i+1]<<14|e[i]<<21},len:4},dv:function(e){return new DataView(e.buffer,e.byteOffset)},Token:{UINT8:{len:1,get:function(e,i){return KrajeeFileTypeConfig.dv(e).getUint8(i)},put:function(e,i,t){return KrajeeFileTypeConfig.dv(e).setUint8(i,t),i+1}},UINT16_LE:{len:2,get:function(e,i){return KrajeeFileTypeConfig.dv(e).getUint16(i,!0)},put:function(e,i,t){return KrajeeFileTypeConfig.dv(e).setUint16(i,t,!0),i+2}},UINT16_BE:{len:2,get:function(e,i){return KrajeeFileTypeConfig.dv(e).getUint16(i)},put:function(e,i,t){return KrajeeFileTypeConfig.dv(e).setUint16(i,t),i+2}},INT32_BE:{len:4,get:function(e,i){return KrajeeFileTypeConfig.dv(e).getInt32(i)},put:function(e,i,t){return KrajeeFileTypeConfig.dv(e).setInt32(i,t),i+4}},UINT32_LE:{len:4,get:function(e,i){return KrajeeFileTypeConfig.dv(e).getUint32(i,!0)},put:function(e,i,t){return KrajeeFileTypeConfig.dv(e).setUint32(i,t,!0),i+4}},UINT32_BE:{len:4,get:function(e,i){return KrajeeFileTypeConfig.dv(e).getUint32(i)},put:function(e,i,t){return KrajeeFileTypeConfig.dv(e).setUint32(i,t),i+4}},UINT64_LE:{len:8,get:function(e,i){return KrajeeFileTypeConfig.dv(e).getBigUint64(i,!0)},put:function(e,i,t){return KrajeeFileTypeConfig.dv(e).setBigUint64(i,t,!0),i+8}},UINT64_BE:{len:8,get:function(e,i){return KrajeeFileTypeConfig.dv(e).getBigUint64(i)},put:function(e,i,t){return KrajeeFileTypeConfig.dv(e).setBigUint64(i,t),i+8}}}};class EndOfStreamError extends Error{constructor(){super(KrajeeFileTypeConfig.defaultMessages)}}class StringType{constructor(e,i){this.len=e,this.encoding=i}get(e,i){return Buffer.from(e).toString(this.encoding,i,i+this.len)}}class BufferTokenizer{constructor(e,i){this.position=0,this.numBuffer=new Uint8Array(8),this.fileInfo=i||{},this.uint8Array=e,this.fileInfo.size=this.fileInfo.size?this.fileInfo.size:e.length}async readToken(e,i=this.position){const t=Buffer.alloc(e.len);if(await this.readBuffer(t,{position:i})<e.len)throw new EndOfStreamError;return e.get(t,0)}async peekToken(e,i=this.position){const t=Buffer.alloc(e.len);if(await this.peekBuffer(t,{position:i})<e.len)throw new EndOfStreamError;return e.get(t,0)}async readBuffer(e,i){if(i&&i.position){if(i.position<this.position)throw new Error("`options.position` must be equal or greater than `tokenizer.position`");this.position=i.position}const t=await this.peekBuffer(e,i);return this.position+=t,t}async peekBuffer(e,i){const t=this.normalizeOptions(e,i),r=Math.min(this.uint8Array.length-t.position,t.length);if(!t.mayBeLess&&r<t.length)throw new EndOfStreamError;return e.set(this.uint8Array.subarray(t.position,t.position+r),t.offset),r}async readNumber(e){if(await this.readBuffer(this.numBuffer,{length:e.len})<e.len)throw new EndOfStreamError;return e.get(this.numBuffer,0)}async peekNumber(e){if(await this.peekBuffer(this.numBuffer,{length:e.len})<e.len)throw new EndOfStreamError;return e.get(this.numBuffer,0)}async close(){}async ignore(e){if(void 0!==this.fileInfo.size){const i=this.fileInfo.size-this.position;if(e>i)return this.position+=i,i}return this.position+=e,e}normalizeOptions(e,i){if(i&&void 0!==i.position&&i.position<this.position)throw new Error("`options.position` must be equal or greater than `tokenizer.position`");return i?{mayBeLess:!0===i.mayBeLess,offset:i.offset?i.offset:0,length:i.length?i.length:e.length-(i.offset?i.offset:0),position:i.position?i.position:this.position}:{mayBeLess:!1,offset:0,length:e.length,position:this.position}}}class FileTypeParser{_check(e,i,t){t={offset:0,...t};for(const[r,n]of i.entries())if(t.mask){if(n!==(t.mask[r]&e[r+t.offset]))return!1}else if(n!==e[r+t.offset])return!1;return!0}check(e,i){return this._check(this.buffer,e,i)}stringToBytes(e){return[...e].map(e=>e.charCodeAt(0))}checkString(e,i){return this.check(this.stringToBytes(e),i)}async parse(e){if(!(e instanceof Uint8Array||e instanceof ArrayBuffer))throw new TypeError(`Expected the \`input\` argument to be of type \`Uint8Array\` or \`Buffer\` or \`ArrayBuffer\`, got \`${typeof e}\``);const i=e instanceof Uint8Array?e:new Uint8Array(e);if(!(i&&i.length>1))return;const t=new BufferTokenizer(i);try{return this.parseTokenizer(t)}catch(e){if(!(e instanceof EndOfStreamError))throw e}}async parseTokenizer(e){const i=KrajeeFileTypeConfig.Token;if(this.buffer=Buffer.alloc(KrajeeFileTypeConfig.minimumBytes),void 0===e.fileInfo.size&&(e.fileInfo.size=Number.MAX_SAFE_INTEGER),this.tokenizer=e,await e.peekBuffer(this.buffer,{length:12,mayBeLess:!0}),this.check([66,77]))return{ext:"bmp",mime:"image/bmp"};if(this.check([11,119]))return{ext:"ac3",mime:"audio/vnd.dolby.dd-raw"};if(this.check([120,1]))return{ext:"dmg",mime:"application/x-apple-diskimage"};if(this.check([77,90]))return{ext:"exe",mime:"application/x-msdownload"};if(this.check([37,33]))return await e.peekBuffer(this.buffer,{length:24,mayBeLess:!0}),this.checkString("PS-Adobe-",{offset:2})&&this.checkString(" EPSF-",{offset:14})?{ext:"eps",mime:"application/eps"}:{ext:"ps",mime:"application/postscript"};if(this.check([31,160])||this.check([31,157]))return{ext:"Z",mime:"application/x-compress"};if(this.check([71,73,70]))return{ext:"gif",mime:"image/gif"};if(this.check([255,216,255]))return{ext:"jpg",mime:"image/jpeg"};if(this.check([73,73,188]))return{ext:"jxr",mime:"image/vnd.ms-photo"};if(this.check([31,139,8]))return{ext:"gz",mime:"application/gzip"};if(this.check([66,90,104]))return{ext:"bz2",mime:"application/x-bzip2"};if(this.checkString("ID3")){await e.ignore(6);const i=await e.readToken(KrajeeFileTypeConfig.uint32SyncSafeToken);return e.position+i>e.fileInfo.size?{ext:"mp3",mime:"audio/mpeg"}:(await e.ignore(i),fileTypeFromTokenizer(e))}if(this.checkString("MP+"))return{ext:"mpc",mime:"audio/x-musepack"};if((67===this.buffer[0]||70===this.buffer[0])&&this.check([87,83],{offset:1}))return{ext:"swf",mime:"application/x-shockwave-flash"};if(this.checkString("FLIF"))return{ext:"flif",mime:"image/flif"};if(this.checkString("8BPS"))return{ext:"psd",mime:"image/vnd.adobe.photoshop"};if(this.checkString("WEBP",{offset:8}))return{ext:"webp",mime:"image/webp"};if(this.checkString("MPCK"))return{ext:"mpc",mime:"audio/x-musepack"};if(this.checkString("FORM"))return{ext:"aif",mime:"audio/aiff"};if(this.checkString("icns",{offset:0}))return{ext:"icns",mime:"image/icns"};if(this.check([80,75,3,4])){try{for(;e.position+30<e.fileInfo.size;){await e.readBuffer(this.buffer,{length:30});const i={compressedSize:this.buffer.readUInt32LE(18),uncompressedSize:this.buffer.readUInt32LE(22),filenameLength:this.buffer.readUInt16LE(26),extraFieldLength:this.buffer.readUInt16LE(28)};if(i.filename=await e.readToken(new StringType(i.filenameLength,"utf-8")),await e.ignore(i.extraFieldLength),"META-INF/mozilla.rsa"===i.filename)return{ext:"xpi",mime:"application/x-xpinstall"};if(i.filename.endsWith(".rels")||i.filename.endsWith(".xml")){switch(i.filename.split("/")[0]){case"_rels":break;case"word":return{ext:"docx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"};case"ppt":return{ext:"pptx",mime:"application/vnd.openxmlformats-officedocument.presentationml.presentation"};case"xl":return{ext:"xlsx",mime:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}}if(i.filename.startsWith("xl/"))return{ext:"xlsx",mime:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"};if(i.filename.startsWith("3D/")&&i.filename.endsWith(".model"))return{ext:"3mf",mime:"model/3mf"};if("mimetype"===i.filename&&i.compressedSize===i.uncompressedSize){switch((await e.readToken(new StringType(i.compressedSize,"utf-8"))).trim()){case"application/epub+zip":return{ext:"epub",mime:"application/epub+zip"};case"application/vnd.oasis.opendocument.text":return{ext:"odt",mime:"application/vnd.oasis.opendocument.text"};case"application/vnd.oasis.opendocument.spreadsheet":return{ext:"ods",mime:"application/vnd.oasis.opendocument.spreadsheet"};case"application/vnd.oasis.opendocument.presentation":return{ext:"odp",mime:"application/vnd.oasis.opendocument.presentation"}}}if(0===i.compressedSize){let i=-1;for(;i<0&&e.position<e.fileInfo.size;)await e.peekBuffer(this.buffer,{mayBeLess:!0}),i=this.buffer.indexOf("504B0304",0,"hex"),await e.ignore(i>=0?i:this.buffer.length)}else await e.ignore(i.compressedSize)}}catch(e){if(!(e instanceof EndOfStreamError))throw e}return{ext:"zip",mime:"application/zip"}}if(this.checkString("OggS")){await e.ignore(28);const i=Buffer.alloc(8);return await e.readBuffer(i),this._check(i,[79,112,117,115,72,101,97,100])?{ext:"opus",mime:"audio/opus"}:this._check(i,[128,116,104,101,111,114,97])?{ext:"ogv",mime:"video/ogg"}:this._check(i,[1,118,105,100,101,111,0])?{ext:"ogm",mime:"video/ogg"}:this._check(i,[127,70,76,65,67])?{ext:"oga",mime:"audio/ogg"}:this._check(i,[83,112,101,101,120,32,32])?{ext:"spx",mime:"audio/ogg"}:this._check(i,[1,118,111,114,98,105,115])?{ext:"ogg",mime:"audio/ogg"}:{ext:"ogx",mime:"application/ogg"}}if(this.check([80,75])&&(3===this.buffer[2]||5===this.buffer[2]||7===this.buffer[2])&&(4===this.buffer[3]||6===this.buffer[3]||8===this.buffer[3]))return{ext:"zip",mime:"application/zip"};if(this.checkString("ftyp",{offset:4})&&0!=(96&this.buffer[8])){const e=this.buffer.toString("binary",8,12).replace("\0"," ").trim();switch(e){case"avif":case"avis":return{ext:"avif",mime:"image/avif"};case"mif1":return{ext:"heic",mime:"image/heif"};case"msf1":return{ext:"heic",mime:"image/heif-sequence"};case"heic":case"heix":return{ext:"heic",mime:"image/heic"};case"hevc":case"hevx":return{ext:"heic",mime:"image/heic-sequence"};case"qt":return{ext:"mov",mime:"video/quicktime"};case"M4V":case"M4VH":case"M4VP":return{ext:"m4v",mime:"video/x-m4v"};case"M4P":return{ext:"m4p",mime:"video/mp4"};case"M4B":return{ext:"m4b",mime:"audio/mp4"};case"M4A":return{ext:"m4a",mime:"audio/x-m4a"};case"F4V":return{ext:"f4v",mime:"video/mp4"};case"F4P":return{ext:"f4p",mime:"video/mp4"};case"F4A":return{ext:"f4a",mime:"audio/mp4"};case"F4B":return{ext:"f4b",mime:"audio/mp4"};case"crx":return{ext:"cr3",mime:"image/x-canon-cr3"};default:return e.startsWith("3g")?e.startsWith("3g2")?{ext:"3g2",mime:"video/3gpp2"}:{ext:"3gp",mime:"video/3gpp"}:{ext:"mp4",mime:"video/mp4"}}}if(this.checkString("MThd"))return{ext:"mid",mime:"audio/midi"};if(this.checkString("wOFF")&&(this.check([0,1,0,0],{offset:4})||this.checkString("OTTO",{offset:4})))return{ext:"woff",mime:"font/woff"};if(this.checkString("wOF2")&&(this.check([0,1,0,0],{offset:4})||this.checkString("OTTO",{offset:4})))return{ext:"woff2",mime:"font/woff2"};if(this.check([212,195,178,161])||this.check([161,178,195,212]))return{ext:"pcap",mime:"application/vnd.tcpdump.pcap"};if(this.checkString("DSD "))return{ext:"dsf",mime:"audio/x-dsf"};if(this.checkString("LZIP"))return{ext:"lz",mime:"application/x-lzip"};if(this.checkString("fLaC"))return{ext:"flac",mime:"audio/x-flac"};if(this.check([66,80,71,251]))return{ext:"bpg",mime:"image/bpg"};if(this.checkString("wvpk"))return{ext:"wv",mime:"audio/wavpack"};if(this.checkString("%PDF")){await e.ignore(1350);const i=10485760,t=Buffer.alloc(Math.min(i,e.fileInfo.size));return await e.readBuffer(t,{mayBeLess:!0}),t.includes(Buffer.from("AIPrivateData"))?{ext:"ai",mime:"application/postscript"}:{ext:"pdf",mime:"application/pdf"}}if(this.check([0,97,115,109]))return{ext:"wasm",mime:"application/wasm"};if(this.check([73,73])){const e=await this.readTiffHeader(!1);if(e)return e}if(this.check([77,77])){const e=await this.readTiffHeader(!0);if(e)return e}if(this.checkString("MAC "))return{ext:"ape",mime:"audio/ape"};if(this.check([26,69,223,163])){async function t(){const t=await e.peekNumber(i.UINT8);let r=128,n=0;for(;0==(t&r);)++n,r>>=1;const s=Buffer.alloc(n+1);return await e.readBuffer(s),s}async function r(){const e=await t(),i=await t();i[0]^=128>>i.length-1;const r=Math.min(6,i.length);return{id:e.readUIntBE(0,e.length),len:i.readUIntBE(i.length-r,r)}}const n=await r();switch(await async function(i,t){for(;t>0;){const i=await r();if(17026===i.id)return(await e.readToken(new StringType(i.len,"utf-8"))).replace(/\00.*$/g,"");await e.ignore(i.len),--t}}(0,n.len)){case"webm":return{ext:"webm",mime:"video/webm"};case"matroska":return{ext:"mkv",mime:"video/x-matroska"};default:return}}if(this.check([82,73,70,70])){if(this.check([65,86,73],{offset:8}))return{ext:"avi",mime:"video/vnd.avi"};if(this.check([87,65,86,69],{offset:8}))return{ext:"wav",mime:"audio/vnd.wave"};if(this.check([81,76,67,77],{offset:8}))return{ext:"qcp",mime:"audio/qcelp"}}if(this.checkString("SQLi"))return{ext:"sqlite",mime:"application/x-sqlite3"};if(this.check([78,69,83,26]))return{ext:"nes",mime:"application/x-nintendo-nes-rom"};if(this.checkString("Cr24"))return{ext:"crx",mime:"application/x-google-chrome-extension"};if(this.checkString("MSCF")||this.checkString("ISc("))return{ext:"cab",mime:"application/vnd.ms-cab-compressed"};if(this.check([237,171,238,219]))return{ext:"rpm",mime:"application/x-rpm"};if(this.check([197,208,211,198]))return{ext:"eps",mime:"application/eps"};if(this.check([40,181,47,253]))return{ext:"zst",mime:"application/zstd"};if(this.check([127,69,76,70]))return{ext:"elf",mime:"application/x-elf"};if(this.check([79,84,84,79,0]))return{ext:"otf",mime:"font/otf"};if(this.checkString("#!AMR"))return{ext:"amr",mime:"audio/amr"};if(this.checkString("{\\rtf"))return{ext:"rtf",mime:"application/rtf"};if(this.check([70,76,86,1]))return{ext:"flv",mime:"video/x-flv"};if(this.checkString("IMPM"))return{ext:"it",mime:"audio/x-it"};if(this.checkString("-lh0-",{offset:2})||this.checkString("-lh1-",{offset:2})||this.checkString("-lh2-",{offset:2})||this.checkString("-lh3-",{offset:2})||this.checkString("-lh4-",{offset:2})||this.checkString("-lh5-",{offset:2})||this.checkString("-lh6-",{offset:2})||this.checkString("-lh7-",{offset:2})||this.checkString("-lzs-",{offset:2})||this.checkString("-lz4-",{offset:2})||this.checkString("-lz5-",{offset:2})||this.checkString("-lhd-",{offset:2}))return{ext:"lzh",mime:"application/x-lzh-compressed"};if(this.check([0,0,1,186])){if(this.check([33],{offset:4,mask:[241]}))return{ext:"mpg",mime:"video/MP1S"};if(this.check([68],{offset:4,mask:[196]}))return{ext:"mpg",mime:"video/MP2P"}}if(this.checkString("ITSF"))return{ext:"chm",mime:"application/vnd.ms-htmlhelp"};if(this.check([253,55,122,88,90,0]))return{ext:"xz",mime:"application/x-xz"};if(this.checkString("<?xml "))return{ext:"xml",mime:"application/xml"};if(this.check([55,122,188,175,39,28]))return{ext:"7z",mime:"application/x-7z-compressed"};if(this.check([82,97,114,33,26,7])&&(0===this.buffer[6]||1===this.buffer[6]))return{ext:"rar",mime:"application/x-rar-compressed"};if(this.checkString("solid "))return{ext:"stl",mime:"model/stl"};if(this.checkString("BLENDER"))return{ext:"blend",mime:"application/x-blender"};if(this.checkString("!<arch>")){return await e.ignore(8),"debian-binary"===await e.readToken(new StringType(13,"ascii"))?{ext:"deb",mime:"application/x-deb"}:{ext:"ar",mime:"application/x-unix-archive"}}if(this.check([137,80,78,71,13,10,26,10])){async function n(){return{length:await e.readToken(i.INT32_BE),type:await e.readToken(new StringType(4,"binary"))}}await e.ignore(8);do{const i=await n();if(i.length<0)return;switch(i.type){case"IDAT":return{ext:"png",mime:"image/png"};case"acTL":return{ext:"apng",mime:"image/apng"};default:await e.ignore(i.length+4)}}while(e.position+8<e.fileInfo.size);return{ext:"png",mime:"image/png"}}if(this.check([65,82,82,79,87,49,0,0]))return{ext:"arrow",mime:"application/x-apache-arrow"};if(this.check([103,108,84,70,2,0,0,0]))return{ext:"glb",mime:"model/gltf-binary"};if(this.check([102,114,101,101],{offset:4})||this.check([109,100,97,116],{offset:4})||this.check([109,111,111,118],{offset:4})||this.check([119,105,100,101],{offset:4}))return{ext:"mov",mime:"video/quicktime"};if(this.check([239,187,191])&&this.checkString("<?xml",{offset:3}))return{ext:"xml",mime:"application/xml"};if(this.check([73,73,82,79,8,0,0,0,24]))return{ext:"orf",mime:"image/x-olympus-orf"};if(this.checkString("gimp xcf "))return{ext:"xcf",mime:"image/x-xcf"};if(this.check([73,73,85,0,24,0,0,0,136,231,116,216]))return{ext:"rw2",mime:"image/x-panasonic-rw2"};if(this.check([48,38,178,117,142,102,207,17,166,217])){async function s(){const t=Buffer.alloc(16);return await e.readBuffer(t),{id:t,size:Number(await e.readToken(i.UINT64_LE))}}for(await e.ignore(30);e.position+24<e.fileInfo.size;){const i=await s();let t=i.size-24;if(this._check(i.id,[145,7,220,183,183,169,207,17,142,230,0,192,12,32,83,101])){const i=Buffer.alloc(16);if(t-=await e.readBuffer(i),this._check(i,[64,158,105,248,77,91,207,17,168,253,0,128,95,92,68,43]))return{ext:"asf",mime:"audio/x-ms-asf"};if(this._check(i,[192,239,25,188,77,91,207,17,168,253,0,128,95,92,68,43]))return{ext:"asf",mime:"video/x-ms-asf"};break}await e.ignore(t)}return{ext:"asf",mime:"application/vnd.ms-asf"}}if(this.check([171,75,84,88,32,49,49,187,13,10,26,10]))return{ext:"ktx",mime:"image/ktx"};if((this.check([126,16,4])||this.check([126,24,4]))&&this.check([48,77,73,69],{offset:4}))return{ext:"mie",mime:"application/x-mie"};if(this.check([39,10,0,0,0,0,0,0,0,0,0,0],{offset:2}))return{ext:"shp",mime:"application/x-esri-shape"};if(this.check([0,0,0,12,106,80,32,32,13,10,135,10])){switch(await e.ignore(20),await e.readToken(new StringType(4,"ascii"))){case"jp2 ":return{ext:"jp2",mime:"image/jp2"};case"jpx ":return{ext:"jpx",mime:"image/jpx"};case"jpm ":return{ext:"jpm",mime:"image/jpm"};case"mjp2":return{ext:"mj2",mime:"image/mj2"};default:return}}if(this.check([255,10])||this.check([0,0,0,12,74,88,76,32,13,10,135,10]))return{ext:"jxl",mime:"image/jxl"};if(this.check([254,255,0,60,0,63,0,120,0,109,0,108])||this.check([255,254,60,0,63,0,120,0,109,0,108,0]))return{ext:"xml",mime:"application/xml"};if(this.check([0,0,1,186])||this.check([0,0,1,179]))return{ext:"mpg",mime:"video/mpeg"};if(this.check([0,1,0,0,0]))return{ext:"ttf",mime:"font/ttf"};if(this.check([0,0,1,0]))return{ext:"ico",mime:"image/x-icon"};if(this.check([0,0,2,0]))return{ext:"cur",mime:"image/x-icon"};if(this.check([208,207,17,224,161,177,26,225]))return{ext:"cfb",mime:"application/x-cfb"};if(await e.peekBuffer(this.buffer,{length:Math.min(256,e.fileInfo.size),mayBeLess:!0}),this.checkString("BEGIN:")){if(this.checkString("VCARD",{offset:6}))return{ext:"vcf",mime:"text/vcard"};if(this.checkString("VCALENDAR",{offset:6}))return{ext:"ics",mime:"text/calendar"}}if(this.checkString("FUJIFILMCCD-RAW"))return{ext:"raf",mime:"image/x-fujifilm-raf"};if(this.checkString("Extended Module:"))return{ext:"xm",mime:"audio/x-xm"};if(this.checkString("Creative Voice File"))return{ext:"voc",mime:"audio/x-voc"};if(this.check([4,0,0,0])&&this.buffer.length>=16){const e=this.buffer.readUInt32LE(12);if(e>12&&this.buffer.length>=e+16)try{const i=this.buffer.slice(16,e+16).toString();if(JSON.parse(i).files)return{ext:"asar",mime:"application/x-asar"}}catch(e){console.log(e)}}if(this.check([6,14,43,52,2,5,1,1,13,1,2,1,1,2]))return{ext:"mxf",mime:"application/mxf"};if(this.checkString("SCRM",{offset:44}))return{ext:"s3m",mime:"audio/x-s3m"};if(this.check([71])&&this.check([71],{offset:188}))return{ext:"mts",mime:"video/mp2t"};if(this.check([71],{offset:4})&&this.check([71],{offset:196}))return{ext:"mts",mime:"video/mp2t"};if(this.check([66,79,79,75,77,79,66,73],{offset:60}))return{ext:"mobi",mime:"application/x-mobipocket-ebook"};if(this.check([68,73,67,77],{offset:128}))return{ext:"dcm",mime:"application/dicom"};if(this.check([76,0,0,0,1,20,2,0,0,0,0,0,192,0,0,0,0,0,0,70]))return{ext:"lnk",mime:"application/x.ms.shortcut"};if(this.check([98,111,111,107,0,0,0,0,109,97,114,107,0,0,0,0]))return{ext:"alias",mime:"application/x.apple.alias"};if(this.check([76,80],{offset:34})&&(this.check([0,0,1],{offset:8})||this.check([1,0,2],{offset:8})||this.check([2,0,2],{offset:8})))return{ext:"eot",mime:"application/vnd.ms-fontobject"};if(this.check([6,6,237,245,216,29,70,229,189,49,239,231,254,116,183,29]))return{ext:"indd",mime:"application/x-indesign"};if(await e.peekBuffer(this.buffer,{length:Math.min(512,e.fileInfo.size),mayBeLess:!0}),KrajeeFileTypeConfig.tarHeaderChecksumMatches(this.buffer))return{ext:"tar",mime:"application/x-tar"};if(this.check([255,254,255,14,83,0,107,0,101,0,116,0,99,0,104,0,85,0,112,0,32,0,77,0,111,0,100,0,101,0,108,0]))return{ext:"skp",mime:"application/vnd.sketchup.skp"};if(this.checkString("-----BEGIN PGP MESSAGE-----"))return{ext:"pgp",mime:"application/pgp-encrypted"};if(this.buffer.length>=2&&this.check([255,224],{offset:0,mask:[255,224]})){if(this.check([16],{offset:1,mask:[22]}))return this.check([8],{offset:1,mask:[8]}),{ext:"aac",mime:"audio/aac"};if(this.check([2],{offset:1,mask:[6]}))return{ext:"mp3",mime:"audio/mpeg"};if(this.check([4],{offset:1,mask:[6]}))return{ext:"mp2",mime:"audio/mpeg"};if(this.check([6],{offset:1,mask:[6]}))return{ext:"mp1",mime:"audio/mpeg"}}return{}}async readTiffTag(e){const i=KrajeeFileTypeConfig.Token,t=await this.tokenizer.readToken(e?i.UINT16_BE:i.UINT16_LE);switch(this.tokenizer.ignore(10),t){case 50341:return{ext:"arw",mime:"image/x-sony-arw"};case 50706:return{ext:"dng",mime:"image/x-adobe-dng"}}}async readTiffIFD(e){const i=KrajeeFileTypeConfig.Token,t=await this.tokenizer.readToken(e?i.UINT16_BE:i.UINT16_LE);for(let i=0;i<t;++i){const i=await this.readTiffTag(e);if(i)return i}}async readTiffHeader(e){const i=KrajeeFileTypeConfig.Token,t=(e?i.UINT16_BE:i.UINT16_LE).get(this.buffer,2),r=(e?i.UINT32_BE:i.UINT32_LE).get(this.buffer,4);if(42===t){if(r>=6){if(this.checkString("CR",{offset:8}))return{ext:"cr2",mime:"image/x-canon-cr2"};if(r>=8&&(this.check([28,0,254,0],{offset:8})||this.check([31,0,11,0],{offset:8})))return{ext:"nef",mime:"image/x-nikon-nef"}}await this.tokenizer.ignore(r);const e=await this.readTiffIFD(!1);return e||{ext:"tif",mime:"image/tiff"}}if(43===t)return{ext:"tif",mime:"image/tiff"}}}