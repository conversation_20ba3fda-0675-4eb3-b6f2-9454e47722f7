"%1$@ %2$@ has been downloaded and is ready to use! Would you like to install it and relaunch %1$@ now?" = "%1$@ %2$@ er hentet og klar til brug! Vil du installere og genstarte %1$@ nu?";

"%1$@ can't be updated when it's running from a read-only volume like a disk image or an optical drive. Move %1$@ to your Applications folder, relaunch it from there, and try again." = "%1$@ kan ikke opdateres når det køres fra en kun læsbar enhed. Flyt %1$@ til mappen Programmer, genstart derfra og prøv igen.";

"%@ %@ is currently the newest version available." = "%1$@ %2$@ er den aktuelle version.";

/* Description text for SUUpdateAlert when the update is downloadable. */
"%@ %@ is now available--you have %@. Would you like to download it now?" = "%1$@ %2$@ er tilgængelig! Du har %3$@. Skal den hentes nu?";

/* Description text for SUUpdateAlert when the update informational with no download. */
"%@ %@ is now available--you have %@. Would you like to learn more about this update on the web?" = "%1$@ %2$@ is now available--you have %3$@. Would you like to learn more about this update on the web?";

"%@ downloaded" = "%@ hentet";

"%@ of %@" = "%1$@ af %2$@";

"A new version of %@ is available!" = "En ny version af %@ er tilgængelig!";

"A new version of %@ is ready to install!" = "En ny version af %@ er klar til installering!";

"An error occurred in retrieving update information. Please try again later." = "Kunne ikke modtage informationer om opdateringer. Kontroller at du har forbindelse til internettet eller prøv igen senere.";

"An error occurred while downloading the update. Please try again later." = "Opdateringen kunne ikke hentes. Prøv igen senere.";

"An error occurred while extracting the archive. Please try again later." = "Arkivet kunne ikke udpakkes. Prøv igen senere.";

"An error occurred while installing the update. Please try again later." = "Opdateringen kunne ikke installeres. Prøv igen senere.";

"An error occurred while parsing the update feed." = "An error occurred while parsing the update feed.";

"An error occurred while relaunching %1$@, but the new version will be available next time you run %1$@." = "%1$@ kunne ikke genstartes. Den nye version vil være tilgængelig, næste gang %1$@ startes.";

/* the unit for bytes */
"B" = "B";

"Cancel" = "Annuller";

"Cancel Update" = "Annuller opdatering";

"Checking for updates..." = "Søger efter opdateringer…";

/* Take care not to overflow the status window. */
"Downloading update..." = "Henter opdatering…";

/* Take care not to overflow the status window. */
"Extracting update..." = "Udpakker arkiver…";

/* the unit for gigabytes */
"GB" = "GB";

"Install and Relaunch" = "Installer og genstart";

/* Take care not to overflow the status window. */
"Installing update..." = "Installerer opdatering…";

/* the unit for kilobytes */
"KB" = "KB";

/* the unit for megabytes */
"MB" = "MB";

/* OK button. */
"OK" = "OK";

/* Status message on progress window once download has finished. */
"Ready to Install" = "Klar til installering";

/* Message that is optionally shown at startup to allow users to turn on/off update checks. */
"Should %1$@ automatically check for updates? You can always check for updates manually from the %1$@ menu." = "Skal %1$@ søge efter opdateringer automatisk? Du kan altid søge efter opdateringer manuelt fra programmets menu.";

"Update Error!" = "Der opstod en fejl!";

"Updating %@" = "Opdaterer %@";

/* 'Error' message when the user checks for updates but is already current or the feed doesn't contain any updates. (not necessarily shown in UI) */
"You already have the newest version of %@." = "Du har den seneste version af %@.";

/* Status message shown when the user checks for updates but is already current or the feed doesn't contain any updates. */
"You're up-to-date!" = "Der er ingen opdateringer";

/* Alternative name for "Install" button if we have a paid update or other update
	without a download but with a URL. */
"Learn More..." = "Læs mere…";

